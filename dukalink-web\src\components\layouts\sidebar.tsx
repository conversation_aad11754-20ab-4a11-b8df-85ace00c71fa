"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { NavigationLink } from "@/components/ui/navigation-link";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useCurrentUser } from "@/features/auth/hooks/use-auth";
import { cn } from "@/lib/utils";
import {
  Activity,
  ArrowLeftRight as ArrowLeftRightIcon,
  Banknote,
  BarChart3Icon,
  BarChart4,
  Box as BoxIcon,
  Building,
  BuildingIcon,
  ChevronDown,
  ChevronRight,
  Clipboard,
  CreditCard,
  DollarSign,
  FolderTree,
  HomeIcon,
  Landmark,
  MapPin,
  Package,
  PackageOpen,
  Receipt,
  Settings,
  Shield,
  ShoppingBag,
  Tag,
  Truck as TruckIcon,
  User,
  UserCheck,
  Users,
  Users2,
  UsersIcon,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";

interface SidebarItemProps {
  href: string;
  icon: React.ReactNode;
  title: string;
  isActive?: boolean;
}

function SidebarItem({ href, icon, title, isActive }: SidebarItemProps) {
  return (
    <Button
      asChild
      variant={isActive ? "secondary" : "ghost"}
      className={cn("w-full justify-start", isActive ? "bg-secondary" : "")}
    >
      <NavigationLink href={href} prefetch={true}>
        <span className="mr-2">{icon}</span>
        {title}
      </NavigationLink>
    </Button>
  );
}

interface NavigationGroupProps {
  group: {
    name: string;
    items: {
      href: string;
      icon: React.ReactNode;
      title: string;
      roles?: string[];
    }[];
  };
  pathname: string;
  userRoleName: string;
}

function NavigationGroup({
  group,
  pathname,
  userRoleName,
}: NavigationGroupProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Check if any item in the group is active
  const isGroupActive =
    group &&
    Array.isArray(group.items) &&
    group.items.some(
      (item) => pathname === item.href || pathname.startsWith(`${item.href}/`)
    );

  // Auto-expand the group if an item is active
  useEffect(() => {
    if (isGroupActive) {
      setIsOpen(true);
    }
  }, [isGroupActive]);

  // Check if group and items exist before rendering
  if (!group || !group.items) {
    return null;
  }

  // Filter items based on user role
  const filteredItems = group.items.filter(
    (item) => !item.roles || (userRoleName && item.roles.includes(userRoleName))
  );

  // Don't render the group if there are no items to show
  if (filteredItems.length === 0) {
    return null;
  }

  return (
    <div className="space-y-1">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between px-3 py-2 text-sm font-medium"
          >
            <div className="flex items-center">
              <span>{group.name}</span>
            </div>
            {isOpen ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-1 pl-2">
          {filteredItems.map((item) => (
            <SidebarItem
              key={`${item.href}-${item.title}`}
              href={item.href}
              icon={item.icon}
              title={item.title}
              isActive={pathname === item.href}
            />
          ))}
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}

export function Sidebar() {
  const { data: user } = useCurrentUser();
  const pathname = usePathname();

  // Define navigation items grouped by category
  const navigationGroups = [
    {
      name: "Main",
      items: [
        {
          href: "/dashboard",
          icon: <HomeIcon className="h-4 w-4" />,
          title: "Dashboard",
        },
      ],
    },
    {
      name: "Reports & Analytics",
      items: [
        {
          href: "/reports",
          icon: <BarChart3Icon className="h-4 w-4" />,
          title: "Reports Dashboard",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/sales-summary",
          icon: <BarChart4 className="h-4 w-4" />,
          title: "Sales Summary",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/sales-by-item",
          icon: <PackageOpen className="h-4 w-4" />,
          title: "Sales by Item",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/sales-by-category",
          icon: <ShoppingBag className="h-4 w-4" />,
          title: "Sales by Category",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/mpesa-banking",
          icon: <DollarSign className="h-4 w-4" />,
          title: "Banking",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/cash-status",
          icon: <Banknote className="h-4 w-4" />,
          title: "Cash Status",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "operations_manager",
            "assistant_operations_manager",
          ],
        },
        {
          href: "/reports/dsa-sales",
          icon: <Users className="h-4 w-4" />,
          title: "DSA Sales",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/receipts",
          icon: <Receipt className="h-4 w-4" />,
          title: "Receipts",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/shifts",
          icon: <Clipboard className="h-4 w-4" />,
          title: "Shifts",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/stock-report",
          icon: <Package className="h-4 w-4" />,
          title: "Stock Report",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "stock_admin",
          ],
        },
        {
          href: "/expense-analytics",
          icon: <Activity className="h-4 w-4" />,
          title: "Expense Analytics",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "operations_manager",
            "assistant_operations_manager",
          ],
        },
      ],
    },
    {
      name: "Administration",
      items: [
        // Tenants menu item removed
        {
          href: "/users",
          icon: <UsersIcon className="h-4 w-4" />,
          title: "Users",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/roles",
          icon: <Shield className="h-4 w-4" />,
          title: "Roles",
          roles: ["super_admin", "company_admin"],
        },
        {
          href: "/branches",
          icon: <BuildingIcon className="h-4 w-4" />,
          title: "Branches",
          roles: ["super_admin", "company_admin"],
        },
        {
          href: "/locations",
          icon: <MapPin className="h-4 w-4" />,
          title: "Locations",
          roles: ["super_admin", "company_admin", "tenant_admin"],
        },
        {
          href: "/employees",
          icon: <Users2 className="h-4 w-4" />,
          title: "Employees",
          roles: ["super_admin", "company_admin"],
        },
      ],
    },
    {
      name: "Products & Inventory",
      items: [
        {
          href: "/products",
          icon: <Package className="h-4 w-4" />,
          title: "Products",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
          ],
        },
        {
          href: "/categories",
          icon: <FolderTree className="h-4 w-4" />,
          title: "Categories",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/brands",
          icon: <Tag className="h-4 w-4" />,
          title: "Brands",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/inventory",
          icon: <BoxIcon className="h-4 w-4" />,
          title: "Inventory",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "finance_manager",
            "accountant",
            "float_manager",
          ],
        },
        {
          href: "/inventory/suppliers",
          icon: <TruckIcon className="h-4 w-4" />,
          title: "Suppliers",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
          ],
        },
        {
          href: "/inventory/purchases",
          icon: <ShoppingBag className="h-4 w-4" />,
          title: "Purchases",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
          ],
        },
        {
          href: "/inventory/stock-cards",
          icon: <Clipboard className="h-4 w-4" />,
          title: "Stock Cards",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "accountant",
          ],
        },
        {
          href: "/inventory/transfers",
          icon: <ArrowLeftRightIcon className="h-4 w-4" />,
          title: "Stock Transfers",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "finance_manager",
            "accountant",
            "float_manager",
          ],
        },
        {
          href: "/inventory/reports",
          icon: <BarChart3Icon className="h-4 w-4" />,
          title: "Inventory Reports",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "finance_manager",
            "accountant",
            "float_manager",
          ],
        },
      ],
    },
    {
      name: "Float Management",
      items: [
        {
          href: "/float",
          icon: <Banknote className="h-4 w-4" />,
          title: "Float Dashboard",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "float_manager",
          ],
        },
        // Temporarily hidden until API is implemented
        // {
        //   href: "/float/transactions",
        //   icon: <WalletIcon className="h-4 w-4" />,
        //   title: "Float Transactions",
        //   roles: ["super_admin", "company_admin", "tenant_admin", "float_manager"],
        // },
        {
          href: "/float/movements",
          icon: <TruckIcon className="h-4 w-4" />,
          title: "Float Movements",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "float_manager",
          ],
        },
        {
          href: "/float/reconciliations",
          icon: <BarChart3Icon className="h-4 w-4" />,
          title: "Float Reconciliations",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "float_manager",
          ],
        },
      ],
    },
    {
      name: "Banking Management",
      items: [
        {
          href: "/banking",
          icon: <Landmark className="h-4 w-4" />,
          title: "Banking Records",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "float_manager",
          ],
        },
        {
          href: "/banking/summary",
          icon: <BarChart3Icon className="h-4 w-4" />,
          title: "Banking Summary",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "float_manager",
          ],
        },
      ],
    },
    {
      name: "M-Pesa Management",
      items: [
        {
          href: "/mpesa/transactions",
          icon: <Banknote className="h-4 w-4" />,
          title: "M-Pesa Transactions",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "branch_admin",
            "accountant",
          ],
        },
      ],
    },
    {
      name: "Expenses Management",
      items: [
        {
          href: "/expenses",
          icon: <Banknote className="h-4 w-4" />,
          title: "Expenses",
          roles: [
            "company_admin",
            "branch_admin",
            "branch_manager",
            "accountant",
          ],
        },
      ],
    },
    {
      name: "POS Management",
      items: [
        {
          href: "/pos",
          icon: <CreditCard className="h-4 w-4" />,
          title: "POS Dashboard",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/pos/sessions",
          icon: <Clipboard className="h-4 w-4" />,
          title: "POS Sessions",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/dashboard/cash-balance",
          icon: <Banknote className="h-4 w-4" />,
          title: "Cash Balance",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "float_manager",
          ],
        },
        {
          href: "/sales",
          icon: <Receipt className="h-4 w-4" />,
          title: "Sales",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/customers",
          icon: <Users2 className="h-4 w-4" />,
          title: "Customers",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
      ],
    },
    {
      name: "DSA Management",
      items: [
        {
          href: "/dsa",
          icon: <UserCheck className="h-4 w-4" />,
          title: "DSA Dashboard",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/dsa/customers",
          icon: <Users className="h-4 w-4" />,
          title: "DSA Agents",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/dsa/assignments",
          icon: <Package className="h-4 w-4" />,
          title: "Stock Assignments",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        // Commented out DSA Reconciliations as requested
        // {
        //   href: "/dsa/reconciliations",
        //   icon: <BarChart3Icon className="h-4 w-4" />,
        //   title: "DSA Reconciliations",
        //   roles: [
        //     "super_admin",
        //     "company_admin",
        //     "tenant_admin",
        //     "branch_manager",
        //   ],
        // },
      ],
    },

    // Settings section completely hidden for production - not implemented
    // {
    //   name: "Settings",
    //   items: [
    //     {
    //       href: "/settings/system",
    //       icon: <Settings className="h-4 w-4" />,
    //       title: "System Settings",
    //       roles: ["super_admin"],
    //     },
    //     {
    //       href: "/settings/company",
    //       icon: <Building className="h-4 w-4" />,
    //       title: "Company Settings",
    //       roles: ["super_admin", "company_admin", "tenant_admin"],
    //     },
    //     {
    //       href: "/settings/payment-methods",
    //       icon: <Banknote className="h-4 w-4" />,
    //       title: "Payment Methods",
    //       roles: ["super_admin", "company_admin", "tenant_admin"],
    //     },
    //     {
    //       href: "/settings/health",
    //       icon: <Activity className="h-4 w-4" />,
    //       title: "System Health",
    //       roles: ["super_admin"],
    //     },
    //     {
    //       href: "/profile",
    //       icon: <User className="h-4 w-4" />,
    //       title: "Profile",
    //     },
    //   ],
    // },
  ];

  // Safely access role_name with type checking
  const userRoleName =
    user && typeof user === "object" && "role_name" in user
      ? user.role_name
      : "";

  // Note: We filter navigation groups directly in the JSX rendering

  return (
    <div className="hidden border-r bg-card md:block md:w-64">
      <div className="flex h-14 items-center border-b px-4">
        <NavigationLink
          href="/dashboard"
          className="flex items-center gap-2 font-semibold"
        >
          <span className="text-primary">DukaLink</span>
        </NavigationLink>
      </div>
      <ScrollArea className="h-[calc(100vh-3.5rem)]">
        <div className="px-3 py-2">
          <div className="space-y-4">
            {/* Render dashboard item separately */}
            {navigationGroups &&
              navigationGroups.length > 0 &&
              navigationGroups[0]?.items
                ?.filter(
                  (item) =>
                    !item.roles ||
                    (userRoleName && item.roles.includes(userRoleName))
                )
                .map((item) => (
                  <SidebarItem
                    key={`${item.href}-${item.title}`}
                    href={item.href}
                    icon={item.icon}
                    title={item.title}
                    isActive={pathname === item.href}
                  />
                ))}

            {/* Render all other groups */}
            {navigationGroups &&
              navigationGroups.length > 1 &&
              navigationGroups
                .slice(1)
                .filter((group) => {
                  // For regular navigation items (not in a group)
                  if (!group.items) {
                    const item = group as any;
                    return (
                      !item.roles ||
                      (userRoleName && item.roles.includes(userRoleName))
                    );
                  }

                  // For grouped items
                  const filteredItems = group.items.filter(
                    (item) =>
                      !item.roles ||
                      (userRoleName && item.roles.includes(userRoleName))
                  );
                  return filteredItems.length > 0;
                })
                .map((group, index) => (
                  <NavigationGroup
                    key={`${group.name}-${index}`}
                    group={group}
                    pathname={pathname}
                    userRoleName={userRoleName}
                  />
                ))}
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
