const {
  BankingTransaction,
  BankingTransactionReceipt,
  Branch,
  User,
  Tenant,
  Bank,
  RBACGrants,
} = require("../models");
const { Op } = require("sequelize");
const AppError = require("../utils/error");
const logger = require("../utils/logger");
const path = require("path");
const fs = require("fs");
const {
  uploadReceiptImage,
  deleteFile,
  receiptsDir,
} = require("../utils/file-upload");
const sequelize = require("../../config/database");

/**
 * Get all banking transactions
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAllBankingTransactions = async (req, res, next) => {
  try {
    const {
      tenant_id,
      branch_id,
      user_id,
      banking_method,
      status,
      start_date,
      end_date,
      page = 1,
      limit = 20,
    } = req.query;

    // Build where clause
    const whereClause = { deleted_at: null };

    if (tenant_id) {
      whereClause.tenant_id = tenant_id;
    } else if (req.user?.tenant_id) {
      whereClause.tenant_id = req.user.tenant_id;
    }

    if (branch_id) {
      whereClause.branch_id = branch_id;
    }

    if (user_id) {
      whereClause.user_id = user_id;
    }

    if (banking_method) {
      whereClause.banking_method = banking_method;
    }

    if (status) {
      whereClause.status = status;
    }

    if (start_date && end_date) {
      whereClause.transaction_date = {
        [Op.between]: [start_date, end_date],
      };
    } else if (start_date) {
      whereClause.transaction_date = {
        [Op.gte]: start_date,
      };
    } else if (end_date) {
      whereClause.transaction_date = {
        [Op.lte]: end_date,
      };
    }

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Get banking transactions
    const { count, rows: bankingTransactions } =
      await BankingTransaction.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Branch,
            attributes: ["id", "name", "location"],
          },
          {
            model: User,
            attributes: ["id", "name", "email"],
          },
          {
            model: Tenant,
            attributes: ["id", "name"],
          },
          {
            model: User,
            as: "Creator",
            attributes: ["id", "name", "email"],
          },
          {
            model: BankingTransactionReceipt,
            as: "receipts",
            attributes: [
              "id",
              "file_name",
              "file_path",
              "file_type",
              "file_size",
              "created_at",
            ],
          },
          {
            model: Bank,
            attributes: ["id", "name", "code", "type"],
          },
        ],
        order: [
          ["transaction_date", "DESC"],
          ["created_at", "DESC"],
        ],
        limit: parseInt(limit),
        offset,
      });

    // Calculate total pages
    const totalPages = Math.ceil(count / limit);

    return res.status(200).json({
      data: bankingTransactions,
      meta: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        total_pages: totalPages,
      },
    });
  } catch (error) {
    logger.error(`Error getting banking transactions: ${error.message}`);
    next(error);
  }
};

/**
 * Get banking transaction by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getBankingTransactionById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const bankingTransaction = await BankingTransaction.findOne({
      where: { id, deleted_at: null },
      include: [
        {
          model: Branch,
          attributes: ["id", "name", "location"],
        },
        {
          model: User,
          attributes: ["id", "name", "email"],
        },
        {
          model: Tenant,
          attributes: ["id", "name"],
        },
        {
          model: User,
          as: "Creator",
          attributes: ["id", "name", "email"],
        },
        {
          model: BankingTransactionReceipt,
          as: "receipts",
          attributes: [
            "id",
            "file_name",
            "file_path",
            "file_type",
            "file_size",
            "created_at",
          ],
        },
        {
          model: Bank,
          attributes: ["id", "name", "code", "type"],
        },
      ],
    });

    if (!bankingTransaction) {
      return next(new AppError("Banking transaction not found", 404));
    }

    // All users are allowed to view banking transactions
    // No permission checks needed

    return res.status(200).json(bankingTransaction);
  } catch (error) {
    logger.error(`Error getting banking transaction: ${error.message}`);
    next(error);
  }
};

/**
 * Create a new banking transaction with optional receipt image
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createBankingTransaction = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      branch_id,
      user_id,
      transaction_date,
      amount,
      banking_method,
      bank_id,
      reference_number,
      status,
      expected_amount,
      notes,
      employee_id,
    } = req.body;

    // Helper function to check if selected bank is CBA
    const isCBABank = async (bankId) => {
      if (!bankId) return false;

      const bank = await Bank.findOne({
        where: { id: bankId, deleted_at: null },
        transaction,
      });

      if (!bank) return false;

      const bankName = bank.name?.toLowerCase() || "";
      const bankCode = bank.code?.toLowerCase() || "";
      return (
        bankName.includes("cba") ||
        bankName.includes("commercial bank of africa") ||
        bankCode === "cba"
      );
    };

    // Check if this is a CBA bank transaction
    const isCBA = await isCBABank(bank_id);

    // Validate required fields with CBA exceptions
    if (!branch_id || !amount || !banking_method) {
      // If there's an uploaded file, delete it
      if (req.file) {
        await deleteFile(req.file.path);
      }
      await transaction.rollback();
      return next(
        new AppError(
          "Missing required fields. Branch ID, amount, and banking method are required.",
          400
        )
      );
    }

    // Validate reference number (optional for CBA bank)
    if (!reference_number && !isCBA) {
      // If there's an uploaded file, delete it
      if (req.file) {
        await deleteFile(req.file.path);
      }
      await transaction.rollback();
      return next(
        new AppError(
          "Reference number is required for non-CBA bank transactions.",
          400
        )
      );
    }

    // Validate receipt image (optional for CBA bank)
    if (!req.file && !isCBA) {
      await transaction.rollback();
      return next(
        new AppError(
          "Receipt image is required for non-CBA bank transactions.",
          400
        )
      );
    }

    // Validate bank_id for bank and agent methods
    if ((banking_method === "bank" || banking_method === "agent") && !bank_id) {
      // If there's an uploaded file, delete it
      if (req.file) {
        await deleteFile(req.file.path);
      }
      await transaction.rollback();
      return next(
        new AppError(
          `Bank/Agent selection is required for ${banking_method} method.`,
          400
        )
      );
    }

    // Check if branch exists
    const branch = await Branch.findOne({
      where: { id: branch_id, deleted_at: null },
      transaction,
    });

    if (!branch) {
      // If there's an uploaded file, delete it
      if (req.file) {
        await deleteFile(req.file.path);
      }
      await transaction.rollback();
      return next(new AppError("Branch not found", 404));
    }

    // All users are allowed to create banking transactions for any branch
    // No permission checks needed

    // Calculate discrepancy if expected amount is provided
    let discrepancy = null;
    if (expected_amount !== undefined && expected_amount !== null) {
      discrepancy = parseFloat(expected_amount) - parseFloat(amount);
    }

    // Create banking transaction
    const bankingTransaction = await BankingTransaction.create(
      {
        tenant_id: req.user.tenant_id,
        branch_id,
        user_id: employee_id || user_id || req.user.id, // Use employee_id as user_id if provided
        transaction_date: transaction_date || new Date(),
        amount,
        banking_method,
        bank_id:
          banking_method === "bank" || banking_method === "agent"
            ? bank_id
            : null,
        reference_number: reference_number || null, // Allow null for CBA bank
        status: "pending", // Set initial status to pending
        expected_amount: expected_amount || null,
        discrepancy,
        notes,
        created_by: req.user.id,
        last_updated_by: req.user.id,
      },
      { transaction }
    );

    // If there's a receipt image, create a receipt record
    let receipt = null;
    if (req.file) {
      receipt = await BankingTransactionReceipt.create(
        {
          banking_transaction_id: bankingTransaction.id,
          file_name: req.file.originalname,
          file_path: req.file.filename,
          file_type: req.file.mimetype,
          file_size: req.file.size,
          created_by: req.user.id,
          last_updated_by: req.user.id,
        },
        { transaction }
      );
    }

    await transaction.commit();

    // Fetch the created banking transaction with associations
    const createdBankingTransaction = await BankingTransaction.findByPk(
      bankingTransaction.id,
      {
        include: [
          {
            model: Branch,
            attributes: ["id", "name", "location"],
          },
          {
            model: User,
            attributes: ["id", "name", "email"],
          },
          {
            model: Tenant,
            attributes: ["id", "name"],
          },
          {
            model: User,
            as: "Creator",
            attributes: ["id", "name", "email"],
          },
          {
            model: BankingTransactionReceipt,
            as: "receipts",
            attributes: [
              "id",
              "file_name",
              "file_path",
              "file_type",
              "file_size",
              "created_at",
            ],
          },
        ],
      }
    );

    return res.status(201).json(createdBankingTransaction);
  } catch (error) {
    // If there's an uploaded file, delete it
    if (req.file) {
      await deleteFile(req.file.path).catch((err) => {
        logger.error(`Error deleting file: ${err.message}`);
      });
    }
    await transaction.rollback();
    logger.error(`Error creating banking transaction: ${error.message}`);
    next(error);
  }
};

/**
 * Update a banking transaction
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updateBankingTransaction = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const {
      amount,
      banking_method,
      reference_number,

      status,
      expected_amount,
      notes,
    } = req.body;

    // Find the banking transaction
    const bankingTransaction = await BankingTransaction.findOne({
      where: { id, deleted_at: null },
      transaction,
    });

    if (!bankingTransaction) {
      await transaction.rollback();
      return next(new AppError("Banking transaction not found", 404));
    }

    // All users are allowed to update banking transactions
    // No permission checks needed

    // Calculate discrepancy if amount or expected amount is updated
    let discrepancy = bankingTransaction.discrepancy;
    if (
      (amount !== undefined && amount !== null) ||
      (expected_amount !== undefined && expected_amount !== null)
    ) {
      const newAmount =
        amount !== undefined
          ? parseFloat(amount)
          : parseFloat(bankingTransaction.amount);
      const newExpectedAmount =
        expected_amount !== undefined
          ? parseFloat(expected_amount)
          : parseFloat(bankingTransaction.expected_amount || 0);
      discrepancy = newExpectedAmount - newAmount;
    }

    // Update banking transaction
    await bankingTransaction.update(
      {
        amount: amount || bankingTransaction.amount,
        banking_method: banking_method || bankingTransaction.banking_method,
        reference_number:
          reference_number || bankingTransaction.reference_number,

        status: status || bankingTransaction.status,
        expected_amount:
          expected_amount !== undefined
            ? expected_amount
            : bankingTransaction.expected_amount,
        discrepancy,
        notes: notes !== undefined ? notes : bankingTransaction.notes,
        last_updated_by: req.user.id,
      },
      { transaction }
    );

    await transaction.commit();

    // Fetch the updated banking transaction with associations
    const updatedBankingTransaction = await BankingTransaction.findByPk(
      bankingTransaction.id,
      {
        include: [
          {
            model: Branch,
            attributes: ["id", "name", "location"],
          },
          {
            model: User,
            attributes: ["id", "name", "email"],
          },
          {
            model: Tenant,
            attributes: ["id", "name"],
          },

          {
            model: User,
            as: "Creator",
            attributes: ["id", "name", "email"],
          },
        ],
      }
    );

    return res.status(200).json(updatedBankingTransaction);
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error updating banking transaction: ${error.message}`);
    next(error);
  }
};

/**
 * Delete a banking transaction
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deleteBankingTransaction = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    // Find the banking transaction
    const bankingTransaction = await BankingTransaction.findOne({
      where: { id, deleted_at: null },
      transaction,
    });

    if (!bankingTransaction) {
      await transaction.rollback();
      return next(new AppError("Banking transaction not found", 404));
    }

    // All users are allowed to delete banking transactions
    // No permission checks needed

    // Soft delete the banking transaction
    await bankingTransaction.update(
      {
        deleted_at: new Date(),
        last_updated_by: req.user.id,
      },
      { transaction }
    );

    await transaction.commit();

    return res.status(200).json({
      message: "Banking transaction deleted successfully",
    });
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error deleting banking transaction: ${error.message}`);
    next(error);
  }
};

/**
 * Get daily banking summary
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getDailyBankingSummary = async (req, res, next) => {
  try {
    const { branch_id, start_date, end_date } = req.query;

    if (!branch_id || !start_date) {
      return next(new AppError("Branch ID and start date are required", 400));
    }

    // Build where clause
    const whereClause = {
      deleted_at: null,
      branch_id,
    };

    if (req.user?.tenant_id) {
      whereClause.tenant_id = req.user.tenant_id;
    }

    if (start_date && end_date) {
      whereClause.transaction_date = {
        [Op.between]: [start_date, end_date],
      };
    } else if (start_date) {
      whereClause.transaction_date = {
        [Op.gte]: start_date,
      };
    }

    // Get banking transactions grouped by date and method
    const dailySummary = await BankingTransaction.findAll({
      attributes: [
        "transaction_date",
        "banking_method",
        [sequelize.fn("SUM", sequelize.col("amount")), "total_amount"],
        [sequelize.fn("COUNT", sequelize.col("id")), "transaction_count"],
      ],
      where: whereClause,
      group: ["transaction_date", "banking_method"],
      order: [["transaction_date", "DESC"]],
      raw: true,
    });

    // Format the results
    const formattedSummary = {};

    dailySummary.forEach((item) => {
      const date = item.transaction_date;

      if (!formattedSummary[date]) {
        formattedSummary[date] = {
          date,
          bank: 0,
          agent: 0,
          mpesa: 0,
          total: 0,
          transaction_count: 0,
        };
      }

      formattedSummary[date][item.banking_method] = parseFloat(
        item.total_amount
      );
      formattedSummary[date].total += parseFloat(item.total_amount);
      formattedSummary[date].transaction_count += parseInt(
        item.transaction_count
      );
    });

    return res.status(200).json(Object.values(formattedSummary));
  } catch (error) {
    logger.error(`Error getting daily banking summary: ${error.message}`);
    next(error);
  }
};

/**
 * Upload receipt image for a banking transaction
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const uploadBankingTransactionReceipt = async (req, res, next) => {
  try {
    const { id } = req.params;
    const file = req.file;

    if (!file) {
      return next(new AppError("No file uploaded", 400));
    }

    // Find the banking transaction
    const bankingTransaction = await BankingTransaction.findOne({
      where: { id, deleted_at: null },
    });

    if (!bankingTransaction) {
      // Delete the uploaded file
      await deleteFile(file.path);
      return next(new AppError("Banking transaction not found", 404));
    }

    // All users are allowed to upload receipts for banking transactions
    // No permission checks needed

    // Create receipt record
    const receipt = await BankingTransactionReceipt.create({
      banking_transaction_id: bankingTransaction.id,
      file_name: file.originalname,
      file_path: file.filename,
      file_type: file.mimetype,
      file_size: file.size,
      created_by: req.user.id,
      last_updated_by: req.user.id,
    });

    return res.status(201).json({
      id: receipt.id,
      banking_transaction_id: receipt.banking_transaction_id,
      file_name: receipt.file_name,
      file_path: receipt.file_path,
      file_type: receipt.file_type,
      file_size: receipt.file_size,
      created_at: receipt.created_at,
    });
  } catch (error) {
    logger.error(
      `Error uploading banking transaction receipt: ${error.message}`
    );
    next(error);
  }
};

/**
 * Get receipt image for a banking transaction
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getBankingTransactionReceipt = async (req, res, next) => {
  try {
    const { id, receiptId } = req.params;

    // Find the banking transaction
    const bankingTransaction = await BankingTransaction.findOne({
      where: { id, deleted_at: null },
    });

    if (!bankingTransaction) {
      return next(new AppError("Banking transaction not found", 404));
    }

    // All users are allowed to view receipts for banking transactions
    // No permission checks needed

    // Find the receipt
    const receipt = await BankingTransactionReceipt.findOne({
      where: {
        id: receiptId,
        banking_transaction_id: id,
        deleted_at: null,
      },
    });

    if (!receipt) {
      return next(new AppError("Receipt not found", 404));
    }

    // Get the file path
    const filePath = path.join(receiptsDir, receipt.file_path);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return next(new AppError("Receipt file not found", 404));
    }

    // Send the file
    return res.sendFile(filePath);
  } catch (error) {
    logger.error(`Error getting banking transaction receipt: ${error.message}`);
    next(error);
  }
};

/**
 * Delete receipt image for a banking transaction
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deleteBankingTransactionReceipt = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    const { id, receiptId } = req.params;

    // Find the banking transaction
    const bankingTransaction = await BankingTransaction.findOne({
      where: { id, deleted_at: null },
      transaction,
    });

    if (!bankingTransaction) {
      await transaction.rollback();
      return next(new AppError("Banking transaction not found", 404));
    }

    // All users are allowed to delete receipts for banking transactions
    // No permission checks needed

    // Find the receipt
    const receipt = await BankingTransactionReceipt.findOne({
      where: {
        id: receiptId,
        banking_transaction_id: id,
        deleted_at: null,
      },
      transaction,
    });

    if (!receipt) {
      await transaction.rollback();
      return next(new AppError("Receipt not found", 404));
    }

    // Get the file path
    const filePath = path.join(receiptsDir, receipt.file_path);

    // Soft delete the receipt
    await receipt.update(
      {
        deleted_at: new Date(),
        last_updated_by: req.user.id,
      },
      { transaction }
    );

    await transaction.commit();

    // Delete the file (non-blocking)
    if (fs.existsSync(filePath)) {
      deleteFile(filePath).catch((err) => {
        logger.error(`Error deleting receipt file: ${err.message}`);
      });
    }

    return res.status(200).json({
      message: "Receipt deleted successfully",
    });
  } catch (error) {
    await transaction.rollback();
    logger.error(
      `Error deleting banking transaction receipt: ${error.message}`
    );
    next(error);
  }
};

/**
 * Approve a banking transaction
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const approveBankingTransaction = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    // Find the banking transaction
    const bankingTransaction = await BankingTransaction.findOne({
      where: { id, deleted_at: null },
      transaction,
    });

    if (!bankingTransaction) {
      await transaction.rollback();
      return next(new AppError("Banking transaction not found", 404));
    }

    // Check if user has permission to approve banking transactions
    try {
      // Map role for backward compatibility
      let mappedRole = req.user.role_name;
      if (mappedRole === "branch_admin") mappedRole = "branch_manager";
      if (mappedRole === "admin") mappedRole = "super_admin";

      // Check permission directly from database (more reliable than RBAC service)
      const permission = await RBACGrants.findOne({
        where: {
          role: mappedRole,
          resource: "banking_approval",
          action: "create:any",
        },
      });

      if (!permission) {
        await transaction.rollback();
        return next(
          new AppError(
            "You do not have permission to approve banking transactions",
            403
          )
        );
      }
    } catch (permissionError) {
      logger.error(`Permission check failed: ${permissionError.message}`);
      await transaction.rollback();
      return next(new AppError("Error checking permissions", 500));
    }

    // Check if transaction is already approved or rejected
    if (bankingTransaction.status !== "pending") {
      await transaction.rollback();
      return next(
        new AppError(
          `Banking transaction is already ${bankingTransaction.status}`,
          400
        )
      );
    }

    // Update the banking transaction
    await bankingTransaction.update(
      {
        status: "completed", // Set status to completed when approved
        approved_by: req.user.id,
        approval_date: new Date(),
        last_updated_by: req.user.id,
      },
      { transaction }
    );

    await transaction.commit();

    // Return the updated banking transaction
    const updatedTransaction = await BankingTransaction.findOne({
      where: { id },
      include: [
        {
          model: Branch,
          attributes: ["id", "name", "location"],
        },
        {
          model: User,
          attributes: ["id", "name", "email"],
        },
        {
          model: Tenant,
          attributes: ["id", "name"],
        },
        {
          model: User,
          as: "Creator",
          attributes: ["id", "name", "email"],
        },
        {
          model: User,
          as: "Approver",
          attributes: ["id", "name", "email"],
        },
        {
          model: BankingTransactionReceipt,
          as: "receipts",
          attributes: [
            "id",
            "file_name",
            "file_path",
            "file_type",
            "file_size",
            "created_at",
          ],
        },
        {
          model: Bank,
          attributes: ["id", "name", "code", "type"],
        },
      ],
    });

    res.status(200).json({
      status: "success",
      data: updatedTransaction,
    });
  } catch (error) {
    await transaction.rollback();
    logger.error("Error approving banking transaction:", error);
    next(error);
  }
};

/**
 * Reject a banking transaction
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const rejectBankingTransaction = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { rejection_reason } = req.body;

    // Validate rejection reason
    if (!rejection_reason) {
      await transaction.rollback();
      return next(new AppError("Rejection reason is required", 400));
    }

    // Find the banking transaction
    const bankingTransaction = await BankingTransaction.findOne({
      where: { id, deleted_at: null },
      transaction,
    });

    if (!bankingTransaction) {
      await transaction.rollback();
      return next(new AppError("Banking transaction not found", 404));
    }

    // Check if user has permission to reject banking transactions
    try {
      // Map role for backward compatibility
      let mappedRole = req.user.role_name;
      if (mappedRole === "branch_admin") mappedRole = "branch_manager";
      if (mappedRole === "admin") mappedRole = "super_admin";

      // Check permission directly from database (more reliable than RBAC service)
      const permission = await RBACGrants.findOne({
        where: {
          role: mappedRole,
          resource: "banking_rejection",
          action: "create:any",
        },
      });

      if (!permission) {
        await transaction.rollback();
        return next(
          new AppError(
            "You do not have permission to reject banking transactions",
            403
          )
        );
      }
    } catch (permissionError) {
      logger.error(`Permission check failed: ${permissionError.message}`);
      await transaction.rollback();
      return next(new AppError("Error checking permissions", 500));
    }

    // Check if transaction is already approved or rejected
    if (bankingTransaction.status !== "pending") {
      await transaction.rollback();
      return next(
        new AppError(
          `Banking transaction is already ${bankingTransaction.status}`,
          400
        )
      );
    }

    // Update the banking transaction
    await bankingTransaction.update(
      {
        status: "failed", // Set status to failed when rejected
        approved_by: req.user.id,
        approval_date: new Date(),
        rejection_reason,
        last_updated_by: req.user.id,
      },
      { transaction }
    );

    await transaction.commit();

    // Return the updated banking transaction
    const updatedTransaction = await BankingTransaction.findOne({
      where: { id },
      include: [
        {
          model: Branch,
          attributes: ["id", "name", "location"],
        },
        {
          model: User,
          attributes: ["id", "name", "email"],
        },
        {
          model: Tenant,
          attributes: ["id", "name"],
        },
        {
          model: User,
          as: "Creator",
          attributes: ["id", "name", "email"],
        },
        {
          model: User,
          as: "Approver",
          attributes: ["id", "name", "email"],
        },
        {
          model: BankingTransactionReceipt,
          as: "receipts",
          attributes: [
            "id",
            "file_name",
            "file_path",
            "file_type",
            "file_size",
            "created_at",
          ],
        },
        {
          model: Bank,
          attributes: ["id", "name", "code", "type"],
        },
      ],
    });

    res.status(200).json({
      status: "success",
      data: updatedTransaction,
    });
  } catch (error) {
    await transaction.rollback();
    logger.error("Error rejecting banking transaction:", error);
    next(error);
  }
};

module.exports = {
  getAllBankingTransactions,
  getBankingTransactionById,
  createBankingTransaction,
  updateBankingTransaction,
  deleteBankingTransaction,
  getDailyBankingSummary,
  uploadBankingTransactionReceipt,
  getBankingTransactionReceipt,
  deleteBankingTransactionReceipt,
  approveBankingTransaction,
  rejectBankingTransaction,
};
