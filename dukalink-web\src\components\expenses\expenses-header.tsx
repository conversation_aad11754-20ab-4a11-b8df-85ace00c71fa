"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useCurrentUser } from "@/features/auth/hooks/use-auth";
import { useBranches } from "@/features/branches/hooks/use-branches";
import { useRegions } from "@/features/regions/hooks/use-regions";
import { handleExpenseExport } from "@/features/reports/utils/export-handlers";
import { Download, ListFilter, Search } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface ExpensesHeaderProps {
  isLoading?: boolean;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
  statusFilter?: string;
  onStatusChange?: (status: string | undefined) => void;
  branchFilter?: number;
  onBranchChange?: (branchId: number | undefined) => void;
  regionFilter?: number;
  onRegionChange?: (regionId: number | undefined) => void;
  onPageReset?: () => void;
}

export function ExpensesHeader({
  isLoading = false,
  searchQuery = "",
  onSearchChange,
  statusFilter,
  onStatusChange,
  branchFilter,
  onBranchChange,
  regionFilter,
  onRegionChange,
  onPageReset,
}: ExpensesHeaderProps) {
  const router = useRouter();
  const { data: user } = useCurrentUser();

  // Fetch branches and regions for filtering
  const { data: branchesData } = useBranches({
    limit: 100,
    status: "active",
  });

  const { data: regionsData } = useRegions();

  // Filter branches by selected region
  const filteredBranches = branchesData?.data?.filter((branch) => {
    if (!regionFilter) return true; // Show all branches if no region selected
    return branch.region_id === regionFilter;
  }) || [];

  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [isExporting, setIsExporting] = useState(false);

  // Sync local search query with prop
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  // Handle status filter change
  const handleStatusChange = (value: string) => {
    if (value === "all") {
      onStatusChange?.(undefined);
    } else {
      onStatusChange?.(value);
    }
    onPageReset?.(); // Reset to page 1 when filters change
  };

  // Handle region filter change
  const handleRegionChange = (value: string) => {
    if (value === "all") {
      onRegionChange?.(undefined);
      // Don't clear branch filter when clearing region
    } else {
      onRegionChange?.(parseInt(value, 10));
      // Clear branch filter when region changes to avoid conflicts
      onBranchChange?.(undefined);
    }
    onPageReset?.(); // Reset to page 1 when filters change
  };

  // Handle branch filter change
  const handleBranchChange = (value: string) => {
    if (value === "all") {
      onBranchChange?.(undefined);
    } else {
      onBranchChange?.(parseInt(value, 10));
    }
    onPageReset?.(); // Reset to page 1 when filters change
  };

  // Handle search
  const handleSearch = () => {
    onSearchChange?.(localSearchQuery);
    onPageReset?.(); // Reset to page 1 when searching
  };

  // Handle search input keydown
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setLocalSearchQuery("");
    onSearchChange?.("");
    onStatusChange?.(undefined);
    onBranchChange?.(undefined);
    onRegionChange?.(undefined);
    onPageReset?.(); // Reset to page 1 when clearing filters
  };

  // Handle backend export
  const handleExport = async () => {
    setIsExporting(true);
    try {
      await handleExpenseExport({
        branch_id: branchFilter,
        region_id: regionFilter,
        status: statusFilter as
          | "pending"
          | "approved"
          | "partially_approved"
          | "declined"
          | undefined,
        report_type: "detailed", // Default to detailed report
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="flex flex-1 items-center gap-2">
        <div className="relative flex-1 sm:max-w-xs">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search expenses..."
            className="pl-8"
            value={localSearchQuery}
            onChange={(e) => setLocalSearchQuery(e.target.value)}
            onKeyDown={handleKeyDown}
          />
        </div>
        <Button variant="outline" onClick={handleSearch}>
          Search
        </Button>
      </div>

      <div className="flex items-center gap-2">
        {/* Region Filter - Only show for company_admin */}
        {user?.role_name === "company_admin" && (
          <Select
            value={regionFilter ? regionFilter.toString() : "all"}
            onValueChange={handleRegionChange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by region" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Regions</SelectItem>
              {regionsData?.data?.map((region) => (
                <SelectItem key={region.id} value={region.id.toString()}>
                  {region.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {/* Branch Filter - Only show for company_admin and super_admin */}
        {user?.role_name === "company_admin" && (
          <Select
            value={branchFilter ? branchFilter.toString() : "all"}
            onValueChange={handleBranchChange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by branch" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Branches</SelectItem>
              {filteredBranches.map((branch) => (
                <SelectItem key={branch.id} value={branch.id.toString()}>
                  {branch.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        <Select
          value={statusFilter || "all"}
          onValueChange={handleStatusChange}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="partially_approved">
              Partially Approved
            </SelectItem>
            <SelectItem value="declined">Declined</SelectItem>
          </SelectContent>
        </Select>

        <Button variant="outline" onClick={handleClearFilters}>
          Clear Filters
        </Button>

        <Button
          onClick={handleExport}
          variant="outline"
          size="default"
          disabled={isLoading || isExporting}
        >
          {isExporting ? (
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
          ) : (
            <Download className="h-4 w-4 mr-2" />
          )}
          {isExporting ? "Exporting..." : "Export to Excel"}
        </Button>

        {user?.role_name === "company_admin" && (
          <Button
            variant="outline"
            onClick={() => router.push("/expenses/categories")}
          >
            <ListFilter className="h-4 w-4 mr-2" />
            Categories
          </Button>
        )}
      </div>
    </div>
  );
}
