"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useCurrentUser } from "../hooks/use-auth";
import { rbacService, GroupedGrants } from "@/features/users/api/rbac-service";
import { useToast } from "@/components/ui/use-toast";

interface PermissionContextType {
  permissions: GroupedGrants | null;
  isLoading: boolean;
  error: string | null;
  refreshPermissions: () => Promise<void>;
  hasPermission: (
    resource: string,
    action: string,
    scope?: "any" | "own"
  ) => boolean;
  logAvailableGrants: (resource: string) => void;
}

const PermissionContext = createContext<PermissionContextType | undefined>(
  undefined
);

export function PermissionProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: user, isLoading: isUserLoading } = useCurrentUser();
  const [permissions, setPermissions] = useState<GroupedGrants | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchPermissions = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);

      // Get the user's role
      const roleName = user.role_name;

      // Fetch permissions for this role
      const response = await rbacService.getRoleGrants(roleName);

      // Set the permissions in state
      setPermissions(response.grants);
    } catch (err) {
      console.error("Error fetching permissions:", err);
      setError("Failed to load permissions");
      toast.error("Error", {
        description:
          "Failed to load permissions. Some features may be unavailable.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch permissions when the user data is loaded
  useEffect(() => {
    if (!isUserLoading && user) {
      fetchPermissions();
    }
  }, [isUserLoading, user]);

  // Function to refresh permissions
  const refreshPermissions = async () => {
    await fetchPermissions();
  };

  // Helper function to log available grants for debugging
  const logAvailableGrants = (resource: string) => {
    if (!permissions) {
      console.log(`[PermissionContext] No permissions loaded yet`);
      return;
    }

    if (!permissions[resource]) {
      console.log(
        `[PermissionContext] No grants found for resource: ${resource}`
      );
      return;
    }

    console.log(
      `[PermissionContext] Available grants for resource ${resource}:`
    );
    Object.keys(permissions[resource]).forEach((action) => {
      console.log(`  - ${action}`);
    });
  };

  // Function to check if a user has a specific permission
  const hasPermission = (
    resource: string,
    action: string,
    scope: "any" | "own" = "any"
  ): boolean => {
    // If permissions are still loading or there's an error, deny access
    if (isLoading || error || !permissions || !user) {
      return false;
    }

    // Format the action string
    const formattedAction = `${action}:${scope}`;

    // Special case for company_admin, super_admin, and float_manager
    if (
      user.role_name === "company_admin" ||
      user.role_name === "super_admin" ||
      user.role_name === "float_manager"
    ) {
      // For debugging purposes, still check if the permission exists
      if (permissions[resource]) {
        const hasAction = !!permissions[resource][formattedAction];

        if (!hasAction) {
          console.log(
            `[PermissionContext] Admin override: ${resource}:${formattedAction} not found in grants but access granted`
          );
        }
      } else {
        console.log(
          `[PermissionContext] Admin override: Resource ${resource} not found in grants but access granted`
        );
      }

      // Always grant access to admin roles and float_manager
      return true;
    }

    // For accountant role, check if they have the permission or a related permission
    if (user.role_name === "accountant") {
      // Check if the user has the resource permission
      if (!permissions[resource]) {
        // For accountants, check if they have access to related resources
        // For example, if checking for "banking" but they have "financial_reports"
        if (
          (resource === "banking" && permissions["financial_reports"]) ||
          (resource === "expenses" && permissions["expense_reports"]) ||
          (resource === "inventory" && permissions["stock_reports"])
        ) {
          console.log(
            `[PermissionContext] Accountant has access to related resource for: ${resource}`
          );
          return true;
        }

        // For debugging, log that the resource wasn't found
        console.log(
          `[PermissionContext] Resource not found for accountant: ${resource}`
        );
        console.log(
          `[PermissionContext] Available resources for accountant:`,
          Object.keys(permissions)
        );

        return false;
      }

      // Check if the resource has the action
      const hasAction = !!permissions[resource][formattedAction];

      // For debugging, log the result
      if (!hasAction) {
        // For accountants, check if they have a read permission when checking for other actions
        if (
          action.startsWith("create:") ||
          action.startsWith("update:") ||
          action.startsWith("delete:")
        ) {
          const readAction = `read:${scope}`;
          if (permissions[resource][readAction]) {
            console.log(
              `[PermissionContext] Accountant has read permission for: ${resource}`
            );
            // For accountants, allow read access but not write access
            return false;
          }
        }

        console.log(
          `[PermissionContext] Action not found for accountant: ${formattedAction} for resource: ${resource}`
        );
        logAvailableGrants(resource);
      }

      return hasAction;
    }

    // For branch_admin role, be more permissive
    if (user.role_name === "branch_admin") {
      // Check if the user has the resource permission
      if (!permissions[resource]) {
        // For branch_admin, allow access to certain resources even if not explicitly granted
        if (
          resource === "dashboard" ||
          resource === "profile" ||
          resource === "employees" ||
          resource === "inventory" ||
          resource === "products" ||
          resource === "sales" ||
          resource === "pos_sessions" ||
          resource === "customers"
        ) {
          console.log(
            `[PermissionContext] Branch admin has implicit access to: ${resource}`
          );
          return true;
        }

        // For debugging, log that the resource wasn't found
        console.log(
          `[PermissionContext] Resource not found for branch_admin: ${resource}`
        );
        console.log(
          `[PermissionContext] Available resources for branch_admin:`,
          Object.keys(permissions)
        );

        return false;
      }

      // Check if the resource has the action
      const hasAction = !!permissions[resource][formattedAction];

      // For debugging, log the result
      if (!hasAction) {
        console.log(
          `[PermissionContext] Action not found for branch_admin: ${formattedAction} for resource: ${resource}`
        );
        logAvailableGrants(resource);
      }

      return hasAction;
    }

    // For all other roles, check if they have the exact permission

    // Check if the user has the resource permission
    if (!permissions[resource]) {
      // For debugging, log that the resource wasn't found
      console.log(`[PermissionContext] Resource not found: ${resource}`);

      // Log all available resources for debugging
      console.log(
        `[PermissionContext] Available resources:`,
        Object.keys(permissions)
      );

      return false;
    }

    // Check if the resource has the action
    const hasAction = !!permissions[resource][formattedAction];

    // For debugging, log the result
    if (!hasAction) {
      console.log(
        `[PermissionContext] Action not found: ${formattedAction} for resource: ${resource}`
      );
      logAvailableGrants(resource);
    }

    return hasAction;
  };

  const value = {
    permissions,
    isLoading,
    error,
    refreshPermissions,
    hasPermission,
    logAvailableGrants,
  };

  return (
    <PermissionContext.Provider value={value}>
      {children}
    </PermissionContext.Provider>
  );
}

export function usePermissionContext() {
  const context = useContext(PermissionContext);
  if (context === undefined) {
    throw new Error(
      "usePermissionContext must be used within a PermissionProvider"
    );
  }
  return context;
}
