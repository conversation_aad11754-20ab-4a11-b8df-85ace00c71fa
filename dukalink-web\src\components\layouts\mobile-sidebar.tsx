"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { NavigationLink } from "@/components/ui/navigation-link";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useCurrentUser } from "@/features/auth/hooks/use-auth";
import { usePermissions } from "@/features/auth/hooks/use-permissions";
import { navigationPermissions } from "@/lib/navigation-permissions";
import {
  Activity,
  ArrowLeftRight as ArrowLeftRightIcon,
  Banknote,
  BarChart3,
  BarChart3Icon,
  BarChart4,
  Box as BoxIcon,
  Building,
  BuildingIcon,
  ChevronDown,
  ChevronRight,
  Clipboard,
  CreditCard,
  Database,
  DollarSign,
  FolderTree,
  HomeIcon,
  Landmark,
  MapPin,
  Package,
  PackageOpen,
  Receipt,
  Settings,
  Shield,
  ShoppingBag,
  Smartphone,
  Tag,
  Truck as TruckIcon,
  User,
  UserCheck,
  Users,
  Users2,
  UsersIcon,
  X,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";

interface MobileSidebarItemProps {
  href: string;
  icon: React.ReactNode;
  title: string;
  isActive?: boolean;
  onClick?: () => void;
}

function MobileSidebarItem({
  href,
  icon,
  title,
  isActive = false,
  onClick,
}: MobileSidebarItemProps) {
  return (
    <Button
      asChild
      variant={isActive ? "secondary" : "ghost"}
      className="w-full justify-start"
      onClick={onClick}
    >
      <NavigationLink href={href} prefetch={true}>
        <span className="mr-2">{icon}</span>
        {title}
      </NavigationLink>
    </Button>
  );
}

interface NavigationGroup {
  name: string;
  items: {
    href: string;
    icon: React.ReactNode;
    title: string;
    permissionKey?: string;
    roles?: string[]; // Keep for backward compatibility
  }[];
}

function MobileNavigationGroup({
  group,
  pathname,
  userRoleName,
  onItemClick,
}: {
  group: NavigationGroup;
  pathname: string;
  userRoleName: string;
  onItemClick?: () => void;
}) {
  const { hasPermission } = usePermissions();
  const [isOpen, setIsOpen] = useState(false);

  // Check if any item in the group is active
  const isGroupActive =
    group &&
    Array.isArray(group.items) &&
    group.items.some(
      (item) => pathname === item.href || pathname.startsWith(`${item.href}/`)
    );

  // Auto-expand the group if an item is active
  useEffect(() => {
    if (isGroupActive) {
      setIsOpen(true);
    }
  }, [isGroupActive]);

  // Check if group and items exist before rendering
  if (!group || !group.items) {
    return null;
  }

  // Filter items based on permissions
  const filteredItems = group.items.filter((item) => {
    // If no permission key is specified, check roles for backward compatibility
    if (!item.permissionKey) {
      return !item.roles || (userRoleName && item.roles.includes(userRoleName));
    }

    // Get the permission required for this item
    const permission = navigationPermissions[item.permissionKey];

    // If permission mapping doesn't exist, hide the item
    if (!permission) return false;

    // Check if user has the required permission
    return hasPermission(
      permission.resource,
      permission.action,
      permission.scope
    );
  });

  // Don't render the group if there are no items to show
  if (filteredItems.length === 0) {
    return null;
  }

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen} className="space-y-1">
      <CollapsibleTrigger asChild>
        <Button
          variant="ghost"
          className="w-full justify-between px-3 py-2 text-sm font-medium"
        >
          <div className="flex items-center">
            <span>{group.name}</span>
          </div>
          {isOpen ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="space-y-1 pl-4">
        {filteredItems.map((item) => (
          <MobileSidebarItem
            key={`${item.href}-${item.title}`}
            href={item.href}
            icon={item.icon}
            title={item.title}
            isActive={pathname === item.href}
            onClick={onItemClick}
          />
        ))}
      </CollapsibleContent>
    </Collapsible>
  );
}

export function MobileSidebar({ onClose }: { onClose?: () => void }) {
  const { data: user } = useCurrentUser();
  const pathname = usePathname();

  // Define navigation groups
  const navigationGroups: NavigationGroup[] = [
    {
      name: "Main",
      items: [
        {
          href: "/dashboard",
          icon: <HomeIcon className="h-4 w-4" />,
          title: "Dashboard",
          permissionKey: "dashboard",
        },
      ],
    },
    {
      name: "Reports & Analytics",
      items: [
        {
          href: "/reports",
          icon: <BarChart3Icon className="h-4 w-4" />,
          title: "Reports Dashboard",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "float_manager",
          ],
        },
        {
          href: "/reports/sales-summary",
          icon: <BarChart4 className="h-4 w-4" />,
          title: "Sales Summary",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/sales-by-item",
          icon: <PackageOpen className="h-4 w-4" />,
          title: "Sales by Item",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/sales-by-category",
          icon: <ShoppingBag className="h-4 w-4" />,
          title: "Sales by Category",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/current-stock-levels",
          icon: <Package className="h-4 w-4" />,
          title: "Current Stock Levels",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "stock_admin",
          ],
        },
        {
          href: "/reports/stock-history",
          icon: <Package className="h-4 w-4" />,
          title: "Stock History",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "stock_admin",
          ],
        },
        {
          href: "/reports/banking-transactions",
          icon: <CreditCard className="h-4 w-4" />,
          title: "Banking Transactions",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/tax-report",
          icon: <Activity className="h-4 w-4" />,
          title: "Tax Report",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/mpesa-banking",
          icon: <DollarSign className="h-4 w-4" />,
          title: "Banking",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/mpesa-transactions",
          icon: <CreditCard className="h-4 w-4" />,
          title: "M-Pesa Transactions",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "operations_manager",
            "assistant_operations_manager",
            "float_manager",
          ],
        },
        {
          href: "/reports/running-balances",
          icon: <BarChart3 className="h-4 w-4" />,
          title: "Running Balances",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "operations_manager",
            "assistant_operations_manager",
            "float_manager",
          ],
        },
        {
          href: "/reports/dsa-sales",
          icon: <Users className="h-4 w-4" />,
          title: "DSA Sales",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/receipts",
          icon: <Receipt className="h-4 w-4" />,
          title: "Receipts",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/shifts",
          icon: <Clipboard className="h-4 w-4" />,
          title: "Shifts",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/stock-report",
          icon: <Package className="h-4 w-4" />,
          title: "Stock Report",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "stock_admin",
          ],
        },
        {
          href: "/reports/phone-repairs",
          icon: <Smartphone className="h-4 w-4" />,
          title: "Phone Repairs",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/cash-status",
          icon: <DollarSign className="h-4 w-4" />,
          title: "Cash Status",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "float_manager",
          ],
        },
        {
          href: "/reports/cash-float",
          icon: <Banknote className="h-4 w-4" />,
          title: "Cash Float",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "float_manager",
          ],
        },
        {
          href: "/expense-analytics",
          icon: <BarChart3 className="h-4 w-4" />,
          title: "Expense Analytics",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "float_manager",
          ],
        },
      ],
    },
    {
      name: "Administration",
      items: [
        // Tenants menu item removed
        {
          href: "/users",
          icon: <UsersIcon className="h-4 w-4" />,
          title: "Users",
          permissionKey: "users",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/roles",
          icon: <Shield className="h-4 w-4" />,
          title: "Roles",
          permissionKey: "roles",
          roles: ["super_admin", "company_admin", "accountant"],
        },
        // RBAC items temporarily hidden for production
        // {
        //   href: "/rbac",
        //   icon: <Database className="h-4 w-4" />,
        //   title: "RBAC",
        //   permissionKey: "rbac",
        //   roles: ["super_admin"],
        // },
        // {
        //   href: "/rbac-test",
        //   icon: <Shield className="h-4 w-4" />,
        //   title: "RBAC Test",
        //   permissionKey: "rbac",
        //   roles: ["super_admin"],
        // },
        // {
        //   href: "/permission-guard-test",
        //   icon: <Shield className="h-4 w-4" />,
        //   title: "Permission Guard Test",
        //   permissionKey: "rbac",
        //   roles: ["super_admin", "company_admin"],
        // },
        {
          href: "/branches",
          icon: <BuildingIcon className="h-4 w-4" />,
          title: "Branches",
          roles: ["super_admin", "company_admin", "accountant"],
        },
        {
          href: "/locations",
          icon: <MapPin className="h-4 w-4" />,
          title: "Locations",
          roles: ["super_admin", "company_admin", "tenant_admin", "accountant"],
        },
        {
          href: "/employees",
          icon: <Users2 className="h-4 w-4" />,
          title: "Employees",
          roles: ["super_admin", "company_admin", "accountant"],
        },
      ],
    },
    {
      name: "Products & Inventory",
      items: [
        {
          href: "/products",
          icon: <Package className="h-4 w-4" />,
          title: "Products",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
          ],
        },
        {
          href: "/categories",
          icon: <FolderTree className="h-4 w-4" />,
          title: "Categories",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/brands",
          icon: <Tag className="h-4 w-4" />,
          title: "Brands",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/inventory",
          icon: <BoxIcon className="h-4 w-4" />,
          title: "Inventory",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "finance_manager",
            "accountant",
            "float_manager",
          ],
        },
        {
          href: "/inventory/transfers",
          icon: <ArrowLeftRightIcon className="h-4 w-4" />,
          title: "Stock Transfers",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "finance_manager",
            "accountant",
            "float_manager",
          ],
        },
        {
          href: "/inventory/reports",
          icon: <BarChart3Icon className="h-4 w-4" />,
          title: "Inventory Reports",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "finance_manager",
            "accountant",
            "float_manager",
          ],
        },
      ],
    },
    {
      name: "Float Management",
      items: [
        {
          href: "/float",
          icon: <Banknote className="h-4 w-4" />,
          title: "Float Dashboard",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "float_manager",
          ],
        },
        // Temporarily hidden until API is implemented
        // {
        //   href: "/float/transactions",
        //   icon: <WalletIcon className="h-4 w-4" />,
        //   title: "Float Transactions",
        //   roles: ["super_admin", "company_admin", "tenant_admin", "float_manager"],
        // },
        {
          href: "/float/movements",
          icon: <TruckIcon className="h-4 w-4" />,
          title: "Float Movements",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "float_manager",
          ],
        },
        {
          href: "/float/reconciliations",
          icon: <BarChart3Icon className="h-4 w-4" />,
          title: "Float Reconciliations",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "float_manager",
          ],
        },
      ],
    },
    {
      name: "Banking Management",
      items: [
        {
          href: "/banking",
          icon: <Landmark className="h-4 w-4" />,
          title: "Banking Records",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "float_manager",
          ],
        },
        {
          href: "/banking/summary",
          icon: <BarChart3Icon className="h-4 w-4" />,
          title: "Banking Summary",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "float_manager",
          ],
        },
      ],
    },
    {
      name: "M-Pesa Management",
      items: [
        {
          href: "/mpesa/transactions",
          icon: <CreditCard className="h-4 w-4" />,
          title: "M-Pesa Transactions",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "float_manager",
          ],
        },
      ],
    },
    {
      name: "POS Management",
      items: [
        {
          href: "/pos",
          icon: <CreditCard className="h-4 w-4" />,
          title: "POS Dashboard",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/pos/sessions",
          icon: <Clipboard className="h-4 w-4" />,
          title: "POS Sessions",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/dashboard/cash-balance",
          icon: <Banknote className="h-4 w-4" />,
          title: "Cash Balance",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "float_manager",
          ],
        },
        {
          href: "/sales",
          icon: <Receipt className="h-4 w-4" />,
          title: "Sales",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/customers",
          icon: <Users2 className="h-4 w-4" />,
          title: "Customers",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
      ],
    },
    {
      name: "DSA Management",
      items: [
        {
          href: "/dsa",
          icon: <UserCheck className="h-4 w-4" />,
          title: "DSA Dashboard",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/dsa/customers",
          icon: <Users className="h-4 w-4" />,
          title: "DSA Agents",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/dsa/agents",
          icon: <UserCheck className="h-4 w-4" />,
          title: "DSA Agents (Legacy)",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/dsa/assignments",
          icon: <Package className="h-4 w-4" />,
          title: "Stock Assignments",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        // Commented out DSA Reconciliations as requested
        // {
        //   href: "/dsa/reconciliations",
        //   icon: <BarChart3Icon className="h-4 w-4" />,
        //   title: "DSA Reconciliations",
        //   roles: [
        //     "super_admin",
        //     "company_admin",
        //     "tenant_admin",
        //     "branch_manager",
        //   ],
        // },
      ],
    },
    {
      name: "Phone Repairs",
      items: [
        {
          href: "/phone-repairs",
          icon: <Smartphone className="h-4 w-4" />,
          title: "All Repairs",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/phone-repairs/new",
          icon: <Smartphone className="h-4 w-4" />,
          title: "New Repair",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
      ],
    },
    {
      name: "Expenses Management",
      items: [
        {
          href: "/expenses",
          icon: <Banknote className="h-4 w-4" />,
          title: "Expenses",
          roles: [
            "company_admin",
            "branch_admin",
            "branch_manager",
            "accountant",
          ],
        },
      ],
    },

    {
      name: "Settings",
      items: [
        // Settings items temporarily hidden for production
        // {
        //   href: "/settings/system",
        //   icon: <Settings className="h-4 w-4" />,
        //   title: "System Settings",
        //   roles: ["super_admin"],
        // },
        // {
        //   href: "/settings/company",
        //   icon: <Building className="h-4 w-4" />,
        //   title: "Company Settings",
        //   roles: ["super_admin", "company_admin", "tenant_admin"],
        // },
        // {
        //   href: "/settings/payment-methods",
        //   icon: <Banknote className="h-4 w-4" />,
        //   title: "Payment Methods",
        //   roles: ["super_admin", "company_admin", "tenant_admin"],
        // },
        // {
        //   href: "/settings/health",
        //   icon: <Activity className="h-4 w-4" />,
        //   title: "System Health",
        //   roles: ["super_admin"],
        // },
        {
          href: "/profile",
          icon: <User className="h-4 w-4" />,
          title: "Profile",
        },
      ],
    },
  ];

  // Safely access role_name with type checking
  const userRoleName =
    user && typeof user === "object" && "role_name" in user
      ? user.role_name
      : "";

  return (
    <div className="flex flex-col h-full">
      <div className="flex h-14 items-center border-b px-4 justify-between">
        <NavigationLink
          href="/dashboard"
          className="flex items-center gap-2 font-semibold"
          onClick={onClose}
        >
          <span className="text-primary">DukaLink</span>
        </NavigationLink>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="h-5 w-5" />
          <span className="sr-only">Close menu</span>
        </Button>
      </div>
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {/* Render dashboard item separately */}
          {navigationGroups &&
            navigationGroups.length > 0 &&
            navigationGroups[0]?.items
              ?.filter(
                (item) =>
                  !item.roles ||
                  (userRoleName && item.roles.includes(userRoleName))
              )
              .map((item) => (
                <MobileSidebarItem
                  key={`${item.href}-${item.title}`}
                  href={item.href}
                  icon={item.icon}
                  title={item.title}
                  isActive={pathname === item.href}
                  onClick={onClose}
                />
              ))}

          {/* Render all other groups */}
          {navigationGroups &&
            navigationGroups.length > 1 &&
            navigationGroups
              .slice(1)
              .filter((group) => {
                const filteredItems = group.items.filter(
                  (item) =>
                    !item.roles ||
                    (userRoleName && item.roles.includes(userRoleName))
                );
                return filteredItems.length > 0;
              })
              .map((group, index) => (
                <MobileNavigationGroup
                  key={`${group.name}-${index}`}
                  group={group}
                  pathname={pathname}
                  userRoleName={userRoleName}
                  onItemClick={onClose}
                />
              ))}
        </div>
      </ScrollArea>
    </div>
  );
}
