const {
  PosSession,
  User,
  Branch,
  Region,
  PosSessionReconciliation,
  Sale,
  CashTransaction,
  BankingTransaction,
  PaymentMethod,
  Expense,
  ExpenseCategory,
  Bank,
} = require("../models");
const AppError = require("../utils/error");
const logger = require("../utils/logger");
const sequelize = require("../../config/database");
const { Op } = require("sequelize");
const { createShiftStockSnapshots } = require("../jobs/stock-snapshot.job");

/**
 * Check if a POS session has any problematic expenses that would prevent closing
 * This includes:
 * 1. Any expense that is not fully approved (except declined)
 * 2. Any expense that is missing a POP receipt
 *
 * @param {number} sessionId - The ID of the POS session to check
 * @returns {Promise<{hasProblematicExpenses: boolean, expenses: Array, type: string}>} - Result object with flag, list of expenses, and problem type
 */
const checkForProblematicExpenses = async (sessionId) => {
  try {
    logger.info(
      `Starting checkForProblematicExpenses for session ${sessionId}`
    );

    // Find all expenses for this session
    const allExpenses = await Expense.findAll({
      where: {
        pos_session_id: sessionId,
        // Exclude all declined expenses as they don't block closing
        status: {
          [Op.notIn]: ["declined", "first_declined", "final_declined"],
        },
      },
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name"],
        },
        {
          model: ExpenseCategory,
          as: "category",
          attributes: ["id", "name", "is_shop_allowed"],
        },
      ],
    });

    logger.info(
      `Found ${allExpenses.length} expenses for session ${sessionId}`
    );

    // Log all expenses for debugging
    allExpenses.forEach((exp) => {
      logger.info(
        `Expense ${exp.id}: status=${exp.status}, receipt_image=${
          exp.receipt_image ? "present" : "missing"
        }`
      );
    });

    // Filter expenses into problematic categories
    // An expense is unapproved if it's not in the 'approved' status
    // This includes all other statuses: 'pending', 'partially_approved', 'pending_first_approval', 'first_approved', 'pending_second_approval'
    // Declined expenses are already excluded in the query above
    const unapprovedExpenses = allExpenses.filter((exp) => {
      logger.info(`Checking expense ${exp.id} with status ${exp.status}`);
      // Only check if it's not approved
      return exp.status !== "approved";
    });

    // Only check for missing POP on non-shop-allowed expenses
    const expensesWithoutPOP = allExpenses.filter((exp) => {
      // If the expense has no category, default to requiring POP (safer option)
      const isShopAllowed = exp.category?.is_shop_allowed || false;

      // Log the expense category and shop allowed status for debugging
      logger.info(
        `Checking expense ${exp.id} for POP: category=${
          exp.category?.name || "none"
        }, is_shop_allowed=${isShopAllowed}`
      );

      // If it's a shop allowed expense, it doesn't need a POP
      if (isShopAllowed) {
        return false;
      }

      // Otherwise, check if it has a receipt image
      return !exp.receipt_image || exp.receipt_image === "";
    });

    // Combine the problematic expenses (removing duplicates)
    const problematicExpenseIds = new Set();
    const problematicExpenses = [];

    // First add unapproved expenses
    if (unapprovedExpenses.length > 0) {
      unapprovedExpenses.forEach((exp) => {
        if (!problematicExpenseIds.has(exp.id)) {
          problematicExpenseIds.add(exp.id);
          problematicExpenses.push({
            ...exp.toJSON(),
            problem: "Not fully approved",
          });
        }
      });
    }

    // Then add expenses without POP
    if (expensesWithoutPOP.length > 0) {
      expensesWithoutPOP.forEach((exp) => {
        if (!problematicExpenseIds.has(exp.id)) {
          problematicExpenseIds.add(exp.id);
          problematicExpenses.push({
            ...exp.toJSON(),
            problem:
              "Missing POP receipt (required for Non-Shop Allowed expenses)",
          });
        } else {
          // If already in the list, update the problem description
          const existingIndex = problematicExpenses.findIndex(
            (item) => item.id === exp.id
          );
          if (existingIndex !== -1) {
            problematicExpenses[existingIndex].problem +=
              " and Missing POP receipt (required for Non-Shop Allowed expenses)";
          }
        }
      });
    }

    // Determine the type of problem for the error message
    let problemType = "";
    if (unapprovedExpenses.length > 0 && expensesWithoutPOP.length > 0) {
      problemType = "mixed";
    } else if (unapprovedExpenses.length > 0) {
      problemType = "unapproved";
    } else if (expensesWithoutPOP.length > 0) {
      problemType = "no_pop";
    }

    return {
      hasProblematicExpenses: problematicExpenses.length > 0,
      expenses: problematicExpenses,
      type: problemType,
    };
  } catch (error) {
    logger.error(`Error checking for problematic expenses: ${error.message}`);
    throw error;
  }
};

/**
 * Check if a POS session has any unapproved banking transactions that would prevent closing
 * Banking transactions must be approved (status = 'completed') before shift closure
 *
 * @param {number} sessionId - The ID of the POS session to check
 * @param {number} branchId - The branch ID to check banking transactions for
 * @returns {Promise<{hasUnapprovedBanking: boolean, transactions: Array}>} - Result object with flag and list of unapproved transactions
 */
const checkForUnapprovedBankingTransactions = async (sessionId, branchId) => {
  try {
    logger.info(
      `Starting checkForUnapprovedBankingTransactions for session ${sessionId}, branch ${branchId}`
    );

    // Get the session start and end times to check banking transactions within the session period
    const session = await PosSession.findByPk(sessionId);
    if (!session) {
      logger.warn(`Session ${sessionId} not found`);
      return { hasUnapprovedBanking: false, transactions: [] };
    }

    // Define the time range for checking banking transactions
    // Check from session start time to current time (for open sessions) or session end time (for closed sessions)
    const startTime = session.start_time;
    const endTime = session.end_time || new Date(); // Use current time if session is still open

    // Find all banking transactions for this branch within the session timeframe that are not approved
    const unapprovedBankingTransactions = await BankingTransaction.findAll({
      where: {
        branch_id: branchId,
        status: "pending", // Only check pending transactions
        created_at: {
          [Op.between]: [startTime, endTime],
        },
        deleted_at: null,
      },
      include: [
        {
          model: User,
          attributes: ["id", "name"],
        },
        {
          model: Bank,
          attributes: ["id", "name"],
        },
      ],
    });

    logger.info(
      `Found ${unapprovedBankingTransactions.length} unapproved banking transactions for session ${sessionId}`
    );

    // Log each unapproved banking transaction for debugging
    unapprovedBankingTransactions.forEach((transaction) => {
      logger.info(
        `Unapproved banking transaction ${transaction.id}: amount=${transaction.amount}, method=${transaction.banking_method}, status=${transaction.status}`
      );
    });

    return {
      hasUnapprovedBanking: unapprovedBankingTransactions.length > 0,
      transactions: unapprovedBankingTransactions.map((transaction) => ({
        ...transaction.toJSON(),
        problem: "Banking transaction not approved",
      })),
    };
  } catch (error) {
    logger.error(
      `Error checking for unapproved banking transactions: ${error.message}`
    );
    throw error;
  }
};

/**
 * Get all POS sessions
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAllPosSessions = async (req, res, next) => {
  try {
    const { user_id, branch_id, region_id, status, include_sales_totals } = req.query;

    const whereClause = { deleted_at: null };

    // Apply role-based filtering:
    // - Regular users can see their own sessions + active sessions in their branch
    // - Branch admins can see all sessions in their branch
    // - Company admins can see all sessions (optionally filtered by branch_id)
    if (req.user.role_name === "company_admin") {
      // Company admin can see all sessions, but can filter by branch_id if provided
      if (branch_id) {
        whereClause.branch_id = branch_id;
      }
      // Can filter by user_id if provided
      if (user_id) {
        whereClause.user_id = user_id;
      }
    } else if (
      req.user.role_name === "branch_admin" ||
      req.user.role_name === "branch_manager"
    ) {
      // Branch admin/manager can only see sessions in their branch
      whereClause.branch_id = req.user.branch_id;

      // Can filter by user_id if provided
      if (user_id) {
        whereClause.user_id = user_id;
      }
    } else {
      // Regular users can see:
      // 1. Their own sessions (all statuses)
      // 2. Active (open) sessions in their branch (for shift visibility)
      if (status === "open") {
        // For active sessions, allow all users in the branch to see them
        whereClause.branch_id = req.user.branch_id;

        // Can still filter by user_id if provided
        if (user_id) {
          whereClause.user_id = user_id;
        }
      } else {
        // For closed sessions or when no status filter is applied, restrict to user's own sessions
        whereClause.user_id = req.user.id;
      }
    }

    // Handle region filtering for company admins
    if (region_id && req.user.role_name === "company_admin") {
      // Get all branches in the specified region
      const branchesInRegion = await Branch.findAll({
        where: { region_id, deleted_at: null },
        attributes: ["id"],
      });

      // Extract branch IDs
      const branchIds = branchesInRegion.map((branch) => branch.id);

      // If we found branches in this region, filter sessions by those branch IDs
      if (branchIds.length > 0) {
        // Remove any existing branch_id filter
        delete whereClause.branch_id;

        // Add the branch_id IN clause
        whereClause.branch_id = {
          [Op.in]: branchIds,
        };
      }
    }

    if (status) {
      whereClause.status = status;
    }

    const posSessions = await PosSession.findAll({
      where: whereClause,
      include: [
        {
          model: User,
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          attributes: ["id", "name", "location", "region_id"],
          include: [
            {
              model: Region,
              attributes: ["id", "name", "code"],
              required: false,
            },
          ],
        },
        {
          model: PosSessionReconciliation,
          as: "reconciliation",
        },
        {
          model: Expense,
          required: false,
          include: [
            {
              model: ExpenseCategory,
              as: "category",
              attributes: ["id", "name"],
            },
          ],
        },
      ],
    });

    // If include_sales_totals is true, fetch and include sales totals for each session
    if (include_sales_totals === "true") {
      logger.info("Including sales totals in POS sessions response");

      // Get session IDs
      const sessionIds = posSessions.map((session) => session.id);

      // Fetch sales totals for all sessions in one query for better performance
      const salesTotals = await Sale.findAll({
        where: {
          pos_session_id: { [Op.in]: sessionIds },
          status: "completed",
          deleted_at: null,
        },
        attributes: [
          "pos_session_id",
          [sequelize.fn("SUM", sequelize.col("total_amount")), "total_sales"],
          [sequelize.fn("COUNT", sequelize.col("id")), "transaction_count"],
        ],
        group: ["pos_session_id"],
        raw: true,
      });

      // Fetch mpesa transaction counts for each session based on date
      // Since we don't have pos_session_id in mpesa_transactions, we'll count by branch and date
      const mpesaTransactionCounts = await Promise.all(
        posSessions.map(async (session) => {
          const sessionDate = new Date(session.start_time).toISOString().split('T')[0];

          const mpesaCount = await require("../models").MpesaTransaction.count({
            where: {
              branch_id: session.branch_id,
              transaction_date: sessionDate,
              deleted_at: null,
            },
          });

          return {
            pos_session_id: session.id,
            mpesa_transaction_count: mpesaCount,
          };
        })
      );

      // Create a map for quick lookup
      const salesTotalsMap = salesTotals.reduce((map, item) => {
        map[item.pos_session_id] = {
          total_sales: parseFloat(item.total_sales || 0),
          transaction_count: parseInt(item.transaction_count || 0),
        };
        return map;
      }, {});

      const mpesaCountsMap = mpesaTransactionCounts.reduce((map, item) => {
        map[item.pos_session_id] = item.mpesa_transaction_count;
        return map;
      }, {});

      // Add sales totals and mpesa transaction counts to each session
      posSessions.forEach((session) => {
        const totals = salesTotalsMap[session.id] || {
          total_sales: 0,
          transaction_count: 0,
        };
        const mpesaCount = mpesaCountsMap[session.id] || 0;

        session.dataValues.total_sales = totals.total_sales;
        session.dataValues.transaction_count = totals.transaction_count;
        session.dataValues.mpesa_transaction_count = mpesaCount;
      });
    }

    return res.status(200).json(posSessions);
  } catch (error) {
    logger.error(`Error fetching POS sessions: ${error.message}`);
    next(error);
  }
};

/**
 * Get POS session by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getPosSessionById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { include_cash_transactions } = req.query;

    // Build where clause based on user role
    const whereClause = { id, deleted_at: null };

    // Apply role-based access control:
    // - Regular users can see their own sessions + active sessions in their branch
    // - Branch admins can see all sessions in their branch

    // - Company admins can see all sessions
    if (req.user.role_name !== "company_admin") {
      if (
        req.user.role_name === "branch_admin" ||
        req.user.role_name === "branch_manager"
      ) {
        // Branch admin/manager can only see sessions in their branch
        whereClause.branch_id = req.user.branch_id;
      } else {
        // Regular users can see their own sessions OR active sessions in their branch
        // We'll need to check this after fetching the session since we need to know its status
        // For now, just set branch_id to allow fetching, then we'll validate access after
        whereClause.branch_id = req.user.branch_id;
      }
    }

    // Build include array based on query parameters
    const includeModels = [
      {
        model: User,
        attributes: ["id", "name", "email", "role_id"],
      },
      {
        model: Branch,
        attributes: ["id", "name", "location"],
      },
      {
        model: PosSessionReconciliation,
        as: "reconciliation",
      },
    ];

    // Always include cash transactions in the response
    logger.info("Including cash transactions in the response");
    includeModels.push({
      model: CashTransaction,
      required: false, // Use LEFT JOIN to include sessions even if they have no transactions
      include: [
        {
          model: User,
          attributes: ["id", "name", "email"],
        },
      ],
    });

    // Include expenses associated with this session
    logger.info("Including expenses in the response");
    includeModels.push({
      model: Expense,
      required: false, // Use LEFT JOIN to include sessions even if they have no expenses
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email"],
        },
        {
          model: ExpenseCategory,
          as: "category",
          attributes: ["id", "name", "description"],
        },
      ],
    });

    const posSession = await PosSession.findOne({
      where: whereClause,
      include: includeModels,
    });

    if (!posSession) {
      return next(new AppError("POS session not found", 404));
    }

    // Additional access control for regular users
    if (
      req.user.role_name !== "company_admin" &&
      req.user.role_name !== "branch_admin"
    ) {
      // Regular users can access active (open) sessions in their branch
      // No need to check if it's their own session - any active session in the branch is accessible
      if (posSession.status !== "open") {
        return next(
          new AppError(
            "You can only access active sessions in your branch",
            403
          )
        );
      }
    }

    // If cash transactions are included, also calculate and include the summary
    if (include_cash_transactions === "true") {
      // Log the request for debugging
      logger.info(
        `POS session ${id} requested with cash transactions included`
      );

      // Check if CashTransactions were loaded
      if (posSession.CashTransactions) {
        logger.info(
          `POS session ${id} has ${posSession.CashTransactions.length} cash transactions`
        );
        // Calculate cash transaction summary
        const cashInTransactions = posSession.CashTransactions.filter(
          (t) => t.type === "cash_in"
        );
        const cashOutTransactions = posSession.CashTransactions.filter(
          (t) => t.type === "cash_out"
        );

        // Calculate totals with proper number handling
        const cashInTotal = cashInTransactions.reduce((sum, t) => {
          const amount = parseFloat(t.amount || 0);
          return isNaN(amount) ? sum : sum + amount;
        }, 0);

        const cashOutTotal = cashOutTransactions.reduce((sum, t) => {
          const amount = parseFloat(t.amount || 0);
          return isNaN(amount) ? sum : sum + amount;
        }, 0);

        const cashSummary = {
          cash_in: {
            total_amount: cashInTotal,
            transaction_count: cashInTransactions.length,
          },
          cash_out: {
            total_amount: cashOutTotal,
            transaction_count: cashOutTransactions.length,
          },
        };

        // Add the summary to the response
        posSession.dataValues.cash_summary = cashSummary;

        // Log the cash summary for debugging
        logger.info(
          `Cash summary for session ${id}:`,
          JSON.stringify(cashSummary)
        );
      } else {
        logger.warn(
          `No CashTransactions found for session ${id} despite include_cash_transactions=true`
        );
      }
    }

    // Log the final response structure for debugging
    logger.info(
      `Sending POS session ${id} response with keys: ${Object.keys(
        posSession.dataValues
      ).join(", ")}`
    );
    if (posSession.dataValues.CashTransactions) {
      logger.info(
        `Response includes ${posSession.dataValues.CashTransactions.length} cash transactions`
      );
    } else {
      logger.warn(
        `Response does NOT include CashTransactions even though include_cash_transactions=${include_cash_transactions}`
      );
    }

    // Log expenses information
    if (posSession.dataValues.Expenses) {
      logger.info(
        `Response includes ${posSession.dataValues.Expenses.length} expenses`
      );

      // Calculate total expenses
      const totalExpenses = posSession.dataValues.Expenses.reduce(
        (sum, expense) => {
          return sum + parseFloat(expense.amount || 0);
        },
        0
      );

      // Add expenses summary to the response
      posSession.dataValues.expenses_summary = {
        count: posSession.dataValues.Expenses.length,
        total_amount: totalExpenses,
      };
    } else {
      logger.info("Response does not include any expenses");
    }

    return res.status(200).json(posSession);
  } catch (error) {
    logger.error(`Error fetching POS session: ${error.message}`);
    next(error);
  }
};

/**
 * Create a new POS session
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createPosSession = async (req, res, next) => {
  try {
    const {
      branch_id,
      opening_cash_balance,
      opening_mpesa_balance,
      opening_mpesa_float,
      user_id: targetUserId,
    } = req.body;

    // Get user ID from authenticated user or from request body
    const user_id = targetUserId || req.user.id;

    // All users can start their own shifts
    // Branch admins can start shifts for users in their branch
    // Company admins can start shifts for any user

    // If creating a session for another user, check if the current user has permission
    if (targetUserId && targetUserId !== req.user.id) {
      // Check if user has permission to start shifts for others
      if (req.user.role_name === "company_admin") {
        // Company admin can start shifts for any user
        logger.info(
          `Company admin ${req.user.id} starting shift for user ${targetUserId}`
        );
      } else if (
        req.user.role_name === "branch_admin" ||
        req.user.role_name === "branch_manager"
      ) {
        // Branch admin/manager can only start shifts for users in their branch
        const targetUser = await User.findOne({
          where: {
            id: targetUserId,
            branch_id: req.user.branch_id,
            deleted_at: null,
          },
        });

        if (!targetUser) {
          return next(
            new AppError(
              "You can only start shifts for users in your branch",
              403
            )
          );
        }

        logger.info(
          `Branch admin/manager ${req.user.id} starting shift for user ${targetUserId} in branch ${req.user.branch_id}`
        );
      } else {
        // Regular users cannot start shifts for others
        return next(
          new AppError(
            "You are not authorized to start a shift for another user",
            403
          )
        );
      }
    }

    if (!branch_id) {
      return next(new AppError("Branch ID is required", 400));
    }

    // Check if user exists and is a POS operator
    const user = await User.findOne({
      where: { id: user_id, deleted_at: null },
    });

    if (!user) {
      return next(new AppError("User not found", 404));
    }

    // Permission check is handled by middleware

    // Check if branch exists
    const branch = await Branch.findOne({
      where: { id: branch_id, deleted_at: null },
    });

    if (!branch) {
      return next(new AppError("Branch not found", 404));
    }

    // Check if user already has an open session
    const existingSession = await PosSession.findOne({
      where: {
        user_id,
        status: "open",
        deleted_at: null,
      },
    });

    if (existingSession) {
      return next(new AppError("You already have an open POS session", 400));
    }

    // Find the last closed session and its reconciliation for this branch to get the opening values
    const lastClosedSession = await PosSession.findOne({
      where: {
        branch_id,
        status: "closed",
        deleted_at: null,
      },
      include: [
        {
          model: PosSessionReconciliation,
          as: "reconciliation",
        },
      ],
      order: [["end_time", "DESC"]],
    });

    // Get previous closing values from reconciliation record
    let previousClosingCashBalance = 0;
    let previousClosingMpesaBalance = 0;
    let previousClosingMpesaFloat = 0;

    if (lastClosedSession && lastClosedSession.reconciliation) {
      previousClosingCashBalance = parseFloat(
        lastClosedSession.reconciliation.closing_cash_balance || 0
      );
      previousClosingMpesaBalance = parseFloat(
        lastClosedSession.reconciliation.closing_mpesa_balance || 0
      );
      previousClosingMpesaFloat = parseFloat(
        lastClosedSession.reconciliation.closing_mpesa_float || 0
      );
      logger.info(
        `Found previous reconciliation with closing_cash_balance: ${previousClosingCashBalance}, closing_mpesa_balance: ${previousClosingMpesaBalance}, closing_mpesa_float: ${previousClosingMpesaFloat}`
      );
    } else {
      logger.info("No previous reconciliation found, using default values");
    }

    // Use the closing cash balance from reconciliation as the opening cash balance for this session
    const sessionOpeningCashBalance =
      opening_cash_balance !== undefined
        ? parseFloat(opening_cash_balance)
        : previousClosingCashBalance;

    // Use the closing MPESA balance from reconciliation as the opening MPESA balance for this session
    const sessionOpeningMpesaBalance =
      opening_mpesa_balance !== undefined
        ? parseFloat(opening_mpesa_balance)
        : previousClosingMpesaBalance;

    // For MPESA float (agent transactions), use the provided value or the previous session's closing float from reconciliation
    const sessionOpeningMpesaFloat =
      opening_mpesa_float !== undefined
        ? parseFloat(opening_mpesa_float)
        : previousClosingMpesaFloat;

    // Create a new session with only the three opening balance fields from reconciliation
    // All other fields should be initialized to their default values
    const posSession = await PosSession.create({
      user_id,
      branch_id,
      // Only these three fields should be carried forward from previous reconciliation
      opening_cash_balance: sessionOpeningCashBalance,
      opening_mpesa_balance: sessionOpeningMpesaBalance,
      opening_mpesa_float: sessionOpeningMpesaFloat,
      // Initialize running balances to the same as opening balances
      running_cash_balance: sessionOpeningCashBalance,
      running_mpesa_balance: sessionOpeningMpesaBalance,
      running_mpesa_float: sessionOpeningMpesaFloat,
      // All other fields should be initialized to their default values
      cash_paid_in: 0,
      cash_paid_out: 0,
      float_in: 0,
      float_out: 0,
      total_deposits: 0,
      total_withdrawals: 0,
      status: "open",
    });

    return res.status(201).json({
      message: "POS session created successfully",
      session: posSession,
      session_status: "active",
      session_id: posSession.id,
    });
  } catch (error) {
    logger.error(`Error creating POS session: ${error.message}`);
    next(error);
  }
};

/**
 * Close a POS session
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const closePosSession = async (req, res, next) => {
  try {
    // Log the entire request body at the beginning of the function
    logger.info(`CLOSE SESSION REQUEST BODY: ${JSON.stringify(req.body)}`);

    const { id } = req.params;
    const {
      closing_cash_balance,
      running_mpesa_balance,
      closing_mpesa_balance, // Added explicit parameter for actual MPESA paybill balance
      closing_mpesa_float, // Added explicit parameter for actual Mobile Money float
      running_mpesa_float,
      cash_paid_in,
      cash_paid_out,
      total_sales,
      total_cash_sales, // Added for cash sales calculation
      total_expenses, // Added for expenses calculation
      discrepancies,
      notes,
      total_bankings, // Added for banking transactions total
      total_dsa_sales, // Added for DSA sales total
      // Additional fields that might be sent from the mobile app
      closingMpesaAmount,
      expectedMpesaAmount,
    } = req.body;

    // Get user ID from authenticated user
    const user_id = req.user.id;

    // Check if user is a POS operator
    const user = await User.findOne({
      where: { id: user_id, deleted_at: null },
    });

    if (!user) {
      return next(new AppError("User not found", 404));
    }

    // Permission check is handled by middleware

    // Find the session
    const posSession = await PosSession.findOne({
      where: {
        id,
        status: "open",
        deleted_at: null,
      },
    });

    if (!posSession) {
      return next(new AppError("Open POS session not found", 404));
    }

    // Time validation is handled by the frontend to use user's local timezone
    // Backend validation removed to prevent timezone conflicts

    // Check if user has permission to close this session
    if (req.user.role_name === "company_admin") {
      // Company admin can close any session
      logger.info(
        `Company admin ${req.user.id} closing session ${id} for user ${posSession.user_id}`
      );
    } else if (req.user.role_name === "branch_admin") {
      // Branch admin can only close sessions in their branch
      if (posSession.branch_id !== req.user.branch_id) {
        return next(
          new AppError("You can only close sessions in your branch", 403)
        );
      }
      logger.info(
        `Branch admin ${req.user.id} closing session ${id} for user ${posSession.user_id} in branch ${req.user.branch_id}`
      );
    } else {
      // Regular users can close any active session in their branch
      if (posSession.branch_id !== req.user.branch_id) {
        return next(
          new AppError("You can only close sessions in your branch", 403)
        );
      }
      logger.info(
        `User ${req.user.id} closing session ${id} for user ${posSession.user_id} in branch ${req.user.branch_id}`
      );
    }

    // Check if the request includes the allow_declined_expenses flag
    // Handle both boolean true and string "true" values
    const rawAllowDeclinedExpenses = req.body.allow_declined_expenses;

    // Log the raw value for debugging
    logger.info(
      `Raw allow_declined_expenses value: ${rawAllowDeclinedExpenses}`
    );
    logger.info(`Type of raw value: ${typeof rawAllowDeclinedExpenses}`);
    logger.info(`Request body: ${JSON.stringify(req.body)}`);

    // Force the flag to true for testing
    // This is a temporary workaround to ensure shift closure works
    // Remove this line after confirming the fix works
    const allowDeclinedExpenses = false;

    // Check for any problematic expenses that would prevent closing the shift
    logger.info(`Checking for problematic expenses for session ${id}`);
    const {
      hasProblematicExpenses,
      expenses: problematicExpenses,
      type,
    } = await checkForProblematicExpenses(id);

    logger.info(
      `Session ${id} has problematic expenses: ${hasProblematicExpenses}, count: ${
        problematicExpenses?.length || 0
      }, type: ${type}`
    );

    // Log the decision process
    logger.info(
      `Decision process: hasProblematicExpenses=${hasProblematicExpenses}, allowDeclinedExpenses=true (forced)`
    );

    // Log the raw value again for clarity
    logger.info(
      `Raw allow_declined_expenses from request: ${rawAllowDeclinedExpenses}`
    );

    // If there are problematic expenses and we're not allowing declined expenses, block the close
    if (hasProblematicExpenses && !allowDeclinedExpenses) {
      // Format expense details for the error message
      const expenseDetails = problematicExpenses
        .map((exp) => {
          return `- ${exp.description || "Expense"} (${
            exp.category?.name || "No category"
          }) for ${exp.amount} KES (Status: ${exp.status}, Problem: ${
            exp.problem
          })`;
        })
        .join("\n");

      // Create a detailed error message based on the type of problem
      let errorMessage = "";

      if (type === "mixed") {
        errorMessage = `Cannot close shift. There are expenses that need attention:\n\n${expenseDetails}\n\nPlease ensure all expenses are approved (not partially approved) and have POP receipts attached before closing the shift.`;
      } else if (type === "unapproved") {
        errorMessage = `Cannot close shift. There are expenses that are not approved:\n\n${expenseDetails}\n\nPlease wait for these expenses to be approved (not partially approved) or contact an administrator.`;
      } else if (type === "no_pop") {
        errorMessage = `Cannot close shift. There are expenses missing POP receipts:\n\n${expenseDetails}\n\nPlease attach receipts to these expenses before closing the shift.`;
      }

      logger.warn(
        `Session ${id} close attempt blocked due to problematic expenses: ${problematicExpenses.length} expenses found (type: ${type})`
      );
      return next(new AppError(errorMessage, 400));
    } else if (hasProblematicExpenses && allowDeclinedExpenses) {
      // If there are problematic expenses but we're allowing declined expenses, log and continue
      logger.info(
        `Session ${id} has problematic expenses but allow_declined_expenses flag is set to true. Allowing shift closure.`
      );
    }

    // Check for any unapproved banking transactions that would prevent closing the shift
    logger.info(
      `Checking for unapproved banking transactions for session ${id}`
    );
    const {
      hasUnapprovedBanking,
      transactions: unapprovedBankingTransactions,
    } = await checkForUnapprovedBankingTransactions(id, posSession.branch_id);

    logger.info(
      `Session ${id} has unapproved banking transactions: ${hasUnapprovedBanking}, count: ${
        unapprovedBankingTransactions?.length || 0
      }`
    );

    // If there are unapproved banking transactions, block the close
    if (hasUnapprovedBanking) {
      // Format banking transaction details for the error message
      const bankingDetails = unapprovedBankingTransactions
        .map((transaction) => {
          return `- ${transaction.banking_method} deposit of ${transaction.amount} KES (Reference: ${
            transaction.reference_number || "N/A"
          }, Status: ${transaction.status})`;
        })
        .join("\n");

      const errorMessage = `Cannot close shift. There are banking transactions that need approval:\n\n${bankingDetails}\n\nPlease ensure all banking transactions are approved before closing the shift.`;

      logger.warn(
        `Session ${id} close attempt blocked due to unapproved banking transactions: ${unapprovedBankingTransactions.length} transactions found`
      );
      return next(new AppError(errorMessage, 400));
    }

    // Calculate total sales if not provided
    let calculatedTotalSales = total_sales;
    if (!calculatedTotalSales) {
      const sales = await Sale.findAll({
        where: {
          pos_session_id: posSession.id,
          status: "completed",
          deleted_at: null,
        },
        attributes: [
          [sequelize.fn("SUM", sequelize.col("total_amount")), "total"],
        ],
        raw: true,
      });

      calculatedTotalSales = sales[0].total || 0;
    }

    // Calculate cash and M-PESA differences
    const openingCashBalance = parseFloat(posSession.opening_cash_balance || 0);
    const closingCashBalance = parseFloat(closing_cash_balance || 0);
    const cashDifference = closingCashBalance - openingCashBalance;

    // Get cash sales, expenses, deposits, and withdrawals for proper cash balance calculation
    const totalCashSales = parseFloat(total_cash_sales || 0);
    const totalExpenses = parseFloat(total_expenses || 0);
    const totalDeposits = parseFloat(posSession.total_deposits || 0);
    const totalWithdrawals = parseFloat(posSession.total_withdrawals || 0);

    // Expected cash balance should be:
    // opening_cash_balance + cash_sales + deposits - expenses - withdrawals
    // Note: cash_paid_in and cash_paid_out are legacy fields, we now use the comprehensive calculation
    const cashPaidIn = parseFloat(cash_paid_in || posSession.cash_paid_in || 0);
    const cashPaidOut = parseFloat(
      cash_paid_out || posSession.cash_paid_out || 0
    );

    // Use the new calculation that includes cash sales, deposits, expenses, and withdrawals
    const expectedCashBalance =
      openingCashBalance +
      totalCashSales +
      totalDeposits -
      totalExpenses -
      totalWithdrawals;
    const cashBalanceVariance = closingCashBalance - expectedCashBalance;

    logger.info(
      `Cash Balance: Opening=${openingCashBalance}, Cash Sales=${totalCashSales}, Deposits=${totalDeposits}, Expenses=${totalExpenses}, Withdrawals=${totalWithdrawals}`
    );
    logger.info(
      `Cash Balance: Expected=${expectedCashBalance}, Actual=${closingCashBalance}, Variance=${cashBalanceVariance}`
    );
    logger.info(
      `Legacy Cash Fields: Paid In=${cashPaidIn}, Paid Out=${cashPaidOut}`
    );

    const openingMpesaBalance = parseFloat(
      posSession.opening_mpesa_balance || 0
    );
    const runningMpesaBalance = parseFloat(running_mpesa_balance || 0);
    const mpesaDifference = runningMpesaBalance - openingMpesaBalance;

    // Calculate MPESA float difference
    const openingMpesaFloat = parseFloat(posSession.opening_mpesa_float || 0);
    const runningMpesaFloat = parseFloat(running_mpesa_float || 0);

    // Expected MPESA float should be:
    // opening_mpesa_float + total_deposits - total_withdrawals + float_in - float_out
    // Note: totalDeposits and totalWithdrawals are already declared above
    const floatIn = parseFloat(posSession.float_in || 0);
    const floatOut = parseFloat(posSession.float_out || 0);

    const expectedMpesaFloat =
      openingMpesaFloat + totalDeposits - totalWithdrawals + floatIn - floatOut;
    const mpesaFloatVariance = runningMpesaFloat - expectedMpesaFloat;

    logger.info(
      `MPESA Float: Opening=${openingMpesaFloat}, Deposits=${totalDeposits}, Withdrawals=${totalWithdrawals}, In=${floatIn}, Out=${floatOut}`
    );
    logger.info(
      `MPESA Float: Expected=${expectedMpesaFloat}, Actual=${runningMpesaFloat}, Variance=${mpesaFloatVariance}`
    );

    // Calculate total payments
    const totalPayments = cashDifference + mpesaDifference;

    // Calculate discrepancies if not provided
    let calculatedDiscrepancies = discrepancies;
    if (calculatedDiscrepancies === undefined) {
      calculatedDiscrepancies =
        totalPayments - parseFloat(calculatedTotalSales || 0);
    }

    // Generate variance explanations
    let varianceExplanations = [];

    if (cashBalanceVariance !== 0) {
      varianceExplanations.push(
        `Cash Balance variance: ${
          cashBalanceVariance > 0 ? "Excess" : "Shortage"
        } of ${Math.abs(cashBalanceVariance)} KES`
      );
    }

    if (mpesaFloatVariance !== 0) {
      varianceExplanations.push(
        `MPESA Float variance: ${
          mpesaFloatVariance > 0 ? "Excess" : "Shortage"
        } of ${Math.abs(mpesaFloatVariance)} KES`
      );
    }

    if (calculatedDiscrepancies !== 0) {
      varianceExplanations.push(
        `Sales discrepancy: ${
          calculatedDiscrepancies > 0 ? "Excess" : "Shortage"
        } of ${Math.abs(calculatedDiscrepancies)} KES`
      );
    }

    // Update session status, end time, and cash management fields
    await posSession.update({
      status: "closed",
      end_time: new Date(),
      cash_paid_in: cash_paid_in || posSession.cash_paid_in || 0,
      cash_paid_out: cash_paid_out || posSession.cash_paid_out || 0,
      running_cash_balance: closing_cash_balance || 0,
      running_mpesa_balance: running_mpesa_balance || 0,
      running_mpesa_float: running_mpesa_float || 0,
      // Keep the existing values for the new fields
      float_in: posSession.float_in || 0,
      float_out: posSession.float_out || 0,
      total_deposits: posSession.total_deposits || 0,
      total_withdrawals: posSession.total_withdrawals || 0,
    });

    // Prepare notes with variance explanations
    let reconciliationNotes = notes || "";

    if (varianceExplanations.length > 0 && !notes) {
      reconciliationNotes = `Shift closed with cash payments: ${cashDifference}, M-PESA payments: ${mpesaDifference}, total variance: ${
        totalPayments - parseFloat(calculatedTotalSales || 0)
      }`;

      // Add variance explanations if they exist
      if (varianceExplanations.length > 0) {
        reconciliationNotes +=
          "\n\nVariance Explanations:\n- " + varianceExplanations.join("\n- ");
      }
    }

    // Get the created_by user ID from the request body or use the authenticated user
    const created_by = req.body.created_by || req.user.id;

    // Create reconciliation record
    logger.info(
      `Creating reconciliation record for session ${posSession.id} with created_by: ${created_by}`
    );

    // Check if closing_mpesa_float is provided in the request body
    // Log the raw request body for debugging
    logger.info(`Raw request body: ${JSON.stringify(req.body)}`);

    // Check the type and value of closing_mpesa_float
    logger.info(
      `closing_mpesa_float type: ${typeof closing_mpesa_float}, value: ${closing_mpesa_float}, stringified: ${JSON.stringify(
        closing_mpesa_float
      )}`
    );
    logger.info(
      `running_mpesa_float type: ${typeof running_mpesa_float}, value: ${running_mpesa_float}, stringified: ${JSON.stringify(
        running_mpesa_float
      )}`
    );

    // Explicitly parse the values to ensure they're numbers
    const parsedClosingMpesaFloat =
      closing_mpesa_float !== undefined
        ? parseFloat(closing_mpesa_float)
        : undefined;
    const parsedRunningMpesaFloat =
      running_mpesa_float !== undefined ? parseFloat(running_mpesa_float) : 0;

    // Parse additional fields from the mobile app
    const parsedClosingMpesaAmount =
      closingMpesaAmount !== undefined
        ? parseFloat(closingMpesaAmount)
        : undefined;
    const parsedExpectedMpesaAmount =
      expectedMpesaAmount !== undefined
        ? parseFloat(expectedMpesaAmount)
        : undefined;

    // Log the additional fields
    logger.info(`Additional fields from mobile app:
      closingMpesaAmount: ${closingMpesaAmount} (parsed: ${parsedClosingMpesaAmount})
      expectedMpesaAmount: ${expectedMpesaAmount} (parsed: ${parsedExpectedMpesaAmount})
    `);

    // Use the parsed values with fallbacks
    // Priority: closing_mpesa_float > closingMpesaAmount > expectedMpesaAmount > running_mpesa_float
    let closingMpesaFloat;
    if (parsedClosingMpesaFloat !== undefined) {
      closingMpesaFloat = parsedClosingMpesaFloat;
      logger.info(
        `Using closing_mpesa_float: ${closingMpesaFloat} (from request.closing_mpesa_float)`
      );
    } else if (parsedClosingMpesaAmount !== undefined) {
      closingMpesaFloat = parsedClosingMpesaAmount;
      logger.info(
        `Using closing_mpesa_float: ${closingMpesaFloat} (from request.closingMpesaAmount)`
      );
    } else if (parsedExpectedMpesaAmount !== undefined) {
      closingMpesaFloat = parsedExpectedMpesaAmount;
      logger.info(
        `Using closing_mpesa_float: ${closingMpesaFloat} (from request.expectedMpesaAmount)`
      );
    } else {
      closingMpesaFloat = parsedRunningMpesaFloat;
      logger.info(
        `Using closing_mpesa_float: ${closingMpesaFloat} (from request.running_mpesa_float)`
      );
    }

    logger.info(
      `Using closing_mpesa_float: ${closingMpesaFloat} (from request: ${parsedClosingMpesaFloat}, fallback: ${parsedRunningMpesaFloat})`
    );

    // Log all the relevant fields for debugging
    logger.info(`Reconciliation data:
      closing_cash_balance: ${closing_cash_balance} (type: ${typeof closing_cash_balance})
      closing_mpesa_balance: ${closing_mpesa_balance} (type: ${typeof closing_mpesa_balance})
      closing_mpesa_float: ${parsedClosingMpesaFloat} (type: ${typeof parsedClosingMpesaFloat})
      running_mpesa_balance: ${running_mpesa_balance} (type: ${typeof running_mpesa_balance})
      running_mpesa_float: ${parsedRunningMpesaFloat} (type: ${typeof parsedRunningMpesaFloat})
    `);

    // Log the banking and DSA sales data
    logger.info(
      `Total bankings: ${total_bankings || 0}, Total DSA sales: ${
        total_dsa_sales || 0
      }`
    );

    // Log the values before creating the reconciliation record
    logger.info(`Creating reconciliation record with values:
      closing_cash_balance: ${
        closing_cash_balance || 0
      } (type: ${typeof closing_cash_balance})
      closing_mpesa_balance: ${
        closing_mpesa_balance || 0
      } (type: ${typeof closing_mpesa_balance})
      closing_mpesa_float: ${
        closingMpesaFloat || 0
      } (type: ${typeof closingMpesaFloat})
      original closing_mpesa_float: ${
        closing_mpesa_float || 0
      } (type: ${typeof closing_mpesa_float})
      running_mpesa_float: ${
        running_mpesa_float || 0
      } (type: ${typeof running_mpesa_float})
    `);

    // Log the entire request body for debugging
    logger.info(`Full request body: ${JSON.stringify(req.body)}`);

    // Explicitly parse all values to ensure they're numbers
    // Use the already parsed closingMpesaFloat value from above
    const parsedClosingCashBalance = parseFloat(closing_cash_balance || 0);
    const parsedClosingMpesaBalance = parseFloat(closing_mpesa_balance || 0);
    // Don't redeclare parsedClosingMpesaFloat, it's already defined above
    // const parsedClosingMpesaFloat = parseFloat(closingMpesaFloat || 0);
    const parsedTotalSales = parseFloat(calculatedTotalSales || 0);
    const parsedDiscrepancies = parseFloat(calculatedDiscrepancies || 0);
    const parsedTotalBankings = parseFloat(total_bankings || 0);
    const parsedTotalDsaSales = parseFloat(total_dsa_sales || 0);

    // Log the parsed values
    logger.info(`Parsed values for reconciliation record:
      parsedClosingCashBalance: ${parsedClosingCashBalance}
      parsedClosingMpesaBalance: ${parsedClosingMpesaBalance}
      parsedClosingMpesaFloat: ${parsedClosingMpesaFloat}
      parsedTotalSales: ${parsedTotalSales}
      parsedDiscrepancies: ${parsedDiscrepancies}
      parsedTotalBankings: ${parsedTotalBankings}
      parsedTotalDsaSales: ${parsedTotalDsaSales}
    `);

    // Create the reconciliation record with the parsed values
    const reconciliation = await PosSessionReconciliation.create({
      pos_session_id: posSession.id,
      closing_cash_balance: parsedClosingCashBalance,
      closing_mpesa_balance: parsedClosingMpesaBalance, // Use only the actual MPESA paybill balance from close shift form
      closing_mpesa_float: parsedClosingMpesaFloat, // Use the explicit closing_mpesa_float from request body if available
      cash_payments: cashDifference,
      mpesa_payments: mpesaDifference,
      total_sales: parsedTotalSales,
      discrepancies: parsedDiscrepancies,
      total_variance: totalPayments - parsedTotalSales,
      total_bankings: parsedTotalBankings,
      total_dsa_sales: parsedTotalDsaSales,
      notes: reconciliationNotes,
      created_by: created_by,
    });

    // Log the created reconciliation record
    logger.info(
      `Created reconciliation record: ${JSON.stringify(
        reconciliation.toJSON()
      )}`
    );

    // Create stock snapshots for the closed session
    try {
      logger.info(`Creating stock snapshots for session ${posSession.id}`);
      const snapshotResult = await createShiftStockSnapshots(
        posSession.id,
        posSession.branch_id
      );
      logger.info(
        `Stock snapshot creation result: ${JSON.stringify(snapshotResult)}`
      );
    } catch (snapshotError) {
      // Log the error but don't fail the session closure
      logger.error(`Error creating stock snapshots: ${snapshotError.message}`);
      logger.error(snapshotError.stack);
    }

    return res.status(200).json({
      message: "POS session closed successfully",
      session: posSession,
      reconciliation,
      session_status: "none",
      session_required: true,
    });
  } catch (error) {
    logger.error(`Error closing POS session: ${error.message}`);
    next(error);
  }
};

/**
 * Update a POS session
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updatePosSession = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      cash_paid_in,
      cash_paid_out,
      opening_mpesa_balance,
      opening_mpesa_float,
      notes,
    } = req.body;

    // Build where clause based on user role
    const whereClause = { id, status: "open", deleted_at: null };

    // Apply role-based access control:
    // - Regular users can update any active session in their branch
    // - Branch admins can update all sessions in their branch
    // - Company admins can update all sessions
    if (req.user.role_name !== "company_admin") {
      if (
        req.user.role_name === "branch_admin" ||
        req.user.role_name === "branch_manager"
      ) {
        // Branch admin/manager can only update sessions in their branch
        whereClause.branch_id = req.user.branch_id;
      } else {
        // Regular users can update any active session in their branch
        whereClause.branch_id = req.user.branch_id;
      }
    }

    // Find the session
    const posSession = await PosSession.findOne({
      where: whereClause,
      include: [
        {
          model: User,
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          attributes: ["id", "name", "location"],
        },
      ],
    });

    if (!posSession) {
      return next(
        new AppError(
          "Open POS session not found or you do not have permission to update it",
          404
        )
      );
    }

    // Update the session
    await posSession.update({
      cash_paid_in:
        cash_paid_in !== undefined ? cash_paid_in : posSession.cash_paid_in,
      cash_paid_out:
        cash_paid_out !== undefined ? cash_paid_out : posSession.cash_paid_out,
      opening_mpesa_balance:
        opening_mpesa_balance !== undefined
          ? opening_mpesa_balance
          : posSession.opening_mpesa_balance,
      opening_mpesa_float:
        opening_mpesa_float !== undefined
          ? opening_mpesa_float
          : posSession.opening_mpesa_float,
      notes: notes || posSession.notes,
    });

    // Get the updated session
    const updatedSession = await PosSession.findOne({
      where: { id, deleted_at: null },
      include: [
        {
          model: User,
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          attributes: ["id", "name", "location"],
        },
      ],
    });

    return res.status(200).json(updatedSession);
  } catch (error) {
    logger.error(`Error updating POS session: ${error.message}`);
    next(error);
  }
};

/**
 * Delete a POS session (soft delete)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deletePosSession = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Build where clause based on user role
    const whereClause = { id, deleted_at: null };

    // Apply role-based access control:
    // - Regular users cannot delete sessions
    // - Branch admins/managers can delete sessions in their branch
    // - Company admins can delete any session
    if (req.user.role_name !== "company_admin") {
      if (
        req.user.role_name === "branch_admin" ||
        req.user.role_name === "branch_manager"
      ) {
        // Branch admin/manager can only delete sessions in their branch
        whereClause.branch_id = req.user.branch_id;
      } else {
        // Regular users cannot delete sessions
        return next(
          new AppError("You do not have permission to delete POS sessions", 403)
        );
      }
    }

    const posSession = await PosSession.findOne({
      where: whereClause,
    });

    if (!posSession) {
      return next(
        new AppError(
          "POS session not found or you do not have permission to delete it",
          404
        )
      );
    }

    await posSession.update({ deleted_at: new Date() });

    return res.status(200).json({
      message: "POS session deleted successfully",
    });
  } catch (error) {
    logger.error(`Error deleting POS session: ${error.message}`);
    next(error);
  }
};

/**
 * Get total sales for a POS session
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getSessionTotalSales = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Build where clause based on user role
    const whereClause = { id, deleted_at: null };

    // Apply role-based access control:
    // - Regular users can see their own sessions + active sessions in their branch
    // - Branch admins can see all sessions in their branch
    // - Company admins can see all sessions
    if (req.user.role_name !== "company_admin") {
      if (
        req.user.role_name === "branch_admin" ||
        req.user.role_name === "branch_manager"
      ) {
        // Branch admin/manager can only see sessions in their branch
        whereClause.branch_id = req.user.branch_id;
      } else {
        // Regular users can see their own sessions OR active sessions in their branch
        // We'll need to check this after fetching the session since we need to know its status
        // For now, just set branch_id to allow fetching, then we'll validate access after
        whereClause.branch_id = req.user.branch_id;
      }
    }

    // Find the session
    const posSession = await PosSession.findOne({
      where: whereClause,
    });

    if (!posSession) {
      return next(
        new AppError(
          "POS session not found or you do not have permission to access it",
          404
        )
      );
    }

    // Additional access control for regular users
    if (
      req.user.role_name !== "company_admin" &&
      req.user.role_name !== "branch_admin"
    ) {
      // Regular users can only access:
      // 1. Their own sessions (any status)
      // 2. Active (open) sessions in their branch
      if (posSession.user_id !== req.user.id && posSession.status !== "open") {
        return next(
          new AppError("You do not have permission to access this session", 403)
        );
      }
    }

    // Get total sales for this session
    const sales = await Sale.findAll({
      where: {
        pos_session_id: posSession.id,
        status: "completed",
        deleted_at: null,
      },
      attributes: [
        [sequelize.fn("SUM", sequelize.col("total_amount")), "total"],
        [sequelize.fn("COUNT", sequelize.col("id")), "count"],
      ],
      raw: true,
    });

    // Format the response
    const totalSales = parseFloat(sales[0].total || 0);
    const transactionCount = parseInt(sales[0].count || 0);

    return res.status(200).json({
      total_sales: totalSales,
      transaction_count: transactionCount,
    });
  } catch (error) {
    logger.error(`Error fetching session total sales: ${error.message}`);
    next(error);
  }
};

/**
 * Get cash running balance status for branch sessions
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getCashRunningBalanceStatus = async (req, res, next) => {
  try {
    const { branch_id, start_date, end_date } = req.query;

    // Only company_admin users can access this endpoint
    if (req.user.role_name !== "company_admin") {
      return next(
        new AppError("You do not have permission to access this resource", 403)
      );
    }

    // Build where clause for branches
    const branchWhereClause = { deleted_at: null };
    if (branch_id) {
      branchWhereClause.id = branch_id;
    }

    // Get all branches
    const branches = await Branch.findAll({
      where: branchWhereClause,
      attributes: ["id", "name", "location"],
    });

    if (!branches || branches.length === 0) {
      return next(new AppError("No branches found", 404));
    }

    // Prepare date filters
    const dateFilter = {};
    if (start_date) {
      dateFilter.start_time = { [Op.gte]: new Date(start_date) };
    }
    if (end_date) {
      dateFilter.start_time = {
        ...dateFilter.start_time,
        [Op.lte]: new Date(end_date),
      };
    }

    // Prepare result array
    const result = [];

    // Process each branch
    for (const branch of branches) {
      // Get the latest active session for the branch
      const activeSession = await PosSession.findOne({
        where: {
          branch_id: branch.id,
          status: "open",
          deleted_at: null,
          ...dateFilter,
        },
        include: [
          {
            model: User,
            attributes: ["id", "name", "email"],
          },
        ],
        order: [["start_time", "DESC"]],
      });

      // Get the latest closed session for the branch
      const latestClosedSession = await PosSession.findOne({
        where: {
          branch_id: branch.id,
          status: "closed",
          deleted_at: null,
          ...dateFilter,
        },
        include: [
          {
            model: User,
            attributes: ["id", "name", "email"],
          },
          {
            model: PosSessionReconciliation,
          },
        ],
        order: [["end_time", "DESC"]],
      });

      // Get all sessions for the branch within the date range
      const allSessions = await PosSession.findAll({
        where: {
          branch_id: branch.id,
          deleted_at: null,
          ...dateFilter,
        },
        attributes: ["id"],
        raw: true,
      });

      const sessionIds = allSessions.map((session) => session.id);

      // Get total sales for all sessions
      const totalSales = await Sale.findAll({
        where: {
          branch_id: branch.id,
          pos_session_id: { [Op.in]: sessionIds },
          deleted_at: null,
        },
        attributes: [
          [sequelize.fn("SUM", sequelize.col("total_amount")), "total_amount"],
          "payment_method_id",
        ],
        include: [
          {
            model: PaymentMethod,
            attributes: ["id", "name", "code"],
          },
        ],
        group: ["payment_method_id"],
        raw: true,
      });

      // Get total cash transactions (deposits and withdrawals)
      const cashTransactions = await CashTransaction.findAll({
        where: {
          pos_session_id: { [Op.in]: sessionIds },
          deleted_at: null,
        },
        attributes: [
          "type",
          [sequelize.fn("SUM", sequelize.col("amount")), "total_amount"],
        ],
        group: ["type"],
        raw: true,
      });

      // Get total banking transactions
      const bankingTransactions = await BankingTransaction.findAll({
        where: {
          branch_id: branch.id,
          deleted_at: null,
          ...(start_date && { created_at: { [Op.gte]: new Date(start_date) } }),
          ...(end_date && { created_at: { [Op.lte]: new Date(end_date) } }),
        },
        attributes: [
          [sequelize.fn("SUM", sequelize.col("amount")), "total_amount"],
        ],
        raw: true,
      });

      // Get total expenses for all sessions
      const expenseTransactions = await Expense.findAll({
        where: {
          pos_session_id: { [Op.in]: sessionIds },
        },
        attributes: [
          [sequelize.fn("SUM", sequelize.col("amount")), "total_amount"],
        ],
        raw: true,
      });

      // Calculate totals
      const totalCashSales = totalSales
        .filter((sale) => sale["PaymentMethod.code"] === "cash")
        .reduce((sum, sale) => sum + parseFloat(sale.total_amount || 0), 0);

      const totalMobileMoneySales = totalSales
        .filter((sale) => sale["PaymentMethod.code"] === "mpesa")
        .reduce((sum, sale) => sum + parseFloat(sale.total_amount || 0), 0);

      const totalDeposits = cashTransactions
        .filter((tx) => tx.type === "cash_in")
        .reduce((sum, tx) => sum + parseFloat(tx.total_amount || 0), 0);

      const totalWithdrawals = cashTransactions
        .filter((tx) => tx.type === "cash_out")
        .reduce((sum, tx) => sum + parseFloat(tx.total_amount || 0), 0);

      const totalBanked = bankingTransactions.reduce(
        (sum, tx) => sum + parseFloat(tx.total_amount || 0),
        0
      );

      const totalExpenses = expenseTransactions.reduce(
        (sum, expense) => sum + parseFloat(expense.total_amount || 0),
        0
      );

      // Calculate opening balances
      let openingCashBalance = 0;
      let openingMobileMoneyBalance = 0;

      if (activeSession) {
        openingCashBalance = parseFloat(
          activeSession.opening_cash_balance || 0
        );
        openingMobileMoneyBalance = parseFloat(
          activeSession.opening_mpesa_balance || 0
        );
      } else if (
        latestClosedSession &&
        latestClosedSession.PosSessionReconciliation
      ) {
        openingCashBalance = parseFloat(
          latestClosedSession.PosSessionReconciliation.closing_cash_balance || 0
        );
        openingMobileMoneyBalance = parseFloat(
          latestClosedSession.PosSessionReconciliation.closing_mpesa_balance ||
            0
        );
      }

      // Calculate running balances
      const cashRunningBalance =
        openingCashBalance +
        totalCashSales +
        totalDeposits -
        totalWithdrawals -
        totalBanked -
        totalExpenses;
      const mobileMoneyRunningBalance =
        openingMobileMoneyBalance + totalMobileMoneySales;

      // Create branch status object
      const branchStatus = {
        branch_id: branch.id,
        branch_name: branch.name,
        branch_location: branch.location,
        active_session: activeSession
          ? {
              id: activeSession.id,
              start_time: activeSession.start_time,
              user: activeSession.User
                ? {
                    id: activeSession.User.id,
                    name: activeSession.User.name,
                  }
                : null,
            }
          : null,
        opening_cash_balance: openingCashBalance,
        opening_mobile_money_balance: openingMobileMoneyBalance,
        total_sales: totalCashSales + totalMobileMoneySales,
        cash_sales: totalCashSales,
        mobile_money_sales: totalMobileMoneySales,
        total_deposits: totalDeposits,
        total_withdrawals: totalWithdrawals,
        total_banked: totalBanked,
        total_expenses: totalExpenses,
        cash_running_balance: cashRunningBalance,
        mobile_money_running_balance: mobileMoneyRunningBalance,
        last_updated: activeSession
          ? activeSession.updatedAt
          : latestClosedSession
            ? latestClosedSession.updatedAt
            : null,
      };

      result.push(branchStatus);
    }

    return res.status(200).json(result);
  } catch (error) {
    logger.error(
      `Error fetching cash running balance status: ${error.message}`
    );
    next(error);
  }
};

/**
 * Get all data needed for shift closing in a single request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getShiftClosingData = async (req, res) => {
  try {
    const { id } = req.params;

    // Build where clause based on user role
    const whereClause = { id, deleted_at: null };

    // Apply role-based access control:
    // - Regular users can see active sessions in their branch
    // - Branch admins can see all sessions in their branch
    // - Company admins can see all sessions
    if (req.user.role_name !== "company_admin") {
      if (
        req.user.role_name === "branch_admin" ||
        req.user.role_name === "branch_manager"
      ) {
        // Branch admin/manager can only see sessions in their branch
        whereClause.branch_id = req.user.branch_id;
      } else {
        // Regular users can see active sessions in their branch
        // We'll need to check this after fetching the session since we need to know its status
        // For now, just set branch_id to allow fetching, then we'll validate access after
        whereClause.branch_id = req.user.branch_id;
      }
    }

    // Find the session
    const posSession = await PosSession.findOne({
      where: whereClause,
      include: [
        {
          model: User,
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          attributes: ["id", "name", "location"],
        },
      ],
    });

    if (!posSession) {
      logger.error(
        `POS session with ID ${id} not found or user does not have permission to access it`
      );
      return res.status(404).json({
        error: "NotFoundError",
        message:
          "POS session not found or you do not have permission to access it",
      });
    }

    // Additional access control for regular users
    if (
      req.user.role_name !== "company_admin" &&
      req.user.role_name !== "branch_admin"
    ) {
      // Regular users can only access:
      // 1. Their own sessions (any status)
      // 2. Active (open) sessions in their branch
      if (posSession.user_id !== req.user.id && posSession.status !== "open") {
        logger.error(
          `User ${req.user.id} denied access to session ${id} (owner: ${posSession.user_id}, status: ${posSession.status})`
        );
        return res.status(403).json({
          error: "ForbiddenError",
          message: "You do not have permission to access this session",
        });
      }
    }

    // 1. Get total sales and sales by payment method
    let sales = [];
    try {
      // Fix the ambiguous column issue by specifying the table name
      sales = await Sale.findAll({
        where: {
          pos_session_id: posSession.id,
          status: "completed",
          deleted_at: null,
        },
        attributes: [
          [sequelize.fn("SUM", sequelize.col("Sale.total_amount")), "total"],
          [sequelize.fn("COUNT", sequelize.col("Sale.id")), "count"],
        ],
        raw: true,
      });

      logger.info(`Total sales query result: ${JSON.stringify(sales)}`);
    } catch (error) {
      logger.error(
        `Error fetching sales for session ${posSession.id}: ${error.message}`
      );

      // Try an alternative approach using raw SQL if the ORM query fails
      try {
        logger.info(`Trying alternative raw SQL approach for total sales`);

        const query = `
          SELECT
            SUM(total_amount) as total,
            COUNT(id) as count
          FROM
            sales
          WHERE
            pos_session_id = :sessionId
            AND status = 'completed'
            AND deleted_at IS NULL
        `;

        const [results] = await sequelize.query(query, {
          replacements: { sessionId: posSession.id },
          type: sequelize.QueryTypes.SELECT,
        });

        sales = [results];

        logger.info(
          `Alternative total sales query result: ${JSON.stringify(sales)}`
        );
      } catch (altError) {
        logger.error(
          `Alternative total sales query also failed: ${altError.message}`
        );
        sales = [{ total: 0, count: 0 }];
      }
    }

    // Get sales by payment method
    let salesByPaymentMethod = [];
    try {
      // Log the session ID for debugging
      logger.info(
        `Fetching sales by payment method for session ${posSession.id}`
      );

      // Fix the ambiguous column issue by specifying the table name
      salesByPaymentMethod = await Sale.findAll({
        where: {
          pos_session_id: posSession.id,
          status: "completed",
          deleted_at: null,
        },
        attributes: [
          "payment_method_id",
          [
            sequelize.fn("SUM", sequelize.col("Sale.total_amount")),
            "total_amount",
          ],
          [sequelize.fn("COUNT", sequelize.col("Sale.id")), "count"],
        ],
        include: [
          {
            model: PaymentMethod,
            attributes: ["id", "name", "code"],
          },
        ],
        group: [
          "payment_method_id",
          "PaymentMethod.id",
          "PaymentMethod.name",
          "PaymentMethod.code",
        ],
        raw: true,
      });

      // Log the raw results for debugging
      logger.info(
        `Found ${salesByPaymentMethod.length} payment methods with sales for session ${posSession.id}`
      );
      logger.info(
        `Raw sales by payment method: ${JSON.stringify(salesByPaymentMethod)}`
      );
    } catch (error) {
      logger.error(
        `Error fetching sales by payment method for session ${posSession.id}: ${error.message}`
      );

      // Try an alternative approach using raw SQL if the ORM query fails
      try {
        logger.info(`Trying alternative raw SQL approach for payment methods`);

        const query = `
          SELECT
            s.payment_method_id,
            pm.id as 'PaymentMethod.id',
            pm.name as 'PaymentMethod.name',
            pm.code as 'PaymentMethod.code',
            SUM(s.total_amount) as total_amount,
            COUNT(s.id) as count
          FROM
            sales s
          JOIN
            payment_methods pm ON s.payment_method_id = pm.id
          WHERE
            s.pos_session_id = :sessionId
            AND s.status = 'completed'
            AND s.deleted_at IS NULL
            AND pm.deleted_at IS NULL
          GROUP BY
            s.payment_method_id, pm.id, pm.name, pm.code
        `;

        const results = await sequelize.query(query, {
          replacements: { sessionId: posSession.id },
          type: sequelize.QueryTypes.SELECT,
        });

        salesByPaymentMethod = results;

        logger.info(
          `Alternative query found ${salesByPaymentMethod.length} payment methods`
        );
        logger.info(
          `Alternative raw sales by payment method: ${JSON.stringify(
            salesByPaymentMethod
          )}`
        );
      } catch (altError) {
        logger.error(`Alternative query also failed: ${altError.message}`);
        salesByPaymentMethod = [];
      }
    }

    // 2. Get MPESA services data (deposits and withdrawals)
    // Get total deposits and withdrawals from the session
    const totalDeposits = parseFloat(posSession.total_deposits || 0);
    const totalWithdrawals = parseFloat(posSession.total_withdrawals || 0);

    // 3. Get expenses for the session
    let expenses = [];
    try {
      expenses = await Expense.findAll({
        where: {
          pos_session_id: posSession.id,
        },
        include: [
          {
            model: ExpenseCategory,
            as: "category",
            attributes: ["id", "name", "description"],
          },
        ],
      });
    } catch (error) {
      logger.error(
        `Error fetching expenses for session ${posSession.id}: ${error.message}`
      );
      expenses = [];
    }

    // Calculate total expenses
    const totalExpenses = expenses.reduce((sum, expense) => {
      return sum + parseFloat(expense.amount || 0);
    }, 0);

    // 4. Get banking transactions for the branch for the current day
    let bankingTransactions = [];
    try {
      // Get the branch ID from the session
      const branchId = posSession.branch_id;

      // Get the current date at midnight (start of day)
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get the end of day (tomorrow at midnight)
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Format as YYYY-MM-DD for DATEONLY field
      const todayStr = today.toISOString().split("T")[0];

      logger.info(
        `Fetching banking transactions for branch ${branchId} for today (${todayStr})`
      );

      // Try using created_at for date filtering instead of transaction_date
      bankingTransactions = await BankingTransaction.findAll({
        where: {
          branch_id: branchId,
          deleted_at: null,
          created_at: {
            [Op.gte]: today,
            [Op.lt]: tomorrow,
          },
        },
        include: [
          {
            model: Bank,
            attributes: ["id", "name", "code", "type"],
          },
        ],
        order: [["created_at", "DESC"]],
      });

      logger.info(
        `Found ${bankingTransactions.length} banking transactions for branch ${branchId} for today (${todayStr})`
      );

      if (bankingTransactions.length > 0) {
        // Log the first transaction to see its structure
        const firstTransaction = bankingTransactions[0];
        logger.info(
          `First banking transaction: ${JSON.stringify(firstTransaction)}`
        );
        logger.info(
          `Transaction date: ${firstTransaction.transaction_date}, Created at: ${firstTransaction.created_at}`
        );
      } else {
        // If no transactions found for today, check if there are any transactions at all
        logger.info(
          `No banking transactions found for today. Checking for any recent transactions...`
        );

        // Try to find any banking transactions for this branch
        const anyTransactions = await BankingTransaction.findAll({
          where: {
            branch_id: branchId,
            deleted_at: null,
          },
          limit: 5,
          order: [["created_at", "DESC"]],
        });

        if (anyTransactions.length > 0) {
          logger.info(`Found ${anyTransactions.length} recent transactions`);

          // Log each transaction's date information
          anyTransactions.forEach((transaction, index) => {
            const transactionDate = transaction.transaction_date;
            const createdAt = transaction.created_at;
            logger.info(
              `Transaction ${
                index + 1
              }: transaction_date=${transactionDate}, created_at=${createdAt}`
            );
          });

          // Try an alternative approach - use transaction_date with a different format
          logger.info(`Trying alternative approach with transaction_date...`);

          // Try to find transactions with today's transaction_date
          const todayTransactions = await BankingTransaction.findAll({
            where: {
              branch_id: branchId,
              deleted_at: null,
              transaction_date: {
                [Op.eq]: todayStr,
              },
            },
            include: [
              {
                model: Bank,
                attributes: ["id", "name", "code", "type"],
              },
            ],
            limit: 5,
          });

          if (todayTransactions.length > 0) {
            logger.info(
              `Found ${todayTransactions.length} transactions with transaction_date=${todayStr}`
            );
            bankingTransactions = todayTransactions;
          } else {
            logger.info(
              `No transactions found with transaction_date=${todayStr}`
            );

            // Try one more approach with raw SQL
            logger.info(`Trying raw SQL approach...`);
            try {
              const query = `
                SELECT bt.*, b.id as bank_id, b.name as bank_name, b.code as bank_code, b.type as bank_type
                FROM banking_transactions bt
                LEFT JOIN banks b ON bt.bank_id = b.id
                WHERE bt.branch_id = :branchId
                AND bt.deleted_at IS NULL
                AND DATE(bt.transaction_date) = :todayStr
                ORDER BY bt.created_at DESC
                LIMIT 10
              `;

              const rawResults = await sequelize.query(query, {
                replacements: { branchId, todayStr },
                type: sequelize.QueryTypes.SELECT,
              });

              if (rawResults && rawResults.length > 0) {
                logger.info(
                  `Found ${rawResults.length} transactions using raw SQL with DATE(transaction_date)=${todayStr}`
                );

                // Format the results to match the expected structure
                bankingTransactions = rawResults.map((row) => ({
                  id: row.id,
                  branch_id: row.branch_id,
                  amount: row.amount,
                  banking_method: row.banking_method,
                  reference_number: row.reference_number,
                  status: row.status,
                  transaction_date: row.transaction_date,
                  created_at: row.created_at,
                  Bank: row.bank_id
                    ? {
                        id: row.bank_id,
                        name: row.bank_name,
                        code: row.bank_code,
                        type: row.bank_type,
                      }
                    : null,
                }));
              } else {
                logger.info(
                  `No transactions found using raw SQL with DATE(transaction_date)=${todayStr}`
                );
              }
            } catch (sqlError) {
              logger.error(
                `Error executing raw SQL query: ${sqlError.message}`
              );
            }
          }
        } else {
          logger.info(`No banking transactions found for this branch at all.`);
        }
      }
    } catch (error) {
      logger.error(
        `Error fetching banking transactions for branch ${posSession.branch_id}: ${error.message}`
      );
      bankingTransactions = [];
    }

    // Calculate total banking deposits - sum all transactions regardless of status
    const totalBankingDeposits = bankingTransactions.reduce(
      (sum, transaction) => sum + parseFloat(transaction.amount || 0),
      0
    );

    // Format the response
    // Make sure sales is an array with at least one element
    let totalSales =
      Array.isArray(sales) && sales.length > 0
        ? parseFloat(sales[0]?.total || 0)
        : 0;
    const transactionCount =
      Array.isArray(sales) && sales.length > 0
        ? parseInt(sales[0]?.count || 0)
        : 0;

    // Calculate sales by payment method
    let cashSales = 0;
    let mpesaSales = 0;
    let cardSales = 0;
    let bankSales = 0;
    let creditSales = 0;

    // Function to get sales for a specific payment method
    const getSalesByPaymentMethod = async (methodCode) => {
      try {
        // Fix the ambiguous column issue by specifying the table name
        const result = await Sale.findAll({
          where: {
            pos_session_id: posSession.id,
            status: "completed",
            deleted_at: null,
          },
          include: [
            {
              model: PaymentMethod,
              where: { code: methodCode },
              attributes: [],
            },
          ],
          attributes: [
            [
              sequelize.fn("SUM", sequelize.col("Sale.total_amount")),
              "total_amount",
            ],
            [sequelize.fn("COUNT", sequelize.col("Sale.id")), "count"],
          ],
          raw: true,
        });

        let amount = 0;
        let count = 0;

        if (result && result.length > 0) {
          amount = parseFloat(result[0].total_amount || 0);
          count = parseInt(result[0].count || 0);
        }

        logger.info(
          `Direct query for ${methodCode} sales: ${amount} (${count} transactions)`
        );
        return { amount, count };
      } catch (error) {
        logger.error(
          `Error fetching ${methodCode} sales directly: ${error.message}`
        );

        // Try an alternative query approach if the first one fails
        try {
          // Alternative approach using subquery
          const query = `
            SELECT
              SUM(s.total_amount) as total_amount,
              COUNT(s.id) as count
            FROM
              sales s
            JOIN
              payment_methods pm ON s.payment_method_id = pm.id
            WHERE
              s.pos_session_id = :sessionId
              AND s.status = 'completed'
              AND s.deleted_at IS NULL
              AND pm.code = :methodCode
              AND pm.deleted_at IS NULL
          `;

          const [results] = await sequelize.query(query, {
            replacements: {
              sessionId: posSession.id,
              methodCode: methodCode,
            },
            type: sequelize.QueryTypes.SELECT,
          });

          const altAmount = parseFloat(results?.total_amount || 0);
          const altCount = parseInt(results?.count || 0);

          logger.info(
            `Alternative query for ${methodCode} sales: ${altAmount} (${altCount} transactions)`
          );
          return { amount: altAmount, count: altCount };
        } catch (altError) {
          logger.error(
            `Alternative query for ${methodCode} sales also failed: ${altError.message}`
          );

          // Fall back to calculating from salesByPaymentMethod
          if (Array.isArray(salesByPaymentMethod)) {
            const amount = salesByPaymentMethod
              .filter(
                (sale) => sale && sale["PaymentMethod.code"] === methodCode
              )
              .reduce(
                (sum, sale) => sum + parseFloat(sale.total_amount || 0),
                0
              );

            const count = salesByPaymentMethod
              .filter(
                (sale) => sale && sale["PaymentMethod.code"] === methodCode
              )
              .reduce((sum, sale) => sum + parseInt(sale.count || 0), 0);

            return { amount, count };
          }
        }

        return { amount: 0, count: 0 };
      }
    };

    // Get sales for each payment method
    const cashResult = await getSalesByPaymentMethod("cash");
    cashSales = cashResult.amount;

    const mpesaResult = await getSalesByPaymentMethod("mpesa");
    mpesaSales = mpesaResult.amount;

    const cardResult = await getSalesByPaymentMethod("card");
    cardSales = cardResult.amount;

    const bankResult = await getSalesByPaymentMethod("bank");
    bankSales = bankResult.amount;

    const creditResult = await getSalesByPaymentMethod("credit");
    creditSales = creditResult.amount;

    // Log all payment method sales for debugging
    logger.info(`Payment method sales summary:
      Cash: ${cashSales}
      M-PESA: ${mpesaSales}
      Credit Card: ${cardSales}
      Bank Transfer: ${bankSales}
      Credit: ${creditSales}
      Total: ${cashSales + mpesaSales + cardSales + bankSales + creditSales}
    `);

    // Format sales by payment method for easier consumption
    const formattedSalesByPaymentMethod = {};

    // Define standard payment methods to ensure they're all included
    const standardPaymentMethods = [
      { code: "cash", name: "Cash" },
      { code: "mpesa", name: "M-PESA" },
      { code: "card", name: "Credit Card" },
      { code: "bank", name: "Bank Transfer" },
      { code: "credit", name: "Credit" },
    ];

    // Initialize all standard payment methods with zero values
    standardPaymentMethods.forEach((method) => {
      formattedSalesByPaymentMethod[method.code] = {
        name: method.name,
        amount: 0,
        count: 0,
      };
    });

    // Make sure salesByPaymentMethod is an array before processing
    if (Array.isArray(salesByPaymentMethod)) {
      // Log the raw data for debugging
      logger.info(
        `Processing ${salesByPaymentMethod.length} payment methods for formatting`
      );

      salesByPaymentMethod.forEach((sale) => {
        if (!sale) return; // Skip if sale is undefined

        const methodCode = sale["PaymentMethod.code"];
        const methodName = sale["PaymentMethod.name"];
        const saleAmount = parseFloat(sale.total_amount || 0);
        const saleCount = parseInt(sale.count || 0);

        logger.info(
          `Processing payment method: ${methodName} (${methodCode}), amount: ${saleAmount}, count: ${saleCount}`
        );

        if (methodCode) {
          // Update the existing entry (which may have been initialized with zeros)
          formattedSalesByPaymentMethod[methodCode] = {
            name: methodName,
            amount: saleAmount,
            count: saleCount,
          };
        }
      });
    }

    // Ensure all payment method sales values are correctly reflected
    // Cash
    if (formattedSalesByPaymentMethod["cash"]) {
      formattedSalesByPaymentMethod["cash"].amount = cashSales;
      formattedSalesByPaymentMethod["cash"].count = cashResult.count;
    }

    // M-PESA
    if (formattedSalesByPaymentMethod["mpesa"]) {
      formattedSalesByPaymentMethod["mpesa"].amount = mpesaSales;
      formattedSalesByPaymentMethod["mpesa"].count = mpesaResult.count;
    }

    // Credit Card
    if (formattedSalesByPaymentMethod["card"]) {
      formattedSalesByPaymentMethod["card"].amount = cardSales;
      formattedSalesByPaymentMethod["card"].count = cardResult.count;
    }

    // Bank Transfer
    if (formattedSalesByPaymentMethod["bank"]) {
      formattedSalesByPaymentMethod["bank"].amount = bankSales;
      formattedSalesByPaymentMethod["bank"].count = bankResult.count;
    }

    // Credit
    if (formattedSalesByPaymentMethod["credit"]) {
      formattedSalesByPaymentMethod["credit"].amount = creditSales;
      formattedSalesByPaymentMethod["credit"].count = creditResult.count;
    }

    // Log the final formatted data
    logger.info(
      `Final formatted sales by payment method: ${JSON.stringify(
        formattedSalesByPaymentMethod
      )}`
    );

    // Verify the total matches
    const totalFromPaymentMethods = Object.values(
      formattedSalesByPaymentMethod
    ).reduce((sum, method) => sum + (method.amount || 0), 0);

    logger.info(
      `Total from payment methods: ${totalFromPaymentMethods}, Total sales: ${totalSales}`
    );

    // If there's a discrepancy between total sales and the sum of payment methods,
    // use the direct query results without adjustment
    if (Math.abs(totalFromPaymentMethods - totalSales) > 0.01) {
      logger.warn(
        `Discrepancy detected between total sales (${totalSales}) and sum of payment methods (${totalFromPaymentMethods})`
      );

      // Always use the direct query results for payment methods
      // This ensures each payment method gets its actual sales amount
      const paymentMethodSales = {
        cash: cashSales,
        mpesa: mpesaSales,
        card: cardSales,
        bank: bankSales,
        credit: creditSales,
      };

      // Log the payment method sales from direct queries
      logger.info(
        `Payment method sales from direct queries: ${JSON.stringify(
          paymentMethodSales
        )}`
      );

      // Update each payment method with its sales amount from direct queries
      Object.keys(paymentMethodSales).forEach((methodCode) => {
        if (formattedSalesByPaymentMethod[methodCode]) {
          // Always use the direct query result, even if it's zero
          formattedSalesByPaymentMethod[methodCode].amount =
            paymentMethodSales[methodCode];
        } else if (paymentMethodSales[methodCode] > 0) {
          // If the method doesn't exist in formattedSalesByPaymentMethod but has a value, add it
          formattedSalesByPaymentMethod[methodCode] = {
            name: methodCode.charAt(0).toUpperCase() + methodCode.slice(1),
            amount: paymentMethodSales[methodCode],
            count: 0, // We don't have the count, so default to 0
          };
        }
      });

      // Calculate the updated total after applying direct query results
      const updatedTotal = Object.values(formattedSalesByPaymentMethod).reduce(
        (sum, method) => sum + parseFloat(method.amount || 0),
        0
      );

      // If there's still a discrepancy, log it but don't adjust the values
      if (Math.abs(updatedTotal - totalSales) > 0.01 && totalSales > 0) {
        logger.info(
          `Still have discrepancy after direct queries: ${totalSales} vs ${updatedTotal}`
        );
        logger.info(
          `Using the direct query results without adjustment as requested.`
        );
        // We're ignoring the discrepancy and using the payment method values as they are
      }
    }

    // Calculate the final total sales from the payment methods
    const finalTotalSales = Object.values(formattedSalesByPaymentMethod).reduce(
      (sum, method) => sum + parseFloat(method.amount || 0),
      0
    );

    // Log the discrepancy but use the original total sales value
    if (Math.abs(finalTotalSales - totalSales) > 0.01) {
      logger.info(
        `Discrepancy between total sales (${totalSales}) and sum of payment methods (${finalTotalSales})`
      );
      logger.info(
        `Using the original total sales value (${totalSales}) as requested.`
      );
      // Do not update totalSales - keep the original value
    }

    // Prepare the response object
    const response = {
      session: {
        id: posSession.id,
        opening_cash_balance: parseFloat(posSession.opening_cash_balance || 0),
        opening_mpesa_balance: parseFloat(
          posSession.opening_mpesa_balance || 0
        ),
        opening_mpesa_float: parseFloat(posSession.opening_mpesa_float || 0),
        start_time: posSession.start_time,
        user: posSession.User
          ? {
              id: posSession.User.id,
              name: posSession.User.name,
            }
          : null,
        branch: posSession.Branch
          ? {
              id: posSession.Branch.id,
              name: posSession.Branch.name,
            }
          : null,
      },
      sales: {
        total: totalSales,
        transaction_count: transactionCount,
        by_payment_method: formattedSalesByPaymentMethod,
        cash_sales: cashSales,
        non_cash_sales: totalSales - cashSales,
      },
      mpesa_services: {
        deposits: totalDeposits,
        withdrawals: totalWithdrawals,
      },
      mobile_money_services: {
        opening_float: parseFloat(posSession.opening_mpesa_float || 0),
        float_in: parseFloat(posSession.float_in || 0),
        float_out: parseFloat(posSession.float_out || 0),
        total_deposits: parseFloat(posSession.total_deposits || 0),
        total_withdrawals: parseFloat(posSession.total_withdrawals || 0),
        expected_float:
          parseFloat(posSession.opening_mpesa_float || 0) +
          parseFloat(posSession.float_in || 0) -
          parseFloat(posSession.float_out || 0) -
          parseFloat(posSession.total_withdrawals || 0) +
          parseFloat(posSession.total_deposits || 0),
      },
      banking: {
        total: totalBankingDeposits,
        items: bankingTransactions.map((transaction) => {
          // Log each transaction being mapped for debugging
          logger.info(`Mapping banking transaction ${transaction.id}`);

          // Convert Sequelize instance to plain object if needed
          const plainTransaction = transaction.toJSON
            ? transaction.toJSON()
            : transaction;

          return {
            id: plainTransaction.id,
            amount: parseFloat(plainTransaction.amount || 0),
            banking_method: plainTransaction.banking_method,
            reference_number: plainTransaction.reference_number,
            status: plainTransaction.status,
            transaction_date: plainTransaction.transaction_date,
            bank: plainTransaction.Bank
              ? {
                  id: plainTransaction.Bank.id,
                  name: plainTransaction.Bank.name,
                  code: plainTransaction.Bank.code,
                  type: plainTransaction.Bank.type,
                }
              : null,
          };
        }),
      },
      expenses: {
        total: totalExpenses,
        items: expenses.map((expense) => ({
          id: expense.id,
          amount: parseFloat(expense.amount || 0),
          description: expense.description,
          category: expense.category
            ? {
                id: expense.category.id,
                name: expense.category.name,
              }
            : null,
          status: expense.status,
        })),
      },
    };

    // Final verification to ensure payment method amounts match the totals
    // This is a safety check to make sure we're returning correct data
    const finalTotalFromPaymentMethods = Object.values(
      response.sales.by_payment_method
    ).reduce((sum, method) => sum + parseFloat(method.amount || 0), 0);

    if (
      Math.abs(finalTotalFromPaymentMethods - totalSales) > 0.01 &&
      totalSales > 0
    ) {
      logger.warn(
        `Final discrepancy detected between total sales (${totalSales}) and sum of payment methods (${finalTotalFromPaymentMethods})`
      );

      // Log the current payment method values
      logger.info(
        `Current payment method values in response: ${JSON.stringify(
          response.sales.by_payment_method
        )}`
      );

      // Map of payment methods to their direct query results
      const paymentMethodMap = {
        cash: { amount: cashSales, count: cashResult.count },
        mpesa: { amount: mpesaSales, count: mpesaResult.count },
        card: { amount: cardSales, count: cardResult.count },
        bank: { amount: bankSales, count: bankResult.count },
        credit: { amount: creditSales, count: creditResult.count },
      };

      // Log the direct query results
      logger.info(`Direct query results: ${JSON.stringify(paymentMethodMap)}`);

      // Update each payment method with its direct query result
      // This ensures we're using the actual database values
      Object.keys(paymentMethodMap).forEach((methodCode) => {
        if (response.sales.by_payment_method[methodCode]) {
          // Always use the direct query result, even if it's zero
          response.sales.by_payment_method[methodCode].amount =
            paymentMethodMap[methodCode].amount;
          response.sales.by_payment_method[methodCode].count =
            paymentMethodMap[methodCode].count;
        }
      });

      // Calculate the updated total after applying direct query results
      const updatedTotalFromPaymentMethods = Object.values(
        response.sales.by_payment_method
      ).reduce((sum, method) => sum + parseFloat(method.amount || 0), 0);

      // If there's still a discrepancy, log it but don't adjust the values
      if (
        Math.abs(updatedTotalFromPaymentMethods - totalSales) > 0.01 &&
        totalSales > 0
      ) {
        logger.warn(
          `Discrepancy detected between total sales (${totalSales}) and sum of payment methods (${updatedTotalFromPaymentMethods})`
        );
        logger.info(
          `Using the original total sales value (${totalSales}) as requested.`
        );
        // Do not update totalSales or response.sales.total - keep the original values
      }
    }

    // Final check to ensure all payment methods have numeric values
    // But don't modify the actual values, just ensure they're numeric
    Object.keys(response.sales.by_payment_method).forEach((methodCode) => {
      // Only parse if not already a number
      if (
        typeof response.sales.by_payment_method[methodCode].amount !== "number"
      ) {
        response.sales.by_payment_method[methodCode].amount = parseFloat(
          response.sales.by_payment_method[methodCode].amount || 0
        );
      }
      if (
        typeof response.sales.by_payment_method[methodCode].count !== "number"
      ) {
        response.sales.by_payment_method[methodCode].count = parseInt(
          response.sales.by_payment_method[methodCode].count || 0
        );
      }
    });

    // Log the discrepancy between total sales and sum of payment methods
    const responseTotalFromPaymentMethods = Object.values(
      response.sales.by_payment_method
    ).reduce((sum, method) => sum + parseFloat(method.amount || 0), 0);

    if (
      Math.abs(responseTotalFromPaymentMethods - response.sales.total) > 0.01
    ) {
      logger.info(
        `Final discrepancy between total sales (${response.sales.total}) and sum of payment methods (${responseTotalFromPaymentMethods})`
      );
      logger.info(
        `Using the original total sales value (${response.sales.total}) as requested.`
      );
      // Do not update response.sales.total - keep the original value
    }

    // Log the final response for debugging
    logger.info(
      `Final response for shift closing data: ${JSON.stringify(response)}`
    );

    return res.status(200).json(response);
  } catch (error) {
    logger.error(`Error fetching shift closing data: ${error.message}`);
    return res.status(500).json({
      error: "ServerError",
      message: "An error occurred while fetching shift closing data",
    });
  }
};

module.exports = {
  getAllPosSessions,
  getPosSessionById,
  createPosSession,
  updatePosSession,
  closePosSession,
  deletePosSession,
  getSessionTotalSales,
  getCashRunningBalanceStatus,
  getShiftClosingData,
};
