"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatCurrency } from "@/lib/utils";
import { Eye, FileCheck, FileX } from "lucide-react";
import { useRouter } from "next/navigation";
import { DataPagination } from "@/components/ui/data-pagination";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface ExpensesDataTableProps {
  data: any[];
  totalCount: number;
  isLoading?: boolean;
  pagination?: {
    currentPage: number;
    onPageChange: (page: number) => void;
    pageSize: number;
    onPageSizeChange?: (pageSize: number) => void;
  };
}

export function ExpensesDataTable({
  data,
  totalCount,
  isLoading = false,
  pagination,
}: ExpensesDataTableProps) {
  const router = useRouter();
  const pageSize = pagination?.pageSize || 10;
  const currentPage = pagination?.currentPage || 1;
  const totalPages = Math.ceil(totalCount / pageSize);

  // Status badge variants
  const statusVariants = {
    pending: "warning",
    approved: "success",
    partially_approved: "info",
    declined: "destructive",
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Employee</TableHead>
            <TableHead>Branch</TableHead>
            <TableHead>Region</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>POP</TableHead>
            <TableHead>Date</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading && (
            <TableRow>
              <TableCell colSpan={10} className="h-24 text-center">
                <div className="flex justify-center items-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <span className="ml-2">Loading expenses...</span>
                </div>
              </TableCell>
            </TableRow>
          )}
          {!isLoading && data.length === 0 && (
            <TableRow>
              <TableCell colSpan={10} className="h-24 text-center">
                No expense requests found.
              </TableCell>
            </TableRow>
          )}
          {!isLoading &&
            data.map((expense) => (
              <TableRow key={expense.id}>
                <TableCell className="font-medium">{expense.id}</TableCell>
                <TableCell>{expense.user?.name || "Unknown"}</TableCell>
                <TableCell>{expense.branch?.name || "Unknown"}</TableCell>
                <TableCell>{expense.branch?.Region?.name || "Unknown"}</TableCell>
                <TableCell>
                  {expense.category?.name || "Uncategorized"}
                </TableCell>
                <TableCell>{formatCurrency(expense.amount)}</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      statusVariants[
                        expense.status as keyof typeof statusVariants
                      ] as any
                    }
                  >
                    {expense.status.replace("_", " ")}
                  </Badge>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center justify-center">
                          {expense.receipt_image ? (
                            <FileCheck className="h-5 w-5 text-green-500" />
                          ) : (
                            <FileX className="h-5 w-5 text-red-500" />
                          )}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        {expense.receipt_image
                          ? "Receipt attached"
                          : "No receipt attached"}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  {new Date(expense.created_at).toLocaleDateString()}
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => router.push(`/expenses/${expense.id}`)}
                  >
                    <Eye className="h-4 w-4" />
                    <span className="sr-only">View details</span>
                  </Button>
                </TableCell>
              </TableRow>
            ))}
        </TableBody>
      </Table>
      {pagination && (
        <div className="px-4 py-2 border-t">
          <DataPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={pagination.onPageChange}
            pageSize={pageSize}
            onPageSizeChange={pagination.onPageSizeChange}
            totalItems={totalCount}
            isLoading={isLoading}
            showPageSizeSelector={true}
            showItemsInfo={true}
            showFirstLastButtons={true}
          />
        </div>
      )}
    </div>
  );
}
