"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { NavigationLink } from "@/components/ui/navigation-link";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { useCurrentUser, useLogout } from "@/features/auth/hooks/use-auth";
import { usePermissions } from "@/features/auth/hooks/use-permissions";
import { navigationPermissions } from "@/lib/navigation-permissions";
import {
  Activity,
  Banknote,
  BarChart3,
  BarChart3Icon,
  BarChart4,
  Building,
  BuildingIcon,
  ChevronRight,
  Clipboard,
  CreditCard,
  Database,
  DollarSign,
  FileText,
  FolderTree,
  HomeIcon,
  Landmark,
  LogOut,
  Package,
  PackageOpen,
  Receipt,
  Settings,
  Shield,
  ShoppingBag,
  ShoppingCart,
  Tag,
  Truck as TruckIcon,
  User,
  UserCheck,
  Users,
  Users2,
  UsersIcon,
} from "lucide-react";
import { usePathname } from "next/navigation";
import * as React from "react";

type NavItem = {
  title: string;
  href: string;
  icon: React.ReactNode;
  permissionKey?: string;
  roles?: string[]; // Keep for backward compatibility
  items?: {
    title: string;
    href: string;
    permissionKey?: string;
    roles?: string[]; // Keep for backward compatibility
  }[];
};

export function ShadcnSidebar() {
  const { data: user } = useCurrentUser();
  const pathname = usePathname();
  const logout = useLogout();
  const { hasPermission } = usePermissions();
  // const { isMobile } = useSidebar();

  // Define navigation items grouped by category
  const navigationGroups: { name: string; items: NavItem[] }[] = [
    {
      name: "Main",
      items: [
        {
          href: "/dashboard",
          icon: <HomeIcon className="h-4 w-4" />,
          title: "Dashboard",
          permissionKey: "dashboard",
        },
      ],
    },
    {
      name: "Reports & Analytics",
      items: [
        {
          href: "/reports",
          icon: <BarChart3Icon className="h-4 w-4" />,
          title: "Reports Dashboard",
          roles: [
            "super_admin",
            "company_admin",

            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/sales-summary",
          icon: <BarChart4 className="h-4 w-4" />,
          title: "Sales Summary",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/sales-by-item",
          icon: <PackageOpen className="h-4 w-4" />,
          title: "Sales by Item",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/sales-by-category",
          icon: <ShoppingBag className="h-4 w-4" />,
          title: "Sales by Category",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/tax-report",
          icon: <Activity className="h-4 w-4" />,
          title: "Tax Report",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/mpesa-banking",
          icon: <DollarSign className="h-4 w-4" />,
          title: "Banking",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/mpesa-transactions",
          icon: <CreditCard className="h-4 w-4" />,
          title: "M-Pesa Transactions",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "operations_manager",
            "assistant_operations_manager",
          ],
        },
        {
          href: "/reports/running-balances",
          icon: <BarChart3 className="h-4 w-4" />,
          title: "Running Balances",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "operations_manager",
            "assistant_operations_manager",
          ],
        },
        {
          href: "/reports/cash-status",
          icon: <Banknote className="h-4 w-4" />,
          title: "Cash Status",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "operations_manager",
            "assistant_operations_manager",
          ],
        },
        {
          href: "/reports/dsa-sales",
          icon: <Users className="h-4 w-4" />,
          title: "DSA Sales",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/receipts",
          icon: <Receipt className="h-4 w-4" />,
          title: "Receipts",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/shifts",
          icon: <Clipboard className="h-4 w-4" />,
          title: "Shifts",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/reports/stock-report",
          icon: <Package className="h-4 w-4" />,
          title: "Stock Report",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "stock_admin",
          ],
        },
      ],
    },
    {
      name: "Administration",
      items: [
        // Tenants menu item removed
        {
          href: "/users",
          icon: <UsersIcon className="h-4 w-4" />,
          title: "Users",
          permissionKey: "users",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
          ],
        },
        {
          href: "/roles",
          icon: <Shield className="h-4 w-4" />,
          title: "Roles",
          permissionKey: "roles",
          roles: ["super_admin", "company_admin", "accountant"],
        },
        // RBAC items temporarily hidden for production
        // {
        //   href: "/rbac",
        //   icon: <Database className="h-4 w-4" />,
        //   title: "RBAC",
        //   permissionKey: "rbac",
        //   roles: ["super_admin"],
        // },
        // {
        //   href: "/rbac-test",
        //   icon: <Shield className="h-4 w-4" />,
        //   title: "RBAC Test",
        //   permissionKey: "rbac",
        //   roles: ["super_admin"],
        // },
        // {
        //   href: "/permission-guard-test",
        //   icon: <Shield className="h-4 w-4" />,
        //   title: "Permission Guard Test",
        //   permissionKey: "rbac",
        //   roles: ["super_admin", "company_admin"],
        // },
        {
          href: "/branches",
          icon: <BuildingIcon className="h-4 w-4" />,
          title: "Branches",
          roles: ["super_admin", "company_admin", "accountant"],
        },
        {
          href: "/employees",
          icon: <Users2 className="h-4 w-4" />,
          title: "Employees",
          roles: ["super_admin", "company_admin", "accountant"],
        },
      ],
    },
    {
      name: "Products & Inventory",
      items: [
        {
          href: "/products",
          icon: <Package className="h-4 w-4" />,
          title: "Products",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
          ],
        },
        {
          href: "/categories",
          icon: <FolderTree className="h-4 w-4" />,
          title: "Categories",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/brands",
          icon: <Tag className="h-4 w-4" />,
          title: "Brands",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/inventory",
          icon: <Package className="h-4 w-4" />,
          title: "Inventory",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "finance_manager",
            "accountant",
            "float_manager",
          ],
        },
        {
          href: "/inventory/purchases",
          icon: <ShoppingBag className="h-4 w-4" />,
          title: "Purchases",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "accountant",
          ],
        },
        {
          href: "/inventory/purchases/new",
          icon: <ShoppingBag className="h-4 w-4" />,
          title: "Create Purchase",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
          ],
        },
        {
          href: "/inventory/transfers",
          icon: <TruckIcon className="h-4 w-4" />,
          title: "Stock Transfers",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "finance_manager",
            "accountant",
            "float_manager",
          ],
        },
        {
          href: "/inventory/bulk-transfer",
          icon: <TruckIcon className="h-4 w-4" />,
          title: "Bulk Transfer",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
          ],
        },
        {
          href: "/inventory/reports",
          icon: <BarChart3Icon className="h-4 w-4" />,
          title: "Inventory Reports",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "finance_manager",
            "accountant",
            "float_manager",
          ],
        },
      ],
    },
    {
      name: "Procurement",
      items: [
        {
          href: "/procurement",
          icon: <ShoppingCart className="h-4 w-4" />,
          title: "Procurement Dashboard",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "accountant",
          ],
        },
        {
          href: "/procurement/requests",
          icon: <FileText className="h-4 w-4" />,
          title: "Procurement Requests",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "accountant",
          ],
        },
        {
          href: "/procurement/requests/new",
          icon: <ShoppingCart className="h-4 w-4" />,
          title: "Create Request",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
          ],
        },
        {
          href: "/procurement/receipts",
          icon: <Receipt className="h-4 w-4" />,
          title: "Procurement Receipts",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "stock_admin",
            "accountant",
          ],
        },
      ],
    },
    {
      name: "Float Management",
      items: [
        {
          href: "/float",
          icon: <Banknote className="h-4 w-4" />,
          title: "Float Dashboard",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "float_manager",
          ],
        },
        {
          href: "/float/movements",
          icon: <TruckIcon className="h-4 w-4" />,
          title: "Float Movements",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "float_manager",
          ],
        },
        {
          href: "/float/reconciliations",
          icon: <BarChart3Icon className="h-4 w-4" />,
          title: "Float Reconciliations",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "float_manager",
          ],
        },
      ],
    },
    {
      name: "Banking Management",
      items: [
        {
          href: "/banking",
          icon: <Landmark className="h-4 w-4" />,
          title: "Banking Records",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "float_manager",
          ],
        },
        {
          href: "/banking/summary",
          icon: <BarChart3Icon className="h-4 w-4" />,
          title: "Banking Summary",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
            "accountant",
            "float_manager",
          ],
        },
      ],
    },
    {
      name: "Expenses Management",
      items: [
        {
          href: "/expenses",
          icon: <Banknote className="h-4 w-4" />,
          title: "Expenses",
          roles: [
            "company_admin",
            "branch_admin",
            "branch_manager",
            "accountant",
          ],
        },
      ],
    },
    {
      name: "POS Management",
      items: [
        {
          href: "/pos",
          icon: <CreditCard className="h-4 w-4" />,
          title: "POS Dashboard",
          roles: [
            "super_admin",
            "company_admin",
            "branch_admin",
            "branch_manager",
            "pos_operator",
            "shop_attendant",
          ],
        },
        {
          href: "/pos/sessions",
          icon: <Clipboard className="h-4 w-4" />,
          title: "POS Sessions",
          roles: [
            "super_admin",
            "company_admin",
            "branch_admin",
            "branch_manager",
            "pos_operator",
            "shop_attendant",
          ],
        },
        {
          href: "/dashboard/cash-balance",
          icon: <Banknote className="h-4 w-4" />,
          title: "Cash Balance",
          roles: [
            "super_admin",
            "company_admin",
            "branch_admin",
            "branch_manager",
            "float_manager",
          ],
        },
        {
          href: "/sales",
          icon: <Receipt className="h-4 w-4" />,
          title: "Sales",
          roles: [
            "super_admin",
            "company_admin",
            "branch_admin",
            "branch_manager",
            "pos_operator",
            "shop_attendant",
          ],
        },
        {
          href: "/customers",
          icon: <Users2 className="h-4 w-4" />,
          title: "Customers",
          roles: [
            "super_admin",
            "company_admin",
            "branch_admin",
            "branch_manager",
            "pos_operator",
            "shop_attendant",
          ],
        },
      ],
    },
    {
      name: "DSA Management",
      items: [
        {
          href: "/dsa",
          icon: <UserCheck className="h-4 w-4" />,
          title: "DSA Dashboard",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/dsa/customers",
          icon: <Users className="h-4 w-4" />,
          title: "DSA Agents",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/dsa/assignments",
          icon: <Package className="h-4 w-4" />,
          title: "Stock Assignments",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
        {
          href: "/dsa/reconciliations",
          icon: <BarChart3Icon className="h-4 w-4" />,
          title: "DSA Reconciliations",
          roles: [
            "super_admin",
            "company_admin",
            "tenant_admin",
            "branch_manager",
          ],
        },
      ],
    },
    {
      name: "Settings",
      items: [
        // Settings items temporarily hidden for production
        // {
        //   href: "/settings/system",
        //   icon: <Settings className="h-4 w-4" />,
        //   title: "System Settings",
        //   roles: ["super_admin"],
        // },
        // {
        //   href: "/settings/company",
        //   icon: <Building className="h-4 w-4" />,
        //   title: "Company Settings",
        //   roles: ["super_admin", "company_admin"],
        // },
        // {
        //   href: "/settings/payment-methods",
        //   icon: <Banknote className="h-4 w-4" />,
        //   title: "Payment Methods",
        //   roles: ["super_admin", "company_admin"],
        // },
        // {
        //   href: "/settings/health",
        //   icon: <Activity className="h-4 w-4" />,
        //   title: "System Health",
        //   roles: ["super_admin"],
        // },
        {
          href: "/profile",
          icon: <User className="h-4 w-4" />,
          title: "Profile",
        },
      ],
    },
  ];

  // Safely access role_name with type checking
  const userRoleName =
    user && typeof user === "object" && "role_name" in user
      ? user.role_name
      : "";

  const handleLogout = () => {
    logout.mutate();
  };

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        <NavigationLink
          href="/dashboard"
          className="flex items-center gap-2 font-semibold p-2"
        >
          <span className="text-primary text-lg">Simba POS</span>
        </NavigationLink>
      </SidebarHeader>
      <SidebarContent>
        {navigationGroups.map((group, groupIndex) => {
          // Filter items based on permissions
          const filteredItems = group.items.filter((item) => {
            // If no permission key is specified, check roles for backward compatibility
            if (!item.permissionKey) {
              return (
                !item.roles ||
                (userRoleName && item.roles.includes(userRoleName))
              );
            }

            // Get the permission required for this item
            const permission = navigationPermissions[item.permissionKey];

            // If permission mapping doesn't exist, hide the item
            if (!permission) return false;

            // Check if user has the required permission
            return hasPermission(
              permission.resource,
              permission.action,
              permission.scope
            );
          });

          // Don't render the group if there are no items to show
          if (filteredItems.length === 0) {
            return null;
          }

          return (
            <SidebarGroup key={`${group.name}-${groupIndex}`}>
              <SidebarGroupLabel>{group.name}</SidebarGroupLabel>
              <SidebarMenu>
                {filteredItems.map((item) => {
                  const isActive =
                    pathname === item.href ||
                    pathname.startsWith(`${item.href}/`);

                  // If the item has subitems, render a collapsible
                  if (item.items && item.items.length > 0) {
                    // Filter subitems based on permissions
                    const filteredSubItems = item.items.filter((subItem) => {
                      // If no permission key is specified, check roles for backward compatibility
                      if (!subItem.permissionKey) {
                        return (
                          !subItem.roles ||
                          (userRoleName && subItem.roles.includes(userRoleName))
                        );
                      }

                      // Get the permission required for this item
                      const permission =
                        navigationPermissions[subItem.permissionKey];

                      // If permission mapping doesn't exist, hide the item
                      if (!permission) return false;

                      // Check if user has the required permission
                      return hasPermission(
                        permission.resource,
                        permission.action,
                        permission.scope
                      );
                    });

                    // Don't render if there are no subitems to show
                    if (filteredSubItems.length === 0) {
                      return null;
                    }

                    return (
                      <Collapsible
                        key={item.href}
                        asChild
                        defaultOpen={isActive}
                        className="group/collapsible"
                      >
                        <SidebarMenuItem>
                          <CollapsibleTrigger asChild>
                            <SidebarMenuButton tooltip={item.title}>
                              {item.icon}
                              <span>{item.title}</span>
                              <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                            </SidebarMenuButton>
                          </CollapsibleTrigger>
                          <CollapsibleContent>
                            <SidebarMenuSub>
                              {filteredSubItems.map((subItem) => (
                                <SidebarMenuSubItem key={subItem.href}>
                                  <SidebarMenuSubButton
                                    asChild
                                    isActive={pathname === subItem.href}
                                  >
                                    <NavigationLink href={subItem.href}>
                                      <span>{subItem.title}</span>
                                    </NavigationLink>
                                  </SidebarMenuSubButton>
                                </SidebarMenuSubItem>
                              ))}
                            </SidebarMenuSub>
                          </CollapsibleContent>
                        </SidebarMenuItem>
                      </Collapsible>
                    );
                  }

                  // Otherwise, render a simple menu item
                  return (
                    <SidebarMenuItem key={item.href}>
                      <SidebarMenuButton
                        asChild
                        isActive={isActive}
                        tooltip={item.title}
                      >
                        <NavigationLink href={item.href}>
                          {item.icon}
                          <span>{item.title}</span>
                        </NavigationLink>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroup>
          );
        })}
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" onClick={handleLogout}>
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage
                  src="/placeholder-avatar.jpg"
                  alt={user?.name || "User"}
                />
                <AvatarFallback className="rounded-lg">
                  {user?.name ? user.name.charAt(0) : "U"}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">
                  {user?.name || "User"}
                </span>
                <span className="truncate text-xs">
                  {user?.email || "<EMAIL>"}
                </span>
              </div>
              <LogOut className="ml-auto h-4 w-4" />
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
