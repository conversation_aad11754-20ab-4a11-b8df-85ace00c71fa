/**
 * TypeScript interfaces for Reports API endpoints
 * Based on REPORTS_ENDPOINT_GUIDE.md specification
 */

// Common types
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

export interface ProductInfo {
  id: number;
  name: string;
  sku: string;
}

export interface SessionInfo {
  id: number;
  start_time: string;
  end_time: string;
  status: string;
  user: {
    id: number;
    name: string;
  };
}

export interface StockMovement {
  id: number;
  type: string;
  quantity: number;
  reason: string;
  created_at: string;
}

// Stock History Report Types
export interface StockHistoryParams {
  product_id: number;
  branch_id?: number;
  start_date?: string;
  end_date?: string;
  include_movements?: boolean;
  format?: 'json' | 'excel';
}

export interface StockSnapshot {
  id: number;
  date: string;
  snapshot_type: 'EOD' | 'SHIFT';
  quantity: number;
  unit_cost: number;
  total_value: number;
  branch_id: number;
  branch_name: string;
  branch_location: string;
  pos_session_id: number | null;
  session_info: SessionInfo | null;
  created_at: string;
}

export interface StockHistoryResponse {
  status: 'success';
  product: ProductInfo;
  snapshots: StockSnapshot[];
  movements: StockMovement[];
}

// Sales Summary Report Types
export interface SalesSummaryParams {
  start_date: string;
  end_date: string;
  product_id?: number;
  category_id?: number;
  branch_id?: number;
  region_id?: number;
  location_id?: number;
  format?: 'json' | 'excel';
}

export interface SalesSummary {
  total_sales: number;
  total_amount: number;
  total_discount: number;
  net_amount: number;
  total_items: number;
  average_sale_value: number;
}

export interface SalesByProduct {
  product_id: number;
  product_name: string;
  product_sku: string;
  category_id: number;
  category_name: string;
  quantity: number;
  total_amount: number;
  discount_amount: number;
  net_amount: number;
}

export interface SalesByBranch {
  branch_id: number;
  branch_name: string;
  branch_location: string;
  region_id: number | null;
  region_name: string;
  total_sales: number;
  total_amount: number;
  discount_amount: number;
  net_amount: number;
}

export interface SalesDetail {
  id: number;
  receipt_number: string;
  branch_id: number;
  branch_name: string;
  branch_location: string;
  region_id: number | null;
  region_name: string;
  user_id: number;
  user_name: string;
  total_amount: number;
  discount_amount: number;
  net_amount: number;
  payment_method: string;
  created_at: string;
  items: SalesItemDetail[];
}

export interface SalesItemDetail {
  id: number;
  product_id: number;
  product_name: string;
  product_sku: string;
  category_id: number;
  category_name: string;
  quantity: number;
  unit_price: number;
  discount_amount: number;
  total_price: number;
  created_at: string;
}

export interface SalesSummaryResponse {
  status: 'success';
  filters: SalesSummaryParams;
  summary: SalesSummary;
  by_product: SalesByProduct[];
  by_branch: SalesByBranch[];
  sales: SalesDetail[];
}

// Banking Transactions Report Types
export interface BankingTransactionsParams {
  start_date?: string;
  end_date?: string;
  branch_id?: number;
  region_id?: number;
  banking_method?: 'bank' | 'agent' | 'mpesa';
  transaction_type?: 'deposit' | 'withdrawal' | 'transfer';
  bank_id?: number;
  status?: 'pending' | 'completed' | 'failed' | 'reconciled';
  format?: 'json' | 'excel';
  page?: number;
  limit?: number;
}

export interface BankingTransactionsSummary {
  total_transactions: number;
  total_amount: number;
  by_method: Array<{
    method: string;
    count: number;
    total: number;
  }>;
  by_type: Array<{
    type: string;
    count: number;
    total: number;
  }>;
}

export interface BankingTransaction {
  id: number;
  branch_id: number;
  branch_name: string;
  branch_location: string;
  region_id: number | null;
  region_name: string;
  user_id: number;
  user_name: string;
  transaction_date: string;
  amount: number;
  banking_method: string;
  transaction_type: string;
  bank_id: number;
  bank_name: string;
  bank_type: string;
  reference_number: string;
  status: string;
  approval_status: string;
  approved_by: number;
  approver_name: string;
  approval_date: string;
  rejection_reason: string | null;
  expected_amount: number;
  discrepancy: number;
  notes: string;
  has_receipt: boolean;
  receipts: Array<{
    id: number;
    file_name: string;
    file_type: string;
    created_at: string;
  }>;
  created_by: number;
  creator_name: string;
  created_at: string;
}

export interface BankingTransactionsResponse {
  status: 'success';
  filters: BankingTransactionsParams;
  pagination: PaginationInfo;
  summary: BankingTransactionsSummary;
  transactions: BankingTransaction[];
}

// Expense Export Types
export interface ExpenseExportParams {
  start_date?: string;
  end_date?: string;
  branch_id?: number;
  region_id?: number;
  category_id?: number;
  user_id?: number;
  status?: 'pending' | 'approved' | 'partially_approved' | 'declined';
  report_type?: 'detailed' | 'summary' | 'by_category' | 'by_branch' | 'by_user';
}

// Export response is a Blob for Excel files
export type ExpenseExportResponse = Blob;
