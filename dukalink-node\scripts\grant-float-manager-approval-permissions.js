const mysql = require("mysql2/promise");
require("dotenv").config();

const logger = {
  info: (msg) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
  error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`),
};

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || "localhost",
  user: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "",
  database: process.env.DB_NAME || "dukalink_api",
  port: process.env.DB_PORT || 3306,
};

async function grantFloatManagerApprovalPermissions() {
  let connection;

  try {
    logger.info("Connecting to database...");
    connection = await mysql.createConnection(dbConfig);

    logger.info("Starting transaction...");
    await connection.beginTransaction();

    // Define the new approval permissions for float_manager
    const approvalPermissions = [
      // Banking transaction approvals
      {
        role: "float_manager",
        resource: "banking_approval",
        action: "create:any",
        attributes: JSON.stringify({ "*": true }),
      },
      {
        role: "float_manager",
        resource: "banking_rejection",
        action: "create:any",
        attributes: JSON.stringify({ "*": true }),
      },

      // MPESA float reconciliation approvals
      {
        role: "float_manager",
        resource: "mpesa_float_approval",
        action: "create:any",
        attributes: JSON.stringify({ "*": true }),
      },
      {
        role: "float_manager",
        resource: "mpesa_float_rejection",
        action: "create:any",
        attributes: JSON.stringify({ "*": true }),
      },

      // Cash float approvals
      {
        role: "float_manager",
        resource: "cash_float_approval",
        action: "create:any",
        attributes: JSON.stringify({ "*": true }),
      },
      {
        role: "float_manager",
        resource: "cash_float_rejection",
        action: "create:any",
        attributes: JSON.stringify({ "*": true }),
      },

      // General float management approvals
      {
        role: "float_manager",
        resource: "float_reconciliation_approval",
        action: "create:any",
        attributes: JSON.stringify({ "*": true }),
      },
      {
        role: "float_manager",
        resource: "float_reconciliation_rejection",
        action: "create:any",
        attributes: JSON.stringify({ "*": true }),
      },
    ];

    // Also grant the same permissions to company_admin for consistency
    const companyAdminPermissions = approvalPermissions.map((perm) => ({
      ...perm,
      role: "company_admin",
    }));

    // Combine all permissions
    const allPermissions = [...approvalPermissions, ...companyAdminPermissions];

    logger.info("Checking for existing permissions...");

    // Check which permissions already exist
    const existingPermissions = [];
    for (const perm of allPermissions) {
      const [rows] = await connection.execute(
        "SELECT id FROM rbac_grants WHERE role = ? AND resource = ? AND action = ?",
        [perm.role, perm.resource, perm.action]
      );

      if (rows.length > 0) {
        existingPermissions.push(
          `${perm.role}:${perm.resource}:${perm.action}`
        );
        logger.warn(
          `Permission already exists: ${perm.role} -> ${perm.action} ${perm.resource}`
        );
      }
    }

    // Filter out existing permissions
    const newPermissions = allPermissions.filter(
      (perm) =>
        !existingPermissions.includes(
          `${perm.role}:${perm.resource}:${perm.action}`
        )
    );

    if (newPermissions.length === 0) {
      logger.info("All permissions already exist. No changes needed.");
      await connection.rollback();
      return;
    }

    logger.info(`Adding ${newPermissions.length} new permissions...`);

    // Insert new permissions
    for (const perm of newPermissions) {
      await connection.execute(
        `INSERT INTO rbac_grants (role, resource, action, attributes, created_at, updated_at) 
         VALUES (?, ?, ?, ?, NOW(), NOW())`,
        [perm.role, perm.resource, perm.action, perm.attributes]
      );

      logger.info(`✓ Added: ${perm.role} -> ${perm.action} ${perm.resource}`);
    }

    logger.info("Committing transaction...");
    await connection.commit();

    logger.info(
      "✅ Successfully granted approval permissions to float_manager and company_admin!"
    );

    // Display summary
    logger.info("\n=== PERMISSION SUMMARY ===");
    logger.info("float_manager can now:");
    logger.info("- Approve/reject banking transactions");
    logger.info("- Approve/reject MPESA float reconciliations");
    logger.info("- Approve/reject cash float operations");
    logger.info("- Approve/reject float reconciliations");

    logger.info("\ncompany_admin retains all existing permissions plus:");
    logger.info("- All the same approval permissions as float_manager");

    logger.info("\n=== NEXT STEPS ===");
    logger.info(
      "1. Update banking-transaction.controller.js to use RBAC instead of hardcoded role checks"
    );
    logger.info("2. Update MPESA float controllers to use RBAC for approvals");
    logger.info("3. Add RBAC middleware to approval routes");
    logger.info("4. Test the approval functionality with float_manager role");
  } catch (error) {
    logger.error(`Error granting permissions: ${error.message}`);
    if (connection) {
      logger.info("Rolling back transaction...");
      await connection.rollback();
    }
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      logger.info("Database connection closed.");
    }
  }
}

// Execute the script
if (require.main === module) {
  grantFloatManagerApprovalPermissions()
    .then(() => {
      logger.info("Script completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      logger.error(`Script failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { grantFloatManagerApprovalPermissions };
