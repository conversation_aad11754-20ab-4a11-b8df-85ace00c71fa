{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/providers/search-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { createContext, useContext, useState, useEffect, ReactNode } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\ntype SearchContextType = {\r\n  isOpen: boolean;\r\n  openSearch: () => void;\r\n  closeSearch: () => void;\r\n  toggleSearch: () => void;\r\n};\r\n\r\nconst SearchContext = createContext<SearchContextType | undefined>(undefined);\r\n\r\nexport function SearchProvider({ children }: { children: ReactNode }) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const router = useRouter();\r\n\r\n  const openSearch = () => setIsOpen(true);\r\n  const closeSearch = () => setIsOpen(false);\r\n  const toggleSearch = () => setIsOpen((prev) => !prev);\r\n\r\n  // Add keyboard shortcut (Ctrl+K or Cmd+K) to open search\r\n  useEffect(() => {\r\n    const handleKeyDown = (e: KeyboardEvent) => {\r\n      if ((e.ctrlKey || e.metaKey) && e.key === \"k\") {\r\n        e.preventDefault();\r\n        toggleSearch();\r\n      }\r\n      \r\n      // Close on escape key\r\n      if (e.key === \"Escape\" && isOpen) {\r\n        closeSearch();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [isOpen]);\r\n\r\n  // Close search when route changes\r\n  useEffect(() => {\r\n    const handleRouteChange = () => {\r\n      closeSearch();\r\n    };\r\n\r\n    // This is a simplified approach since Next.js App Router doesn't have a direct route change event\r\n    // In a real implementation, you might want to use a more robust solution\r\n    window.addEventListener(\"popstate\", handleRouteChange);\r\n    \r\n    return () => {\r\n      window.removeEventListener(\"popstate\", handleRouteChange);\r\n    };\r\n  }, [router]);\r\n\r\n  return (\r\n    <SearchContext.Provider value={{ isOpen, openSearch, closeSearch, toggleSearch }}>\r\n      {children}\r\n    </SearchContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useSearch() {\r\n  const context = useContext(SearchContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useSearch must be used within a SearchProvider\");\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAYA,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS,eAAe,EAAE,QAAQ,EAA2B;;IAClE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,IAAM,UAAU;IACnC,MAAM,cAAc,IAAM,UAAU;IACpC,MAAM,eAAe,IAAM,UAAU,CAAC,OAAS,CAAC;IAEhD,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB,CAAC;oBACrB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;wBAC7C,EAAE,cAAc;wBAChB;oBACF;oBAEA,sBAAsB;oBACtB,IAAI,EAAE,GAAG,KAAK,YAAY,QAAQ;wBAChC;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;4CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;mCAAG;QAAC;KAAO;IAEX,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;8DAAoB;oBACxB;gBACF;;YAEA,kGAAkG;YAClG,yEAAyE;YACzE,OAAO,gBAAgB,CAAC,YAAY;YAEpC;4CAAO;oBACL,OAAO,mBAAmB,CAAC,YAAY;gBACzC;;QACF;mCAAG;QAAC;KAAO;IAEX,qBACE,6LAAC,cAAc,QAAQ;QAAC,OAAO;YAAE;YAAQ;YAAY;YAAa;QAAa;kBAC5E;;;;;;AAGP;GA9CgB;;QAEC,qIAAA,CAAA,YAAS;;;KAFV;AAgDT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\nimport { format } from \"date-fns\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Format a date string to a more readable format\r\n * @param dateString - ISO date string\r\n * @param options - Intl.DateTimeFormatOptions\r\n * @returns Formatted date string\r\n */\r\nexport function formatDate(\r\n  dateString: string,\r\n  options: Intl.DateTimeFormatOptions = {\r\n    year: \"numeric\",\r\n    month: \"short\",\r\n    day: \"numeric\",\r\n  }\r\n): string {\r\n  if (!dateString) return \"\";\r\n\r\n  const date = new Date(dateString);\r\n\r\n  // Check if the date is valid\r\n  if (isNaN(date.getTime())) {\r\n    return \"Invalid date\";\r\n  }\r\n\r\n  return new Intl.DateTimeFormat(\"en-US\", options).format(date);\r\n}\r\n\r\n/**\r\n * Format a number or string as currency\r\n * @param amount - Number or string to format\r\n * @param currency - Currency code (default: KES)\r\n * @returns Formatted currency string\r\n */\r\n/**\r\n * Safely format a number with a fixed number of decimal places\r\n * @param value - The value to format\r\n * @param decimals - Number of decimal places (default: 2)\r\n * @param defaultValue - Default value to return if input is invalid (default: '0.00')\r\n * @returns Formatted string with fixed decimal places\r\n */\r\nexport function formatNumber(\r\n  value: any,\r\n  decimals: number = 2,\r\n  defaultValue: string = '0.00'\r\n): string {\r\n  if (value === undefined || value === null) {\r\n    return defaultValue;\r\n  }\r\n\r\n  try {\r\n    const numValue = typeof value === 'number' ? value : Number(value);\r\n    if (isNaN(numValue)) {\r\n      return defaultValue;\r\n    }\r\n    return numValue.toFixed(decimals);\r\n  } catch (error) {\r\n    console.error('Error formatting number:', error);\r\n    return defaultValue;\r\n  }\r\n}\r\n\r\nexport function formatCurrency(\r\n  amount: number | string | null | undefined,\r\n  currency: string = \"KES\"\r\n): string {\r\n  // Handle null, undefined, or empty string\r\n  if (amount === null || amount === undefined || amount === \"\") {\r\n    return new Intl.NumberFormat(\"en-KE\", {\r\n      style: \"currency\",\r\n      currency,\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 2,\r\n    }).format(0);\r\n  }\r\n\r\n  // Convert string to number if needed\r\n  const numericAmount =\r\n    typeof amount === \"string\" ? parseFloat(amount) : amount;\r\n\r\n  // Check if the conversion resulted in a valid number\r\n  if (isNaN(numericAmount)) {\r\n    return new Intl.NumberFormat(\"en-KE\", {\r\n      style: \"currency\",\r\n      currency,\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 2,\r\n    }).format(0);\r\n  }\r\n\r\n  return new Intl.NumberFormat(\"en-KE\", {\r\n    style: \"currency\",\r\n    currency,\r\n    minimumFractionDigits: 0,\r\n    maximumFractionDigits: 2,\r\n  }).format(numericAmount);\r\n}\r\n\r\n/**\r\n * Format a date for API requests (YYYY-MM-DD)\r\n * @param date - Date object or string\r\n * @returns Formatted date string\r\n */\r\nexport function formatDateForApi(date: Date | string): string {\r\n  if (!date) return \"\";\r\n  const d = typeof date === \"string\" ? new Date(date) : date;\r\n  return format(d, \"yyyy-MM-dd\");\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,WACd,UAAkB,EAClB,UAAsC;IACpC,MAAM;IACN,OAAO;IACP,KAAK;AACP,CAAC;IAED,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,IAAI,KAAK;IAEtB,6BAA6B;IAC7B,IAAI,MAAM,KAAK,OAAO,KAAK;QACzB,OAAO;IACT;IAEA,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS,SAAS,MAAM,CAAC;AAC1D;AAeO,SAAS,aACd,KAAU,EACV,WAAmB,CAAC,EACpB,eAAuB,MAAM;IAE7B,IAAI,UAAU,aAAa,UAAU,MAAM;QACzC,OAAO;IACT;IAEA,IAAI;QACF,MAAM,WAAW,OAAO,UAAU,WAAW,QAAQ,OAAO;QAC5D,IAAI,MAAM,WAAW;YACnB,OAAO;QACT;QACA,OAAO,SAAS,OAAO,CAAC;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAEO,SAAS,eACd,MAA0C,EAC1C,WAAmB,KAAK;IAExB,0CAA0C;IAC1C,IAAI,WAAW,QAAQ,WAAW,aAAa,WAAW,IAAI;QAC5D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP;YACA,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,qCAAqC;IACrC,MAAM,gBACJ,OAAO,WAAW,WAAW,WAAW,UAAU;IAEpD,qDAAqD;IACrD,IAAI,MAAM,gBAAgB;QACxB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP;YACA,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAOO,SAAS,iBAAiB,IAAmB;IAClD,IAAI,CAAC,MAAM,OAAO;IAClB,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,GAAG;AACnB", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/cookies.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\n\r\n// Function to get a cookie value\r\nexport function getCookie(name: string): string | null {\r\n  if (typeof document === \"undefined\") {\r\n    console.log(`getCookie(${name}) called on server side, returning null`);\r\n    return null; // Return null on server side\r\n  }\r\n\r\n  console.log(`Getting cookie: ${name}, all cookies:`, document.cookie);\r\n\r\n  // Try to get from cookie first\r\n  const value = `; ${document.cookie}`;\r\n  const parts = value.split(`; ${name}=`);\r\n  if (parts.length === 2) {\r\n    const cookieValue = parts.pop()?.split(\";\").shift() || null;\r\n    console.log(\r\n      `Cookie ${name} found with value: ${cookieValue ? \"exists\" : \"null\"}`\r\n    );\r\n    return cookieValue;\r\n  }\r\n\r\n  // If not found in cookie, try localStorage as fallback\r\n  try {\r\n    const localStorageValue = localStorage.getItem(name);\r\n    if (localStorageValue) {\r\n      console.log(`Cookie ${name} not found in cookies, but found in localStorage`);\r\n\r\n      // If found in localStorage but not in cookies, restore the cookie\r\n      setCookie(name, localStorageValue);\r\n\r\n      return localStorageValue;\r\n    }\r\n  } catch (e) {\r\n    console.warn(\"Failed to access localStorage:\", e);\r\n  }\r\n\r\n  console.log(`Cookie ${name} not found in cookies or localStorage`);\r\n  return null;\r\n}\r\n\r\n// Function to set a cookie\r\nexport function setCookie(name: string, value: string, days: number = 30): void {\r\n  if (typeof document === \"undefined\") {\r\n    console.log(\"setCookie called on server side, ignoring\");\r\n    return; // Do nothing on server side\r\n  }\r\n\r\n  const expires = new Date();\r\n  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);\r\n\r\n  // Build cookie string with appropriate attributes\r\n  // Using path=/ to ensure the cookie is available across the entire site\r\n  // Using SameSite=Lax to allow the cookie to be sent with same-site requests\r\n  // Not using HttpOnly as we need to access the cookie from JavaScript\r\n  let cookieString = `${name}=${encodeURIComponent(\r\n    value\r\n  )};expires=${expires.toUTCString()};path=/;SameSite=Lax;max-age=${days * 24 * 60 * 60}`;\r\n\r\n  // Add Secure flag in production or if using HTTPS\r\n  if (window.location.protocol === \"https:\") {\r\n    cookieString += \";Secure\";\r\n  }\r\n\r\n  document.cookie = cookieString;\r\n\r\n  // Store in localStorage as a fallback\r\n  try {\r\n    localStorage.setItem(name, value);\r\n  } catch (e) {\r\n    console.warn(\"Failed to store token in localStorage:\", e);\r\n  }\r\n\r\n  console.log(\r\n    `Cookie set: ${name}=${\r\n      value ? \"value exists\" : \"empty\"\r\n    }, expires in ${days} days`\r\n  );\r\n\r\n  // Log cookies immediately after setting\r\n  const allCookies = document.cookie;\r\n  console.log(\"All cookies after setting:\", allCookies);\r\n\r\n  // Double-check if cookie was set correctly\r\n  setTimeout(() => {\r\n    const checkCookie = getCookie(name);\r\n    console.log(\r\n      `Cookie check after setting ${name}:`,\r\n      checkCookie ? \"exists\" : \"missing\"\r\n    );\r\n  }, 100);\r\n}\r\n\r\n// Function to delete a cookie\r\nexport function deleteCookie(name: string): void {\r\n  if (typeof document === \"undefined\") {\r\n    console.log(`deleteCookie(${name}) called on server side, ignoring`);\r\n    return; // Do nothing on server side\r\n  }\r\n\r\n  console.log(`Deleting cookie: ${name}`);\r\n\r\n  // Delete the cookie by setting an expired date\r\n  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;SameSite=Lax`;\r\n\r\n  // Also try with different paths to ensure it's deleted\r\n  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;\r\n  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;`;\r\n\r\n  // Also remove from localStorage if it exists\r\n  try {\r\n    localStorage.removeItem(name);\r\n  } catch (e) {\r\n    console.warn(\"Failed to remove token from localStorage:\", e);\r\n  }\r\n\r\n  // Log cookies immediately after deletion\r\n  const allCookies = document.cookie;\r\n  console.log(\"All cookies after deletion:\", allCookies);\r\n}\r\n\r\n// Custom hook for using cookies\r\nexport function useCookie(key: string, initialValue: string | null = null) {\r\n  const [storedValue, setStoredValue] = useState<string | null>(initialValue);\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n\r\n  // Initialize cookie value on client-side\r\n  useEffect(() => {\r\n    // Use a flag to ensure we only set initialized once\r\n    let isMounted = true;\r\n\r\n    const initializeCookie = () => {\r\n      console.log(`useCookie(${key}) initializing...`);\r\n      try {\r\n        const item = getCookie(key);\r\n        if (item !== null && isMounted) {\r\n          console.log(`useCookie(${key}) found value, setting state`);\r\n          setStoredValue(item);\r\n        } else if (isMounted) {\r\n          console.log(\r\n            `useCookie(${key}) no value found, using initialValue:`,\r\n            initialValue\r\n          );\r\n        }\r\n\r\n        if (isMounted) {\r\n          setIsInitialized(true);\r\n          console.log(\r\n            `useCookie(${key}) initialized with value:`,\r\n            item || initialValue\r\n          );\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error initializing cookie ${key}:`, error);\r\n        if (isMounted) {\r\n          // Even on error, we should set initialized to true to avoid getting stuck\r\n          setIsInitialized(true);\r\n        }\r\n      }\r\n    };\r\n\r\n    // Initialize immediately\r\n    initializeCookie();\r\n\r\n    // Set a backup timeout to ensure we don't get stuck in loading state\r\n    const timeoutId = setTimeout(() => {\r\n      if (!isInitialized && isMounted) {\r\n        console.warn(\r\n          `useCookie(${key}) initialization timed out, forcing initialized state`\r\n        );\r\n        setIsInitialized(true);\r\n      }\r\n    }, 1000); // 1 second timeout as a safety\r\n\r\n    return () => {\r\n      isMounted = false;\r\n      clearTimeout(timeoutId);\r\n    };\r\n  }, [key, initialValue, isInitialized]);\r\n\r\n  // Function to update cookie\r\n  const setValue = (value: string | null) => {\r\n    console.log(\r\n      `useCookie(${key}) setValue called with:`,\r\n      value ? \"value exists\" : \"null\"\r\n    );\r\n    setStoredValue(value);\r\n    if (value === null) {\r\n      deleteCookie(key);\r\n    } else {\r\n      setCookie(key, value);\r\n    }\r\n  };\r\n\r\n  return {\r\n    value: storedValue,\r\n    setValue,\r\n    isInitialized,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;;AAFA;;AAKO,SAAS,UAAU,IAAY;IACpC,IAAI,OAAO,aAAa,aAAa;QACnC,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,KAAK,uCAAuC,CAAC;QACtE,OAAO,MAAM,6BAA6B;IAC5C;IAEA,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,KAAK,cAAc,CAAC,EAAE,SAAS,MAAM;IAEpE,+BAA+B;IAC/B,MAAM,QAAQ,CAAC,EAAE,EAAE,SAAS,MAAM,EAAE;IACpC,MAAM,QAAQ,MAAM,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IACtC,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,MAAM,cAAc,MAAM,GAAG,IAAI,MAAM,KAAK,WAAW;QACvD,QAAQ,GAAG,CACT,CAAC,OAAO,EAAE,KAAK,mBAAmB,EAAE,cAAc,WAAW,QAAQ;QAEvE,OAAO;IACT;IAEA,uDAAuD;IACvD,IAAI;QACF,MAAM,oBAAoB,aAAa,OAAO,CAAC;QAC/C,IAAI,mBAAmB;YACrB,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,gDAAgD,CAAC;YAE5E,kEAAkE;YAClE,UAAU,MAAM;YAEhB,OAAO;QACT;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,IAAI,CAAC,kCAAkC;IACjD;IAEA,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,qCAAqC,CAAC;IACjE,OAAO;AACT;AAGO,SAAS,UAAU,IAAY,EAAE,KAAa,EAAE,OAAe,EAAE;IACtE,IAAI,OAAO,aAAa,aAAa;QACnC,QAAQ,GAAG,CAAC;QACZ,QAAQ,4BAA4B;IACtC;IAEA,MAAM,UAAU,IAAI;IACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK;IAE1D,kDAAkD;IAClD,wEAAwE;IACxE,4EAA4E;IAC5E,qEAAqE;IACrE,IAAI,eAAe,GAAG,KAAK,CAAC,EAAE,mBAC5B,OACA,SAAS,EAAE,QAAQ,WAAW,GAAG,6BAA6B,EAAE,OAAO,KAAK,KAAK,IAAI;IAEvF,kDAAkD;IAClD,IAAI,OAAO,QAAQ,CAAC,QAAQ,KAAK,UAAU;QACzC,gBAAgB;IAClB;IAEA,SAAS,MAAM,GAAG;IAElB,sCAAsC;IACtC,IAAI;QACF,aAAa,OAAO,CAAC,MAAM;IAC7B,EAAE,OAAO,GAAG;QACV,QAAQ,IAAI,CAAC,0CAA0C;IACzD;IAEA,QAAQ,GAAG,CACT,CAAC,YAAY,EAAE,KAAK,CAAC,EACnB,QAAQ,iBAAiB,QAC1B,aAAa,EAAE,KAAK,KAAK,CAAC;IAG7B,wCAAwC;IACxC,MAAM,aAAa,SAAS,MAAM;IAClC,QAAQ,GAAG,CAAC,8BAA8B;IAE1C,2CAA2C;IAC3C,WAAW;QACT,MAAM,cAAc,UAAU;QAC9B,QAAQ,GAAG,CACT,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC,EACrC,cAAc,WAAW;IAE7B,GAAG;AACL;AAGO,SAAS,aAAa,IAAY;IACvC,IAAI,OAAO,aAAa,aAAa;QACnC,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,KAAK,iCAAiC,CAAC;QACnE,QAAQ,4BAA4B;IACtC;IAEA,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,MAAM;IAEtC,+CAA+C;IAC/C,SAAS,MAAM,GAAG,GAAG,KAAK,2DAA2D,CAAC;IAEtF,uDAAuD;IACvD,SAAS,MAAM,GAAG,GAAG,KAAK,8CAA8C,CAAC;IACzE,SAAS,MAAM,GAAG,GAAG,KAAK,wCAAwC,CAAC;IAEnE,6CAA6C;IAC7C,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,OAAO,GAAG;QACV,QAAQ,IAAI,CAAC,6CAA6C;IAC5D;IAEA,yCAAyC;IACzC,MAAM,aAAa,SAAS,MAAM;IAClC,QAAQ,GAAG,CAAC,+BAA+B;AAC7C;AAGO,SAAS,UAAU,GAAW,EAAE,eAA8B,IAAI;;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,oDAAoD;YACpD,IAAI,YAAY;YAEhB,MAAM;wDAAmB;oBACvB,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,iBAAiB,CAAC;oBAC/C,IAAI;wBACF,MAAM,OAAO,UAAU;wBACvB,IAAI,SAAS,QAAQ,WAAW;4BAC9B,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,4BAA4B,CAAC;4BAC1D,eAAe;wBACjB,OAAO,IAAI,WAAW;4BACpB,QAAQ,GAAG,CACT,CAAC,UAAU,EAAE,IAAI,qCAAqC,CAAC,EACvD;wBAEJ;wBAEA,IAAI,WAAW;4BACb,iBAAiB;4BACjB,QAAQ,GAAG,CACT,CAAC,UAAU,EAAE,IAAI,yBAAyB,CAAC,EAC3C,QAAQ;wBAEZ;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC,EAAE;wBACnD,IAAI,WAAW;4BACb,0EAA0E;4BAC1E,iBAAiB;wBACnB;oBACF;gBACF;;YAEA,yBAAyB;YACzB;YAEA,qEAAqE;YACrE,MAAM,YAAY;iDAAW;oBAC3B,IAAI,CAAC,iBAAiB,WAAW;wBAC/B,QAAQ,IAAI,CACV,CAAC,UAAU,EAAE,IAAI,qDAAqD,CAAC;wBAEzE,iBAAiB;oBACnB;gBACF;gDAAG,OAAO,+BAA+B;YAEzC;uCAAO;oBACL,YAAY;oBACZ,aAAa;gBACf;;QACF;8BAAG;QAAC;QAAK;QAAc;KAAc;IAErC,4BAA4B;IAC5B,MAAM,WAAW,CAAC;QAChB,QAAQ,GAAG,CACT,CAAC,UAAU,EAAE,IAAI,uBAAuB,CAAC,EACzC,QAAQ,iBAAiB;QAE3B,eAAe;QACf,IAAI,UAAU,MAAM;YAClB,aAAa;QACf,OAAO;YACL,UAAU,KAAK;QACjB;IACF;IAEA,OAAO;QACL,OAAO;QACP;QACA;IACF;AACF;GA7EgB", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/api-client.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport axios, { AxiosInstance, AxiosRequestConfig, AxiosError } from \"axios\";\r\nimport { QueryClient } from \"@tanstack/react-query\";\r\nimport { getCookie, setCookie } from \"./cookies\";\r\n\r\n// Define a redirect callback type\r\ntype RedirectCallback = (path: string) => void;\r\n\r\n// Define an auth error callback type\r\ntype AuthErrorCallback = (message: string) => void;\r\n\r\n// Default redirect implementation - will be overridden by client components\r\nlet redirectCallback: RedirectCallback = (path: string) => {\r\n  console.warn(\"Redirect callback not set, attempted to redirect to:\", path);\r\n  // Use window.location as a fallback if we're in the browser\r\n  if (typeof window !== \"undefined\") {\r\n    window.location.href = path;\r\n  }\r\n};\r\n\r\n// Default auth error callback - will be overridden by client components\r\nlet authErrorCallback: AuthErrorCallback = (message: string) => {\r\n  console.warn(\"Auth error callback not set, auth error:\", message);\r\n};\r\n\r\n// Reference to the query client for cache invalidation\r\nlet queryClientRef: QueryClient | null = null;\r\n\r\n// Use the environment variable for API URL\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL;\r\n\r\nclass ApiClient {\r\n  // Method to set a custom redirect callback\r\n  public setRedirectCallback(callback: RedirectCallback) {\r\n    redirectCallback = callback;\r\n  }\r\n\r\n  // Method to set the query client reference\r\n  public setQueryClient(queryClient: QueryClient) {\r\n    queryClientRef = queryClient;\r\n  }\r\n\r\n  // Method to set a custom auth error callback\r\n  public setAuthErrorCallback(callback: AuthErrorCallback) {\r\n    authErrorCallback = callback;\r\n  }\r\n\r\n  // Method to handle authentication errors\r\n  public handleAuthError(errorMessage?: string) {\r\n    console.log(\r\n      \"Handling auth error - clearing tokens and showing auth error dialog\"\r\n    );\r\n\r\n    // Get a user-friendly error message\r\n    const message =\r\n      errorMessage || \"Your session has expired. Please log in again.\";\r\n\r\n    // Call the auth error callback with the message\r\n    authErrorCallback(message);\r\n\r\n    // We don't clear tokens or redirect here anymore\r\n    // That will be handled by the auth error context when the user confirms the dialog\r\n  }\r\n  private client: AxiosInstance;\r\n  private isRefreshing = false;\r\n  private refreshSubscribers: ((token: string) => void)[] = [];\r\n\r\n  constructor() {\r\n    this.client = axios.create({\r\n      baseURL: API_URL,\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      timeout: 60000, // 60 seconds - increased from 30 seconds to handle larger file uploads\r\n    });\r\n\r\n    this.setupInterceptors();\r\n  }\r\n\r\n  // Method to set auth token for requests\r\n  public setAuthToken(token: string | null) {\r\n    if (token) {\r\n      this.client.defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\r\n\r\n      // Also store in localStorage as a fallback\r\n      try {\r\n        localStorage.setItem(\"token\", token);\r\n      } catch (e) {\r\n        console.warn(\"Failed to store token in localStorage:\", e);\r\n      }\r\n    } else {\r\n      delete this.client.defaults.headers.common[\"Authorization\"];\r\n\r\n      // Also remove from localStorage\r\n      try {\r\n        localStorage.removeItem(\"token\");\r\n      } catch (e) {\r\n        console.warn(\"Failed to remove token from localStorage:\", e);\r\n      }\r\n    }\r\n  }\r\n\r\n  private setupInterceptors() {\r\n    // Request interceptor\r\n    this.client.interceptors.request.use(\r\n      async (config) => {\r\n        // Debug log to check if Authorization header is set\r\n        const hasAuthHeader = config.headers?.Authorization ? true : false;\r\n        console.log(\r\n          `API Request to ${config.url}: Auth header ${\r\n            hasAuthHeader ? \"present\" : \"missing\"\r\n          }`\r\n        );\r\n\r\n        // If no auth header is set but we have a token in localStorage, set it\r\n        if (!hasAuthHeader && typeof window !== \"undefined\") {\r\n          const token =\r\n            localStorage.getItem(\"token\") ||\r\n            localStorage.getItem(\"accessToken\");\r\n          if (token && config.headers) {\r\n            console.log(\r\n              `Setting missing auth header for ${config.url} from localStorage`\r\n            );\r\n            config.headers.Authorization = `Bearer ${token}`;\r\n          }\r\n        }\r\n\r\n        return config;\r\n      },\r\n      (error) => Promise.reject(error)\r\n    );\r\n\r\n    // Response interceptor\r\n    this.client.interceptors.response.use(\r\n      (response) => response,\r\n      async (error: AxiosError) => {\r\n        const originalRequest = error.config as AxiosRequestConfig & {\r\n          _retry?: boolean;\r\n        };\r\n\r\n        // If unauthorized error and not retrying\r\n        if (error.response?.status === 401 && !originalRequest._retry) {\r\n          console.log(\"401 Unauthorized error detected:\", error.config?.url);\r\n\r\n          // Check if the error message indicates an invalid or expired token\r\n          const errorData = error.response?.data as any;\r\n          const isTokenError =\r\n            errorData?.message === \"Invalid or expired token\" ||\r\n            errorData?.error === \"Unauthorized\" ||\r\n            errorData?.error === \"Error\";\r\n\r\n          // If already refreshing, queue this request\r\n          if (this.isRefreshing) {\r\n            console.log(\"Token refresh already in progress, queuing request\");\r\n            return new Promise((resolve) => {\r\n              this.refreshSubscribers.push((token: string) => {\r\n                if (originalRequest.headers) {\r\n                  originalRequest.headers.Authorization = `Bearer ${token}`;\r\n                }\r\n                resolve(this.client(originalRequest));\r\n              });\r\n            });\r\n          }\r\n\r\n          // Mark as retrying and refreshing\r\n          originalRequest._retry = true;\r\n          this.isRefreshing = true;\r\n\r\n          // Try to get refresh token from cookies or localStorage\r\n          const refreshToken =\r\n            typeof document !== \"undefined\"\r\n              ? getCookie(\"refreshToken\") ||\r\n                localStorage.getItem(\"refreshToken\")\r\n              : null;\r\n\r\n          if (refreshToken) {\r\n            console.log(\r\n              \"Refresh token found, attempting to refresh access token\"\r\n            );\r\n\r\n            try {\r\n              // Call the refresh token endpoint\r\n              const response = await this.client.post(\"/auth/refresh-token\", {\r\n                refreshToken,\r\n              });\r\n\r\n              const { accessToken, refreshToken: newRefreshToken } =\r\n                response.data;\r\n\r\n              if (accessToken) {\r\n                console.log(\"Token refresh successful\");\r\n\r\n                // Update tokens in cookies and localStorage\r\n                if (typeof document !== \"undefined\") {\r\n                  setCookie(\"accessToken\", accessToken);\r\n                  if (newRefreshToken) {\r\n                    setCookie(\"refreshToken\", newRefreshToken);\r\n                  }\r\n                }\r\n\r\n                // Update auth header\r\n                this.setAuthToken(accessToken);\r\n\r\n                // Execute all queued requests with new token\r\n                this.refreshSubscribers.forEach((callback) =>\r\n                  callback(accessToken)\r\n                );\r\n                this.refreshSubscribers = [];\r\n\r\n                // Reset refreshing flag\r\n                this.isRefreshing = false;\r\n\r\n                // Retry the original request\r\n                if (originalRequest.headers) {\r\n                  originalRequest.headers.Authorization = `Bearer ${accessToken}`;\r\n                }\r\n\r\n                return this.client(originalRequest);\r\n              }\r\n            } catch (refreshError) {\r\n              console.error(\"Token refresh failed:\", refreshError);\r\n            }\r\n          }\r\n\r\n          // If we get here, token refresh failed or no refresh token available\r\n          this.isRefreshing = false;\r\n          this.refreshSubscribers = [];\r\n\r\n          // If it's a token error or refresh failed, handle auth error\r\n          console.log(\r\n            \"Token refresh failed or not possible, handling auth error\"\r\n          );\r\n          const errorMessage =\r\n            errorData?.message ||\r\n            \"Your session has expired. Please log in again.\";\r\n          this.handleAuthError(errorMessage);\r\n          return Promise.reject(error);\r\n        }\r\n\r\n        // Handle other errors\r\n        return Promise.reject(this.handleError(error));\r\n      }\r\n    );\r\n  }\r\n\r\n  private handleError(error: AxiosError) {\r\n    // Customize error handling\r\n    if (error.response) {\r\n      // Server responded with error status\r\n      const data = error.response.data as any;\r\n\r\n      if (data.message) {\r\n        error.message = data.message;\r\n      }\r\n\r\n      // Add custom error properties\r\n      (error as any).isApiError = true;\r\n      (error as any).statusCode = error.response.status;\r\n    } else if (error.request) {\r\n      // Request made but no response received\r\n      error.message = \"Network error. Please check your connection.\";\r\n      (error as any).isNetworkError = true;\r\n    }\r\n\r\n    return error;\r\n  }\r\n\r\n  // Public methods\r\n  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\r\n    try {\r\n      console.log(`Making GET request to ${url}`);\r\n      const response = await this.client.get<T>(url, config);\r\n      return response.data;\r\n    } catch (error: any) {\r\n      // Suppress specific tenant settings errors to avoid console noise\r\n      if (\r\n        url.includes(\"/tenants/\") &&\r\n        url.includes(\"/settings\") &&\r\n        error.response?.status === 404\r\n      ) {\r\n        console.log(`Resource not found for ${url} - suppressing error`);\r\n      } else {\r\n        console.error(`Error in GET request to ${url}:`, error.message);\r\n      }\r\n\r\n      // Check if it's a connection error\r\n      if (\r\n        error.code === \"ECONNREFUSED\" ||\r\n        error.code === \"ECONNABORTED\" ||\r\n        error.message.includes(\"Network Error\")\r\n      ) {\r\n        console.warn(\"Connection error detected, trying direct backend API\");\r\n\r\n        // Try direct backend API as fallback\r\n        try {\r\n          // Get the API URL from environment variable\r\n          const baseUrl = process.env.NEXT_PUBLIC_API_URL;\r\n\r\n          // Get token from localStorage\r\n          const token =\r\n            typeof window !== \"undefined\"\r\n              ? localStorage.getItem(\"token\") ||\r\n                localStorage.getItem(\"accessToken\")\r\n              : null;\r\n\r\n          if (!token) {\r\n            throw new Error(\"No authentication token available\");\r\n          }\r\n\r\n          // Make direct request to backend\r\n          const directResponse = await fetch(`${baseUrl}${url}`, {\r\n            headers: {\r\n              Authorization: `Bearer ${token}`,\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n            ...config,\r\n          });\r\n\r\n          if (!directResponse.ok) {\r\n            const errorData = await directResponse.json();\r\n            throw new Error(\r\n              errorData.message || `Backend API error: ${directResponse.status}`\r\n            );\r\n          }\r\n\r\n          return await directResponse.json();\r\n        } catch (fallbackError: any) {\r\n          console.error(\"Fallback request also failed:\", fallbackError.message);\r\n          throw fallbackError;\r\n        }\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async post<T>(\r\n    url: string,\r\n    data?: any,\r\n    config?: AxiosRequestConfig\r\n  ): Promise<T> {\r\n    // Special handling for FormData (file uploads)\r\n    if (data instanceof FormData) {\r\n      console.log(`Posting FormData to ${url}`);\r\n\r\n      // For FormData, explicitly set the Content-Type header to multipart/form-data\r\n      // but let Axios handle the boundary\r\n      const formDataConfig = {\r\n        ...config,\r\n        headers: {\r\n          ...config?.headers,\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n        // Increase timeout for file uploads\r\n        timeout: config?.timeout || 180000, // 3 minutes default for file uploads\r\n        // Add onUploadProgress handler if not provided\r\n        onUploadProgress:\r\n          config?.onUploadProgress ||\r\n          ((progressEvent) => {\r\n            const percentCompleted = Math.round(\r\n              (progressEvent.loaded * 100) / (progressEvent.total || 1)\r\n            );\r\n            console.log(`Upload progress: ${percentCompleted}%`);\r\n          }),\r\n      };\r\n\r\n      try {\r\n        // Log the FormData contents for debugging\r\n        console.log(\"FormData contents:\");\r\n        for (const pair of data.entries()) {\r\n          if (pair[1] instanceof File) {\r\n            const file = pair[1] as File;\r\n            console.log(\r\n              `${pair[0]}: File(${file.name}, ${file.size} bytes, ${file.type})`\r\n            );\r\n          } else {\r\n            console.log(`${pair[0]}: ${pair[1]}`);\r\n          }\r\n        }\r\n\r\n        console.log(\r\n          \"Sending file upload request with config:\",\r\n          JSON.stringify({\r\n            url,\r\n            method: \"POST\",\r\n            timeout: formDataConfig.timeout,\r\n            headers: { ...formDataConfig.headers },\r\n          })\r\n        );\r\n\r\n        // Use Axios for file uploads with proper CORS settings\r\n        console.log(\"Using Axios for file upload with FormData\");\r\n\r\n        // Create a special config for file uploads\r\n        const uploadConfig = {\r\n          ...formDataConfig,\r\n          // Don't set Content-Type header, let Axios set it with the boundary\r\n          headers: {\r\n            ...formDataConfig.headers,\r\n            \"Content-Type\": undefined, // This lets the browser set the correct Content-Type with boundary\r\n          },\r\n          // Don't use withCredentials for cross-origin requests if you're using '*' in CORS\r\n          withCredentials: false,\r\n        };\r\n\r\n        console.log(\r\n          \"Upload config:\",\r\n          JSON.stringify({\r\n            url,\r\n            method: \"POST\",\r\n            timeout: uploadConfig.timeout,\r\n            withCredentials: uploadConfig.withCredentials,\r\n            headers: { ...uploadConfig.headers },\r\n          })\r\n        );\r\n\r\n        const response = await this.client.post<T>(url, data, uploadConfig);\r\n        return response.data;\r\n      } catch (error: any) {\r\n        console.error(`Error posting FormData to ${url}:`, error.message);\r\n\r\n        // Add more detailed error logging\r\n        if (error.code === \"ECONNABORTED\") {\r\n          console.error(\r\n            \"Request timed out. Consider increasing the timeout value.\"\r\n          );\r\n        } else if (error.response) {\r\n          console.error(\r\n            \"Server responded with error:\",\r\n            error.response.status,\r\n            error.response.data\r\n          );\r\n        } else if (error.request) {\r\n          console.error(\r\n            \"No response received from server. Network issue or CORS problem.\"\r\n          );\r\n        }\r\n\r\n        throw error;\r\n      }\r\n    } else {\r\n      // Regular post request\r\n      try {\r\n        const response = await this.client.post<T>(url, data, config);\r\n        return response.data;\r\n      } catch (error: any) {\r\n        // Check for stock limit exceeded error\r\n        if (\r\n          error.message &&\r\n          error.message.includes(\r\n            \"Assignment would exceed DSA user's stock limit\"\r\n          )\r\n        ) {\r\n          // Extract the limit information from the error message\r\n          const match = error.message.match(\r\n            /stock limit of (\\d+) items\\. Current total: (\\d+), Requested: (\\d+)/\r\n          );\r\n          if (match) {\r\n            const [, limit, current, requested] = match;\r\n            throw new Error(\r\n              `Stock limit exceeded: You cannot assign ${requested} more items. The DSA already has ${current} items assigned out of a limit of ${limit}.`\r\n            );\r\n          } else {\r\n            throw new Error(\r\n              \"Cannot assign more stock: The DSA user has reached their stock limit.\"\r\n            );\r\n          }\r\n        }\r\n\r\n        // Check for transaction errors\r\n        if (\r\n          error.message &&\r\n          error.message.includes(\"Transaction cannot be rolled back\")\r\n        ) {\r\n          console.error(\r\n            \"Database transaction error detected. The operation could not be completed.\"\r\n          );\r\n          throw new Error(\r\n            \"The operation could not be completed due to a database error. Please try again later.\"\r\n          );\r\n        }\r\n\r\n        // Add more detailed error logging for other types of errors\r\n        if (error.code === \"ECONNABORTED\") {\r\n          console.error(\r\n            \"Request timed out. Consider increasing the timeout value.\"\r\n          );\r\n        } else if (error.response) {\r\n          console.error(\r\n            \"Server responded with error:\",\r\n            error.response.status,\r\n            error.response.data\r\n          );\r\n        } else if (error.request) {\r\n          console.error(\r\n            \"No response received from server. Network issue or CORS problem.\"\r\n          );\r\n        } else {\r\n          console.error(`Error posting to ${url}:`, error.message);\r\n        }\r\n\r\n        throw error;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Helper method to get auth headers\r\n  private getAuthHeaders() {\r\n    const token = localStorage.getItem(\"token\");\r\n    return token ? { Authorization: `Bearer ${token}` } : {};\r\n  }\r\n\r\n  public async put<T>(\r\n    url: string,\r\n    data?: any,\r\n    config?: AxiosRequestConfig\r\n  ): Promise<T> {\r\n    try {\r\n      const response = await this.client.put<T>(url, data, config);\r\n      return response.data;\r\n    } catch (error: any) {\r\n      // Check for stock limit exceeded error\r\n      if (\r\n        error.message &&\r\n        error.message.includes(\"Assignment would exceed DSA user's stock limit\")\r\n      ) {\r\n        // Extract the limit information from the error message\r\n        const match = error.message.match(\r\n          /stock limit of (\\d+) items\\. Current total: (\\d+), Requested: (\\d+)/\r\n        );\r\n        if (match) {\r\n          const [, limit, current, requested] = match;\r\n          throw new Error(\r\n            `Stock limit exceeded: You cannot assign ${requested} more items. The DSA already has ${current} items assigned out of a limit of ${limit}.`\r\n          );\r\n        } else {\r\n          throw new Error(\r\n            \"Cannot assign more stock: The DSA user has reached their stock limit.\"\r\n          );\r\n        }\r\n      }\r\n\r\n      // Check for transaction errors\r\n      if (\r\n        error.message &&\r\n        error.message.includes(\"Transaction cannot be rolled back\")\r\n      ) {\r\n        console.error(\r\n          \"Database transaction error detected. The operation could not be completed.\"\r\n        );\r\n        throw new Error(\r\n          \"The operation could not be completed due to a database error. Please try again later.\"\r\n        );\r\n      }\r\n\r\n      console.error(`Error putting to ${url}:`, error.message);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\r\n    try {\r\n      const response = await this.client.delete<T>(url, config);\r\n      return response.data;\r\n    } catch (error: any) {\r\n      // Check for stock limit exceeded error\r\n      if (\r\n        error.message &&\r\n        error.message.includes(\"Assignment would exceed DSA user's stock limit\")\r\n      ) {\r\n        // Extract the limit information from the error message\r\n        const match = error.message.match(\r\n          /stock limit of (\\d+) items\\. Current total: (\\d+), Requested: (\\d+)/\r\n        );\r\n        if (match) {\r\n          const [, limit, current, requested] = match;\r\n          throw new Error(\r\n            `Stock limit exceeded: You cannot assign ${requested} more items. The DSA already has ${current} items assigned out of a limit of ${limit}.`\r\n          );\r\n        } else {\r\n          throw new Error(\r\n            \"Cannot assign more stock: The DSA user has reached their stock limit.\"\r\n          );\r\n        }\r\n      }\r\n\r\n      // Check for transaction errors\r\n      if (\r\n        error.message &&\r\n        error.message.includes(\"Transaction cannot be rolled back\")\r\n      ) {\r\n        console.error(\r\n          \"Database transaction error detected. The operation could not be completed.\"\r\n        );\r\n        throw new Error(\r\n          \"The operation could not be completed due to a database error. Please try again later.\"\r\n        );\r\n      }\r\n\r\n      console.error(`Error deleting from ${url}:`, error.message);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  public async patch<T>(\r\n    url: string,\r\n    data?: any,\r\n    config?: AxiosRequestConfig\r\n  ): Promise<T> {\r\n    try {\r\n      const response = await this.client.patch<T>(url, data, config);\r\n      return response.data;\r\n    } catch (error: any) {\r\n      // Check for stock limit exceeded error\r\n      if (\r\n        error.message &&\r\n        error.message.includes(\"Assignment would exceed DSA user's stock limit\")\r\n      ) {\r\n        // Extract the limit information from the error message\r\n        const match = error.message.match(\r\n          /stock limit of (\\d+) items\\. Current total: (\\d+), Requested: (\\d+)/\r\n        );\r\n        if (match) {\r\n          const [, limit, current, requested] = match;\r\n          throw new Error(\r\n            `Stock limit exceeded: You cannot assign ${requested} more items. The DSA already has ${current} items assigned out of a limit of ${limit}.`\r\n          );\r\n        } else {\r\n          throw new Error(\r\n            \"Cannot assign more stock: The DSA user has reached their stock limit.\"\r\n          );\r\n        }\r\n      }\r\n\r\n      // Check for transaction errors\r\n      if (\r\n        error.message &&\r\n        error.message.includes(\"Transaction cannot be rolled back\")\r\n      ) {\r\n        console.error(\r\n          \"Database transaction error detected. The operation could not be completed.\"\r\n        );\r\n        throw new Error(\r\n          \"The operation could not be completed due to a database error. Please try again later.\"\r\n        );\r\n      }\r\n\r\n      console.error(`Error patching to ${url}:`, error.message);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n// Create singleton instance\r\nconst apiClient = new ApiClient();\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AA8BgB;AA5BhB;AAEA;AAJA;;;AAYA,4EAA4E;AAC5E,IAAI,mBAAqC,CAAC;IACxC,QAAQ,IAAI,CAAC,wDAAwD;IACrE,4DAA4D;IAC5D,wCAAmC;QACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;AACF;AAEA,wEAAwE;AACxE,IAAI,oBAAuC,CAAC;IAC1C,QAAQ,IAAI,CAAC,4CAA4C;AAC3D;AAEA,uDAAuD;AACvD,IAAI,iBAAqC;AAEzC,2CAA2C;AAC3C,MAAM;AAEN,MAAM;IACJ,2CAA2C;IACpC,oBAAoB,QAA0B,EAAE;QACrD,mBAAmB;IACrB;IAEA,2CAA2C;IACpC,eAAe,WAAwB,EAAE;QAC9C,iBAAiB;IACnB;IAEA,6CAA6C;IACtC,qBAAqB,QAA2B,EAAE;QACvD,oBAAoB;IACtB;IAEA,yCAAyC;IAClC,gBAAgB,YAAqB,EAAE;QAC5C,QAAQ,GAAG,CACT;QAGF,oCAAoC;QACpC,MAAM,UACJ,gBAAgB;QAElB,gDAAgD;QAChD,kBAAkB;IAElB,iDAAiD;IACjD,mFAAmF;IACrF;IACQ,OAAsB;IACtB,eAAe,MAAM;IACrB,qBAAkD,EAAE,CAAC;IAE7D,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACzB,SAAS;YACT,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEA,wCAAwC;IACjC,aAAa,KAAoB,EAAE;QACxC,IAAI,OAAO;YACT,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;YAExE,2CAA2C;YAC3C,IAAI;gBACF,aAAa,OAAO,CAAC,SAAS;YAChC,EAAE,OAAO,GAAG;gBACV,QAAQ,IAAI,CAAC,0CAA0C;YACzD;QACF,OAAO;YACL,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB;YAE3D,gCAAgC;YAChC,IAAI;gBACF,aAAa,UAAU,CAAC;YAC1B,EAAE,OAAO,GAAG;gBACV,QAAQ,IAAI,CAAC,6CAA6C;YAC5D;QACF;IACF;IAEQ,oBAAoB;QAC1B,sBAAsB;QACtB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,OAAO;YACL,oDAAoD;YACpD,MAAM,gBAAgB,OAAO,OAAO,EAAE,gBAAgB,OAAO;YAC7D,QAAQ,GAAG,CACT,CAAC,eAAe,EAAE,OAAO,GAAG,CAAC,cAAc,EACzC,gBAAgB,YAAY,WAC5B;YAGJ,uEAAuE;YACvE,IAAI,CAAC,iBAAiB,aAAkB,aAAa;gBACnD,MAAM,QACJ,aAAa,OAAO,CAAC,YACrB,aAAa,OAAO,CAAC;gBACvB,IAAI,SAAS,OAAO,OAAO,EAAE;oBAC3B,QAAQ,GAAG,CACT,CAAC,gCAAgC,EAAE,OAAO,GAAG,CAAC,kBAAkB,CAAC;oBAEnE,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;gBAClD;YACF;YAEA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;QAG5B,uBAAuB;QACvB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC,WAAa,UACd,OAAO;YACL,MAAM,kBAAkB,MAAM,MAAM;YAIpC,yCAAyC;YACzC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;gBAC7D,QAAQ,GAAG,CAAC,oCAAoC,MAAM,MAAM,EAAE;gBAE9D,mEAAmE;gBACnE,MAAM,YAAY,MAAM,QAAQ,EAAE;gBAClC,MAAM,eACJ,WAAW,YAAY,8BACvB,WAAW,UAAU,kBACrB,WAAW,UAAU;gBAEvB,4CAA4C;gBAC5C,IAAI,IAAI,CAAC,YAAY,EAAE;oBACrB,QAAQ,GAAG,CAAC;oBACZ,OAAO,IAAI,QAAQ,CAAC;wBAClB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;4BAC5B,IAAI,gBAAgB,OAAO,EAAE;gCAC3B,gBAAgB,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;4BAC3D;4BACA,QAAQ,IAAI,CAAC,MAAM,CAAC;wBACtB;oBACF;gBACF;gBAEA,kCAAkC;gBAClC,gBAAgB,MAAM,GAAG;gBACzB,IAAI,CAAC,YAAY,GAAG;gBAEpB,wDAAwD;gBACxD,MAAM,eACJ,OAAO,aAAa,cAChB,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD,EAAE,mBACV,aAAa,OAAO,CAAC,kBACrB;gBAEN,IAAI,cAAc;oBAChB,QAAQ,GAAG,CACT;oBAGF,IAAI;wBACF,kCAAkC;wBAClC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB;4BAC7D;wBACF;wBAEA,MAAM,EAAE,WAAW,EAAE,cAAc,eAAe,EAAE,GAClD,SAAS,IAAI;wBAEf,IAAI,aAAa;4BACf,QAAQ,GAAG,CAAC;4BAEZ,4CAA4C;4BAC5C,IAAI,OAAO,aAAa,aAAa;gCACnC,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD,EAAE,eAAe;gCACzB,IAAI,iBAAiB;oCACnB,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;gCAC5B;4BACF;4BAEA,qBAAqB;4BACrB,IAAI,CAAC,YAAY,CAAC;4BAElB,6CAA6C;4BAC7C,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,WAC/B,SAAS;4BAEX,IAAI,CAAC,kBAAkB,GAAG,EAAE;4BAE5B,wBAAwB;4BACxB,IAAI,CAAC,YAAY,GAAG;4BAEpB,6BAA6B;4BAC7B,IAAI,gBAAgB,OAAO,EAAE;gCAC3B,gBAAgB,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,aAAa;4BACjE;4BAEA,OAAO,IAAI,CAAC,MAAM,CAAC;wBACrB;oBACF,EAAE,OAAO,cAAc;wBACrB,QAAQ,KAAK,CAAC,yBAAyB;oBACzC;gBACF;gBAEA,qEAAqE;gBACrE,IAAI,CAAC,YAAY,GAAG;gBACpB,IAAI,CAAC,kBAAkB,GAAG,EAAE;gBAE5B,6DAA6D;gBAC7D,QAAQ,GAAG,CACT;gBAEF,MAAM,eACJ,WAAW,WACX;gBACF,IAAI,CAAC,eAAe,CAAC;gBACrB,OAAO,QAAQ,MAAM,CAAC;YACxB;YAEA,sBAAsB;YACtB,OAAO,QAAQ,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;QACzC;IAEJ;IAEQ,YAAY,KAAiB,EAAE;QACrC,2BAA2B;QAC3B,IAAI,MAAM,QAAQ,EAAE;YAClB,qCAAqC;YACrC,MAAM,OAAO,MAAM,QAAQ,CAAC,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,OAAO,GAAG,KAAK,OAAO;YAC9B;YAEA,8BAA8B;YAC7B,MAAc,UAAU,GAAG;YAC3B,MAAc,UAAU,GAAG,MAAM,QAAQ,CAAC,MAAM;QACnD,OAAO,IAAI,MAAM,OAAO,EAAE;YACxB,wCAAwC;YACxC,MAAM,OAAO,GAAG;YACf,MAAc,cAAc,GAAG;QAClC;QAEA,OAAO;IACT;IAEA,iBAAiB;IACjB,MAAa,IAAO,GAAW,EAAE,MAA2B,EAAc;QACxE,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,KAAK;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAI,KAAK;YAC/C,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,kEAAkE;YAClE,IACE,IAAI,QAAQ,CAAC,gBACb,IAAI,QAAQ,CAAC,gBACb,MAAM,QAAQ,EAAE,WAAW,KAC3B;gBACA,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,IAAI,oBAAoB,CAAC;YACjE,OAAO;gBACL,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,OAAO;YAChE;YAEA,mCAAmC;YACnC,IACE,MAAM,IAAI,KAAK,kBACf,MAAM,IAAI,KAAK,kBACf,MAAM,OAAO,CAAC,QAAQ,CAAC,kBACvB;gBACA,QAAQ,IAAI,CAAC;gBAEb,qCAAqC;gBACrC,IAAI;oBACF,4CAA4C;oBAC5C,MAAM;oBAEN,8BAA8B;oBAC9B,MAAM,QACJ,uCACI,aAAa,OAAO,CAAC,YACrB,aAAa,OAAO,CAAC;oBAG3B,IAAI,CAAC,OAAO;wBACV,MAAM,IAAI,MAAM;oBAClB;oBAEA,iCAAiC;oBACjC,MAAM,iBAAiB,MAAM,MAAM,GAAG,UAAU,KAAK,EAAE;wBACrD,SAAS;4BACP,eAAe,CAAC,OAAO,EAAE,OAAO;4BAChC,gBAAgB;wBAClB;wBACA,GAAG,MAAM;oBACX;oBAEA,IAAI,CAAC,eAAe,EAAE,EAAE;wBACtB,MAAM,YAAY,MAAM,eAAe,IAAI;wBAC3C,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,mBAAmB,EAAE,eAAe,MAAM,EAAE;oBAEtE;oBAEA,OAAO,MAAM,eAAe,IAAI;gBAClC,EAAE,OAAO,eAAoB;oBAC3B,QAAQ,KAAK,CAAC,iCAAiC,cAAc,OAAO;oBACpE,MAAM;gBACR;YACF;YAEA,MAAM;QACR;IACF;IAEA,MAAa,KACX,GAAW,EACX,IAAU,EACV,MAA2B,EACf;QACZ,+CAA+C;QAC/C,IAAI,gBAAgB,UAAU;YAC5B,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,KAAK;YAExC,8EAA8E;YAC9E,oCAAoC;YACpC,MAAM,iBAAiB;gBACrB,GAAG,MAAM;gBACT,SAAS;oBACP,GAAG,QAAQ,OAAO;oBAClB,gBAAgB;gBAClB;gBACA,oCAAoC;gBACpC,SAAS,QAAQ,WAAW;gBAC5B,+CAA+C;gBAC/C,kBACE,QAAQ,oBACR,CAAC,CAAC;oBACA,MAAM,mBAAmB,KAAK,KAAK,CACjC,AAAC,cAAc,MAAM,GAAG,MAAO,CAAC,cAAc,KAAK,IAAI,CAAC;oBAE1D,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;gBACrD,CAAC;YACL;YAEA,IAAI;gBACF,0CAA0C;gBAC1C,QAAQ,GAAG,CAAC;gBACZ,KAAK,MAAM,QAAQ,KAAK,OAAO,GAAI;oBACjC,IAAI,IAAI,CAAC,EAAE,YAAY,MAAM;wBAC3B,MAAM,OAAO,IAAI,CAAC,EAAE;wBACpB,QAAQ,GAAG,CACT,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;oBAEtE,OAAO;wBACL,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;oBACtC;gBACF;gBAEA,QAAQ,GAAG,CACT,4CACA,KAAK,SAAS,CAAC;oBACb;oBACA,QAAQ;oBACR,SAAS,eAAe,OAAO;oBAC/B,SAAS;wBAAE,GAAG,eAAe,OAAO;oBAAC;gBACvC;gBAGF,uDAAuD;gBACvD,QAAQ,GAAG,CAAC;gBAEZ,2CAA2C;gBAC3C,MAAM,eAAe;oBACnB,GAAG,cAAc;oBACjB,oEAAoE;oBACpE,SAAS;wBACP,GAAG,eAAe,OAAO;wBACzB,gBAAgB;oBAClB;oBACA,kFAAkF;oBAClF,iBAAiB;gBACnB;gBAEA,QAAQ,GAAG,CACT,kBACA,KAAK,SAAS,CAAC;oBACb;oBACA,QAAQ;oBACR,SAAS,aAAa,OAAO;oBAC7B,iBAAiB,aAAa,eAAe;oBAC7C,SAAS;wBAAE,GAAG,aAAa,OAAO;oBAAC;gBACrC;gBAGF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAI,KAAK,MAAM;gBACtD,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,OAAO;gBAEhE,kCAAkC;gBAClC,IAAI,MAAM,IAAI,KAAK,gBAAgB;oBACjC,QAAQ,KAAK,CACX;gBAEJ,OAAO,IAAI,MAAM,QAAQ,EAAE;oBACzB,QAAQ,KAAK,CACX,gCACA,MAAM,QAAQ,CAAC,MAAM,EACrB,MAAM,QAAQ,CAAC,IAAI;gBAEvB,OAAO,IAAI,MAAM,OAAO,EAAE;oBACxB,QAAQ,KAAK,CACX;gBAEJ;gBAEA,MAAM;YACR;QACF,OAAO;YACL,uBAAuB;YACvB,IAAI;gBACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAI,KAAK,MAAM;gBACtD,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAY;gBACnB,uCAAuC;gBACvC,IACE,MAAM,OAAO,IACb,MAAM,OAAO,CAAC,QAAQ,CACpB,mDAEF;oBACA,uDAAuD;oBACvD,MAAM,QAAQ,MAAM,OAAO,CAAC,KAAK,CAC/B;oBAEF,IAAI,OAAO;wBACT,MAAM,GAAG,OAAO,SAAS,UAAU,GAAG;wBACtC,MAAM,IAAI,MACR,CAAC,wCAAwC,EAAE,UAAU,iCAAiC,EAAE,QAAQ,kCAAkC,EAAE,MAAM,CAAC,CAAC;oBAEhJ,OAAO;wBACL,MAAM,IAAI,MACR;oBAEJ;gBACF;gBAEA,+BAA+B;gBAC/B,IACE,MAAM,OAAO,IACb,MAAM,OAAO,CAAC,QAAQ,CAAC,sCACvB;oBACA,QAAQ,KAAK,CACX;oBAEF,MAAM,IAAI,MACR;gBAEJ;gBAEA,4DAA4D;gBAC5D,IAAI,MAAM,IAAI,KAAK,gBAAgB;oBACjC,QAAQ,KAAK,CACX;gBAEJ,OAAO,IAAI,MAAM,QAAQ,EAAE;oBACzB,QAAQ,KAAK,CACX,gCACA,MAAM,QAAQ,CAAC,MAAM,EACrB,MAAM,QAAQ,CAAC,IAAI;gBAEvB,OAAO,IAAI,MAAM,OAAO,EAAE;oBACxB,QAAQ,KAAK,CACX;gBAEJ,OAAO;oBACL,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,OAAO;gBACzD;gBAEA,MAAM;YACR;QACF;IACF;IAEA,oCAAoC;IAC5B,iBAAiB;QACvB,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,OAAO,QAAQ;YAAE,eAAe,CAAC,OAAO,EAAE,OAAO;QAAC,IAAI,CAAC;IACzD;IAEA,MAAa,IACX,GAAW,EACX,IAAU,EACV,MAA2B,EACf;QACZ,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAI,KAAK,MAAM;YACrD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,uCAAuC;YACvC,IACE,MAAM,OAAO,IACb,MAAM,OAAO,CAAC,QAAQ,CAAC,mDACvB;gBACA,uDAAuD;gBACvD,MAAM,QAAQ,MAAM,OAAO,CAAC,KAAK,CAC/B;gBAEF,IAAI,OAAO;oBACT,MAAM,GAAG,OAAO,SAAS,UAAU,GAAG;oBACtC,MAAM,IAAI,MACR,CAAC,wCAAwC,EAAE,UAAU,iCAAiC,EAAE,QAAQ,kCAAkC,EAAE,MAAM,CAAC,CAAC;gBAEhJ,OAAO;oBACL,MAAM,IAAI,MACR;gBAEJ;YACF;YAEA,+BAA+B;YAC/B,IACE,MAAM,OAAO,IACb,MAAM,OAAO,CAAC,QAAQ,CAAC,sCACvB;gBACA,QAAQ,KAAK,CACX;gBAEF,MAAM,IAAI,MACR;YAEJ;YAEA,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,OAAO;YACvD,MAAM;QACR;IACF;IAEA,MAAa,OAAU,GAAW,EAAE,MAA2B,EAAc;QAC3E,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAI,KAAK;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,uCAAuC;YACvC,IACE,MAAM,OAAO,IACb,MAAM,OAAO,CAAC,QAAQ,CAAC,mDACvB;gBACA,uDAAuD;gBACvD,MAAM,QAAQ,MAAM,OAAO,CAAC,KAAK,CAC/B;gBAEF,IAAI,OAAO;oBACT,MAAM,GAAG,OAAO,SAAS,UAAU,GAAG;oBACtC,MAAM,IAAI,MACR,CAAC,wCAAwC,EAAE,UAAU,iCAAiC,EAAE,QAAQ,kCAAkC,EAAE,MAAM,CAAC,CAAC;gBAEhJ,OAAO;oBACL,MAAM,IAAI,MACR;gBAEJ;YACF;YAEA,+BAA+B;YAC/B,IACE,MAAM,OAAO,IACb,MAAM,OAAO,CAAC,QAAQ,CAAC,sCACvB;gBACA,QAAQ,KAAK,CACX;gBAEF,MAAM,IAAI,MACR;YAEJ;YAEA,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,OAAO;YAC1D,MAAM;QACR;IACF;IAEA,MAAa,MACX,GAAW,EACX,IAAU,EACV,MAA2B,EACf;QACZ,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAI,KAAK,MAAM;YACvD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,uCAAuC;YACvC,IACE,MAAM,OAAO,IACb,MAAM,OAAO,CAAC,QAAQ,CAAC,mDACvB;gBACA,uDAAuD;gBACvD,MAAM,QAAQ,MAAM,OAAO,CAAC,KAAK,CAC/B;gBAEF,IAAI,OAAO;oBACT,MAAM,GAAG,OAAO,SAAS,UAAU,GAAG;oBACtC,MAAM,IAAI,MACR,CAAC,wCAAwC,EAAE,UAAU,iCAAiC,EAAE,QAAQ,kCAAkC,EAAE,MAAM,CAAC,CAAC;gBAEhJ,OAAO;oBACL,MAAM,IAAI,MACR;gBAEJ;YACF;YAEA,+BAA+B;YAC/B,IACE,MAAM,OAAO,IACb,MAAM,OAAO,CAAC,QAAQ,CAAC,sCACvB;gBACA,QAAQ,KAAK,CACX;gBAEF,MAAM,IAAI,MACR;YAEJ;YAEA,QAAQ,KAAK,CAAC,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,OAAO;YACxD,MAAM;QACR;IACF;AACF;AAEA,4BAA4B;AAC5B,MAAM,YAAY,IAAI;uCACP", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/api/auth-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport {\r\n  LoginRequest,\r\n  LoginResponse,\r\n  RefreshTokenRequest,\r\n  RefreshTokenResponse,\r\n  ApiResponse,\r\n  User,\r\n} from \"@/types\";\r\nimport {\r\n  ForgotPasswordRequest,\r\n  ResetPasswordRequest,\r\n} from \"../hooks/use-forgot-password\";\r\n\r\nexport const authService = {\r\n  login: async (credentials: LoginRequest): Promise<LoginResponse> => {\r\n    console.log('Attempting login with credentials:', { email: credentials.email, password: '******' });\r\n\r\n    try {\r\n      const response = await apiClient.post<LoginResponse>(\r\n        \"/auth/login\",\r\n        credentials,\r\n        {\r\n          // Add a longer timeout specifically for login\r\n          timeout: 60000, // 60 seconds\r\n        }\r\n      );\r\n\r\n      console.log('Login successful');\r\n\r\n      // Tokens will be stored by the hook that calls this method\r\n      // We don't directly interact with localStorage here to avoid hydration issues\r\n\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  refreshToken: async (refreshToken: string): Promise<RefreshTokenResponse> => {\r\n    const request: RefreshTokenRequest = { refreshToken };\r\n    const response = await apiClient.post<RefreshTokenResponse>(\r\n      \"/auth/refresh-token\",\r\n      request\r\n    );\r\n\r\n    // Tokens will be stored by the hook that calls this method\r\n    // We don't directly interact with localStorage here to avoid hydration issues\r\n\r\n    return response;\r\n  },\r\n\r\n  logout: async (): Promise<void> => {\r\n    // Tokens will be cleared by the hook that calls this method\r\n    // We don't directly interact with localStorage here to avoid hydration issues\r\n  },\r\n\r\n  getCurrentUser: async (): Promise<User> => {\r\n    return apiClient.get<User>(\"/auth/me\");\r\n  },\r\n\r\n  isAuthenticated: (): boolean => {\r\n    // This should be called from client components only\r\n    // For server components, default to not authenticated\r\n    if (typeof window === \"undefined\") {\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      // Use document.cookie instead of localStorage\r\n      const cookies = document.cookie.split(\";\");\r\n      const tokenCookie = cookies.find((cookie) =>\r\n        cookie.trim().startsWith(\"accessToken=\")\r\n      );\r\n      return !!tokenCookie;\r\n    } catch {\r\n      return false;\r\n    }\r\n  },\r\n\r\n  forgotPassword: async (\r\n    data: ForgotPasswordRequest\r\n  ): Promise<ApiResponse<null>> => {\r\n    return apiClient.post<ApiResponse<null>>(\"/auth/forgot-password\", data);\r\n  },\r\n\r\n  resetPassword: async (\r\n    data: ResetPasswordRequest\r\n  ): Promise<ApiResponse<null>> => {\r\n    return apiClient.post<ApiResponse<null>>(\"/auth/reset-password\", data);\r\n  },\r\n\r\n  updateProfile: async (data: Record<string, any>): Promise<User> => {\r\n    return apiClient.put(\"/auth/profile\", data);\r\n  },\r\n\r\n  changePassword: async (data: {\r\n    current_password: string;\r\n    new_password: string;\r\n    confirm_password: string;\r\n  }): Promise<ApiResponse<null>> => {\r\n    return apiClient.post<ApiResponse<null>>(\"/auth/change-password\", data);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAcO,MAAM,cAAc;IACzB,OAAO,OAAO;QACZ,QAAQ,GAAG,CAAC,sCAAsC;YAAE,OAAO,YAAY,KAAK;YAAE,UAAU;QAAS;QAEjG,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CACnC,eACA,aACA;gBACE,8CAA8C;gBAC9C,SAAS;YACX;YAGF,QAAQ,GAAG,CAAC;YAEZ,2DAA2D;YAC3D,8EAA8E;YAE9E,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM;QACR;IACF;IAEA,cAAc,OAAO;QACnB,MAAM,UAA+B;YAAE;QAAa;QACpD,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CACnC,uBACA;QAGF,2DAA2D;QAC3D,8EAA8E;QAE9E,OAAO;IACT;IAEA,QAAQ;IACN,4DAA4D;IAC5D,8EAA8E;IAChF;IAEA,gBAAgB;QACd,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAO;IAC7B;IAEA,iBAAiB;QACf,oDAAoD;QACpD,sDAAsD;QACtD,uCAAmC;;QAEnC;QAEA,IAAI;YACF,8CAA8C;YAC9C,MAAM,UAAU,SAAS,MAAM,CAAC,KAAK,CAAC;YACtC,MAAM,cAAc,QAAQ,IAAI,CAAC,CAAC,SAChC,OAAO,IAAI,GAAG,UAAU,CAAC;YAE3B,OAAO,CAAC,CAAC;QACX,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,gBAAgB,OACd;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAoB,yBAAyB;IACpE;IAEA,eAAe,OACb;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAoB,wBAAwB;IACnE;IAEA,eAAe,OAAO;QACpB,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,iBAAiB;IACxC;IAEA,gBAAgB,OAAO;QAKrB,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAoB,yBAAyB;IACpE;AACF", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/hooks/use-auth-tokens.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useCallback, useEffect } from \"react\";\r\nimport { useCookie } from \"@/lib/cookies\";\r\nimport apiClient from \"@/lib/api-client\";\r\nimport { authService } from \"@/features/auth/api/auth-service\";\r\n\r\nexport function useAuthTokens() {\r\n  const {\r\n    value: accessToken,\r\n    setValue: setAccessToken,\r\n    isInitialized: isAccessTokenInitialized,\r\n  } = useCookie(\"accessToken\");\r\n  const {\r\n    value: refreshToken,\r\n    setValue: setRefreshToken,\r\n    isInitialized: isRefreshTokenInitialized,\r\n  } = useCookie(\"refreshToken\");\r\n\r\n  const isInitialized = isAccessTokenInitialized && isRefreshTokenInitialized;\r\n\r\n  const setTokens = useCallback(\r\n    (access: string, refresh: string) => {\r\n      // Set in cookies\r\n      setAccessToken(access);\r\n      setRefreshToken(refresh);\r\n\r\n      // Also set in localStorage as fallback\r\n      if (typeof window !== \"undefined\") {\r\n        try {\r\n          localStorage.setItem(\"accessToken\", access);\r\n          localStorage.setItem(\"refreshToken\", refresh);\r\n          localStorage.setItem(\"token\", access); // For backward compatibility\r\n        } catch (e) {\r\n          console.warn(\"Failed to store tokens in localStorage:\", e);\r\n        }\r\n      }\r\n\r\n      // Update API client auth header\r\n      apiClient.setAuthToken(access);\r\n    },\r\n    [setAccessToken, setRefreshToken]\r\n  );\r\n\r\n  const clearTokens = useCallback(() => {\r\n    // Clear cookies\r\n    setAccessToken(null);\r\n    setRefreshToken(null);\r\n\r\n    // Also clear localStorage\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        localStorage.removeItem(\"accessToken\");\r\n        localStorage.removeItem(\"refreshToken\");\r\n        localStorage.removeItem(\"token\"); // For backward compatibility\r\n      } catch (e) {\r\n        console.warn(\"Failed to remove tokens from localStorage:\", e);\r\n      }\r\n    }\r\n\r\n    // Clear API client auth header\r\n    apiClient.setAuthToken(null);\r\n  }, [setAccessToken, setRefreshToken]);\r\n\r\n  // Function to refresh the token\r\n  const refreshAccessToken = useCallback(async () => {\r\n    // Try to get refresh token from cookie or localStorage\r\n    const currentRefreshToken = refreshToken ||\r\n      (typeof window !== \"undefined\" ? localStorage.getItem(\"refreshToken\") : null);\r\n\r\n    if (!currentRefreshToken) {\r\n      console.error(\"No refresh token available\");\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      console.log(\"Attempting to refresh token...\");\r\n      const response = await authService.refreshToken(currentRefreshToken);\r\n\r\n      if (response && response.accessToken) {\r\n        console.log(\"Token refresh successful\");\r\n\r\n        // Update tokens in cookies and localStorage\r\n        setTokens(response.accessToken, response.refreshToken || currentRefreshToken);\r\n\r\n        // Update auth header\r\n        apiClient.setAuthToken(response.accessToken);\r\n\r\n        // Also store in localStorage as fallback\r\n        if (typeof window !== \"undefined\") {\r\n          try {\r\n            localStorage.setItem(\"accessToken\", response.accessToken);\r\n            if (response.refreshToken) {\r\n              localStorage.setItem(\"refreshToken\", response.refreshToken);\r\n            }\r\n          } catch (e) {\r\n            console.warn(\"Failed to store tokens in localStorage:\", e);\r\n          }\r\n        }\r\n\r\n        return true;\r\n      }\r\n      return false;\r\n    } catch (error) {\r\n      console.error(\"Failed to refresh token:\", error);\r\n      return false;\r\n    }\r\n  }, [refreshToken, setTokens]);\r\n\r\n  // Set up a timer to refresh the token before it expires\r\n  useEffect(() => {\r\n    if (!accessToken || !refreshToken) return;\r\n\r\n    // Refresh token 5 minutes before it expires (assuming 1 hour expiry)\r\n    const refreshInterval = 55 * 60 * 1000; // 55 minutes\r\n\r\n    const timerId = setTimeout(() => {\r\n      refreshAccessToken();\r\n    }, refreshInterval);\r\n\r\n    return () => clearTimeout(timerId);\r\n  }, [accessToken, refreshToken, refreshAccessToken]);\r\n\r\n  return {\r\n    accessToken,\r\n    refreshToken,\r\n    setTokens,\r\n    clearTokens,\r\n    refreshAccessToken,\r\n    isInitialized,\r\n    isAuthenticated: !!accessToken,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,EACJ,OAAO,WAAW,EAClB,UAAU,cAAc,EACxB,eAAe,wBAAwB,EACxC,GAAG,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD,EAAE;IACd,MAAM,EACJ,OAAO,YAAY,EACnB,UAAU,eAAe,EACzB,eAAe,yBAAyB,EACzC,GAAG,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD,EAAE;IAEd,MAAM,gBAAgB,4BAA4B;IAElD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAC1B,CAAC,QAAgB;YACf,iBAAiB;YACjB,eAAe;YACf,gBAAgB;YAEhB,uCAAuC;YACvC,wCAAmC;gBACjC,IAAI;oBACF,aAAa,OAAO,CAAC,eAAe;oBACpC,aAAa,OAAO,CAAC,gBAAgB;oBACrC,aAAa,OAAO,CAAC,SAAS,SAAS,6BAA6B;gBACtE,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC,2CAA2C;gBAC1D;YACF;YAEA,gCAAgC;YAChC,8HAAA,CAAA,UAAS,CAAC,YAAY,CAAC;QACzB;+CACA;QAAC;QAAgB;KAAgB;IAGnC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC9B,gBAAgB;YAChB,eAAe;YACf,gBAAgB;YAEhB,0BAA0B;YAC1B,wCAAmC;gBACjC,IAAI;oBACF,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC,UAAU,6BAA6B;gBACjE,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC,8CAA8C;gBAC7D;YACF;YAEA,+BAA+B;YAC/B,8HAAA,CAAA,UAAS,CAAC,YAAY,CAAC;QACzB;iDAAG;QAAC;QAAgB;KAAgB;IAEpC,gCAAgC;IAChC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACrC,uDAAuD;YACvD,MAAM,sBAAsB,gBAC1B,CAAC,uCAAgC,aAAa,OAAO,CAAC,sDAAsB;YAE9E,IAAI,CAAC,qBAAqB;gBACxB,QAAQ,KAAK,CAAC;gBACd,OAAO;YACT;YAEA,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,WAAW,MAAM,oJAAA,CAAA,cAAW,CAAC,YAAY,CAAC;gBAEhD,IAAI,YAAY,SAAS,WAAW,EAAE;oBACpC,QAAQ,GAAG,CAAC;oBAEZ,4CAA4C;oBAC5C,UAAU,SAAS,WAAW,EAAE,SAAS,YAAY,IAAI;oBAEzD,qBAAqB;oBACrB,8HAAA,CAAA,UAAS,CAAC,YAAY,CAAC,SAAS,WAAW;oBAE3C,yCAAyC;oBACzC,wCAAmC;wBACjC,IAAI;4BACF,aAAa,OAAO,CAAC,eAAe,SAAS,WAAW;4BACxD,IAAI,SAAS,YAAY,EAAE;gCACzB,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;4BAC5D;wBACF,EAAE,OAAO,GAAG;4BACV,QAAQ,IAAI,CAAC,2CAA2C;wBAC1D;oBACF;oBAEA,OAAO;gBACT;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,OAAO;YACT;QACF;wDAAG;QAAC;QAAc;KAAU;IAE5B,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,eAAe,CAAC,cAAc;YAEnC,qEAAqE;YACrE,MAAM,kBAAkB,KAAK,KAAK,MAAM,aAAa;YAErD,MAAM,UAAU;mDAAW;oBACzB;gBACF;kDAAG;YAEH;2CAAO,IAAM,aAAa;;QAC5B;kCAAG;QAAC;QAAa;QAAc;KAAmB;IAElD,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;AACF;GA7HgB;;QAKV,wHAAA,CAAA,YAAS;QAKT,wHAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 1234, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/types/api.ts"], "sourcesContent": ["/**\r\n * DukaLink API TypeScript Definitions\r\n *\r\n * This file contains TypeScript type definitions for the DukaLink API.\r\n * These types are based on the Swagger documentation and provide type safety\r\n * for API requests and responses.\r\n */\r\n\r\n// ==========================================\r\n// Common Types\r\n// ==========================================\r\n\r\n/**\r\n * Paginated response wrapper\r\n */\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  pagination: {\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n    totalPages: number;\r\n  };\r\n  // For backward compatibility with older API responses\r\n  page?: number;\r\n  limit?: number;\r\n  total?: number;\r\n  next_page_url?: string;\r\n  prev_page_url?: string;\r\n}\r\n\r\n/**\r\n * Standard API response\r\n */\r\nexport interface ApiResponse<T> {\r\n  data: T;\r\n  message?: string;\r\n}\r\n\r\n/**\r\n * Error response\r\n */\r\nexport interface ApiErrorResponse {\r\n  error: string;\r\n  message: string;\r\n  details?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Query parameters for pagination\r\n */\r\nexport interface PaginationParams {\r\n  page?: number;\r\n  limit?: number;\r\n}\r\n\r\n/**\r\n * Query parameters for filtering\r\n */\r\nexport interface FilterParams {\r\n  [key: string]: string | number | boolean | undefined;\r\n}\r\n\r\n/**\r\n * Query parameters for sorting\r\n */\r\nexport interface SortParams {\r\n  sort?: string;\r\n  order?: \"asc\" | \"desc\";\r\n}\r\n\r\n/**\r\n * Combined query parameters\r\n */\r\nexport type QueryParams = PaginationParams & FilterParams & SortParams;\r\n\r\n// ==========================================\r\n// Authentication Types\r\n// ==========================================\r\n\r\n/**\r\n * Login request\r\n */\r\nexport interface LoginRequest {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\n/**\r\n * Login response\r\n */\r\nexport interface LoginResponse {\r\n  message: string;\r\n  accessToken: string;\r\n  refreshToken: string;\r\n  id: number;\r\n  name: string;\r\n  email: string | null;\r\n  phone: string | null;\r\n  role_id: number;\r\n  role_name: string;\r\n  tenant_id: number | null;\r\n  branch_id: number | null;\r\n  location_id: number | null;\r\n  language_preference: string;\r\n  role?: {\r\n    id: number;\r\n    name: string;\r\n    permissions: Permission[];\r\n  };\r\n  tenant?: Tenant | null;\r\n  branch?: Branch | null;\r\n  location?: Location | null;\r\n  session_status?: \"active\" | \"none\";\r\n  session_id?: number;\r\n  session_data?: any;\r\n}\r\n\r\n/**\r\n * Refresh token request\r\n */\r\nexport interface RefreshTokenRequest {\r\n  refreshToken: string;\r\n}\r\n\r\n/**\r\n * Refresh token response\r\n */\r\nexport interface RefreshTokenResponse {\r\n  accessToken: string;\r\n  refreshToken: string;\r\n}\r\n\r\n// ==========================================\r\n// User Types\r\n// ==========================================\r\n\r\n/**\r\n * User entity\r\n */\r\nexport interface User {\r\n  id: number;\r\n  name: string;\r\n  email: string | null;\r\n  phone: string | null;\r\n  role_id: number;\r\n  role_name: string;\r\n  tenant_id: number | null;\r\n  branch_id: number | null;\r\n  location_id: number | null;\r\n  language_preference: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at: string | null;\r\n  role?: {\r\n    id: number;\r\n    name: string;\r\n    permissions: Permission[];\r\n  };\r\n  tenant?: Tenant | null;\r\n  branch?: Branch | null;\r\n  location?: Location | null;\r\n}\r\n\r\n/**\r\n * Create user request\r\n */\r\nexport interface CreateUserRequest {\r\n  name: string;\r\n  email: string;\r\n  password: string;\r\n  role_id: number; // Required field\r\n  role_name?: string; // Kept for backward compatibility but deprecated\r\n  tenant_id?: number;\r\n  branch_id?: number;\r\n  location_id?: number;\r\n  phone?: string;\r\n  language_preference?: string;\r\n  created_by?: number; // ID of the user creating this user\r\n}\r\n\r\n/**\r\n * Update user request\r\n */\r\nexport interface UpdateUserRequest {\r\n  name?: string;\r\n  email?: string;\r\n  role_id?: number;\r\n  branch_id?: number;\r\n  location_id?: number;\r\n  phone?: string;\r\n  language_preference?: string;\r\n}\r\n\r\n/**\r\n * Update user status request\r\n */\r\nexport interface UpdateUserStatusRequest {\r\n  status: \"active\" | \"inactive\";\r\n}\r\n\r\n// ==========================================\r\n// Role and Permission Types\r\n// ==========================================\r\n\r\n/**\r\n * Role entity\r\n */\r\nexport interface Role {\r\n  id: number;\r\n  name: string;\r\n  description: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  Permissions?: Permission[];\r\n  // For backward compatibility\r\n  permissions?: Permission[];\r\n}\r\n\r\n/**\r\n * Permission entity\r\n */\r\nexport interface Permission {\r\n  id: number;\r\n  name: string;\r\n  resource: string;\r\n  action: string;\r\n  description: string | null;\r\n}\r\n\r\n/**\r\n * Create role request\r\n */\r\nexport interface CreateRoleRequest {\r\n  name: string;\r\n  description?: string;\r\n  permission_ids?: number[];\r\n}\r\n\r\n/**\r\n * Update role request\r\n */\r\nexport interface UpdateRoleRequest {\r\n  name?: string;\r\n  description?: string;\r\n  permission_ids?: number[];\r\n}\r\n\r\n// ==========================================\r\n// Tenant Types\r\n// ==========================================\r\n\r\n/**\r\n * Tenant entity\r\n */\r\nexport interface Tenant {\r\n  id: number;\r\n  name: string;\r\n  subdomain: string;\r\n  access_token: string;\r\n  business_type?: string;\r\n  industry?: string;\r\n  registration_number?: string;\r\n  tax_id?: string;\r\n  year_established?: number | null;\r\n  email?: string | null;\r\n  phone?: string | null;\r\n  website?: string | null;\r\n  address?: string;\r\n  city?: string | null;\r\n  state_province?: string | null;\r\n  postal_code?: string | null;\r\n  country?: string | null;\r\n  logo_url?: string;\r\n  primary_color?: string;\r\n  secondary_color?: string;\r\n  timezone?: string;\r\n  currency?: string;\r\n  date_format?: string;\r\n  settings?: Record<string, any> | null;\r\n  business_hours?: Record<string, any> | null;\r\n  status: \"active\" | \"inactive\" | \"suspended\" | \"pending\";\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at: string | null;\r\n  created_by?: number | null;\r\n  last_updated_by?: number | null;\r\n}\r\n\r\n/**\r\n * Create tenant request\r\n */\r\nexport interface CreateTenantRequest {\r\n  name: string;\r\n  subdomain: string;\r\n  business_type?: string;\r\n  industry?: string;\r\n  registration_number?: string;\r\n  tax_id?: string;\r\n  email?: string;\r\n  phone?: string;\r\n  address?: string;\r\n  city?: string;\r\n  country?: string;\r\n  adminName: string;\r\n  adminEmail: string;\r\n  adminPassword: string;\r\n}\r\n\r\n/**\r\n * Update tenant request\r\n */\r\nexport interface UpdateTenantRequest {\r\n  name?: string;\r\n  business_type?: string;\r\n  industry?: string;\r\n  registration_number?: string;\r\n  tax_id?: string;\r\n  email?: string;\r\n  phone?: string;\r\n  website?: string;\r\n  address?: string;\r\n  city?: string;\r\n  state_province?: string;\r\n  postal_code?: string;\r\n  country?: string;\r\n  logo_url?: string;\r\n  primary_color?: string;\r\n  secondary_color?: string;\r\n  timezone?: string;\r\n  currency?: string;\r\n  date_format?: string;\r\n  settings?: Record<string, any>;\r\n  business_hours?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Update tenant status request\r\n */\r\nexport interface UpdateTenantStatusRequest {\r\n  status: \"active\" | \"inactive\" | \"suspended\" | \"pending\";\r\n}\r\n\r\n// ==========================================\r\n// Branch Types\r\n// ==========================================\r\n\r\n/**\r\n * Branch entity\r\n */\r\nexport interface Branch {\r\n  id: number;\r\n  tenant_id: number;\r\n  name: string;\r\n  location?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at: string | null;\r\n}\r\n\r\n/**\r\n * Create branch request\r\n */\r\nexport interface CreateBranchRequest {\r\n  tenant_id: number;\r\n  name: string;\r\n  location?: string;\r\n}\r\n\r\n/**\r\n * Update branch request\r\n */\r\nexport interface UpdateBranchRequest {\r\n  name?: string;\r\n  location?: string;\r\n}\r\n\r\n// ==========================================\r\n// Location Types\r\n// ==========================================\r\n\r\n/**\r\n * Location entity\r\n */\r\nexport interface Location {\r\n  id: number;\r\n  tenant_id: number;\r\n  name: string;\r\n  type: \"HQ\" | \"SHOP\";\r\n  parent_location_id?: number | null;\r\n  region?: string | null;\r\n  settings?: Record<string, any> | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by?: number | null;\r\n  last_updated_by?: number | null;\r\n}\r\n\r\n/**\r\n * Create location request\r\n */\r\nexport interface CreateLocationRequest {\r\n  tenant_id: number;\r\n  name: string;\r\n  type: \"HQ\" | \"SHOP\";\r\n  parent_location_id?: number;\r\n  region?: string;\r\n  settings?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Update location request\r\n */\r\nexport interface UpdateLocationRequest {\r\n  name?: string;\r\n  type?: \"HQ\" | \"SHOP\";\r\n  parent_location_id?: number;\r\n  region?: string;\r\n  settings?: Record<string, any>;\r\n}\r\n\r\n// ==========================================\r\n// Product Types\r\n// ==========================================\r\n\r\n/**\r\n * Product entity\r\n */\r\nexport interface Product {\r\n  id: number;\r\n  tenant_id: number;\r\n  category_id?: number | null;\r\n  name: string;\r\n  sku?: string | null;\r\n  has_serial: boolean;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at: string | null;\r\n  created_by?: number | null;\r\n  last_updated_by?: number | null;\r\n  category?: ProductCategory;\r\n  // Fields added from stock items\r\n  price?: number;\r\n  buying_price?: number;\r\n  stock_quantity?: number;\r\n  status?: \"active\" | \"inactive\" | string;\r\n  image_url?: string;\r\n  // Fields used in product-details component\r\n  description?: string;\r\n  barcode?: string;\r\n  has_variants?: boolean;\r\n  is_taxable?: boolean;\r\n  tax_rate?: number;\r\n  weight?: number;\r\n  weight_unit?: string;\r\n  dimensions?: string;\r\n  sale_price?: number;\r\n  reorder_level?: number;\r\n  variants?: ProductVariant[];\r\n  // Include relationships that might be returned by the API\r\n  stock?: HqStockItem[];\r\n  branch_stock?: {\r\n    branch_id: number;\r\n    quantity: number;\r\n    branch?: Branch;\r\n  }[];\r\n  stock_items?: {\r\n    id: number;\r\n    location_id: number;\r\n    quantity: number;\r\n    location?: Location;\r\n  }[];\r\n}\r\n\r\n/**\r\n * Product variant entity\r\n */\r\nexport interface ProductVariant {\r\n  id: number;\r\n  name: string;\r\n  sku?: string;\r\n  price: number;\r\n  stock_quantity: number;\r\n  status: string;\r\n  attributes: Record<string, string>;\r\n}\r\n\r\n/**\r\n * Product category entity\r\n */\r\nexport interface ProductCategory {\r\n  id: number;\r\n  tenant_id: number;\r\n  name: string;\r\n  description?: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\n/**\r\n * Create product request\r\n */\r\nexport interface CreateProductRequest {\r\n  tenant_id: number;\r\n  category_id?: number;\r\n  name: string;\r\n  sku?: string;\r\n  has_serial: boolean;\r\n}\r\n\r\n/**\r\n * Update product request\r\n */\r\nexport interface UpdateProductRequest {\r\n  category_id?: number;\r\n  name?: string;\r\n  sku?: string;\r\n  has_serial?: boolean;\r\n}\r\n\r\n/**\r\n * Create product category request\r\n */\r\nexport interface CreateProductCategoryRequest {\r\n  tenant_id: number;\r\n  name: string;\r\n  description?: string;\r\n}\r\n\r\n/**\r\n * Update product category request\r\n */\r\nexport interface UpdateProductCategoryRequest {\r\n  name?: string;\r\n  description?: string;\r\n}\r\n\r\n// ==========================================\r\n// Inventory Types\r\n// ==========================================\r\n\r\n/**\r\n * HQ stock item entity\r\n */\r\nexport interface HqStockItem {\r\n  id: number;\r\n  tenant_id: number;\r\n  product_id: number;\r\n  quantity: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at: string | null;\r\n  created_by?: number | null;\r\n  last_updated_by?: number | null;\r\n  Tenant?: {\r\n    id: number;\r\n    name: string;\r\n    subdomain: string;\r\n  };\r\n  Product?: {\r\n    id: number;\r\n    name: string;\r\n    sku: string;\r\n    has_serial: boolean;\r\n  };\r\n}\r\n\r\n/**\r\n * Stock adjustment entity\r\n */\r\nexport interface StockAdjustment {\r\n  id: number;\r\n  location_id: number;\r\n  product_id: number;\r\n  adjustment_type: \"increase\" | \"decrease\";\r\n  quantity: number;\r\n  reason: string;\r\n  notes?: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by?: number | null;\r\n  last_updated_by?: number | null;\r\n}\r\n\r\n/**\r\n * Create stock adjustment request\r\n */\r\nexport interface CreateStockAdjustmentRequest {\r\n  location_id: number;\r\n  product_id: number;\r\n  adjustment_type: \"increase\" | \"decrease\";\r\n  quantity: number;\r\n  reason: string;\r\n  notes?: string;\r\n}\r\n\r\n/**\r\n * Stock transfer entity\r\n */\r\nexport interface StockTransfer {\r\n  id: number;\r\n  source_location_id: number;\r\n  destination_location_id: number;\r\n  status:\r\n    | \"pending\"\r\n    | \"approved\"\r\n    | \"rejected\"\r\n    | \"in_transit\"\r\n    | \"received\"\r\n    | \"partially_received\";\r\n  notes?: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by?: number | null;\r\n  last_updated_by?: number | null;\r\n  items: StockTransferItem[];\r\n  source_location?: Location;\r\n  destination_location?: Location;\r\n  creator?: User;\r\n  updater?: User;\r\n}\r\n\r\n/**\r\n * Stock transfer item entity\r\n */\r\nexport interface StockTransferItem {\r\n  id: number;\r\n  transfer_id: number;\r\n  product_id: number;\r\n  quantity_sent: number;\r\n  quantity_received?: number | null;\r\n  product?: Product;\r\n}\r\n\r\n/**\r\n * Create stock transfer request\r\n */\r\nexport interface CreateStockTransferRequest {\r\n  source_location_id: number;\r\n  destination_location_id: number;\r\n  notes?: string;\r\n  items: {\r\n    product_id: number;\r\n    quantity: number;\r\n  }[];\r\n}\r\n\r\n/**\r\n * Receive stock transfer request\r\n */\r\nexport interface ReceiveStockTransferRequest {\r\n  items: {\r\n    id: number;\r\n    quantity_received: number;\r\n  }[];\r\n}\r\n\r\n/**\r\n * Reject stock transfer request\r\n */\r\nexport interface RejectStockTransferRequest {\r\n  reason: string;\r\n}\r\n\r\n// ==========================================\r\n// MPESA Float Types\r\n// ==========================================\r\n\r\n/**\r\n * MPESA float reconciliation entity\r\n */\r\nexport interface MpesaFloatReconciliation {\r\n  id: number;\r\n  branch_id: number;\r\n  user_id: number;\r\n  float_assigned: number;\r\n  total_deposits: number;\r\n  total_withdrawals: number;\r\n  closing_balance: number;\r\n  discrepancies?: number;\r\n  notes?: string;\r\n  reconciled_at: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by: number;\r\n  last_updated_by: number;\r\n  User?: {\r\n    id: number;\r\n    name: string;\r\n    email: string;\r\n  };\r\n  Branch?: {\r\n    id: number;\r\n    name: string;\r\n    location: string;\r\n  };\r\n  Creator?: User;\r\n}\r\n\r\n/**\r\n * Create MPESA float reconciliation request\r\n */\r\nexport interface CreateMpesaFloatReconciliationRequest {\r\n  branch_id: number;\r\n  float_assigned: number;\r\n  total_deposits: number;\r\n  total_withdrawals: number;\r\n  closing_balance: number;\r\n  discrepancies?: number;\r\n  notes?: string;\r\n}\r\n\r\n/**\r\n * MPESA float assignment entity\r\n */\r\nexport interface MpesaFloatAssignment {\r\n  id: number;\r\n  branch_id: number;\r\n  amount: number;\r\n  notes?: string;\r\n  assigned_at: string;\r\n  created_by: number;\r\n  last_updated_by: number;\r\n}\r\n\r\n/**\r\n * Create MPESA float assignment request\r\n */\r\nexport interface CreateMpesaFloatAssignmentRequest {\r\n  branch_id: number;\r\n  amount: number;\r\n  notes?: string;\r\n}\r\n\r\n// ==========================================\r\n// DSA Types\r\n// ==========================================\r\n\r\n/**\r\n * DSA agent entity\r\n */\r\nexport interface DsaAgent {\r\n  id: number;\r\n  tenant_id: number;\r\n  name: string;\r\n  phone: string;\r\n  email?: string | null;\r\n  id_number: string;\r\n  status: \"active\" | \"inactive\";\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by?: number | null;\r\n  last_updated_by?: number | null;\r\n}\r\n\r\n/**\r\n * Create DSA agent request\r\n */\r\nexport interface CreateDsaAgentRequest {\r\n  tenant_id: number;\r\n  name: string;\r\n  phone: string;\r\n  email?: string;\r\n  id_number: string;\r\n}\r\n\r\n/**\r\n * Update DSA agent request\r\n */\r\nexport interface UpdateDsaAgentRequest {\r\n  name?: string;\r\n  phone?: string;\r\n  email?: string;\r\n  id_number?: string;\r\n  status?: \"active\" | \"inactive\";\r\n}\r\n\r\n/**\r\n * DSA stock assignment entity\r\n */\r\nexport interface DsaStockAssignment {\r\n  id: number;\r\n  dsa_agent_id: number;\r\n  branch_id: number;\r\n  product_id: number;\r\n  quantity_assigned: number;\r\n  quantity_returned: number;\r\n  assigned_at: string;\r\n  reconciled_at?: string | null;\r\n  created_by?: number | null;\r\n  last_updated_by?: number | null;\r\n  dsa_agent?: DsaAgent;\r\n  branch?: Branch;\r\n  product?: Product;\r\n  creator?: User;\r\n  updater?: User;\r\n}\r\n\r\n/**\r\n * Create DSA stock assignment request\r\n */\r\nexport interface CreateDsaStockAssignmentRequest {\r\n  dsa_agent_id: number;\r\n  branch_id: number;\r\n  product_id: number;\r\n  quantity_assigned: number;\r\n}\r\n\r\n/**\r\n * DSA sale entity\r\n */\r\nexport interface DsaSale {\r\n  id: number;\r\n  dsa_agent_id: number;\r\n  product_id: number;\r\n  quantity_sold: number;\r\n  sale_amount: number;\r\n  sale_date: string;\r\n  created_by?: number | null;\r\n  last_updated_by?: number | null;\r\n  dsa_agent?: DsaAgent;\r\n  product?: Product;\r\n  creator?: User;\r\n  updater?: User;\r\n}\r\n\r\n/**\r\n * Create DSA sale request\r\n */\r\nexport interface CreateDsaSaleRequest {\r\n  dsa_agent_id: number;\r\n  product_id: number;\r\n  quantity_sold: number;\r\n  sale_amount: number;\r\n  sale_date: string;\r\n}\r\n\r\n/**\r\n * DSA stock reconciliation entity\r\n */\r\nexport interface DsaStockReconciliation {\r\n  id: number;\r\n  dsa_agent_id: number;\r\n  total_assigned: number;\r\n  total_sold: number;\r\n  total_returned: number;\r\n  cash_received: number;\r\n  paybill_amount: number;\r\n  notes?: string | null;\r\n  reconciled_at: string;\r\n  created_by?: number | null;\r\n  last_updated_by?: number | null;\r\n  dsa_agent?: DsaAgent;\r\n  creator?: User;\r\n  updater?: User;\r\n}\r\n\r\n/**\r\n * Create DSA stock reconciliation request\r\n */\r\nexport interface CreateDsaStockReconciliationRequest {\r\n  dsa_agent_id: number;\r\n  total_assigned: number;\r\n  total_sold: number;\r\n  total_returned: number;\r\n  cash_received: number;\r\n  paybill_amount: number;\r\n  notes?: string;\r\n}\r\n\r\n// ==========================================\r\n// Sales Types\r\n// ==========================================\r\n\r\n/**\r\n * Sale entity\r\n */\r\nexport interface Sale {\r\n  id: number;\r\n  branch_id: number;\r\n  user_id: number;\r\n  customer_id?: number | null;\r\n  sale_date: string;\r\n  total_amount: number;\r\n  discount_amount?: number;\r\n  tax_amount?: number;\r\n  payment_status: \"paid\" | \"partial\" | \"unpaid\";\r\n  notes?: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by?: number | null;\r\n  last_updated_by?: number | null;\r\n  items: SaleItem[];\r\n  payments: SalePayment[];\r\n  branch?: Branch;\r\n  user?: User;\r\n  customer?: Customer;\r\n  creator?: User;\r\n  updater?: User;\r\n}\r\n\r\n/**\r\n * Sale item entity\r\n */\r\nexport interface SaleItem {\r\n  id: number;\r\n  sale_id: number;\r\n  product_id: number;\r\n  quantity: number;\r\n  unit_price: number;\r\n  discount_amount?: number;\r\n  tax_amount?: number;\r\n  total_amount: number;\r\n  product?: Product;\r\n}\r\n\r\n/**\r\n * Sale payment entity\r\n */\r\nexport interface SalePayment {\r\n  id: number;\r\n  sale_id: number;\r\n  payment_method: \"cash\" | \"mpesa\" | \"card\" | \"bank\" | \"other\";\r\n  amount: number;\r\n  reference?: string | null;\r\n  payment_date: string;\r\n}\r\n\r\n/**\r\n * Create sale request\r\n */\r\nexport interface CreateSaleRequest {\r\n  branch_id: number;\r\n  customer_id?: number;\r\n  sale_date: string;\r\n  notes?: string;\r\n  items: {\r\n    product_id: number;\r\n    quantity: number;\r\n    unit_price: number;\r\n    discount_amount?: number;\r\n    tax_amount?: number;\r\n  }[];\r\n  payments: {\r\n    payment_method: \"cash\" | \"mpesa\" | \"card\" | \"bank\" | \"other\";\r\n    amount: number;\r\n    reference?: string;\r\n    payment_date: string;\r\n  }[];\r\n}\r\n\r\n// ==========================================\r\n// Session Types\r\n// ==========================================\r\n\r\n/**\r\n * POS session entity\r\n */\r\nexport interface PosSession {\r\n  id: number;\r\n  branch_id: number;\r\n  user_id: number;\r\n  opening_balance: number;\r\n  closing_balance?: number;\r\n  cash_sales?: number;\r\n  mpesa_sales?: number;\r\n  card_sales?: number;\r\n  other_sales?: number;\r\n  discrepancies?: number;\r\n  notes?: string | null;\r\n  status: \"open\" | \"closed\";\r\n  opened_at: string;\r\n  closed_at?: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  branch?: Branch;\r\n  user?: User;\r\n  sales?: Sale[];\r\n}\r\n\r\n/**\r\n * Create POS session request\r\n */\r\nexport interface CreatePosSessionRequest {\r\n  branch_id: number;\r\n  opening_balance: number;\r\n  notes?: string;\r\n}\r\n\r\n/**\r\n * Close POS session request\r\n */\r\nexport interface ClosePosSessionRequest {\r\n  closing_balance: number;\r\n  cash_sales?: number;\r\n  mpesa_sales?: number;\r\n  card_sales?: number;\r\n  other_sales?: number;\r\n  discrepancies?: number;\r\n  notes?: string;\r\n}\r\n\r\n// ==========================================\r\n// Customer Types\r\n// ==========================================\r\n\r\n/**\r\n * Customer entity\r\n */\r\nexport interface Customer {\r\n  id: number;\r\n  tenant_id: number;\r\n  name: string;\r\n  phone?: string | null;\r\n  email?: string | null;\r\n  address?: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at: string | null;\r\n  created_by?: number | null;\r\n  last_updated_by?: number | null;\r\n}\r\n\r\n/**\r\n * Create customer request\r\n */\r\nexport interface CreateCustomerRequest {\r\n  tenant_id: number;\r\n  name: string;\r\n  phone?: string;\r\n  email?: string;\r\n  address?: string;\r\n}\r\n\r\n/**\r\n * Update customer request\r\n */\r\nexport interface UpdateCustomerRequest {\r\n  name?: string;\r\n  phone?: string;\r\n  email?: string;\r\n  address?: string;\r\n}\r\n\r\n// ==========================================\r\n// System Settings Types\r\n// ==========================================\r\n\r\n/**\r\n * System settings entity\r\n */\r\nexport interface SystemSettings {\r\n  site_name: string;\r\n  site_description?: string;\r\n  support_email: string;\r\n  support_phone?: string;\r\n\r\n  password_expiry_days?: number;\r\n  max_login_attempts: number;\r\n  session_timeout_minutes: number;\r\n  require_2fa_for_admins: boolean;\r\n\r\n  smtp_host?: string;\r\n  smtp_port?: number;\r\n  smtp_username?: string;\r\n  smtp_password?: string;\r\n  smtp_from_email?: string;\r\n  smtp_from_name?: string;\r\n\r\n  default_theme: \"light\" | \"dark\" | \"system\";\r\n  primary_color?: string;\r\n  logo_url?: string;\r\n  favicon_url?: string;\r\n}\r\n\r\n/**\r\n * System health entity\r\n */\r\nexport interface SystemHealth {\r\n  status: \"healthy\" | \"warning\" | \"critical\";\r\n  last_checked: string;\r\n  uptime: string;\r\n  server: {\r\n    status: \"healthy\" | \"warning\" | \"critical\";\r\n    cpu_usage: number;\r\n    memory_usage: number;\r\n    disk_usage: number;\r\n  };\r\n  database: {\r\n    status: \"healthy\" | \"warning\" | \"critical\";\r\n    connections: number;\r\n    max_connections: number;\r\n    query_response_time: string;\r\n    size: string;\r\n  };\r\n  services: {\r\n    name: string;\r\n    status: \"healthy\" | \"warning\" | \"critical\";\r\n    response_time?: string;\r\n    active_jobs?: number;\r\n    queue_size?: number;\r\n    hit_rate?: string;\r\n  }[];\r\n  metrics: {\r\n    active_users: number;\r\n    requests_per_minute: number;\r\n    average_response_time: string;\r\n    error_rate: number;\r\n  };\r\n  recent_errors: {\r\n    time: string;\r\n    message: string;\r\n    count: number;\r\n  }[];\r\n}\r\n\r\n// ==========================================\r\n// Report Types\r\n// ==========================================\r\n\r\n/**\r\n * Sales report parameters\r\n */\r\nexport interface SalesReportParams extends QueryParams {\r\n  start_date?: string;\r\n  end_date?: string;\r\n  branch_id?: number;\r\n  product_id?: number;\r\n  user_id?: number;\r\n  payment_method?: \"cash\" | \"mpesa\" | \"card\" | \"bank\" | \"other\";\r\n}\r\n\r\n/**\r\n * Inventory report parameters\r\n */\r\nexport interface InventoryReportParams extends QueryParams {\r\n  branch_id?: number;\r\n  product_id?: number;\r\n  category_id?: number;\r\n  low_stock?: boolean;\r\n}\r\n\r\n/**\r\n * DSA report parameters\r\n */\r\nexport interface DsaReportParams extends QueryParams {\r\n  start_date?: string;\r\n  end_date?: string;\r\n  dsa_agent_id?: number;\r\n  product_id?: number;\r\n}\r\n\r\n/**\r\n * MPESA float report parameters\r\n */\r\nexport interface MpesaFloatReportParams extends QueryParams {\r\n  start_date?: string;\r\n  end_date?: string;\r\n  branch_id?: number;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED,6CAA6C;AAC7C,eAAe;AACf,6CAA6C;AAE7C;;CAEC", "debugId": null}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/types/user.ts"], "sourcesContent": ["/**\r\n * User types based on the Swagger definition\r\n */\r\n\r\nimport { Role } from \"./api\";\r\nimport { Branch } from \"./branch\";\r\nimport { Tenant } from \"./tenant\";\r\n\r\n/**\r\n * User entity as defined in the Swagger schema\r\n * This matches the exact structure from the API\r\n */\r\nexport interface ApiUser {\r\n  id: number;\r\n  name: string;\r\n  email: string | null;\r\n  phone: string | null;\r\n  role_id: number;\r\n  role_name: string;\r\n  tenant_id: number;\r\n  branch_id: number | null;\r\n  location_id: number | null;\r\n  language_preference: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n\r\n  // These properties can be returned with different casing\r\n  role?: Role;\r\n  Role?: Role;\r\n  tenant?: Tenant;\r\n  Tenant?: Tenant;\r\n  branch?: Branch;\r\n  Branch?: Branch;\r\n  location?: Location | null;\r\n  Location?: Location | null;\r\n\r\n  // Session related fields (for POS operators)\r\n  session_status?: \"active\" | \"none\";\r\n  session_id?: number;\r\n  session_data?: any;\r\n}\r\n\r\n/**\r\n * Normalized User type that handles inconsistencies in the API response\r\n * This is what should be used throughout the application\r\n */\r\nexport interface User {\r\n  id: number;\r\n  name: string;\r\n  email: string | null;\r\n  phone: string | null;\r\n  role_id: number;\r\n  role_name: string;\r\n  tenant_id: number;\r\n  branch_id: number | null;\r\n  location_id: number | null;\r\n  language_preference: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n\r\n  // Normalized properties\r\n  role?: Role;\r\n  tenant?: Tenant;\r\n  branch?: Branch;\r\n  location?: Location | null;\r\n\r\n  // Session related fields\r\n  session_status?: \"active\" | \"none\";\r\n  session_id?: number;\r\n  session_data?: any;\r\n}\r\n\r\n/**\r\n * Location entity\r\n */\r\nexport interface Location {\r\n  id: number;\r\n  name: string;\r\n  type: string;\r\n  region?: string;\r\n}\r\n\r\n/**\r\n * Function to normalize a user from the API response\r\n * This handles inconsistencies in property casing\r\n */\r\nexport function normalizeUser(apiUser: ApiUser): User {\r\n  // Create a normalized user object\r\n  const normalizedUser: User = {\r\n    ...apiUser,\r\n    // Normalize properties that might have inconsistent casing\r\n    role: apiUser.role || apiUser.Role || null,\r\n    tenant: apiUser.tenant || apiUser.Tenant || null,\r\n    branch: apiUser.branch || apiUser.Branch || null,\r\n    location: apiUser.location || apiUser.Location || null,\r\n  };\r\n\r\n  // If we have a role_id but no role object, create a minimal role object with the name from role_name\r\n  if (apiUser.role_id && !normalizedUser.role && apiUser.role_name) {\r\n    normalizedUser.role = {\r\n      id: apiUser.role_id,\r\n      name: apiUser.role_name,\r\n      description: null,\r\n      created_at: '',\r\n      updated_at: '',\r\n      permissions: []\r\n    };\r\n  }\r\n\r\n  return normalizedUser;\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAsFM,SAAS,cAAc,OAAgB;IAC5C,kCAAkC;IAClC,MAAM,iBAAuB;QAC3B,GAAG,OAAO;QACV,2DAA2D;QAC3D,MAAM,QAAQ,IAAI,IAAI,QAAQ,IAAI,IAAI;QACtC,QAAQ,QAAQ,MAAM,IAAI,QAAQ,MAAM,IAAI;QAC5C,QAAQ,QAAQ,MAAM,IAAI,QAAQ,MAAM,IAAI;QAC5C,UAAU,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,IAAI;IACpD;IAEA,qGAAqG;IACrG,IAAI,QAAQ,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,QAAQ,SAAS,EAAE;QAChE,eAAe,IAAI,GAAG;YACpB,IAAI,QAAQ,OAAO;YACnB,MAAM,QAAQ,SAAS;YACvB,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,aAAa,EAAE;QACjB;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1293, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/types/reports.ts"], "sourcesContent": ["import { Sale } from \"./sale\";\r\n\r\n/**\r\n * Report Filter Parameters\r\n */\r\nexport interface ReportFilterParams {\r\n  start_date?: string;\r\n  end_date?: string;\r\n  pos_session_id?: number;\r\n  user_id?: number;\r\n  employee_id?: number; // Added for employee filtering\r\n  branch_id?: number;\r\n  region_id?: number; // Added for region filtering\r\n  payment_method_id?: number;\r\n  status?: string;\r\n  is_dsa?: boolean;\r\n  time_range?: string; // For frontend time filtering (e.g., \"morning\", \"afternoon\", \"evening\")\r\n}\r\n\r\n/**\r\n * Sales Summary Data\r\n */\r\nexport interface SalesSummaryData {\r\n  grossSales: number;\r\n  netSales: number;\r\n  grossProfit: number;\r\n  totalSales: number;\r\n  averageSale: number;\r\n  totalItems: number;\r\n  totalCustomers: number;\r\n}\r\n\r\n/**\r\n * Sales By Item Data\r\n */\r\nexport interface SalesByItemData {\r\n  productId: number;\r\n  productName: string;\r\n  sku: string;\r\n  quantity: number;\r\n  totalSales: number;\r\n  averagePrice: number;\r\n  profit: number;\r\n}\r\n\r\n/**\r\n * Sales By Category Data\r\n */\r\nexport interface SalesByCategoryData {\r\n  categoryId: number;\r\n  categoryName: string;\r\n  quantity: number;\r\n  totalSales: number;\r\n  profit: number;\r\n  percentage: number;\r\n}\r\n\r\n/**\r\n * Sales By Employee Data\r\n */\r\nexport interface SalesByEmployeeData {\r\n  userId: number;\r\n  userName: string;\r\n  totalSales: number;\r\n  totalTransactions: number;\r\n  averageSale: number;\r\n  profit: number;\r\n}\r\n\r\n/**\r\n * Sales By Payment Type Data\r\n */\r\nexport interface SalesByPaymentTypeData {\r\n  paymentMethodId: number;\r\n  paymentMethodName: string;\r\n  totalSales: number;\r\n  totalTransactions: number;\r\n  averageSale: number;\r\n  percentage: number;\r\n}\r\n\r\n/**\r\n * Shift (POS Session) Summary Data\r\n */\r\nexport interface ShiftSummaryData {\r\n  id: number;\r\n  startTime: string;\r\n  endTime: string | null;\r\n  status: string;\r\n  openingBalance: number;\r\n  closingBalance: number | null;\r\n  totalSales: number;\r\n  totalTransactions: number;\r\n  cashPaidIn: number | null;\r\n  cashPaidOut: number | null;\r\n  discrepancies: number | null;\r\n  userId: number;\r\n  userName: string;\r\n  branchId: number;\r\n  branchName: string;\r\n}\r\n\r\n/**\r\n * Tax Report Data\r\n */\r\nexport interface TaxReportData {\r\n  vatRate: number;\r\n  vatName: string;\r\n  vatAmount: number;\r\n  taxableSales: number;\r\n  transactionCount: number;\r\n}\r\n\r\n/**\r\n * Time Range Option\r\n */\r\nexport interface TimeRangeOption {\r\n  label: string;\r\n  value: string;\r\n  startHour?: number;\r\n  endHour?: number;\r\n}\r\n\r\n/**\r\n * Date Range Option\r\n */\r\nexport interface DateRangeOption {\r\n  label: string;\r\n  value: string;\r\n  startDate?: Date;\r\n  endDate?: Date;\r\n}\r\n\r\n/**\r\n * Chart Data Point\r\n */\r\nexport interface ChartDataPoint {\r\n  date: string;\r\n  value: number;\r\n  label?: string;\r\n}\r\n\r\n/**\r\n * Chart Data\r\n */\r\nexport interface ChartData {\r\n  labels: string[];\r\n  datasets: {\r\n    label: string;\r\n    data: number[];\r\n    backgroundColor?: string | string[];\r\n    borderColor?: string | string[];\r\n    borderWidth?: number;\r\n  }[];\r\n  secondaryData?: ChartData;\r\n}\r\n\r\n/**\r\n * Process sales data into summary statistics\r\n */\r\nexport function processSalesSummary(sales: Sale[]): SalesSummaryData {\r\n  if (!sales || sales.length === 0) {\r\n    return {\r\n      grossSales: 0,\r\n      netSales: 0,\r\n      grossProfit: 0,\r\n      totalSales: 0,\r\n      averageSale: 0,\r\n      totalItems: 0,\r\n      totalCustomers: 0,\r\n    };\r\n  }\r\n\r\n  const grossSales = sales.reduce(\r\n    (sum, sale) => sum + parseFloat(sale.total_amount),\r\n    0\r\n  );\r\n\r\n  // Calculate total cost (buying price * quantity) for all items\r\n  const totalCost = sales.reduce((sum, sale) => {\r\n    if (!sale.SaleItems) return sum;\r\n\r\n    const saleCost = sale.SaleItems.reduce(\r\n      (itemSum, item) =>\r\n        itemSum + parseFloat(item.buying_price) * item.quantity,\r\n      0\r\n    );\r\n\r\n    return sum + saleCost;\r\n  }, 0);\r\n\r\n  // Calculate gross profit\r\n  const grossProfit = grossSales - totalCost;\r\n\r\n  // Count unique customers\r\n  const uniqueCustomers = new Set(sales.map((sale) => sale.customer_id)).size;\r\n\r\n  // Count total items sold\r\n  const totalItems = sales.reduce((sum, sale) => {\r\n    if (!sale.SaleItems) return sum;\r\n    return (\r\n      sum + sale.SaleItems.reduce((itemSum, item) => itemSum + item.quantity, 0)\r\n    );\r\n  }, 0);\r\n\r\n  return {\r\n    grossSales,\r\n    netSales: grossSales, // Assuming net sales is gross sales for now\r\n    grossProfit,\r\n    totalSales: sales.length,\r\n    averageSale: sales.length > 0 ? grossSales / sales.length : 0,\r\n    totalItems,\r\n    totalCustomers: uniqueCustomers,\r\n  };\r\n}\r\n\r\n/**\r\n * Process sales data into sales by item\r\n */\r\nexport function processSalesByItem(sales: Sale[]): SalesByItemData[] {\r\n  if (!sales || sales.length === 0) {\r\n    return [];\r\n  }\r\n\r\n  const itemMap = new Map<number, SalesByItemData>();\r\n\r\n  sales.forEach((sale) => {\r\n    if (!sale.SaleItems) return;\r\n\r\n    sale.SaleItems.forEach((item) => {\r\n      if (!item.Product) return;\r\n\r\n      const productId = item.product_id;\r\n      const existingItem = itemMap.get(productId);\r\n\r\n      if (existingItem) {\r\n        existingItem.quantity += item.quantity;\r\n        existingItem.totalSales += parseFloat(item.total_price);\r\n        const itemProfit =\r\n          (parseFloat(item.unit_price) - parseFloat(item.buying_price)) *\r\n          item.quantity;\r\n        existingItem.profit += itemProfit;\r\n      } else {\r\n        const itemProfit =\r\n          (parseFloat(item.unit_price) - parseFloat(item.buying_price)) *\r\n          item.quantity;\r\n        itemMap.set(productId, {\r\n          productId,\r\n          productName: item.Product.name,\r\n          sku: item.Product.sku,\r\n          quantity: item.quantity,\r\n          totalSales: parseFloat(item.total_price),\r\n          averagePrice: parseFloat(item.unit_price),\r\n          profit: itemProfit,\r\n        });\r\n      }\r\n    });\r\n  });\r\n\r\n  // Calculate average price for each item\r\n  itemMap.forEach((item) => {\r\n    item.averagePrice = item.quantity > 0 ? item.totalSales / item.quantity : 0;\r\n  });\r\n\r\n  return Array.from(itemMap.values());\r\n}\r\n\r\n/**\r\n * Date range options\r\n */\r\nexport const DATE_RANGE_OPTIONS: DateRangeOption[] = [\r\n  { label: \"Today\", value: \"today\" },\r\n  { label: \"Yesterday\", value: \"yesterday\" },\r\n  { label: \"This Week\", value: \"this_week\" },\r\n  { label: \"Last Week\", value: \"last_week\" },\r\n  { label: \"This Month\", value: \"this_month\" },\r\n  { label: \"Last Month\", value: \"last_month\" },\r\n  { label: \"This Year\", value: \"this_year\" },\r\n  { label: \"Last Year\", value: \"last_year\" },\r\n  { label: \"Custom Range\", value: \"custom\" },\r\n];\r\n\r\n/**\r\n * Time range options\r\n */\r\nexport const TIME_RANGE_OPTIONS: TimeRangeOption[] = [\r\n  { label: \"All Day\", value: \"all_day\" },\r\n  { label: \"Morning (6AM-12PM)\", value: \"morning\", startHour: 6, endHour: 12 },\r\n  {\r\n    label: \"Afternoon (12PM-6PM)\",\r\n    value: \"afternoon\",\r\n    startHour: 12,\r\n    endHour: 18,\r\n  },\r\n  { label: \"Evening (6PM-12AM)\", value: \"evening\", startHour: 18, endHour: 24 },\r\n  { label: \"Night (12AM-6AM)\", value: \"night\", startHour: 0, endHour: 6 },\r\n];\r\n"], "names": [], "mappings": ";;;;;;AAgKO,SAAS,oBAAoB,KAAa;IAC/C,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAChC,OAAO;YACL,YAAY;YACZ,UAAU;YACV,aAAa;YACb,YAAY;YACZ,aAAa;YACb,YAAY;YACZ,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa,MAAM,MAAM,CAC7B,CAAC,KAAK,OAAS,MAAM,WAAW,KAAK,YAAY,GACjD;IAGF,+DAA+D;IAC/D,MAAM,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK;QACnC,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO;QAE5B,MAAM,WAAW,KAAK,SAAS,CAAC,MAAM,CACpC,CAAC,SAAS,OACR,UAAU,WAAW,KAAK,YAAY,IAAI,KAAK,QAAQ,EACzD;QAGF,OAAO,MAAM;IACf,GAAG;IAEH,yBAAyB;IACzB,MAAM,cAAc,aAAa;IAEjC,yBAAyB;IACzB,MAAM,kBAAkB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,WAAW,GAAG,IAAI;IAE3E,yBAAyB;IACzB,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK;QACpC,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO;QAC5B,OACE,MAAM,KAAK,SAAS,CAAC,MAAM,CAAC,CAAC,SAAS,OAAS,UAAU,KAAK,QAAQ,EAAE;IAE5E,GAAG;IAEH,OAAO;QACL;QACA,UAAU;QACV;QACA,YAAY,MAAM,MAAM;QACxB,aAAa,MAAM,MAAM,GAAG,IAAI,aAAa,MAAM,MAAM,GAAG;QAC5D;QACA,gBAAgB;IAClB;AACF;AAKO,SAAS,mBAAmB,KAAa;IAC9C,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAChC,OAAO,EAAE;IACX;IAEA,MAAM,UAAU,IAAI;IAEpB,MAAM,OAAO,CAAC,CAAC;QACb,IAAI,CAAC,KAAK,SAAS,EAAE;QAErB,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC;YACtB,IAAI,CAAC,KAAK,OAAO,EAAE;YAEnB,MAAM,YAAY,KAAK,UAAU;YACjC,MAAM,eAAe,QAAQ,GAAG,CAAC;YAEjC,IAAI,cAAc;gBAChB,aAAa,QAAQ,IAAI,KAAK,QAAQ;gBACtC,aAAa,UAAU,IAAI,WAAW,KAAK,WAAW;gBACtD,MAAM,aACJ,CAAC,WAAW,KAAK,UAAU,IAAI,WAAW,KAAK,YAAY,CAAC,IAC5D,KAAK,QAAQ;gBACf,aAAa,MAAM,IAAI;YACzB,OAAO;gBACL,MAAM,aACJ,CAAC,WAAW,KAAK,UAAU,IAAI,WAAW,KAAK,YAAY,CAAC,IAC5D,KAAK,QAAQ;gBACf,QAAQ,GAAG,CAAC,WAAW;oBACrB;oBACA,aAAa,KAAK,OAAO,CAAC,IAAI;oBAC9B,KAAK,KAAK,OAAO,CAAC,GAAG;oBACrB,UAAU,KAAK,QAAQ;oBACvB,YAAY,WAAW,KAAK,WAAW;oBACvC,cAAc,WAAW,KAAK,UAAU;oBACxC,QAAQ;gBACV;YACF;QACF;IACF;IAEA,wCAAwC;IACxC,QAAQ,OAAO,CAAC,CAAC;QACf,KAAK,YAAY,GAAG,KAAK,QAAQ,GAAG,IAAI,KAAK,UAAU,GAAG,KAAK,QAAQ,GAAG;IAC5E;IAEA,OAAO,MAAM,IAAI,CAAC,QAAQ,MAAM;AAClC;AAKO,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAgB,OAAO;IAAS;CAC1C;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAsB,OAAO;QAAW,WAAW;QAAG,SAAS;IAAG;IAC3E;QACE,OAAO;QACP,OAAO;QACP,WAAW;QACX,SAAS;IACX;IACA;QAAE,OAAO;QAAsB,OAAO;QAAW,WAAW;QAAI,SAAS;IAAG;IAC5E;QAAE,OAAO;QAAoB,OAAO;QAAS,WAAW;QAAG,SAAS;IAAE;CACvE", "debugId": null}}, {"offset": {"line": 1450, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/types/index.ts"], "sourcesContent": ["export * from \"./api\";\r\n\r\n// Export specific types from product.ts\r\nexport type { UpdateProductStatusRequest } from \"./product\";\r\n\r\n// Export expense types\r\nexport type {\r\n  ExpenseCategory,\r\n  ExpenseCategoryGroup,\r\n  CreateExpenseCategoryRequest,\r\n  UpdateExpenseCategoryRequest\r\n} from \"./expense\";\r\n\r\n// Export specific types from branch.ts\r\nexport type {\r\n  ExtendedBranch,\r\n  UpdateBranchRequest,\r\n  UpdateBranchStatusRequest,\r\n} from \"./branch\";\r\n\r\n// Export specific types from company.ts\r\nexport type { CompanySettings, UpdateCompanySettingsRequest } from \"./company\";\r\n\r\n// Export specific types from inventory.ts\r\nexport type { UpdateStockTransferStatusRequest } from \"./inventory\";\r\n\r\n// Export Stock Request types\r\nexport type {\r\n  StockRequest,\r\n  StockRequestItem,\r\n  CreateStockRequestRequest,\r\n  CreateStockRequestItemRequest,\r\n  ApproveStockRequestRequest,\r\n  ApproveStockRequestItemRequest,\r\n  DispatchStockRequestRequest,\r\n  ReceiveStockRequestRequest,\r\n  ReceiveStockRequestItemRequest,\r\n  CancelStockRequestRequest,\r\n} from \"./stock-request\";\r\n\r\n// Export User types\r\nexport type { User, ApiUser } from \"./user\";\r\nexport { normalizeUser } from \"./user\";\r\n\r\n// Export Banking types\r\nexport type {\r\n  Banking,\r\n  BankingSummary,\r\n  BankingFilters,\r\n  BankingSummaryFilters,\r\n  CreateBankingRequest,\r\n  UpdateBankingRequest,\r\n  PaginatedBankingResponse,\r\n} from \"./banking\";\r\n\r\n// Export Payment Method types\r\nexport type {\r\n  PaymentMethod,\r\n  CreatePaymentMethodRequest,\r\n  UpdatePaymentMethodRequest,\r\n  UpdatePaymentMethodStatusRequest,\r\n} from \"./payment-method\";\r\n\r\n// Export POS Session types\r\nexport type {\r\n  PosSession,\r\n  PosSessionReconciliation,\r\n  CreatePosSessionRequest,\r\n  ClosePosSessionRequest,\r\n  PosSessionFilters,\r\n} from \"./pos-session\";\r\n\r\n// Export Cash Balance types\r\nexport type {\r\n  CashRunningBalanceStatus,\r\n  CashRunningBalanceFilters,\r\n} from \"./cash-balance\";\r\n\r\n// Export Sale types\r\nexport type { Sale, SaleItem, SaleFilters } from \"./sale\";\r\n\r\n// Export Customer types\r\nexport type {\r\n  Customer,\r\n  CreateCustomerRequest,\r\n  UpdateCustomerRequest,\r\n  CustomerSalesResponse,\r\n  CustomerFilters,\r\n  PaginatedCustomerResponse,\r\n} from \"./customer\";\r\n\r\n// Export Report types\r\nexport type {\r\n  ReportFilterParams,\r\n  SalesSummaryData,\r\n  SalesByItemData,\r\n  SalesByCategoryData,\r\n  SalesByEmployeeData,\r\n  SalesByPaymentTypeData,\r\n  ShiftSummaryData,\r\n  TimeRangeOption,\r\n  DateRangeOption,\r\n  ChartDataPoint,\r\n  ChartData,\r\n} from \"./reports\";\r\nexport {\r\n  processSalesSummary,\r\n  processSalesByItem,\r\n  DATE_RANGE_OPTIONS,\r\n  TIME_RANGE_OPTIONS,\r\n} from \"./reports\";\r\n"], "names": [], "mappings": ";AAAA;AA0CA;AA+DA", "debugId": null}}, {"offset": {"line": 1477, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/api-loader.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface ApiLoaderProps {\r\n  /**\r\n   * Whether the loader is visible\r\n   */\r\n  isVisible: boolean;\r\n\r\n  /**\r\n   * Timeout in milliseconds after which the loader will be hidden\r\n   * @default 15000 (15 seconds)\r\n   */\r\n  timeout?: number;\r\n\r\n  /**\r\n   * Optional className for styling\r\n   */\r\n  className?: string;\r\n}\r\n\r\nexport function ApiLoader({\r\n  isVisible,\r\n  timeout = 15000,\r\n  className\r\n}: ApiLoaderProps) {\r\n  const [show, setShow] = useState(false);\r\n\r\n  // Add a small delay before showing the loader to prevent flashing\r\n  // for very quick requests\r\n  useEffect(() => {\r\n    let showTimer: NodeJS.Timeout;\r\n    let hideTimer: NodeJS.Timeout;\r\n\r\n    if (isVisible) {\r\n      // Show the loader after a short delay (150ms) - reduced from 300ms\r\n      // to make it more responsive\r\n      showTimer = setTimeout(() => {\r\n        setShow(true);\r\n      }, 150);\r\n\r\n      // Set a timeout to hide the loader after the specified time\r\n      hideTimer = setTimeout(() => {\r\n        setShow(false);\r\n      }, timeout);\r\n    } else {\r\n      setShow(false);\r\n    }\r\n\r\n    return () => {\r\n      clearTimeout(showTimer);\r\n      clearTimeout(hideTimer);\r\n    };\r\n  }, [isVisible, timeout]);\r\n\r\n  if (!show) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"fixed inset-0 z-[9999] flex items-center justify-center bg-background/30 backdrop-blur-[2px]\",\r\n        className\r\n      )}\r\n      data-testid=\"api-loader\"\r\n    >\r\n      <div className=\"rounded-md bg-background p-6 shadow-lg border border-border\">\r\n        <Loader2 className=\"h-10 w-10 animate-spin text-primary\" />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAwBO,SAAS,UAAU,EACxB,SAAS,EACT,UAAU,KAAK,EACf,SAAS,EACM;;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,kEAAkE;IAClE,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI;YACJ,IAAI;YAEJ,IAAI,WAAW;gBACb,mEAAmE;gBACnE,6BAA6B;gBAC7B,YAAY;2CAAW;wBACrB,QAAQ;oBACV;0CAAG;gBAEH,4DAA4D;gBAC5D,YAAY;2CAAW;wBACrB,QAAQ;oBACV;0CAAG;YACL,OAAO;gBACL,QAAQ;YACV;YAEA;uCAAO;oBACL,aAAa;oBACb,aAAa;gBACf;;QACF;8BAAG;QAAC;QAAW;KAAQ;IAEvB,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA;QAEF,eAAY;kBAEZ,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI3B;GAnDgB;KAAA", "debugId": null}}, {"offset": {"line": 1566, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/providers/loading-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, {\r\n  createContext,\r\n  useCallback,\r\n  useContext,\r\n  useEffect,\r\n  useState,\r\n} from \"react\";\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { ApiLoader } from \"@/components/ui/api-loader\";\r\n\r\n// Key for localStorage to track if we've already shown loading screens\r\nconst LOADING_SHOWN_KEY = \"loading_shown\";\r\n\r\n// Different loading screen types\r\nexport type LoadingScreenType = \"auth\" | \"main\" | \"role\" | \"api\";\r\n\r\ninterface LoadingContextProps {\r\n  isLoading: Record<LoadingScreenType, boolean>;\r\n  setLoading: (type: LoadingScreenType, value: boolean) => void;\r\n  hasShownLoading: Record<LoadingScreenType, boolean>;\r\n  markLoadingShown: (type: LoadingScreenType) => void;\r\n  resetLoading: (type?: LoadingScreenType) => void;\r\n  // API loading state management\r\n  incrementApiRequests: () => void;\r\n  decrementApiRequests: () => void;\r\n  activeApiRequests: number;\r\n}\r\n\r\nconst LoadingContext = createContext<LoadingContextProps | null>(null);\r\n\r\nexport function useLoading() {\r\n  const context = useContext(LoadingContext);\r\n  if (!context) {\r\n    throw new Error(\"useLoading must be used within a LoadingProvider\");\r\n  }\r\n  return context;\r\n}\r\n\r\ninterface LoadingProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport function LoadingProvider({ children }: LoadingProviderProps) {\r\n  // Track active API requests\r\n  const [activeApiRequests, setActiveApiRequests] = useState(0);\r\n\r\n  // Function to increment active API requests\r\n  const incrementApiRequests = useCallback(() => {\r\n    setActiveApiRequests((prev) => {\r\n      const newCount = prev + 1;\r\n      if (newCount === 1) {\r\n        // First active request, show the loader\r\n        setIsLoadingState((prev) => ({ ...prev, api: true }));\r\n      }\r\n      return newCount;\r\n    });\r\n  }, []);\r\n\r\n  // Function to decrement active API requests\r\n  const decrementApiRequests = useCallback(() => {\r\n    setActiveApiRequests((prev) => {\r\n      const newCount = Math.max(0, prev - 1);\r\n      if (newCount === 0) {\r\n        // No more active requests, hide the loader\r\n        setIsLoadingState((prev) => ({ ...prev, api: false }));\r\n      }\r\n      return newCount;\r\n    });\r\n  }, []);\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n\r\n  // Track loading state for different screens\r\n  const [isLoading, setIsLoadingState] = useState<\r\n    Record<LoadingScreenType, boolean>\r\n  >({\r\n    auth: false,\r\n    main: false,\r\n    role: false,\r\n    api: false,\r\n  });\r\n\r\n  // Track if we've shown loading for different screens in this session\r\n  const [hasShownLoading, setHasShownLoading] = useState<\r\n    Record<LoadingScreenType, boolean>\r\n  >(() => {\r\n    // Only run this on the client side\r\n    if (typeof window === \"undefined\") {\r\n      return {\r\n        auth: false,\r\n        main: false,\r\n        role: false,\r\n        api: false,\r\n      };\r\n    }\r\n\r\n    // Try to get the loading state from localStorage\r\n    try {\r\n      const storedState = localStorage.getItem(LOADING_SHOWN_KEY);\r\n      if (storedState) {\r\n        return JSON.parse(storedState);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error reading loading state from localStorage:\", error);\r\n    }\r\n\r\n    return {\r\n      auth: false,\r\n      main: false,\r\n      role: false,\r\n      api: false,\r\n    };\r\n  });\r\n\r\n  // Set loading state for a specific screen - memoized with useCallback\r\n  const setLoading = useCallback((type: LoadingScreenType, value: boolean) => {\r\n    setIsLoadingState((prev) => ({\r\n      ...prev,\r\n      [type]: value,\r\n    }));\r\n  }, []);\r\n\r\n  // Mark that we've shown loading for a specific screen - memoized with useCallback\r\n  const markLoadingShown = useCallback((type: LoadingScreenType) => {\r\n    setHasShownLoading((prev) => {\r\n      const newState = {\r\n        ...prev,\r\n        [type]: true,\r\n      };\r\n\r\n      // Save to localStorage\r\n      if (typeof window !== \"undefined\") {\r\n        localStorage.setItem(LOADING_SHOWN_KEY, JSON.stringify(newState));\r\n      }\r\n\r\n      return newState;\r\n    });\r\n  }, []);\r\n\r\n  // Reset loading state (for all screens or a specific one) - memoized with useCallback\r\n  const resetLoading = useCallback((type?: LoadingScreenType) => {\r\n    if (type) {\r\n      // Reset for a specific screen\r\n      setHasShownLoading((prev) => {\r\n        const newState = {\r\n          ...prev,\r\n          [type]: false,\r\n        };\r\n\r\n        // Save to localStorage\r\n        if (typeof window !== \"undefined\") {\r\n          localStorage.setItem(LOADING_SHOWN_KEY, JSON.stringify(newState));\r\n        }\r\n\r\n        return newState;\r\n      });\r\n    } else {\r\n      // Reset for all screens\r\n      const newState = {\r\n        auth: false,\r\n        main: false,\r\n        role: false,\r\n        api: false,\r\n      };\r\n\r\n      // Save to localStorage\r\n      if (typeof window !== \"undefined\") {\r\n        localStorage.setItem(LOADING_SHOWN_KEY, JSON.stringify(newState));\r\n      }\r\n\r\n      setHasShownLoading(newState);\r\n    }\r\n  }, []);\r\n\r\n  // Effect to clear the localStorage flag when the user logs out\r\n  useEffect(() => {\r\n    // If auth is initialized but we don't have an access token,\r\n    // the user might have logged out, so clear all flags\r\n    if (isInitialized && !accessToken) {\r\n      resetLoading();\r\n    }\r\n  }, [isInitialized, accessToken, resetLoading]);\r\n\r\n  // Memoize the context value to prevent unnecessary re-renders\r\n  const value = React.useMemo(\r\n    () => ({\r\n      isLoading,\r\n      setLoading,\r\n      hasShownLoading,\r\n      markLoadingShown,\r\n      resetLoading,\r\n      incrementApiRequests,\r\n      decrementApiRequests,\r\n      activeApiRequests,\r\n    }),\r\n    [\r\n      isLoading,\r\n      setLoading,\r\n      hasShownLoading,\r\n      markLoadingShown,\r\n      resetLoading,\r\n      incrementApiRequests,\r\n      decrementApiRequests,\r\n      activeApiRequests,\r\n    ]\r\n  );\r\n\r\n  return (\r\n    <LoadingContext.Provider value={value}>\r\n      {children}\r\n      <ApiLoader isVisible={isLoading.api} timeout={15000} />\r\n    </LoadingContext.Provider>\r\n  );\r\n}\r\n\r\n// Loading component that can be reused across the application\r\ninterface LoadingScreenProps {\r\n  message?: string;\r\n  showSpinner?: boolean;\r\n  showLogo?: boolean;\r\n}\r\n\r\nexport function LoadingScreen({\r\n  message = \"Loading...\",\r\n  showSpinner = true,\r\n  showLogo = true,\r\n}: LoadingScreenProps) {\r\n  return (\r\n    <div className=\"fixed inset-0 z-[9999] flex flex-col items-center justify-center bg-background\">\r\n      {showLogo && (\r\n        <div className=\"mb-8\">\r\n          <img\r\n            src=\"/logo.png\"\r\n            alt=\"Dukalink Logo\"\r\n            className=\"h-12 w-auto\"\r\n            onError={(e) => {\r\n              // Hide the image if it fails to load\r\n              (e.target as HTMLImageElement).style.display = \"none\";\r\n            }}\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      {showSpinner && (\r\n        <div className=\"mb-4\">\r\n          <div className=\"h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent\"></div>\r\n        </div>\r\n      )}\r\n\r\n      <p className=\"text-lg font-medium text-primary\">{message}</p>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAOA;AACA;;;AAVA;;;;AAYA,uEAAuE;AACvE,MAAM,oBAAoB;AAiB1B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA8B;AAE1D,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAYT,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IAChE,4BAA4B;IAC5B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,4CAA4C;IAC5C,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACvC;qEAAqB,CAAC;oBACpB,MAAM,WAAW,OAAO;oBACxB,IAAI,aAAa,GAAG;wBAClB,wCAAwC;wBACxC;iFAAkB,CAAC,OAAS,CAAC;oCAAE,GAAG,IAAI;oCAAE,KAAK;gCAAK,CAAC;;oBACrD;oBACA,OAAO;gBACT;;QACF;4DAAG,EAAE;IAEL,4CAA4C;IAC5C,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACvC;qEAAqB,CAAC;oBACpB,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,OAAO;oBACpC,IAAI,aAAa,GAAG;wBAClB,2CAA2C;wBAC3C;iFAAkB,CAAC,OAAS,CAAC;oCAAE,GAAG,IAAI;oCAAE,KAAK;gCAAM,CAAC;;oBACtD;oBACA,OAAO;gBACT;;QACF;4DAAG,EAAE;IACL,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAEnD,4CAA4C;IAC5C,MAAM,CAAC,WAAW,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE5C;QACA,MAAM;QACN,MAAM;QACN,MAAM;QACN,KAAK;IACP;IAEA,qEAAqE;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;oCAEnD;YACA,mCAAmC;YACnC,uCAAmC;;YAOnC;YAEA,iDAAiD;YACjD,IAAI;gBACF,MAAM,cAAc,aAAa,OAAO,CAAC;gBACzC,IAAI,aAAa;oBACf,OAAO,KAAK,KAAK,CAAC;gBACpB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kDAAkD;YAClE;YAEA,OAAO;gBACL,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,KAAK;YACP;QACF;;IAEA,sEAAsE;IACtE,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC,MAAyB;YACvD;2DAAkB,CAAC,OAAS,CAAC;wBAC3B,GAAG,IAAI;wBACP,CAAC,KAAK,EAAE;oBACV,CAAC;;QACH;kDAAG,EAAE;IAEL,kFAAkF;IAClF,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACpC;iEAAmB,CAAC;oBAClB,MAAM,WAAW;wBACf,GAAG,IAAI;wBACP,CAAC,KAAK,EAAE;oBACV;oBAEA,uBAAuB;oBACvB,wCAAmC;wBACjC,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;oBACzD;oBAEA,OAAO;gBACT;;QACF;wDAAG,EAAE;IAEL,sFAAsF;IACtF,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YAChC,IAAI,MAAM;gBACR,8BAA8B;gBAC9B;iEAAmB,CAAC;wBAClB,MAAM,WAAW;4BACf,GAAG,IAAI;4BACP,CAAC,KAAK,EAAE;wBACV;wBAEA,uBAAuB;wBACvB,wCAAmC;4BACjC,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;wBACzD;wBAEA,OAAO;oBACT;;YACF,OAAO;gBACL,wBAAwB;gBACxB,MAAM,WAAW;oBACf,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,KAAK;gBACP;gBAEA,uBAAuB;gBACvB,wCAAmC;oBACjC,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;gBACzD;gBAEA,mBAAmB;YACrB;QACF;oDAAG,EAAE;IAEL,+DAA+D;IAC/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,4DAA4D;YAC5D,qDAAqD;YACrD,IAAI,iBAAiB,CAAC,aAAa;gBACjC;YACF;QACF;oCAAG;QAAC;QAAe;QAAa;KAAa;IAE7C,8DAA8D;IAC9D,MAAM,QAAQ,6JAAA,CAAA,UAAK,CAAC,OAAO;0CACzB,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;yCACD;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;;YAC7B;0BACD,6LAAC,4IAAA,CAAA,YAAS;gBAAC,WAAW,UAAU,GAAG;gBAAE,SAAS;;;;;;;;;;;;AAGpD;IA1KgB;;QA2ByB,wIAAA,CAAA,gBAAa;;;KA3BtC;AAmLT,SAAS,cAAc,EAC5B,UAAU,YAAY,EACtB,cAAc,IAAI,EAClB,WAAW,IAAI,EACI;IACnB,qBACE,6LAAC;QAAI,WAAU;;YACZ,0BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,KAAI;oBACJ,KAAI;oBACJ,WAAU;oBACV,SAAS,CAAC;wBACR,qCAAqC;wBACpC,EAAE,MAAM,CAAsB,KAAK,CAAC,OAAO,GAAG;oBACjD;;;;;;;;;;;YAKL,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAInB,6LAAC;gBAAE,WAAU;0BAAoC;;;;;;;;;;;;AAGvD;MA9BgB", "debugId": null}}, {"offset": {"line": 1859, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/hooks/use-auth.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { authService } from \"@/features/auth/api/auth-service\";\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport apiClient from \"@/lib/api-client\";\r\nimport { ApiUser, LoginRequest, User, normalizeUser } from \"@/types\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useEffect } from \"react\";\r\nimport { useLoading } from \"@/components/providers/loading-provider\";\r\n\r\nexport function useLogin() {\r\n  const router = useRouter();\r\n  const queryClient = useQueryClient();\r\n  const { setTokens } = useAuthTokens();\r\n  const { setLoading, resetLoading } = useLoading();\r\n\r\n  return useMutation({\r\n    mutationFn: (credentials: LoginRequest) => authService.login(credentials),\r\n    onSuccess: (data) => {\r\n      console.log(\"Login successful, setting tokens:\", {\r\n        accessToken: data.accessToken ? \"exists\" : \"missing\",\r\n      });\r\n\r\n      // Show the loading screen with \"Preparing Dashboard\" message\r\n      // Reset loading state to ensure it shows even if it was shown before\r\n      resetLoading(\"main\");\r\n      setLoading(\"main\", true);\r\n\r\n      // Store tokens in cookies via the hook\r\n      setTokens(data.accessToken, data.refreshToken);\r\n\r\n      // Set the token for API requests\r\n      apiClient.setAuthToken(data.accessToken);\r\n\r\n      // Invalidate and refetch user data\r\n      queryClient.invalidateQueries({ queryKey: [\"currentUser\"] });\r\n\r\n      // Always redirect to dashboard page\r\n      const dashboardRoute = \"/dashboard\";\r\n      console.log(\"Redirecting to dashboard:\", dashboardRoute);\r\n\r\n      // Check if cookie was set properly\r\n      const cookies = document.cookie;\r\n      console.log(\"Cookies before redirect:\", cookies);\r\n\r\n      // Use Next.js router for client-side navigation\r\n      console.log(\"Redirecting to dashboard:\", dashboardRoute);\r\n      router.replace(dashboardRoute);\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Login error:\", error);\r\n      // Ensure loading is turned off in case of error\r\n      setLoading(\"main\", false);\r\n    },\r\n  });\r\n}\r\n\r\nexport function useLogout() {\r\n  const router = useRouter();\r\n  const queryClient = useQueryClient();\r\n  const { clearTokens } = useAuthTokens();\r\n\r\n  return useMutation({\r\n    mutationFn: () => authService.logout(),\r\n    onSuccess: () => {\r\n      // Clear tokens from cookies\r\n      clearTokens();\r\n\r\n      // Clear the auth token from API client\r\n      apiClient.setAuthToken(null);\r\n\r\n      // Clear user data from cache\r\n      queryClient.invalidateQueries({ queryKey: [\"currentUser\"] });\r\n      queryClient.clear();\r\n\r\n      // Use Next.js router for client-side navigation\r\n      console.log(\"Redirecting to login\");\r\n      router.replace(\"/login\");\r\n    },\r\n  });\r\n}\r\n\r\nexport function useCurrentUser() {\r\n  const { accessToken, refreshAccessToken, clearTokens, isInitialized } =\r\n    useAuthTokens();\r\n  const queryClient = useQueryClient();\r\n  const router = useRouter();\r\n\r\n  // Set the token for API requests when it changes\r\n  useEffect(() => {\r\n    console.log(\"useCurrentUser effect:\", {\r\n      accessToken: accessToken ? \"exists\" : \"missing\",\r\n      isInitialized,\r\n    });\r\n    if (isInitialized) {\r\n      apiClient.setAuthToken(accessToken);\r\n    }\r\n  }, [accessToken, isInitialized]);\r\n\r\n  // Try to get the user from the cache first\r\n  const cachedUser = queryClient.getQueryData<User>([\"currentUser\"]);\r\n\r\n  return useQuery({\r\n    queryKey: [\"currentUser\"],\r\n    queryFn: async () => {\r\n      console.log(\r\n        \"Fetching current user with token:\",\r\n        accessToken ? \"exists\" : \"missing\"\r\n      );\r\n      try {\r\n        const apiUser = (await authService.getCurrentUser()) as ApiUser;\r\n        return normalizeUser(apiUser);\r\n      } catch (error: any) {\r\n        // If we get a 401 error, try to refresh the token\r\n        if (error?.statusCode === 401) {\r\n          console.log(\"Token expired, attempting to refresh...\");\r\n\r\n          // Check if the error message indicates an invalid or expired token\r\n          const isTokenError =\r\n            error?.message === \"Invalid or expired token\" ||\r\n            error?.error === \"Unauthorized\" ||\r\n            error?.error === \"Error\";\r\n\r\n          // If it's a specific token error, handle it immediately\r\n          if (isTokenError) {\r\n            console.log(\r\n              \"Invalid or expired token detected, clearing auth state\"\r\n            );\r\n            clearTokens();\r\n            queryClient.clear();\r\n            router.replace(\"/login\");\r\n            throw error;\r\n          }\r\n\r\n          // Otherwise, try to refresh the token\r\n          const refreshed = await refreshAccessToken();\r\n\r\n          if (refreshed) {\r\n            // If refresh was successful, retry the request\r\n            console.log(\"Token refreshed, retrying request\");\r\n            const apiUser = (await authService.getCurrentUser()) as ApiUser;\r\n            return normalizeUser(apiUser);\r\n          } else {\r\n            // If refresh failed, redirect to login\r\n            console.log(\"Token refresh failed, redirecting to login\");\r\n            clearTokens();\r\n            queryClient.clear();\r\n            router.replace(\"/login\");\r\n            throw error;\r\n          }\r\n        }\r\n        throw error;\r\n      }\r\n    },\r\n    enabled: isInitialized && !!accessToken,\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n    // Disable automatic refetching to reduce loading states\r\n    refetchOnWindowFocus: false,\r\n    refetchOnMount: false,\r\n    initialData: cachedUser || undefined,\r\n  });\r\n}\r\n\r\nexport function useIsAuthenticated() {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n\r\n  const isLoading = !isInitialized || isUserLoading;\r\n\r\n  useEffect(() => {\r\n    console.log(\"useIsAuthenticated state:\", {\r\n      accessToken: accessToken ? \"exists\" : \"missing\",\r\n      isInitialized,\r\n      user: user ? \"exists\" : \"missing\",\r\n      isUserLoading,\r\n      isLoading,\r\n    });\r\n  }, [accessToken, isInitialized, user, isUserLoading, isLoading]);\r\n\r\n  return {\r\n    isAuthenticated: !!user && !!accessToken,\r\n    isLoading,\r\n    user: user || null,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;;AATA;;;;;;;;;AAWO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAClC,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD;IAE9C,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;oCAAE,CAAC,cAA8B,oJAAA,CAAA,cAAW,CAAC,KAAK,CAAC;;QAC7D,SAAS;oCAAE,CAAC;gBACV,QAAQ,GAAG,CAAC,qCAAqC;oBAC/C,aAAa,KAAK,WAAW,GAAG,WAAW;gBAC7C;gBAEA,6DAA6D;gBAC7D,qEAAqE;gBACrE,aAAa;gBACb,WAAW,QAAQ;gBAEnB,uCAAuC;gBACvC,UAAU,KAAK,WAAW,EAAE,KAAK,YAAY;gBAE7C,iCAAiC;gBACjC,8HAAA,CAAA,UAAS,CAAC,YAAY,CAAC,KAAK,WAAW;gBAEvC,mCAAmC;gBACnC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;gBAE1D,oCAAoC;gBACpC,MAAM,iBAAiB;gBACvB,QAAQ,GAAG,CAAC,6BAA6B;gBAEzC,mCAAmC;gBACnC,MAAM,UAAU,SAAS,MAAM;gBAC/B,QAAQ,GAAG,CAAC,4BAA4B;gBAExC,gDAAgD;gBAChD,QAAQ,GAAG,CAAC,6BAA6B;gBACzC,OAAO,OAAO,CAAC;YACjB;;QACA,OAAO;oCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,gBAAgB;gBAC9B,gDAAgD;gBAChD,WAAW,QAAQ;YACrB;;IACF;AACF;GA7CgB;;QACC,qIAAA,CAAA,YAAS;QACJ,yLAAA,CAAA,iBAAc;QACZ,wIAAA,CAAA,gBAAa;QACE,yJAAA,CAAA,aAAU;QAExC,iLAAA,CAAA,cAAW;;;AAyCb,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAEpC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;qCAAE,IAAM,oJAAA,CAAA,cAAW,CAAC,MAAM;;QACpC,SAAS;qCAAE;gBACT,4BAA4B;gBAC5B;gBAEA,uCAAuC;gBACvC,8HAAA,CAAA,UAAS,CAAC,YAAY,CAAC;gBAEvB,6BAA6B;gBAC7B,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;gBAC1D,YAAY,KAAK;gBAEjB,gDAAgD;gBAChD,QAAQ,GAAG,CAAC;gBACZ,OAAO,OAAO,CAAC;YACjB;;IACF;AACF;IAvBgB;;QACC,qIAAA,CAAA,YAAS;QACJ,yLAAA,CAAA,iBAAc;QACV,wIAAA,CAAA,gBAAa;QAE9B,iLAAA,CAAA,cAAW;;;AAoBb,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,WAAW,EAAE,aAAa,EAAE,GACnE,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,QAAQ,GAAG,CAAC,0BAA0B;gBACpC,aAAa,cAAc,WAAW;gBACtC;YACF;YACA,IAAI,eAAe;gBACjB,8HAAA,CAAA,UAAS,CAAC,YAAY,CAAC;YACzB;QACF;mCAAG;QAAC;QAAa;KAAc;IAE/B,2CAA2C;IAC3C,MAAM,aAAa,YAAY,YAAY,CAAO;QAAC;KAAc;IAEjE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAc;QACzB,OAAO;uCAAE;gBACP,QAAQ,GAAG,CACT,qCACA,cAAc,WAAW;gBAE3B,IAAI;oBACF,MAAM,UAAW,MAAM,oJAAA,CAAA,cAAW,CAAC,cAAc;oBACjD,OAAO,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE;gBACvB,EAAE,OAAO,OAAY;oBACnB,kDAAkD;oBAClD,IAAI,OAAO,eAAe,KAAK;wBAC7B,QAAQ,GAAG,CAAC;wBAEZ,mEAAmE;wBACnE,MAAM,eACJ,OAAO,YAAY,8BACnB,OAAO,UAAU,kBACjB,OAAO,UAAU;wBAEnB,wDAAwD;wBACxD,IAAI,cAAc;4BAChB,QAAQ,GAAG,CACT;4BAEF;4BACA,YAAY,KAAK;4BACjB,OAAO,OAAO,CAAC;4BACf,MAAM;wBACR;wBAEA,sCAAsC;wBACtC,MAAM,YAAY,MAAM;wBAExB,IAAI,WAAW;4BACb,+CAA+C;4BAC/C,QAAQ,GAAG,CAAC;4BACZ,MAAM,UAAW,MAAM,oJAAA,CAAA,cAAW,CAAC,cAAc;4BACjD,OAAO,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE;wBACvB,OAAO;4BACL,uCAAuC;4BACvC,QAAQ,GAAG,CAAC;4BACZ;4BACA,YAAY,KAAK;4BACjB,OAAO,OAAO,CAAC;4BACf,MAAM;wBACR;oBACF;oBACA,MAAM;gBACR;YACF;;QACA,SAAS,iBAAiB,CAAC,CAAC;QAC5B,WAAW,IAAI,KAAK;QACpB,wDAAwD;QACxD,sBAAsB;QACtB,gBAAgB;QAChB,aAAa,cAAc;IAC7B;AACF;IA/EgB;;QAEZ,wIAAA,CAAA,gBAAa;QACK,yLAAA,CAAA,iBAAc;QACnB,qIAAA,CAAA,YAAS;QAgBjB,8KAAA,CAAA,WAAQ;;;AA6DV,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG;IAEjD,MAAM,YAAY,CAAC,iBAAiB;IAEpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,QAAQ,GAAG,CAAC,6BAA6B;gBACvC,aAAa,cAAc,WAAW;gBACtC;gBACA,MAAM,OAAO,WAAW;gBACxB;gBACA;YACF;QACF;uCAAG;QAAC;QAAa;QAAe;QAAM;QAAe;KAAU;IAE/D,OAAO;QACL,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC7B;QACA,MAAM,QAAQ;IAChB;AACF;IArBgB;;QACyB,wIAAA,CAAA,gBAAa;QACH", "debugId": null}}, {"offset": {"line": 2108, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/role-utils.ts"], "sourcesContent": ["/**\r\n * Determines the appropriate dashboard route based on user role\r\n * @param roleName The user's role name\r\n * @returns The dashboard route for the user's role\r\n */\r\nexport function getDashboardByRole(): string {\r\n  // Redirect all roles to the reports summary page\r\n  return \"/reports/sales-summary\";\r\n}\r\n\r\n/**\r\n * Checks if a user has access to a specific route based on their role\r\n * @param route The route to check access for\r\n * @param roleName The user's role name\r\n * @returns Whether the user has access to the route\r\n */\r\nexport function hasRouteAccess(\r\n  route: string,\r\n  roleName?: string | null\r\n): boolean {\r\n  if (!roleName) return false;\r\n\r\n  // Add detailed debugging\r\n  console.log(\r\n    `[hasRouteAccess] Checking access for route: ${route}, role: ${roleName}`\r\n  );\r\n\r\n  // Super admin has access to everything\r\n  if (roleName.toLowerCase() === \"super_admin\") {\r\n    console.log(`[hasRouteAccess] Super admin has access to all routes`);\r\n    return true;\r\n  }\r\n\r\n  // Define route access by role\r\n  const roleRouteAccess: Record<string, string[]> = {\r\n    branch_admin: [\r\n      \"/dashboard\",\r\n      \"/dashboard/branch\",\r\n      \"/users\",\r\n      \"/employees\",\r\n      \"/employees/[id]\", // Allow branch_admin to view employee details\r\n      \"/products\",\r\n      \"/categories\",\r\n      \"/brands\",\r\n      \"/inventory\",\r\n      \"/inventory/transfers\",\r\n      \"/inventory/reports\",\r\n      \"/banking\",\r\n      \"/banking/summary\",\r\n      \"/mpesa\",\r\n      \"/mpesa/transactions\",\r\n      \"/dsa\",\r\n      \"/dsa/agents\",\r\n      \"/dsa/assignments\",\r\n      \"/dsa/reconciliations\",\r\n      \"/phone-repairs\",\r\n      \"/expenses\",\r\n      \"/expenses/[id]\", // Add access to expense detail pages\r\n      \"/invoices\",\r\n      \"/invoices/[id]\",\r\n      \"/invoices/new\",\r\n      \"/invoices/[id]/edit\",\r\n      \"/credit-notes\",\r\n      \"/credit-notes/[id]\",\r\n      \"/credit-notes/new\",\r\n      \"/credit-notes/[id]/edit\",\r\n      \"/reports\",\r\n      \"/reports/sales-summary\",\r\n      \"/reports/sales-by-item\",\r\n      \"/reports/sales-by-category\",\r\n      \"/reports/sales-by-employee\",\r\n      \"/reports/sales-by-payment-type\",\r\n      \"/reports/mpesa-banking\",\r\n      \"/reports/cash-status\",\r\n      \"/reports/dsa-sales\",\r\n      \"/reports/receipts\",\r\n      \"/reports/shifts\",\r\n      \"/reports/phone-repairs\",\r\n      \"/profile\",\r\n      \"/pos\",\r\n      \"/pos/sessions\",\r\n      \"/sales\",\r\n      \"/customers\",\r\n      \"/procurement\",\r\n      \"/procurement/requests\",\r\n      \"/procurement/requests/new\",\r\n      \"/procurement/receipts\",\r\n      \"/suppliers\",\r\n    ],\r\n    super_admin: [\r\n      \"/dashboard\",\r\n      \"/super-admin\", // Keep this for backward compatibility\r\n      \"/tenants\",\r\n      \"/users\",\r\n      \"/roles\",\r\n      \"/rbac\", // Add RBAC route for super_admin\r\n      \"/branches\",\r\n      \"/locations\",\r\n      \"/locations/create\",\r\n      \"/locations/[id]\",\r\n      \"/locations/[id]/edit\",\r\n      \"/employees\",\r\n      \"/products\",\r\n      \"/categories\",\r\n      \"/brands\",\r\n      \"/inventory\",\r\n      \"/inventory/transfers\",\r\n      \"/inventory/reports\",\r\n      \"/float\",\r\n      // \"/float/transactions\", // Temporarily disabled until API is implemented\r\n      \"/float/movements\",\r\n      \"/float/reconciliations\",\r\n      \"/float/topups\",\r\n      \"/banking\",\r\n      \"/banking/summary\",\r\n      \"/dsa\",\r\n      \"/dsa/agents\",\r\n      \"/dsa/assignments\",\r\n      \"/dsa/reconciliations\",\r\n      \"/phone-repairs\",\r\n      \"/expenses\",\r\n      \"/invoices\",\r\n      \"/invoices/[id]\",\r\n      \"/invoices/new\",\r\n      \"/invoices/[id]/edit\",\r\n      \"/credit-notes\",\r\n      \"/credit-notes/[id]\",\r\n      \"/credit-notes/new\",\r\n      \"/credit-notes/[id]/edit\",\r\n      \"/settings/system\",\r\n      \"/settings/company\",\r\n      \"/settings/payment-methods\",\r\n      \"/settings/health\",\r\n      \"/profile\",\r\n      \"/procurement\",\r\n      \"/procurement/requests\",\r\n      \"/procurement/requests/new\",\r\n      \"/procurement/receipts\",\r\n      \"/suppliers\",\r\n    ],\r\n    company_admin: [\r\n      \"/dashboard\",\r\n      \"/dashboard/company\",\r\n      \"/users\",\r\n      \"/roles\",\r\n      // \"/rbac\", // Add RBAC route for company_admin\r\n      \"/branches\",\r\n      \"/locations\",\r\n      \"/locations/create\",\r\n      \"/locations/[id]\",\r\n      \"/locations/[id]/edit\",\r\n      \"/employees\",\r\n      \"/products\",\r\n      \"/categories\",\r\n      \"/brands\",\r\n      \"/inventory\",\r\n      \"/inventory/transfers\",\r\n      \"/inventory/reports\",\r\n      \"/float\",\r\n      // \"/float/transactions\", // Temporarily disabled until API is implemented\r\n      \"/float/movements\",\r\n      \"/float/reconciliations\",\r\n      \"/float/topups\",\r\n      \"/banking\",\r\n      \"/banking/summary\",\r\n      \"/mpesa\",\r\n      \"/mpesa/transactions\",\r\n      \"/dsa\",\r\n      \"/dsa/agents\",\r\n      \"/dsa/assignments\",\r\n      \"/dsa/reconciliations\",\r\n      \"/phone-repairs\",\r\n      \"/expenses\",\r\n      \"/expense-analytics\",\r\n      \"/invoices\",\r\n      \"/invoices/[id]\",\r\n      \"/invoices/new\",\r\n      \"/invoices/[id]/edit\",\r\n      \"/credit-notes\",\r\n      \"/credit-notes/[id]\",\r\n      \"/credit-notes/new\",\r\n      \"/credit-notes/[id]/edit\",\r\n      \"/reports\",\r\n      \"/reports/sales-summary\",\r\n      \"/reports/sales-by-item\",\r\n      \"/reports/sales-by-category\",\r\n      \"/reports/sales-by-employee\",\r\n      \"/reports/sales-by-payment-type\",\r\n      \"/reports/mpesa-banking\",\r\n      \"/reports/running-balances\",\r\n      \"/reports/mpesa-transactions\",\r\n      \"/reports/cash-status\",\r\n      \"/reports/dsa-sales\",\r\n      \"/reports/receipts\",\r\n      \"/reports/shifts\",\r\n      \"/reports/phone-repairs\",\r\n      \"/settings/company\",\r\n      \"/settings/payment-methods\",\r\n      \"/profile\",\r\n      \"/pos\",\r\n      \"/pos/sessions\",\r\n      \"/sales\",\r\n      \"/customers\",\r\n      \"/procurement\",\r\n      \"/procurement/requests\",\r\n      \"/procurement/requests/new\",\r\n      \"/procurement/receipts\",\r\n      \"/suppliers\",\r\n    ],\r\n    tenant_admin: [\r\n      \"/dashboard\",\r\n      \"/dashboard/company\",\r\n      \"/users\",\r\n      \"/roles\",\r\n      // \"/rbac\", // Add RBAC route for tenant_admin\r\n      \"/branches\",\r\n      \"/locations\",\r\n      \"/locations/create\",\r\n      \"/locations/[id]\",\r\n      \"/locations/[id]/edit\",\r\n      \"/employees\",\r\n      \"/products\",\r\n      \"/categories\",\r\n      \"/brands\",\r\n      \"/inventory\",\r\n      \"/inventory/transfers\",\r\n      \"/inventory/reports\",\r\n      \"/float\",\r\n      // \"/float/transactions\", // Temporarily disabled until API is implemented\r\n      \"/float/movements\",\r\n      \"/float/reconciliations\",\r\n      \"/float/topups\",\r\n      \"/banking\",\r\n      \"/banking/summary\",\r\n      \"/mpesa\",\r\n      \"/mpesa/transactions\",\r\n      \"/dsa\",\r\n      \"/dsa/agents\",\r\n      \"/dsa/assignments\",\r\n      \"/dsa/reconciliations\",\r\n      \"/phone-repairs\",\r\n      \"/expenses\",\r\n      \"/invoices\",\r\n      \"/invoices/[id]\",\r\n      \"/invoices/new\",\r\n      \"/invoices/[id]/edit\",\r\n      \"/reports\",\r\n      \"/reports/sales-summary\",\r\n      \"/reports/sales-by-item\",\r\n      \"/reports/sales-by-category\",\r\n      \"/reports/sales-by-employee\",\r\n      \"/reports/running-balances\",\r\n      \"/reports/sales-by-payment-type\",\r\n      \"/reports/mpesa-transactions\",\r\n      \"/reports/mpesa-banking\",\r\n      \"/reports/cash-status\",\r\n      \"/reports/dsa-sales\",\r\n      \"/reports/receipts\",\r\n      \"/reports/shifts\",\r\n      \"/reports/phone-repairs\",\r\n      \"/settings/company\",\r\n      \"/settings/payment-methods\",\r\n      \"/profile\",\r\n      \"/pos\",\r\n      \"/pos/sessions\",\r\n      \"/sales\",\r\n      \"/customers\",\r\n      \"/procurement\",\r\n      \"/procurement/requests\",\r\n      \"/procurement/requests/new\",\r\n      \"/procurement/receipts\",\r\n      \"/suppliers\",\r\n    ],\r\n    // Branch Manager role removed as they don't login via the web\r\n    stock_admin: [\r\n      \"/dashboard\",\r\n      \"/dashboard/stock\",\r\n      \"/products\",\r\n      \"/categories\",\r\n      \"/brands\",\r\n      \"/inventory\",\r\n      \"/inventory/transfers\",\r\n      \"/inventory/reports\",\r\n      \"/phone-repairs\",\r\n      \"/reports\",\r\n      \"/reports/running-balances\",\r\n      \"/reports/sales-summary\",\r\n      \"/reports/sales-by-item\",\r\n      \"/reports/sales-by-category\",\r\n      \"/reports/mpesa-transactions\",\r\n      \"/reports/sales-by-employee\",\r\n      \"/reports/sales-by-payment-type\",\r\n      \"/reports/mpesa-banking\",\r\n      \"/reports/cash-status\",\r\n      \"/reports/dsa-sales\",\r\n      \"/reports/receipts\",\r\n      \"/reports/shifts\",\r\n      \"/reports/phone-repairs\",\r\n      \"/profile\",\r\n      \"/procurement\",\r\n      \"/procurement/requests\",\r\n      \"/procurement/requests/new\",\r\n      \"/procurement/receipts\",\r\n      \"/suppliers\",\r\n    ],\r\n    float_manager: [\r\n      \"/dashboard\",\r\n      \"/dashboard/float\",\r\n      \"/dashboard/cash-balance\", // Cash Balance access\r\n      \"/float\",\r\n      \"/brands\",\r\n      \"/inventory\",\r\n      \"/inventory/reports\",\r\n      // \"/float/transactions\", // Temporarily disabled until API is implemented\r\n      \"/float/movements\",\r\n      \"/float/reconciliations\",\r\n      \"/float/topups\", // MPESA Float Top-ups\r\n      \"/banking\", // Banking Operations\r\n      \"/banking/summary\", // Banking Summary\r\n      \"/mpesa\", // MPESA Management\r\n      \"/mpesa/transactions\", // MPESA Transactions\r\n      \"/pos/sessions\", // POS Session Reconciliations\r\n      \"/reports/running-balances\",\r\n      \"/reports\",\r\n      \"/reports/sales-summary\",\r\n      \"/reports/sales-by-item\",\r\n      \"/reports/mpesa-transactions\",\r\n      \"/reports/sales-by-category\",\r\n      \"/reports/sales-by-employee\",\r\n      \"/reports/sales-by-payment-type\",\r\n      \"/reports/mpesa-banking\",\r\n      \"/reports/cash-status\", // Cash Status Reports\r\n      \"/reports/cash-float\", // Cash Float Reports\r\n      \"/reports/dsa-sales\",\r\n      \"/reports/receipts\",\r\n      \"/reports/shifts\",\r\n      \"/reports/phone-repairs\",\r\n      \"/expense-analytics\", // Financial Reports/Analytics\r\n      \"/profile\",\r\n      \"/procurement\",\r\n      \"/procurement/requests\",\r\n      \"/procurement/receipts\",\r\n      \"/suppliers\",\r\n    ],\r\n    accountant: [\r\n      \"/dashboard\",\r\n      \"/dashboard/finance\",\r\n      \"/reports\",\r\n      \"/reports/sales-summary\",\r\n      \"/reports/mpesa-transactions\",\r\n      \"/reports/sales-by-item\",\r\n      \"/reports/sales-by-category\",\r\n      \"/reports/sales-by-employee\",\r\n      \"/reports/sales-by-payment-type\",\r\n      \"/reports/mpesa-banking\",\r\n      \"/reports/cash-status\",\r\n      \"/reports/dsa-sales\",\r\n      \"/reports/receipts\",\r\n      \"/reports/shifts\",\r\n      \"/reports/phone-repairs\",\r\n      \"/reports/expense-reports\",\r\n      \"/brands\",\r\n      \"/inventory\",\r\n      \"/inventory/reports\",\r\n      \"/banking\",\r\n      \"/banking/summary\",\r\n      \"/expenses\",\r\n      \"/expenses/[id]\",\r\n      \"/expense-analytics\",\r\n      \"/invoices\",\r\n      \"/invoices/[id]\",\r\n      \"/invoices/new\",\r\n      \"/invoices/[id]/edit\",\r\n      \"/credit-notes\",\r\n      \"/credit-notes/[id]\",\r\n      \"/credit-notes/new\",\r\n      \"/credit-notes/[id]/edit\",\r\n      \"/profile\",\r\n      \"/users\",\r\n      \"/locations\",\r\n      \"/locations/create\",\r\n      \"/locations/[id]\",\r\n      \"/locations/[id]/edit\",\r\n      \"/employees\",\r\n      \"/products\",\r\n      \"/categories\",\r\n      \"/procurement\",\r\n      \"/procurement/requests\",\r\n      \"/procurement/receipts\",\r\n      \"/suppliers\",\r\n    ],\r\n    finance_manager: [\r\n      \"/reports/running-balances\",\r\n      \"/dashboard\",\r\n      \"/dashboard/pos\",\r\n      \"/reports\",\r\n      \"/brands\",\r\n      \"/inventory\",\r\n      \"/inventory/reports\",\r\n      \"/reports/mpesa-transactions\",\r\n      \"/reports/sales-summary\",\r\n      \"/reports/sales-by-item\",\r\n      \"/reports/sales-by-category\",\r\n      \"/reports/sales-by-employee\",\r\n      \"/reports/sales-by-payment-type\",\r\n      \"/reports/mpesa-banking\",\r\n      \"/reports/cash-status\",\r\n      \"/reports/dsa-sales\",\r\n      \"/reports/receipts\",\r\n      \"/reports/shifts\",\r\n      \"/reports/phone-repairs\",\r\n      \"/profile\",\r\n      \"/pos\",\r\n      \"/pos/sessions\",\r\n      \"/sales\",\r\n      \"/customers\",\r\n      \"/procurement\",\r\n      \"/procurement/requests\",\r\n      \"/procurement/receipts\",\r\n      \"/suppliers\",\r\n    ],\r\n    operations_manager: [\r\n      \"/dashboard\",\r\n      \"/dashboard/operations\",\r\n      \"/users\",\r\n      \"/employees\",\r\n      \"/employees/[id]\",\r\n      \"/products\",\r\n      \"/categories\",\r\n      \"/brands\",\r\n      \"/inventory\",\r\n      \"/inventory/transfers\",\r\n      \"/inventory/reports\",\r\n      \"/banking\",\r\n      \"/banking/summary\",\r\n      \"/mpesa\",\r\n      \"/mpesa/transactions\",\r\n      \"/dsa\",\r\n      \"/reports/running-balances\",\r\n      \"/dsa/agents\",\r\n      \"/dsa/assignments\",\r\n      \"/dsa/reconciliations\",\r\n      \"/phone-repairs\",\r\n      \"/expenses\",\r\n      \"/expenses/[id]\",\r\n      \"/expense-analytics\",\r\n      \"/invoices\",\r\n      \"/invoices/[id]\",\r\n      \"/invoices/new\",\r\n      \"/invoices/[id]/edit\",\r\n      \"/reports/mpesa-transactions\",\r\n      \"/reports\",\r\n      \"/reports/sales-summary\",\r\n      \"/reports/sales-by-item\",\r\n      \"/reports/sales-by-category\",\r\n      \"/reports/sales-by-employee\",\r\n      \"/reports/sales-by-payment-type\",\r\n      \"/reports/mpesa-banking\",\r\n      \"/reports/cash-status\",\r\n      \"/reports/dsa-sales\",\r\n      \"/reports/receipts\",\r\n      \"/reports/shifts\",\r\n      \"/reports/phone-repairs\",\r\n      \"/reports/expense-reports\",\r\n      \"/profile\",\r\n      \"/pos\",\r\n      \"/pos/sessions\",\r\n      \"/sales\",\r\n      \"/customers\",\r\n      \"/procurement\",\r\n      \"/procurement/requests\",\r\n      \"/procurement/requests/new\",\r\n      \"/procurement/receipts\",\r\n      \"/suppliers\",\r\n    ],\r\n    assistant_operations_manager: [\r\n      \"/dashboard\",\r\n      \"/dashboard/operations\",\r\n      \"/users\",\r\n      \"/employees\",\r\n      \"/employees/[id]\",\r\n      \"/products\",\r\n      \"/categories\",\r\n      \"/brands\",\r\n      \"/inventory\",\r\n      \"/inventory/transfers\",\r\n      \"/inventory/reports\",\r\n      \"/banking\",\r\n      \"/banking/summary\",\r\n      \"/mpesa\",\r\n      \"/reports/running-balances\",\r\n      \"/mpesa/transactions\",\r\n      \"/dsa\",\r\n      \"/dsa/agents\",\r\n      \"/dsa/assignments\",\r\n      \"/dsa/reconciliations\",\r\n      \"/phone-repairs\",\r\n      \"/expenses\",\r\n      \"/expenses/[id]\",\r\n      \"/reports/mpesa-transactions\",\r\n      \"/expense-analytics\",\r\n      \"/invoices\",\r\n      \"/invoices/[id]\",\r\n      \"/invoices/new\",\r\n      \"/invoices/[id]/edit\",\r\n      \"/reports\",\r\n      \"/reports/sales-summary\",\r\n      \"/reports/sales-by-item\",\r\n      \"/reports/sales-by-category\",\r\n      \"/reports/sales-by-employee\",\r\n      \"/reports/sales-by-payment-type\",\r\n      \"/reports/mpesa-banking\",\r\n      \"/reports/cash-status\",\r\n      \"/reports/dsa-sales\",\r\n      \"/reports/receipts\",\r\n      \"/reports/shifts\",\r\n      \"/reports/phone-repairs\",\r\n      \"/reports/expense-reports\",\r\n      \"/profile\",\r\n      \"/pos\",\r\n      \"/pos/sessions\",\r\n      \"/sales\",\r\n      \"/customers\",\r\n      \"/procurement\",\r\n      \"/procurement/requests\",\r\n      \"/procurement/requests/new\",\r\n      \"/procurement/receipts\",\r\n      \"/suppliers\",\r\n    ],\r\n  };\r\n\r\n  // Check if the role has access to the route or its parent route\r\n  const roleLower = roleName.toLowerCase();\r\n  const accessibleRoutes = roleRouteAccess[roleLower] || [];\r\n\r\n  const hasAccess = accessibleRoutes.some(\r\n    (accessibleRoute) =>\r\n      route === accessibleRoute || route.startsWith(`${accessibleRoute}/`)\r\n  );\r\n\r\n  // Add more detailed debugging\r\n  console.log(\r\n    `[hasRouteAccess] Access result for ${route}: ${\r\n      hasAccess ? \"ALLOWED\" : \"DENIED\"\r\n    }`\r\n  );\r\n\r\n  if (!hasAccess) {\r\n    // Find the closest matching route for better debugging\r\n    const closestRoutes = accessibleRoutes\r\n      .filter((r) => route.includes(r) || r.includes(route))\r\n      .sort((a, b) => {\r\n        // Sort by similarity (length of common substring)\r\n        const aCommon = commonSubstringLength(route, a);\r\n        const bCommon = commonSubstringLength(route, b);\r\n        return bCommon - aCommon;\r\n      })\r\n      .slice(0, 3); // Show top 3 closest matches\r\n\r\n    if (closestRoutes.length > 0) {\r\n      console.log(\r\n        `[hasRouteAccess] Closest matching routes for ${roleLower}:`,\r\n        closestRoutes\r\n      );\r\n    } else {\r\n      console.log(`[hasRouteAccess] No similar routes found for ${roleLower}`);\r\n    }\r\n\r\n    // Log the first 10 accessible routes for this role (to avoid console spam)\r\n    console.log(\r\n      `[hasRouteAccess] First 10 accessible routes for ${roleLower}:`,\r\n      accessibleRoutes.slice(0, 10)\r\n    );\r\n  }\r\n\r\n  return hasAccess;\r\n}\r\n\r\n/**\r\n * Helper function to find the length of the longest common substring\r\n */\r\nfunction commonSubstringLength(str1: string, str2: string): number {\r\n  const s1 = str1.toLowerCase();\r\n  const s2 = str2.toLowerCase();\r\n\r\n  // Simple implementation - length of common characters\r\n  let common = 0;\r\n  for (let i = 0; i < Math.min(s1.length, s2.length); i++) {\r\n    if (s1[i] === s2[i]) {\r\n      common++;\r\n    } else {\r\n      break;\r\n    }\r\n  }\r\n\r\n  return common;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AACM,SAAS;IACd,iDAAiD;IACjD,OAAO;AACT;AAQO,SAAS,eACd,KAAa,EACb,QAAwB;IAExB,IAAI,CAAC,UAAU,OAAO;IAEtB,yBAAyB;IACzB,QAAQ,GAAG,CACT,CAAC,4CAA4C,EAAE,MAAM,QAAQ,EAAE,UAAU;IAG3E,uCAAuC;IACvC,IAAI,SAAS,WAAW,OAAO,eAAe;QAC5C,QAAQ,GAAG,CAAC,CAAC,qDAAqD,CAAC;QACnE,OAAO;IACT;IAEA,8BAA8B;IAC9B,MAAM,kBAA4C;QAChD,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,0EAA0E;YAC1E;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,eAAe;YACb;YACA;YACA;YACA;YACA,+CAA+C;YAC/C;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,0EAA0E;YAC1E;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;YACA,8CAA8C;YAC9C;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,0EAA0E;YAC1E;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,8DAA8D;QAC9D,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA,0EAA0E;YAC1E;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;YACV;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,iBAAiB;YACf;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oBAAoB;YAClB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,8BAA8B;YAC5B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,gEAAgE;IAChE,MAAM,YAAY,SAAS,WAAW;IACtC,MAAM,mBAAmB,eAAe,CAAC,UAAU,IAAI,EAAE;IAEzD,MAAM,YAAY,iBAAiB,IAAI,CACrC,CAAC,kBACC,UAAU,mBAAmB,MAAM,UAAU,CAAC,GAAG,gBAAgB,CAAC,CAAC;IAGvE,8BAA8B;IAC9B,QAAQ,GAAG,CACT,CAAC,mCAAmC,EAAE,MAAM,EAAE,EAC5C,YAAY,YAAY,UACxB;IAGJ,IAAI,CAAC,WAAW;QACd,uDAAuD;QACvD,MAAM,gBAAgB,iBACnB,MAAM,CAAC,CAAC,IAAM,MAAM,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,QAC9C,IAAI,CAAC,CAAC,GAAG;YACR,kDAAkD;YAClD,MAAM,UAAU,sBAAsB,OAAO;YAC7C,MAAM,UAAU,sBAAsB,OAAO;YAC7C,OAAO,UAAU;QACnB,GACC,KAAK,CAAC,GAAG,IAAI,6BAA6B;QAE7C,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,QAAQ,GAAG,CACT,CAAC,6CAA6C,EAAE,UAAU,CAAC,CAAC,EAC5D;QAEJ,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,WAAW;QACzE;QAEA,2EAA2E;QAC3E,QAAQ,GAAG,CACT,CAAC,gDAAgD,EAAE,UAAU,CAAC,CAAC,EAC/D,iBAAiB,KAAK,CAAC,GAAG;IAE9B;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,sBAAsB,IAAY,EAAE,IAAY;IACvD,MAAM,KAAK,KAAK,WAAW;IAC3B,MAAM,KAAK,KAAK,WAAW;IAE3B,sDAAsD;IACtD,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,EAAE,GAAG,MAAM,GAAG,IAAK;QACvD,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;YACnB;QACF,OAAO;YACL;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2675, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/route-data.ts"], "sourcesContent": ["import {\r\n  Banknote,\r\n  BarChart3,\r\n  Building,\r\n  Clock,\r\n  CreditCard,\r\n  FileText,\r\n  LucideIcon,\r\n  Package,\r\n  Phone,\r\n  Scale,\r\n  Settings,\r\n  Shield,\r\n  ShoppingBag,\r\n  Tag,\r\n  Truck,\r\n  User,\r\n  UserCheck,\r\n  Users,\r\n} from \"lucide-react\";\r\n\r\nexport interface RouteItem {\r\n  title: string;\r\n  path: string;\r\n  icon: LucideIcon;\r\n  description: string;\r\n  keywords?: string[];\r\n  roles?: string[];\r\n  group: string;\r\n}\r\n\r\nexport const routeGroups = [\r\n  \"Reports\",\r\n  \"Administration\",\r\n  \"Products\",\r\n  \"Inventory\",\r\n  \"Banking\",\r\n  \"DSA\",\r\n  \"Phone Repairs\",\r\n  \"Settings\",\r\n];\r\n\r\nexport const routes: RouteItem[] = [\r\n  // Reports\r\n  {\r\n    title: \"Sales Summary\",\r\n    path: \"/reports/sales-summary\",\r\n    icon: BarChart3,\r\n    description: \"Overview of sales performance and trends\",\r\n    keywords: [\r\n      \"sales\",\r\n      \"summary\",\r\n      \"overview\",\r\n      \"performance\",\r\n      \"trends\",\r\n      \"dashboard\",\r\n    ],\r\n    group: \"Reports\",\r\n  },\r\n  {\r\n    title: \"Sales by Item\",\r\n    path: \"/reports/sales-by-item\",\r\n    icon: ShoppingBag,\r\n    description: \"Sales breakdown by individual products\",\r\n    keywords: [\"sales\", \"items\", \"products\", \"breakdown\"],\r\n    group: \"Reports\",\r\n  },\r\n  {\r\n    title: \"Sales by Category\",\r\n    path: \"/reports/sales-by-category\",\r\n    icon: Tag,\r\n    description: \"Sales breakdown by product categories\",\r\n    keywords: [\"sales\", \"categories\", \"breakdown\"],\r\n    group: \"Reports\",\r\n  },\r\n  {\r\n    title: \"Banking Report\",\r\n    path: \"/reports/mpesa-banking\",\r\n    icon: Banknote,\r\n    description: \"Banking and financial transactions report\",\r\n    keywords: [\"banking\", \"finance\", \"transactions\", \"mpesa\"],\r\n    group: \"Reports\",\r\n  },\r\n  {\r\n    title: \"Cash Status\",\r\n    path: \"/reports/cash-status\",\r\n    icon: Banknote,\r\n    description: \"Cash position and movements across all locations\",\r\n    keywords: [\"cash\", \"status\", \"position\", \"movements\", \"finance\"],\r\n    group: \"Reports\",\r\n  },\r\n\r\n  {\r\n    title: \"DSA Sales\",\r\n    path: \"/reports/dsa-sales\",\r\n    icon: UserCheck,\r\n    description: \"Direct Sales Agent performance and sales\",\r\n    keywords: [\"dsa\", \"sales\", \"agents\", \"direct sales\"],\r\n    group: \"Reports\",\r\n  },\r\n  {\r\n    title: \"Receipts\",\r\n    path: \"/reports/receipts\",\r\n    icon: FileText,\r\n    description: \"Sales receipts and transaction records\",\r\n    keywords: [\"receipts\", \"transactions\", \"records\"],\r\n    group: \"Reports\",\r\n  },\r\n  {\r\n    title: \"Shifts\",\r\n    path: \"/reports/shifts\",\r\n    icon: Clock,\r\n    description: \"POS session and shift reports\",\r\n    keywords: [\"shifts\", \"sessions\", \"pos\"],\r\n    group: \"Reports\",\r\n  },\r\n  {\r\n    title: \"Phone Repairs\",\r\n    path: \"/reports/phone-repairs\",\r\n    icon: Phone,\r\n    description: \"Phone repair tracking and status\",\r\n    keywords: [\"phone\", \"repairs\", \"tracking\", \"status\"],\r\n    group: \"Reports\",\r\n  },\r\n  {\r\n    title: \"Stock Valuation\",\r\n    path: \"/inventory/valuation\",\r\n    icon: Scale,\r\n    description: \"Detailed stock valuation report by method\",\r\n    keywords: [\"inventory\", \"valuation\", \"stock\", \"fifo\", \"lifo\", \"weighted average\"],\r\n    group: \"Reports\",\r\n  },\r\n\r\n  // Administration\r\n  {\r\n    title: \"Tenants\",\r\n    path: \"/tenants\",\r\n    icon: Building,\r\n    description: \"Manage company tenants\",\r\n    keywords: [\"tenants\", \"companies\", \"manage\"],\r\n    roles: [\"super_admin\"],\r\n    group: \"Administration\",\r\n  },\r\n  {\r\n    title: \"Users\",\r\n    path: \"/users\",\r\n    icon: Users,\r\n    description: \"Manage system users and permissions\",\r\n    keywords: [\"users\", \"staff\", \"permissions\", \"accounts\"],\r\n    group: \"Administration\",\r\n  },\r\n  {\r\n    title: \"Roles\",\r\n    path: \"/roles\",\r\n    icon: Shield,\r\n    description: \"Manage user roles and permissions\",\r\n    keywords: [\"roles\", \"permissions\", \"access\"],\r\n    roles: [\"super_admin\", \"tenant_admin\", \"company_admin\"],\r\n    group: \"Administration\",\r\n  },\r\n  {\r\n    title: \"Branches\",\r\n    path: \"/branches\",\r\n    icon: Building,\r\n    description: \"Manage company branches and locations\",\r\n    keywords: [\"branches\", \"locations\", \"stores\"],\r\n    group: \"Administration\",\r\n  },\r\n\r\n  // Products\r\n  {\r\n    title: \"Products\",\r\n    path: \"/products\",\r\n    icon: Package,\r\n    description: \"Manage product catalog\",\r\n    keywords: [\"products\", \"catalog\", \"items\"],\r\n    group: \"Products\",\r\n  },\r\n  {\r\n    title: \"Categories\",\r\n    path: \"/categories\",\r\n    icon: Tag,\r\n    description: \"Manage product categories\",\r\n    keywords: [\"categories\", \"classification\", \"groups\"],\r\n    group: \"Products\",\r\n  },\r\n\r\n  // Inventory\r\n  {\r\n    title: \"Inventory\",\r\n    path: \"/inventory\",\r\n    icon: Package,\r\n    description: \"Manage product inventory and stock\",\r\n    keywords: [\"inventory\", \"stock\", \"products\"],\r\n    group: \"Inventory\",\r\n  },\r\n  {\r\n    title: \"Stock Transfers\",\r\n    path: \"/inventory/transfers\",\r\n    icon: Truck,\r\n    description: \"Manage inventory transfers between branches\",\r\n    keywords: [\"transfers\", \"stock\", \"movement\", \"branches\"],\r\n    group: \"Inventory\",\r\n  },\r\n\r\n  {\r\n    title: \"Inventory Reports\",\r\n    path: \"/inventory/reports\",\r\n    icon: BarChart3,\r\n    description: \"Inventory reports and analytics\",\r\n    keywords: [\"inventory\", \"reports\", \"analytics\", \"stock\"],\r\n    group: \"Inventory\",\r\n  },\r\n\r\n  // Banking\r\n  {\r\n    title: \"Banking Management\",\r\n    path: \"/banking\",\r\n    icon: Banknote,\r\n    description: \"Manage banking records and view summary analytics\",\r\n    keywords: [\"banking\", \"summary\", \"transactions\", \"finance\", \"analytics\", \"reports\", \"mpesa\", \"bank\", \"agent\"],\r\n    group: \"Banking\",\r\n  },\r\n  {\r\n    title: \"M-Pesa Transactions\",\r\n    path: \"/mpesa/transactions\",\r\n    icon: Banknote,\r\n    description: \"Manage M-Pesa transactions\",\r\n    keywords: [\"mpesa\", \"transactions\", \"mobile money\", \"finance\"],\r\n    group: \"Banking\",\r\n  },\r\n  {\r\n    title: \"Float Movements\",\r\n    path: \"/float/movements\",\r\n    icon: Banknote,\r\n    description: \"Track float movements and transfers\",\r\n    keywords: [\"float\", \"movements\", \"transfers\", \"mpesa\"],\r\n    group: \"Banking\",\r\n  },\r\n  {\r\n    title: \"Float Reconciliations\",\r\n    path: \"/float/reconciliations\",\r\n    icon: FileText,\r\n    description: \"Reconcile float transactions\",\r\n    keywords: [\"float\", \"reconciliations\", \"balance\"],\r\n    group: \"Banking\",\r\n  },\r\n\r\n  // DSA\r\n  {\r\n    title: \"DSA Agents\",\r\n    path: \"/dsa/customers\",\r\n    icon: Users,\r\n    description: \"Manage Direct Sales Agents\",\r\n    keywords: [\"dsa\", \"customers\", \"sales\", \"direct\"],\r\n    group: \"DSA\",\r\n  },\r\n  {\r\n    title: \"DSA Assignments\",\r\n    path: \"/dsa/assignments\",\r\n    icon: FileText,\r\n    description: \"Manage DSA product assignments\",\r\n    keywords: [\"dsa\", \"assignments\", \"products\"],\r\n    group: \"DSA\",\r\n  },\r\n  {\r\n    title: \"DSA Reconciliations\",\r\n    path: \"/dsa/reconciliations\",\r\n    icon: FileText,\r\n    description: \"Reconcile DSA sales and inventory\",\r\n    keywords: [\"dsa\", \"reconciliations\", \"sales\"],\r\n    group: \"DSA\",\r\n  },\r\n\r\n  // Phone Repairs\r\n  {\r\n    title: \"Phone Repairs\",\r\n    path: \"/phone-repairs\",\r\n    icon: Phone,\r\n    description: \"Manage phone repair tickets and status\",\r\n    keywords: [\"phone\", \"repairs\", \"tickets\", \"status\"],\r\n    group: \"Phone Repairs\",\r\n  },\r\n\r\n  // Settings\r\n  {\r\n    title: \"System Settings\",\r\n    path: \"/settings/system\",\r\n    icon: Settings,\r\n    description: \"Configure system settings\",\r\n    keywords: [\"settings\", \"system\", \"configuration\"],\r\n    roles: [\"super_admin\"],\r\n    group: \"Settings\",\r\n  },\r\n  {\r\n    title: \"Company Settings\",\r\n    path: \"/settings/company\",\r\n    icon: Building,\r\n    description: \"Configure company settings\",\r\n    keywords: [\"settings\", \"company\", \"configuration\"],\r\n    roles: [\"tenant_admin\", \"company_admin\"],\r\n    group: \"Settings\",\r\n  },\r\n  {\r\n    title: \"Payment Methods\",\r\n    path: \"/settings/payment-methods\",\r\n    icon: CreditCard,\r\n    description: \"Configure payment methods\",\r\n    keywords: [\"settings\", \"payment\", \"methods\"],\r\n    roles: [\"super_admin\", \"tenant_admin\", \"company_admin\"],\r\n    group: \"Settings\",\r\n  },\r\n  {\r\n    title: \"System Health\",\r\n    path: \"/settings/health\",\r\n    icon: Shield,\r\n    description: \"Monitor system health and status\",\r\n    keywords: [\"health\", \"status\", \"monitoring\"],\r\n    roles: [\"super_admin\"],\r\n    group: \"Settings\",\r\n  },\r\n  {\r\n    title: \"User Profile\",\r\n    path: \"/profile\",\r\n    icon: User,\r\n    description: \"Manage your user profile\",\r\n    keywords: [\"profile\", \"user\", \"account\", \"settings\"],\r\n    group: \"Settings\",\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AA+BO,MAAM,cAAc;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,SAAsB;IACjC,UAAU;IACV;QACE,OAAO;QACP,MAAM;QACN,MAAM,qNAAA,CAAA,YAAS;QACf,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,uNAAA,CAAA,cAAW;QACjB,aAAa;QACb,UAAU;YAAC;YAAS;YAAS;YAAY;SAAY;QACrD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,mMAAA,CAAA,MAAG;QACT,aAAa;QACb,UAAU;YAAC;YAAS;YAAc;SAAY;QAC9C,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,aAAa;QACb,UAAU;YAAC;YAAW;YAAW;YAAgB;SAAQ;QACzD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,aAAa;QACb,UAAU;YAAC;YAAQ;YAAU;YAAY;YAAa;SAAU;QAChE,OAAO;IACT;IAEA;QACE,OAAO;QACP,MAAM;QACN,MAAM,mNAAA,CAAA,YAAS;QACf,aAAa;QACb,UAAU;YAAC;YAAO;YAAS;YAAU;SAAe;QACpD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;QACd,aAAa;QACb,UAAU;YAAC;YAAY;YAAgB;SAAU;QACjD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,UAAU;YAAC;YAAU;YAAY;SAAM;QACvC,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,UAAU;YAAC;YAAS;YAAW;YAAY;SAAS;QACpD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,UAAU;YAAC;YAAa;YAAa;YAAS;YAAQ;YAAQ;SAAmB;QACjF,OAAO;IACT;IAEA,iBAAiB;IACjB;QACE,OAAO;QACP,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,aAAa;QACb,UAAU;YAAC;YAAW;YAAa;SAAS;QAC5C,OAAO;YAAC;SAAc;QACtB,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,UAAU;YAAC;YAAS;YAAS;YAAe;SAAW;QACvD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,aAAa;QACb,UAAU;YAAC;YAAS;YAAe;SAAS;QAC5C,OAAO;YAAC;YAAe;YAAgB;SAAgB;QACvD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,aAAa;QACb,UAAU;YAAC;YAAY;YAAa;SAAS;QAC7C,OAAO;IACT;IAEA,WAAW;IACX;QACE,OAAO;QACP,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;QACb,aAAa;QACb,UAAU;YAAC;YAAY;YAAW;SAAQ;QAC1C,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,mMAAA,CAAA,MAAG;QACT,aAAa;QACb,UAAU;YAAC;YAAc;YAAkB;SAAS;QACpD,OAAO;IACT;IAEA,YAAY;IACZ;QACE,OAAO;QACP,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;QACb,aAAa;QACb,UAAU;YAAC;YAAa;YAAS;SAAW;QAC5C,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,UAAU;YAAC;YAAa;YAAS;YAAY;SAAW;QACxD,OAAO;IACT;IAEA;QACE,OAAO;QACP,MAAM;QACN,MAAM,qNAAA,CAAA,YAAS;QACf,aAAa;QACb,UAAU;YAAC;YAAa;YAAW;YAAa;SAAQ;QACxD,OAAO;IACT;IAEA,UAAU;IACV;QACE,OAAO;QACP,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,aAAa;QACb,UAAU;YAAC;YAAW;YAAW;YAAgB;YAAW;YAAa;YAAW;YAAS;YAAQ;SAAQ;QAC7G,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,aAAa;QACb,UAAU;YAAC;YAAS;YAAgB;YAAgB;SAAU;QAC9D,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,aAAa;QACb,UAAU;YAAC;YAAS;YAAa;YAAa;SAAQ;QACtD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;QACd,aAAa;QACb,UAAU;YAAC;YAAS;YAAmB;SAAU;QACjD,OAAO;IACT;IAEA,MAAM;IACN;QACE,OAAO;QACP,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,UAAU;YAAC;YAAO;YAAa;YAAS;SAAS;QACjD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;QACd,aAAa;QACb,UAAU;YAAC;YAAO;YAAe;SAAW;QAC5C,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;QACd,aAAa;QACb,UAAU;YAAC;YAAO;YAAmB;SAAQ;QAC7C,OAAO;IACT;IAEA,gBAAgB;IAChB;QACE,OAAO;QACP,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,UAAU;YAAC;YAAS;YAAW;YAAW;SAAS;QACnD,OAAO;IACT;IAEA,WAAW;IACX;QACE,OAAO;QACP,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,aAAa;QACb,UAAU;YAAC;YAAY;YAAU;SAAgB;QACjD,OAAO;YAAC;SAAc;QACtB,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,aAAa;QACb,UAAU;YAAC;YAAY;YAAW;SAAgB;QAClD,OAAO;YAAC;YAAgB;SAAgB;QACxC,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,qNAAA,CAAA,aAAU;QAChB,aAAa;QACb,UAAU;YAAC;YAAY;YAAW;SAAU;QAC5C,OAAO;YAAC;YAAe;YAAgB;SAAgB;QACvD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,aAAa;QACb,UAAU;YAAC;YAAU;YAAU;SAAa;QAC5C,OAAO;YAAC;SAAc;QACtB,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;QACV,aAAa;QACb,UAAU;YAAC;YAAW;YAAQ;YAAW;SAAW;QACpD,OAAO;IACT;CACD", "debugId": null}}, {"offset": {"line": 3159, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/global-search.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearch } from \"@/components/providers/search-provider\";\r\nimport { <PERSON><PERSON>, DialogContent, DialogTitle } from \"@/components/ui/dialog\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { hasRouteAccess } from \"@/lib/role-utils\";\r\nimport { RouteItem, routeGroups, routes } from \"@/lib/route-data\";\r\nimport { Command } from \"cmdk\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\n\r\nexport function GlobalSearch() {\r\n  const { isOpen, closeSearch } = useSearch();\r\n  const router = useRouter();\r\n  const { data: user } = useCurrentUser();\r\n  const [search, setSearch] = useState(\"\");\r\n  const [filteredRoutes, setFilteredRoutes] = useState<RouteItem[]>([]);\r\n  const inputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Filter routes based on search query and user role\r\n  useEffect(() => {\r\n    if (!isOpen) return;\r\n\r\n    const userRole = user?.role_name?.toLowerCase() || \"\";\r\n\r\n    // Filter routes based on search query and user role\r\n    const filtered = routes.filter((route) => {\r\n      // Check if user has access to this route\r\n      if (route.roles && !route.roles.includes(userRole)) {\r\n        return false;\r\n      }\r\n\r\n      // Check if route has access based on role utils\r\n      if (!hasRouteAccess(route.path, userRole)) {\r\n        return false;\r\n      }\r\n\r\n      // If no search query, include all accessible routes\r\n      if (!search) return true;\r\n\r\n      // Search in title, description, path, and keywords\r\n      const searchLower = search.toLowerCase();\r\n      return (\r\n        route.title.toLowerCase().includes(searchLower) ||\r\n        route.description.toLowerCase().includes(searchLower) ||\r\n        route.path.toLowerCase().includes(searchLower) ||\r\n        route.keywords?.some((keyword) =>\r\n          keyword.toLowerCase().includes(searchLower)\r\n        )\r\n      );\r\n    });\r\n\r\n    setFilteredRoutes(filtered);\r\n  }, [search, isOpen, user]);\r\n\r\n  // Focus input when dialog opens\r\n  useEffect(() => {\r\n    if (isOpen && inputRef.current) {\r\n      setTimeout(() => {\r\n        inputRef.current?.focus();\r\n      }, 100);\r\n    } else {\r\n      setSearch(\"\");\r\n    }\r\n  }, [isOpen]);\r\n\r\n  // Handle route selection\r\n  const handleSelect = (route: RouteItem) => {\r\n    router.push(route.path);\r\n    closeSearch();\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={(open) => !open && closeSearch()}>\r\n      <DialogContent className=\"p-0 gap-0 max-w-3xl max-h-[85vh] overflow-hidden\">\r\n        <div className=\"sr-only\">\r\n          <DialogTitle>Search</DialogTitle>\r\n        </div>\r\n        <Command className=\"rounded-lg border shadow-md\">\r\n          <div\r\n            className=\"flex items-center border-b px-3\"\r\n            cmdk-input-wrapper=\"\"\r\n          >\r\n            <Command.Input\r\n              ref={inputRef}\r\n              value={search}\r\n              onValueChange={setSearch}\r\n              placeholder=\"Search for pages, features, or settings...\"\r\n              className=\"flex h-12 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50\"\r\n            />\r\n          </div>\r\n          <Command.List className=\"max-h-[500px] overflow-y-auto overflow-x-hidden\">\r\n            {search.length > 0 && filteredRoutes.length === 0 && (\r\n              <Command.Empty className=\"py-6 text-center text-sm\">\r\n                No results found.\r\n              </Command.Empty>\r\n            )}\r\n\r\n            {routeGroups.map((group) => {\r\n              const groupRoutes = filteredRoutes.filter(\r\n                (route) => route.group === group\r\n              );\r\n\r\n              if (groupRoutes.length === 0) return null;\r\n\r\n              return (\r\n                <Command.Group\r\n                  key={group}\r\n                  heading={group}\r\n                  className=\"px-2 py-1\"\r\n                >\r\n                  {groupRoutes.map((route) => (\r\n                    <Command.Item\r\n                      key={route.path}\r\n                      value={`${route.title} ${route.path}`}\r\n                      onSelect={() => handleSelect(route)}\r\n                      className=\"flex items-center gap-2 px-2 py-1.5 rounded-md text-sm cursor-pointer hover:bg-accent data-[selected=true]:bg-accent\"\r\n                    >\r\n                      <div className=\"flex h-8 w-8 items-center justify-center rounded-md border bg-background\">\r\n                        <route.icon className=\"h-4 w-4\" />\r\n                      </div>\r\n                      <div className=\"flex flex-col\">\r\n                        <span className=\"font-medium\">{route.title}</span>\r\n                        <span className=\"text-xs text-muted-foreground\">\r\n                          {route.description}\r\n                        </span>\r\n                      </div>\r\n                    </Command.Item>\r\n                  ))}\r\n                </Command.Group>\r\n              );\r\n            })}\r\n          </Command.List>\r\n\r\n          <div className=\"border-t p-2\">\r\n            <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\r\n              <div className=\"flex gap-2\">\r\n                <span>Navigate with ↑↓</span>\r\n                <span>Select with Enter</span>\r\n              </div>\r\n              <div>\r\n                <kbd className=\"rounded border bg-muted px-1.5 font-mono text-[10px]\">\r\n                  ESC\r\n                </kbd>{\" \"}\r\n                to close\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACpE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,QAAQ;YAEb,MAAM,WAAW,MAAM,WAAW,iBAAiB;YAEnD,oDAAoD;YACpD,MAAM,WAAW,8HAAA,CAAA,SAAM,CAAC,MAAM;mDAAC,CAAC;oBAC9B,yCAAyC;oBACzC,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,KAAK,CAAC,QAAQ,CAAC,WAAW;wBAClD,OAAO;oBACT;oBAEA,gDAAgD;oBAChD,IAAI,CAAC,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,IAAI,EAAE,WAAW;wBACzC,OAAO;oBACT;oBAEA,oDAAoD;oBACpD,IAAI,CAAC,QAAQ,OAAO;oBAEpB,mDAAmD;oBACnD,MAAM,cAAc,OAAO,WAAW;oBACtC,OACE,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACnC,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACzC,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,MAAM,QAAQ,EAAE;2DAAK,CAAC,UACpB,QAAQ,WAAW,GAAG,QAAQ,CAAC;;gBAGrC;;YAEA,kBAAkB;QACpB;iCAAG;QAAC;QAAQ;QAAQ;KAAK;IAEzB,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,UAAU,SAAS,OAAO,EAAE;gBAC9B;8CAAW;wBACT,SAAS,OAAO,EAAE;oBACpB;6CAAG;YACL,OAAO;gBACL,UAAU;YACZ;QACF;iCAAG;QAAC;KAAO;IAEX,yBAAyB;IACzB,MAAM,eAAe,CAAC;QACpB,OAAO,IAAI,CAAC,MAAM,IAAI;QACtB;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACrD,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAEf,6LAAC,yIAAA,CAAA,UAAO;oBAAC,WAAU;;sCACjB,6LAAC;4BACC,WAAU;4BACV,sBAAmB;sCAEnB,cAAA,6LAAC,yIAAA,CAAA,UAAO,CAAC,KAAK;gCACZ,KAAK;gCACL,OAAO;gCACP,eAAe;gCACf,aAAY;gCACZ,WAAU;;;;;;;;;;;sCAGd,6LAAC,yIAAA,CAAA,UAAO,CAAC,IAAI;4BAAC,WAAU;;gCACrB,OAAO,MAAM,GAAG,KAAK,eAAe,MAAM,KAAK,mBAC9C,6LAAC,yIAAA,CAAA,UAAO,CAAC,KAAK;oCAAC,WAAU;8CAA2B;;;;;;gCAKrD,8HAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC;oCAChB,MAAM,cAAc,eAAe,MAAM,CACvC,CAAC,QAAU,MAAM,KAAK,KAAK;oCAG7B,IAAI,YAAY,MAAM,KAAK,GAAG,OAAO;oCAErC,qBACE,6LAAC,yIAAA,CAAA,UAAO,CAAC,KAAK;wCAEZ,SAAS;wCACT,WAAU;kDAET,YAAY,GAAG,CAAC,CAAC,sBAChB,6LAAC,yIAAA,CAAA,UAAO,CAAC,IAAI;gDAEX,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE;gDACrC,UAAU,IAAM,aAAa;gDAC7B,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,MAAM,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAExB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAe,MAAM,KAAK;;;;;;0EAC1C,6LAAC;gEAAK,WAAU;0EACb,MAAM,WAAW;;;;;;;;;;;;;+CAXjB,MAAM,IAAI;;;;;uCANd;;;;;gCAwBX;;;;;;;sCAGF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAuD;;;;;;4CAE/D;4CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B;GA7IgB;;QACkB,wJAAA,CAAA,YAAS;QAC1B,qIAAA,CAAA,YAAS;QACD,kJAAA,CAAA,iBAAc;;;KAHvB", "debugId": null}}, {"offset": {"line": 3458, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/react-query.ts"], "sourcesContent": ["import { QueryClient } from '@tanstack/react-query';\r\n\r\n// Create a query client with default options\r\nexport const queryClient = new QueryClient({\r\n  defaultOptions: {\r\n    queries: {\r\n      refetchOnWindowFocus: false,\r\n      retry: 1,\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n    },\r\n  },\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM,cAAc,IAAI,gLAAA,CAAA,cAAW,CAAC;IACzC,gBAAgB;QACd,SAAS;YACP,sBAAsB;YACtB,OAAO;YACP,WAAW,IAAI,KAAK;QACtB;IACF;AACF", "debugId": null}}, {"offset": {"line": 3481, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/providers/api-redirect-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, useEffect } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport apiClient from \"@/lib/api-client\";\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { queryClient } from \"@/lib/react-query\";\r\n\r\ninterface ApiRedirectProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function ApiRedirectProvider({ children }: ApiRedirectProviderProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n\r\n  useEffect(() => {\r\n    // Set up the redirect callback to use Next.js router\r\n    apiClient.setRedirectCallback((path: string) => {\r\n      router.replace(path);\r\n    });\r\n\r\n    // Set the query client for API client to use for cache invalidation\r\n    apiClient.setQueryClient(queryClient);\r\n  }, [router]);\r\n\r\n  // Set auth token for API requests when it changes\r\n  useEffect(() => {\r\n    console.log(\"ApiRedirectProvider: Auth state change\", {\r\n      accessToken: accessToken ? \"exists\" : \"missing\",\r\n      isInitialized,\r\n    });\r\n\r\n    if (isInitialized) {\r\n      // If we have an access token in cookies, use it\r\n      if (accessToken) {\r\n        console.log(\"ApiRedirectProvider: Setting auth token from cookies\");\r\n        apiClient.setAuthToken(accessToken);\r\n      }\r\n      // Otherwise, try to get it from localStorage as a fallback\r\n      else if (typeof window !== 'undefined') {\r\n        const localToken = localStorage.getItem('token') || localStorage.getItem('accessToken');\r\n        if (localToken) {\r\n          console.log(\"ApiRedirectProvider: Setting auth token from localStorage\");\r\n          apiClient.setAuthToken(localToken);\r\n        }\r\n      }\r\n    }\r\n  }, [accessToken, isInitialized]);\r\n\r\n  // Handle authentication redirects - only on initial mount and auth state changes\r\n  useEffect(() => {\r\n    if (!isInitialized) {\r\n      console.log(\"ApiRedirectProvider: Auth state not initialized yet\");\r\n      return; // Wait until auth state is initialized\r\n    }\r\n\r\n    console.log(\"ApiRedirectProvider: Auth state initialized\", {\r\n      accessToken: accessToken ? \"exists\" : \"missing\",\r\n    });\r\n\r\n    // Set up API client with the current token\r\n    if (accessToken) {\r\n      apiClient.setAuthToken(accessToken);\r\n    }\r\n    // Try localStorage as fallback\r\n    else if (typeof window !== 'undefined') {\r\n      const localToken = localStorage.getItem('token') || localStorage.getItem('accessToken');\r\n      if (localToken) {\r\n        console.log(\"ApiRedirectProvider: Setting auth token from localStorage (second effect)\");\r\n        apiClient.setAuthToken(localToken);\r\n      }\r\n    }\r\n  }, [accessToken, isInitialized]);\r\n\r\n  // We're disabling client-side route-specific redirects to avoid redirect loops\r\n  // The middleware will handle redirects instead\r\n  useEffect(() => {\r\n    if (!isInitialized) return;\r\n\r\n    console.log(\"ApiRedirectProvider: Auth state initialized\", {\r\n      pathname,\r\n      accessToken: accessToken ? \"exists\" : \"missing\",\r\n    });\r\n\r\n    // We're not redirecting here anymore to avoid conflicts with middleware\r\n    // This helps prevent redirect loops between login and dashboard\r\n  }, [accessToken, isInitialized, pathname]);\r\n\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,oBAAoB,EAAE,QAAQ,EAA4B;;IACxE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,qDAAqD;YACrD,8HAAA,CAAA,UAAS,CAAC,mBAAmB;iDAAC,CAAC;oBAC7B,OAAO,OAAO,CAAC;gBACjB;;YAEA,oEAAoE;YACpE,8HAAA,CAAA,UAAS,CAAC,cAAc,CAAC,+HAAA,CAAA,cAAW;QACtC;wCAAG;QAAC;KAAO;IAEX,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,QAAQ,GAAG,CAAC,0CAA0C;gBACpD,aAAa,cAAc,WAAW;gBACtC;YACF;YAEA,IAAI,eAAe;gBACjB,gDAAgD;gBAChD,IAAI,aAAa;oBACf,QAAQ,GAAG,CAAC;oBACZ,8HAAA,CAAA,UAAS,CAAC,YAAY,CAAC;gBACzB,OAEK,wCAAmC;oBACtC,MAAM,aAAa,aAAa,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC;oBACzE,IAAI,YAAY;wBACd,QAAQ,GAAG,CAAC;wBACZ,8HAAA,CAAA,UAAS,CAAC,YAAY,CAAC;oBACzB;gBACF;YACF;QACF;wCAAG;QAAC;QAAa;KAAc;IAE/B,iFAAiF;IACjF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,CAAC,eAAe;gBAClB,QAAQ,GAAG,CAAC;gBACZ,QAAQ,uCAAuC;YACjD;YAEA,QAAQ,GAAG,CAAC,+CAA+C;gBACzD,aAAa,cAAc,WAAW;YACxC;YAEA,2CAA2C;YAC3C,IAAI,aAAa;gBACf,8HAAA,CAAA,UAAS,CAAC,YAAY,CAAC;YACzB,OAEK,wCAAmC;gBACtC,MAAM,aAAa,aAAa,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC;gBACzE,IAAI,YAAY;oBACd,QAAQ,GAAG,CAAC;oBACZ,8HAAA,CAAA,UAAS,CAAC,YAAY,CAAC;gBACzB;YACF;QACF;wCAAG;QAAC;QAAa;KAAc;IAE/B,+EAA+E;IAC/E,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,CAAC,eAAe;YAEpB,QAAQ,GAAG,CAAC,+CAA+C;gBACzD;gBACA,aAAa,cAAc,WAAW;YACxC;QAEA,wEAAwE;QACxE,gEAAgE;QAClE;wCAAG;QAAC;QAAa;QAAe;KAAS;IAEzC,qBAAO;kBAAG;;AACZ;GA/EgB;;QACC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACW,wIAAA,CAAA,gBAAa;;;KAHtC", "debugId": null}}, {"offset": {"line": 3607, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 3670, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,6LAAC,8KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MAjBS;AAmBT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,8KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf;OAVS", "debugId": null}}, {"offset": {"line": 3856, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/context/auth-error-context.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, use<PERSON>ontext, useState, ReactNode } from \"react\";\r\nimport { \r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { useQueryClient } from \"@tanstack/react-query\";\r\n\r\ninterface AuthErrorContextType {\r\n  showAuthError: (message: string) => void;\r\n  clearAuthError: () => void;\r\n}\r\n\r\nconst AuthErrorContext = createContext<AuthErrorContextType | undefined>(undefined);\r\n\r\nexport function AuthErrorProvider({ children }: { children: ReactNode }) {\r\n  const [error, setError] = useState<string | null>(null);\r\n  const router = useRouter();\r\n  const { clearTokens } = useAuthTokens();\r\n  const queryClient = useQueryClient();\r\n\r\n  const showAuthError = (message: string) => {\r\n    setError(message);\r\n  };\r\n\r\n  const clearAuthError = () => {\r\n    setError(null);\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    // Clear tokens\r\n    clearTokens();\r\n    \r\n    // Clear auth header\r\n    if (typeof window !== \"undefined\") {\r\n      localStorage.removeItem(\"token\");\r\n      localStorage.removeItem(\"refreshToken\");\r\n    }\r\n    \r\n    // Clear query cache\r\n    queryClient.clear();\r\n    \r\n    // Clear the error\r\n    clearAuthError();\r\n    \r\n    // Redirect to login\r\n    router.replace(\"/login\");\r\n  };\r\n\r\n  return (\r\n    <AuthErrorContext.Provider value={{ showAuthError, clearAuthError }}>\r\n      {children}\r\n      \r\n      <AlertDialog open={!!error} onOpenChange={() => setError(null)}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Authentication Error</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              {error || \"Your session has expired. Please log in again.\"}\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogAction onClick={handleLogout}>\r\n              Go to Login\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </AuthErrorContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useAuthError() {\r\n  const context = useContext(AuthErrorContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useAuthError must be used within an AuthErrorProvider\");\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AASA;AACA;AACA;;;AAdA;;;;;;AAqBA,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAoC;AAElE,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;;IACrE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IACpC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,gBAAgB,CAAC;QACrB,SAAS;IACX;IAEA,MAAM,iBAAiB;QACrB,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,eAAe;QACf;QAEA,oBAAoB;QACpB,wCAAmC;YACjC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B;QAEA,oBAAoB;QACpB,YAAY,KAAK;QAEjB,kBAAkB;QAClB;QAEA,oBAAoB;QACpB,OAAO,OAAO,CAAC;IACjB;IAEA,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE;YAAe;QAAe;;YAC/D;0BAED,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM,CAAC,CAAC;gBAAO,cAAc,IAAM,SAAS;0BACvD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;8CACpB,SAAS;;;;;;;;;;;;sCAGd,6LAAC,8IAAA,CAAA,oBAAiB;sCAChB,cAAA,6LAAC,8IAAA,CAAA,oBAAiB;gCAAC,SAAS;0CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;GAvDgB;;QAEC,qIAAA,CAAA,YAAS;QACA,wIAAA,CAAA,gBAAa;QACjB,yLAAA,CAAA,iBAAc;;;KAJpB;AAyDT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 3996, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/providers/auth-error-handler.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, useEffect } from \"react\";\r\nimport { useAuthError } from \"@/features/auth/context/auth-error-context\";\r\nimport apiClient from \"@/lib/api-client\";\r\n\r\ninterface AuthErrorHandlerProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function AuthErrorHandler({ children }: AuthErrorHandlerProps) {\r\n  const { showAuthError } = useAuthError();\r\n\r\n  useEffect(() => {\r\n    // Set up the auth error callback\r\n    apiClient.setAuthErrorCallback((message: string) => {\r\n      showAuthError(message);\r\n    });\r\n    \r\n    // Cleanup function\r\n    return () => {\r\n      // Reset the auth error callback to a no-op function\r\n      apiClient.setAuthErrorCallback((message: string) => {\r\n        console.warn(\"Auth error callback not set, auth error:\", message);\r\n      });\r\n    };\r\n  }, [showAuthError]);\r\n\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;;IAClE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kKAAA,CAAA,eAAY,AAAD;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,iCAAiC;YACjC,8HAAA,CAAA,UAAS,CAAC,oBAAoB;8CAAC,CAAC;oBAC9B,cAAc;gBAChB;;YAEA,mBAAmB;YACnB;8CAAO;oBACL,oDAAoD;oBACpD,8HAAA,CAAA,UAAS,CAAC,oBAAoB;sDAAC,CAAC;4BAC9B,QAAQ,IAAI,CAAC,4CAA4C;wBAC3D;;gBACF;;QACF;qCAAG;QAAC;KAAc;IAElB,qBAAO;kBAAG;;AACZ;GAnBgB;;QACY,kKAAA,CAAA,eAAY;;;KADxB", "debugId": null}}, {"offset": {"line": 4056, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useTheme } from \"next-themes\"\r\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  const { theme = \"system\" } = useTheme()\r\n\r\n  return (\r\n    <Sonner\r\n      theme={theme as ToasterProps[\"theme\"]}\r\n      className=\"toaster group\"\r\n      style={\r\n        {\r\n          \"--normal-bg\": \"var(--popover)\",\r\n          \"--normal-text\": \"var(--popover-foreground)\",\r\n          \"--normal-border\": \"var(--border)\",\r\n        } as React.CSSProperties\r\n      }\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Toaster }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf;GAjBM;;QACyB,mJAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}, {"offset": {"line": 4103, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/users/api/rbac-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\n\r\n// Types\r\nexport interface Grant {\r\n  id: number;\r\n  role: string;\r\n  resource: string;\r\n  action: string;\r\n  attributes: any;\r\n}\r\n\r\nexport interface GroupedGrants {\r\n  [resource: string]: {\r\n    [action: string]: boolean;\r\n  };\r\n}\r\n\r\nexport interface ResourcesAndActions {\r\n  resources: string[];\r\n  actions: string[];\r\n  resourceActions: {\r\n    [resource: string]: string[];\r\n  };\r\n}\r\n\r\nexport interface GrantsResponse {\r\n  grants: {\r\n    [role: string]: {\r\n      [resource: string]: {\r\n        [action: string]: any;\r\n      };\r\n    };\r\n  };\r\n  raw: Grant[];\r\n}\r\n\r\nexport interface RoleGrantsResponse {\r\n  role: string;\r\n  grants: GroupedGrants;\r\n  raw: Grant[];\r\n}\r\n\r\nexport interface Role {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  Permissions: Permission[];\r\n}\r\n\r\nexport interface Permission {\r\n  id: number;\r\n  name: string;\r\n  resource: string;\r\n  action: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface RoleGrant {\r\n  resource: string;\r\n  action: string;\r\n  scope?: \"any\" | \"own\";\r\n}\r\n\r\nexport const rbacService = {\r\n  // Get all roles\r\n  getRoles: async () => {\r\n    return apiClient.get<Role[]>(\"/rbac/roles\");\r\n  },\r\n\r\n  // Get role by ID\r\n  getRole: async (id: string | number) => {\r\n    return apiClient.get<Role>(`/rbac/roles/${id}`);\r\n  },\r\n\r\n  // Create role\r\n  createRole: async (roleData: any) => {\r\n    return apiClient.post<Role>(\"/rbac/roles\", roleData);\r\n  },\r\n\r\n  // Update role\r\n  updateRole: async (id: string | number, roleData: any) => {\r\n    return apiClient.put<Role>(`/rbac/roles/${id}`, roleData);\r\n  },\r\n\r\n  // Delete role\r\n  deleteRole: async (id: string | number) => {\r\n    return apiClient.delete<void>(`/rbac/roles/${id}`);\r\n  },\r\n\r\n  // Get all permissions\r\n  getPermissions: async () => {\r\n    return apiClient.get<Permission[]>(\"/rbac/permissions\");\r\n  },\r\n\r\n  // Get all grants\r\n  getAllGrants: async (): Promise<GrantsResponse> => {\r\n    return apiClient.get<GrantsResponse>(\"/rbac/grants\");\r\n  },\r\n\r\n  // Get grants for a specific role\r\n  getRoleGrants: async (role: string): Promise<RoleGrantsResponse> => {\r\n    return apiClient.get<RoleGrantsResponse>(`/rbac/grants/${role}`);\r\n  },\r\n\r\n  // Get all available resources and actions\r\n  getResourcesAndActions: async (): Promise<ResourcesAndActions> => {\r\n    return apiClient.get<ResourcesAndActions>(\"/rbac/resources\");\r\n  },\r\n\r\n  // Create or update a grant\r\n  updateGrant: async (data: {\r\n    role: string;\r\n    resource: string;\r\n    action: string;\r\n    attributes?: any;\r\n  }): Promise<Grant> => {\r\n    // Validate required fields\r\n    if (!data.role || !data.resource || !data.action) {\r\n      throw new Error(\"Role, resource, and action are required\");\r\n    }\r\n\r\n    // Validate action format\r\n    const validActionPattern = /^(create|read|update|delete):(any|own)$/;\r\n    if (!validActionPattern.test(data.action)) {\r\n      throw new Error(\r\n        `Invalid action format: ${data.action}. Must be in format 'operation:scope' (e.g., 'create:any', 'read:own')`\r\n      );\r\n    }\r\n\r\n    // Create a deep copy of the data to avoid modifying the original\r\n    const formattedData = JSON.parse(JSON.stringify(data));\r\n\r\n    // Parse the action to get operation and scope\r\n    const [operation, scope] = data.action.split(\":\");\r\n\r\n    // COMPREHENSIVE FIX: Handle all action types properly\r\n\r\n    // 1. Handle null/undefined/invalid attributes for all action types\r\n    if (\r\n      !formattedData.attributes ||\r\n      typeof formattedData.attributes !== \"object\" ||\r\n      Array.isArray(formattedData.attributes) ||\r\n      Object.keys(formattedData.attributes).length === 0\r\n    ) {\r\n      console.log(\r\n        `Invalid or missing attributes for ${data.action}, using wildcard`\r\n      );\r\n      formattedData.attributes = { \"*\": true };\r\n    }\r\n    // 2. Special handling for different action types\r\n    else {\r\n      // For create:any and read:any, ALWAYS use wildcard attributes\r\n      if ((operation === \"create\" || operation === \"read\") && scope === \"any\") {\r\n        console.log(\r\n          `CRITICAL FIX: Using wildcard attributes for ${data.action} action`\r\n        );\r\n        formattedData.attributes = { \"*\": true };\r\n      }\r\n      // For update:any and delete:any, recommend wildcard but allow specific attributes\r\n      else if (\r\n        (operation === \"update\" || operation === \"delete\") &&\r\n        scope === \"any\"\r\n      ) {\r\n        // If wildcard is already present, ensure it's properly formatted\r\n        if (formattedData.attributes.hasOwnProperty(\"*\")) {\r\n          console.log(`Ensuring proper wildcard format for ${data.action}`);\r\n          formattedData.attributes = { \"*\": true };\r\n        }\r\n        // Otherwise, ensure all values are booleans\r\n        else {\r\n          console.log(`Validating specific attributes for ${data.action}`);\r\n          Object.keys(formattedData.attributes).forEach((key) => {\r\n            if (typeof formattedData.attributes[key] !== \"boolean\") {\r\n              formattedData.attributes[key] = true;\r\n            }\r\n          });\r\n\r\n          // Check if any attributes are true, if not use wildcard\r\n          const hasTrueAttribute = Object.values(formattedData.attributes).some(\r\n            (val) => val === true\r\n          );\r\n          if (!hasTrueAttribute) {\r\n            console.log(\r\n              `No true attributes found for ${data.action}, using wildcard`\r\n            );\r\n            formattedData.attributes = { \"*\": true };\r\n          }\r\n        }\r\n      }\r\n      // For 'own' scope actions, specific attributes are common\r\n      else if (scope === \"own\") {\r\n        console.log(`Processing ${data.action} with 'own' scope`);\r\n\r\n        // Ensure all values are booleans\r\n        Object.keys(formattedData.attributes).forEach((key) => {\r\n          if (typeof formattedData.attributes[key] !== \"boolean\") {\r\n            formattedData.attributes[key] = true;\r\n          }\r\n        });\r\n\r\n        // For read:own, ensure at least one attribute is true\r\n        if (operation === \"read\") {\r\n          const hasTrueAttribute = Object.values(formattedData.attributes).some(\r\n            (val) => val === true\r\n          );\r\n          if (!hasTrueAttribute) {\r\n            console.log(\r\n              `No true attributes found for ${data.action}, using wildcard`\r\n            );\r\n            formattedData.attributes = { \"*\": true };\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // Final safety check for all action types\r\n    if (\r\n      formattedData.attributes &&\r\n      typeof formattedData.attributes === \"object\" &&\r\n      !Array.isArray(formattedData.attributes)\r\n    ) {\r\n      // If it's an empty object after processing, use wildcard\r\n      if (Object.keys(formattedData.attributes).length === 0) {\r\n        console.log(\r\n          `Empty attributes object after processing for ${data.action}, using wildcard`\r\n        );\r\n        formattedData.attributes = { \"*\": true };\r\n      }\r\n\r\n      // Double-check for read:any and create:any to ensure wildcard attributes\r\n      if ((operation === \"create\" || operation === \"read\") && scope === \"any\") {\r\n        if (\r\n          !formattedData.attributes.hasOwnProperty(\"*\") ||\r\n          formattedData.attributes[\"*\"] !== true\r\n        ) {\r\n          console.log(\r\n            `FINAL SAFETY CHECK: Fixing attributes for ${data.action} action`\r\n          );\r\n          formattedData.attributes = { \"*\": true };\r\n        }\r\n      }\r\n    } else {\r\n      // If attributes somehow became invalid during processing, use wildcard\r\n      console.warn(\r\n        `Invalid attributes after processing for ${data.action}, using wildcard`\r\n      );\r\n      formattedData.attributes = { \"*\": true };\r\n    }\r\n\r\n    console.log(\"Sending formatted data to API:\", formattedData);\r\n    return apiClient.post<Grant>(\"/rbac/grants\", formattedData);\r\n  },\r\n\r\n  // Delete a grant\r\n  deleteGrant: async (\r\n    role: string,\r\n    resource: string,\r\n    action: string\r\n  ): Promise<void> => {\r\n    return apiClient.delete<void>(`/rbac/grants/${role}/${resource}/${action}`);\r\n  },\r\n\r\n  // Refresh the RBAC cache\r\n  refreshCache: async (): Promise<void> => {\r\n    return apiClient.post<void>(\"/rbac/refresh\");\r\n  },\r\n\r\n  // Update role grants\r\n  updateRoleGrants: async (roleId: string | number, grants: any) => {\r\n    return apiClient.put<any>(\r\n      `/rbac/grants/${roleId}`,\r\n      { grants },\r\n      { withCredentials: true }\r\n    );\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAiEO,MAAM,cAAc;IACzB,gBAAgB;IAChB,UAAU;QACR,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAS;IAC/B;IAEA,iBAAiB;IACjB,SAAS,OAAO;QACd,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAO,CAAC,YAAY,EAAE,IAAI;IAChD;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAO,eAAe;IAC7C;IAEA,cAAc;IACd,YAAY,OAAO,IAAqB;QACtC,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAO,CAAC,YAAY,EAAE,IAAI,EAAE;IAClD;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,OAAO,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAO,CAAC,YAAY,EAAE,IAAI;IACnD;IAEA,sBAAsB;IACtB,gBAAgB;QACd,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAe;IACrC;IAEA,iBAAiB;IACjB,cAAc;QACZ,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAiB;IACvC;IAEA,iCAAiC;IACjC,eAAe,OAAO;QACpB,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAqB,CAAC,aAAa,EAAE,MAAM;IACjE;IAEA,0CAA0C;IAC1C,wBAAwB;QACtB,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAsB;IAC5C;IAEA,2BAA2B;IAC3B,aAAa,OAAO;QAMlB,2BAA2B;QAC3B,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,MAAM,EAAE;YAChD,MAAM,IAAI,MAAM;QAClB;QAEA,yBAAyB;QACzB,MAAM,qBAAqB;QAC3B,IAAI,CAAC,mBAAmB,IAAI,CAAC,KAAK,MAAM,GAAG;YACzC,MAAM,IAAI,MACR,CAAC,uBAAuB,EAAE,KAAK,MAAM,CAAC,sEAAsE,CAAC;QAEjH;QAEA,iEAAiE;QACjE,MAAM,gBAAgB,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;QAEhD,8CAA8C;QAC9C,MAAM,CAAC,WAAW,MAAM,GAAG,KAAK,MAAM,CAAC,KAAK,CAAC;QAE7C,sDAAsD;QAEtD,mEAAmE;QACnE,IACE,CAAC,cAAc,UAAU,IACzB,OAAO,cAAc,UAAU,KAAK,YACpC,MAAM,OAAO,CAAC,cAAc,UAAU,KACtC,OAAO,IAAI,CAAC,cAAc,UAAU,EAAE,MAAM,KAAK,GACjD;YACA,QAAQ,GAAG,CACT,CAAC,kCAAkC,EAAE,KAAK,MAAM,CAAC,gBAAgB,CAAC;YAEpE,cAAc,UAAU,GAAG;gBAAE,KAAK;YAAK;QACzC,OAEK;YACH,8DAA8D;YAC9D,IAAI,CAAC,cAAc,YAAY,cAAc,MAAM,KAAK,UAAU,OAAO;gBACvE,QAAQ,GAAG,CACT,CAAC,4CAA4C,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;gBAErE,cAAc,UAAU,GAAG;oBAAE,KAAK;gBAAK;YACzC,OAEK,IACH,CAAC,cAAc,YAAY,cAAc,QAAQ,KACjD,UAAU,OACV;gBACA,iEAAiE;gBACjE,IAAI,cAAc,UAAU,CAAC,cAAc,CAAC,MAAM;oBAChD,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,KAAK,MAAM,EAAE;oBAChE,cAAc,UAAU,GAAG;wBAAE,KAAK;oBAAK;gBACzC,OAEK;oBACH,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,KAAK,MAAM,EAAE;oBAC/D,OAAO,IAAI,CAAC,cAAc,UAAU,EAAE,OAAO,CAAC,CAAC;wBAC7C,IAAI,OAAO,cAAc,UAAU,CAAC,IAAI,KAAK,WAAW;4BACtD,cAAc,UAAU,CAAC,IAAI,GAAG;wBAClC;oBACF;oBAEA,wDAAwD;oBACxD,MAAM,mBAAmB,OAAO,MAAM,CAAC,cAAc,UAAU,EAAE,IAAI,CACnE,CAAC,MAAQ,QAAQ;oBAEnB,IAAI,CAAC,kBAAkB;wBACrB,QAAQ,GAAG,CACT,CAAC,6BAA6B,EAAE,KAAK,MAAM,CAAC,gBAAgB,CAAC;wBAE/D,cAAc,UAAU,GAAG;4BAAE,KAAK;wBAAK;oBACzC;gBACF;YACF,OAEK,IAAI,UAAU,OAAO;gBACxB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,iBAAiB,CAAC;gBAExD,iCAAiC;gBACjC,OAAO,IAAI,CAAC,cAAc,UAAU,EAAE,OAAO,CAAC,CAAC;oBAC7C,IAAI,OAAO,cAAc,UAAU,CAAC,IAAI,KAAK,WAAW;wBACtD,cAAc,UAAU,CAAC,IAAI,GAAG;oBAClC;gBACF;gBAEA,sDAAsD;gBACtD,IAAI,cAAc,QAAQ;oBACxB,MAAM,mBAAmB,OAAO,MAAM,CAAC,cAAc,UAAU,EAAE,IAAI,CACnE,CAAC,MAAQ,QAAQ;oBAEnB,IAAI,CAAC,kBAAkB;wBACrB,QAAQ,GAAG,CACT,CAAC,6BAA6B,EAAE,KAAK,MAAM,CAAC,gBAAgB,CAAC;wBAE/D,cAAc,UAAU,GAAG;4BAAE,KAAK;wBAAK;oBACzC;gBACF;YACF;QACF;QAEA,0CAA0C;QAC1C,IACE,cAAc,UAAU,IACxB,OAAO,cAAc,UAAU,KAAK,YACpC,CAAC,MAAM,OAAO,CAAC,cAAc,UAAU,GACvC;YACA,yDAAyD;YACzD,IAAI,OAAO,IAAI,CAAC,cAAc,UAAU,EAAE,MAAM,KAAK,GAAG;gBACtD,QAAQ,GAAG,CACT,CAAC,6CAA6C,EAAE,KAAK,MAAM,CAAC,gBAAgB,CAAC;gBAE/E,cAAc,UAAU,GAAG;oBAAE,KAAK;gBAAK;YACzC;YAEA,yEAAyE;YACzE,IAAI,CAAC,cAAc,YAAY,cAAc,MAAM,KAAK,UAAU,OAAO;gBACvE,IACE,CAAC,cAAc,UAAU,CAAC,cAAc,CAAC,QACzC,cAAc,UAAU,CAAC,IAAI,KAAK,MAClC;oBACA,QAAQ,GAAG,CACT,CAAC,0CAA0C,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;oBAEnE,cAAc,UAAU,GAAG;wBAAE,KAAK;oBAAK;gBACzC;YACF;QACF,OAAO;YACL,uEAAuE;YACvE,QAAQ,IAAI,CACV,CAAC,wCAAwC,EAAE,KAAK,MAAM,CAAC,gBAAgB,CAAC;YAE1E,cAAc,UAAU,GAAG;gBAAE,KAAK;YAAK;QACzC;QAEA,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAQ,gBAAgB;IAC/C;IAEA,iBAAiB;IACjB,aAAa,OACX,MACA,UACA;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAO,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ;IAC5E;IAEA,yBAAyB;IACzB,cAAc;QACZ,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAO;IAC9B;IAEA,qBAAqB;IACrB,kBAAkB,OAAO,QAAyB;QAChD,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAClB,CAAC,aAAa,EAAE,QAAQ,EACxB;YAAE;QAAO,GACT;YAAE,iBAAiB;QAAK;IAE5B;AACF", "debugId": null}}, {"offset": {"line": 4271, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/use-toast.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { toast } from \"sonner\";\r\n\r\nexport interface ToastProps {\r\n  title?: string;\r\n  description?: string;\r\n  variant?: \"default\" | \"destructive\" | \"success\" | \"warning\" | \"info\";\r\n}\r\n\r\nexport function useToast() {\r\n  return {\r\n    toast: {\r\n      success: (title: string, props?: Omit<ToastProps, \"variant\">) => {\r\n        toast.success(title, props);\r\n      },\r\n      error: (title: string, props?: Omit<ToastProps, \"variant\">) => {\r\n        toast.error(title, props);\r\n      },\r\n      warning: (title: string, props?: Omit<ToastProps, \"variant\">) => {\r\n        toast.warning(title, props);\r\n      },\r\n      info: (title: string, props?: Omit<ToastProps, \"variant\">) => {\r\n        toast.info(title, props);\r\n      },\r\n      message: (title: string, props?: Omit<ToastProps, \"variant\">) => {\r\n        toast(title, props);\r\n      },\r\n    },\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAUO,SAAS;IACd,OAAO;QACL,OAAO;YACL,SAAS,CAAC,OAAe;gBACvB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO;YACvB;YACA,OAAO,CAAC,OAAe;gBACrB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO;YACrB;YACA,SAAS,CAAC,OAAe;gBACvB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO;YACvB;YACA,MAAM,CAAC,OAAe;gBACpB,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,OAAO;YACpB;YACA,SAAS,CAAC,OAAe;gBACvB,CAAA,GAAA,2IAAA,CAAA,QAAK,AAAD,EAAE,OAAO;YACf;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 4307, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/context/permission-context.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useEffect, useState } from \"react\";\r\nimport { useCurrentUser } from \"../hooks/use-auth\";\r\nimport { rbacService, GroupedGrants } from \"@/features/users/api/rbac-service\";\r\nimport { useToast } from \"@/components/ui/use-toast\";\r\n\r\ninterface PermissionContextType {\r\n  permissions: GroupedGrants | null;\r\n  isLoading: boolean;\r\n  error: string | null;\r\n  refreshPermissions: () => Promise<void>;\r\n  hasPermission: (\r\n    resource: string,\r\n    action: string,\r\n    scope?: \"any\" | \"own\"\r\n  ) => boolean;\r\n  logAvailableGrants: (resource: string) => void;\r\n}\r\n\r\nconst PermissionContext = createContext<PermissionContextType | undefined>(\r\n  undefined\r\n);\r\n\r\nexport function PermissionProvider({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const [permissions, setPermissions] = useState<GroupedGrants | null>(null);\r\n  const [isLoading, setIsLoading] = useState<boolean>(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const { toast } = useToast();\r\n\r\n  const fetchPermissions = async () => {\r\n    if (!user) return;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n      // Get the user's role\r\n      const roleName = user.role_name;\r\n\r\n      // Fetch permissions for this role\r\n      const response = await rbacService.getRoleGrants(roleName);\r\n\r\n      // Set the permissions in state\r\n      setPermissions(response.grants);\r\n    } catch (err) {\r\n      console.error(\"Error fetching permissions:\", err);\r\n      setError(\"Failed to load permissions\");\r\n      toast.error(\"Error\", {\r\n        description:\r\n          \"Failed to load permissions. Some features may be unavailable.\",\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Fetch permissions when the user data is loaded\r\n  useEffect(() => {\r\n    if (!isUserLoading && user) {\r\n      fetchPermissions();\r\n    }\r\n  }, [isUserLoading, user]);\r\n\r\n  // Function to refresh permissions\r\n  const refreshPermissions = async () => {\r\n    await fetchPermissions();\r\n  };\r\n\r\n  // Helper function to log available grants for debugging\r\n  const logAvailableGrants = (resource: string) => {\r\n    if (!permissions) {\r\n      console.log(`[PermissionContext] No permissions loaded yet`);\r\n      return;\r\n    }\r\n\r\n    if (!permissions[resource]) {\r\n      console.log(\r\n        `[PermissionContext] No grants found for resource: ${resource}`\r\n      );\r\n      return;\r\n    }\r\n\r\n    console.log(\r\n      `[PermissionContext] Available grants for resource ${resource}:`\r\n    );\r\n    Object.keys(permissions[resource]).forEach((action) => {\r\n      console.log(`  - ${action}`);\r\n    });\r\n  };\r\n\r\n  // Function to check if a user has a specific permission\r\n  const hasPermission = (\r\n    resource: string,\r\n    action: string,\r\n    scope: \"any\" | \"own\" = \"any\"\r\n  ): boolean => {\r\n    // If permissions are still loading or there's an error, deny access\r\n    if (isLoading || error || !permissions || !user) {\r\n      return false;\r\n    }\r\n\r\n    // Format the action string\r\n    const formattedAction = `${action}:${scope}`;\r\n\r\n    // Special case for company_admin, super_admin, and float_manager\r\n    if (\r\n      user.role_name === \"company_admin\" ||\r\n      user.role_name === \"super_admin\" ||\r\n      user.role_name === \"float_manager\"\r\n    ) {\r\n      // For debugging purposes, still check if the permission exists\r\n      if (permissions[resource]) {\r\n        const hasAction = !!permissions[resource][formattedAction];\r\n\r\n        if (!hasAction) {\r\n          console.log(\r\n            `[PermissionContext] Admin override: ${resource}:${formattedAction} not found in grants but access granted`\r\n          );\r\n        }\r\n      } else {\r\n        console.log(\r\n          `[PermissionContext] Admin override: Resource ${resource} not found in grants but access granted`\r\n        );\r\n      }\r\n\r\n      // Always grant access to admin roles and float_manager\r\n      return true;\r\n    }\r\n\r\n    // For accountant role, check if they have the permission or a related permission\r\n    if (user.role_name === \"accountant\") {\r\n      // Check if the user has the resource permission\r\n      if (!permissions[resource]) {\r\n        // For accountants, check if they have access to related resources\r\n        // For example, if checking for \"banking\" but they have \"financial_reports\"\r\n        if (\r\n          (resource === \"banking\" && permissions[\"financial_reports\"]) ||\r\n          (resource === \"expenses\" && permissions[\"expense_reports\"]) ||\r\n          (resource === \"inventory\" && permissions[\"stock_reports\"])\r\n        ) {\r\n          console.log(\r\n            `[PermissionContext] Accountant has access to related resource for: ${resource}`\r\n          );\r\n          return true;\r\n        }\r\n\r\n        // For debugging, log that the resource wasn't found\r\n        console.log(\r\n          `[PermissionContext] Resource not found for accountant: ${resource}`\r\n        );\r\n        console.log(\r\n          `[PermissionContext] Available resources for accountant:`,\r\n          Object.keys(permissions)\r\n        );\r\n\r\n        return false;\r\n      }\r\n\r\n      // Check if the resource has the action\r\n      const hasAction = !!permissions[resource][formattedAction];\r\n\r\n      // For debugging, log the result\r\n      if (!hasAction) {\r\n        // For accountants, check if they have a read permission when checking for other actions\r\n        if (\r\n          action.startsWith(\"create:\") ||\r\n          action.startsWith(\"update:\") ||\r\n          action.startsWith(\"delete:\")\r\n        ) {\r\n          const readAction = `read:${scope}`;\r\n          if (permissions[resource][readAction]) {\r\n            console.log(\r\n              `[PermissionContext] Accountant has read permission for: ${resource}`\r\n            );\r\n            // For accountants, allow read access but not write access\r\n            return false;\r\n          }\r\n        }\r\n\r\n        console.log(\r\n          `[PermissionContext] Action not found for accountant: ${formattedAction} for resource: ${resource}`\r\n        );\r\n        logAvailableGrants(resource);\r\n      }\r\n\r\n      return hasAction;\r\n    }\r\n\r\n    // For branch_admin role, be more permissive\r\n    if (user.role_name === \"branch_admin\") {\r\n      // Check if the user has the resource permission\r\n      if (!permissions[resource]) {\r\n        // For branch_admin, allow access to certain resources even if not explicitly granted\r\n        if (\r\n          resource === \"dashboard\" ||\r\n          resource === \"profile\" ||\r\n          resource === \"employees\" ||\r\n          resource === \"inventory\" ||\r\n          resource === \"products\" ||\r\n          resource === \"sales\" ||\r\n          resource === \"pos_sessions\" ||\r\n          resource === \"customers\"\r\n        ) {\r\n          console.log(\r\n            `[PermissionContext] Branch admin has implicit access to: ${resource}`\r\n          );\r\n          return true;\r\n        }\r\n\r\n        // For debugging, log that the resource wasn't found\r\n        console.log(\r\n          `[PermissionContext] Resource not found for branch_admin: ${resource}`\r\n        );\r\n        console.log(\r\n          `[PermissionContext] Available resources for branch_admin:`,\r\n          Object.keys(permissions)\r\n        );\r\n\r\n        return false;\r\n      }\r\n\r\n      // Check if the resource has the action\r\n      const hasAction = !!permissions[resource][formattedAction];\r\n\r\n      // For debugging, log the result\r\n      if (!hasAction) {\r\n        console.log(\r\n          `[PermissionContext] Action not found for branch_admin: ${formattedAction} for resource: ${resource}`\r\n        );\r\n        logAvailableGrants(resource);\r\n      }\r\n\r\n      return hasAction;\r\n    }\r\n\r\n    // For all other roles, check if they have the exact permission\r\n\r\n    // Check if the user has the resource permission\r\n    if (!permissions[resource]) {\r\n      // For debugging, log that the resource wasn't found\r\n      console.log(`[PermissionContext] Resource not found: ${resource}`);\r\n\r\n      // Log all available resources for debugging\r\n      console.log(\r\n        `[PermissionContext] Available resources:`,\r\n        Object.keys(permissions)\r\n      );\r\n\r\n      return false;\r\n    }\r\n\r\n    // Check if the resource has the action\r\n    const hasAction = !!permissions[resource][formattedAction];\r\n\r\n    // For debugging, log the result\r\n    if (!hasAction) {\r\n      console.log(\r\n        `[PermissionContext] Action not found: ${formattedAction} for resource: ${resource}`\r\n      );\r\n      logAvailableGrants(resource);\r\n    }\r\n\r\n    return hasAction;\r\n  };\r\n\r\n  const value = {\r\n    permissions,\r\n    isLoading,\r\n    error,\r\n    refreshPermissions,\r\n    hasPermission,\r\n    logAvailableGrants,\r\n  };\r\n\r\n  return (\r\n    <PermissionContext.Provider value={value}>\r\n      {children}\r\n    </PermissionContext.Provider>\r\n  );\r\n}\r\n\r\nexport function usePermissionContext() {\r\n  const context = useContext(PermissionContext);\r\n  if (context === undefined) {\r\n    throw new Error(\r\n      \"usePermissionContext must be used within a PermissionProvider\"\r\n    );\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAoBA,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EACpC;AAGK,SAAS,mBAAmB,EACjC,QAAQ,EAGT;;IACC,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,aAAa;YACb,SAAS;YAET,sBAAsB;YACtB,MAAM,WAAW,KAAK,SAAS;YAE/B,kCAAkC;YAClC,MAAM,WAAW,MAAM,qJAAA,CAAA,cAAW,CAAC,aAAa,CAAC;YAEjD,+BAA+B;YAC/B,eAAe,SAAS,MAAM;QAChC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS;YACT,MAAM,KAAK,CAAC,SAAS;gBACnB,aACE;YACJ;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,iBAAiB,MAAM;gBAC1B;YACF;QACF;uCAAG;QAAC;QAAe;KAAK;IAExB,kCAAkC;IAClC,MAAM,qBAAqB;QACzB,MAAM;IACR;IAEA,wDAAwD;IACxD,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,aAAa;YAChB,QAAQ,GAAG,CAAC,CAAC,6CAA6C,CAAC;YAC3D;QACF;QAEA,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;YAC1B,QAAQ,GAAG,CACT,CAAC,kDAAkD,EAAE,UAAU;YAEjE;QACF;QAEA,QAAQ,GAAG,CACT,CAAC,kDAAkD,EAAE,SAAS,CAAC,CAAC;QAElE,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC1C,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,QAAQ;QAC7B;IACF;IAEA,wDAAwD;IACxD,MAAM,gBAAgB,CACpB,UACA,QACA,QAAuB,KAAK;QAE5B,oEAAoE;QACpE,IAAI,aAAa,SAAS,CAAC,eAAe,CAAC,MAAM;YAC/C,OAAO;QACT;QAEA,2BAA2B;QAC3B,MAAM,kBAAkB,GAAG,OAAO,CAAC,EAAE,OAAO;QAE5C,iEAAiE;QACjE,IACE,KAAK,SAAS,KAAK,mBACnB,KAAK,SAAS,KAAK,iBACnB,KAAK,SAAS,KAAK,iBACnB;YACA,+DAA+D;YAC/D,IAAI,WAAW,CAAC,SAAS,EAAE;gBACzB,MAAM,YAAY,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB;gBAE1D,IAAI,CAAC,WAAW;oBACd,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,SAAS,CAAC,EAAE,gBAAgB,uCAAuC,CAAC;gBAE/G;YACF,OAAO;gBACL,QAAQ,GAAG,CACT,CAAC,6CAA6C,EAAE,SAAS,uCAAuC,CAAC;YAErG;YAEA,uDAAuD;YACvD,OAAO;QACT;QAEA,iFAAiF;QACjF,IAAI,KAAK,SAAS,KAAK,cAAc;YACnC,gDAAgD;YAChD,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;gBAC1B,kEAAkE;gBAClE,2EAA2E;gBAC3E,IACE,AAAC,aAAa,aAAa,WAAW,CAAC,oBAAoB,IAC1D,aAAa,cAAc,WAAW,CAAC,kBAAkB,IACzD,aAAa,eAAe,WAAW,CAAC,gBAAgB,EACzD;oBACA,QAAQ,GAAG,CACT,CAAC,mEAAmE,EAAE,UAAU;oBAElF,OAAO;gBACT;gBAEA,oDAAoD;gBACpD,QAAQ,GAAG,CACT,CAAC,uDAAuD,EAAE,UAAU;gBAEtE,QAAQ,GAAG,CACT,CAAC,uDAAuD,CAAC,EACzD,OAAO,IAAI,CAAC;gBAGd,OAAO;YACT;YAEA,uCAAuC;YACvC,MAAM,YAAY,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB;YAE1D,gCAAgC;YAChC,IAAI,CAAC,WAAW;gBACd,wFAAwF;gBACxF,IACE,OAAO,UAAU,CAAC,cAClB,OAAO,UAAU,CAAC,cAClB,OAAO,UAAU,CAAC,YAClB;oBACA,MAAM,aAAa,CAAC,KAAK,EAAE,OAAO;oBAClC,IAAI,WAAW,CAAC,SAAS,CAAC,WAAW,EAAE;wBACrC,QAAQ,GAAG,CACT,CAAC,wDAAwD,EAAE,UAAU;wBAEvE,0DAA0D;wBAC1D,OAAO;oBACT;gBACF;gBAEA,QAAQ,GAAG,CACT,CAAC,qDAAqD,EAAE,gBAAgB,eAAe,EAAE,UAAU;gBAErG,mBAAmB;YACrB;YAEA,OAAO;QACT;QAEA,4CAA4C;QAC5C,IAAI,KAAK,SAAS,KAAK,gBAAgB;YACrC,gDAAgD;YAChD,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;gBAC1B,qFAAqF;gBACrF,IACE,aAAa,eACb,aAAa,aACb,aAAa,eACb,aAAa,eACb,aAAa,cACb,aAAa,WACb,aAAa,kBACb,aAAa,aACb;oBACA,QAAQ,GAAG,CACT,CAAC,yDAAyD,EAAE,UAAU;oBAExE,OAAO;gBACT;gBAEA,oDAAoD;gBACpD,QAAQ,GAAG,CACT,CAAC,yDAAyD,EAAE,UAAU;gBAExE,QAAQ,GAAG,CACT,CAAC,yDAAyD,CAAC,EAC3D,OAAO,IAAI,CAAC;gBAGd,OAAO;YACT;YAEA,uCAAuC;YACvC,MAAM,YAAY,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB;YAE1D,gCAAgC;YAChC,IAAI,CAAC,WAAW;gBACd,QAAQ,GAAG,CACT,CAAC,uDAAuD,EAAE,gBAAgB,eAAe,EAAE,UAAU;gBAEvG,mBAAmB;YACrB;YAEA,OAAO;QACT;QAEA,+DAA+D;QAE/D,gDAAgD;QAChD,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;YAC1B,oDAAoD;YACpD,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,UAAU;YAEjE,4CAA4C;YAC5C,QAAQ,GAAG,CACT,CAAC,wCAAwC,CAAC,EAC1C,OAAO,IAAI,CAAC;YAGd,OAAO;QACT;QAEA,uCAAuC;QACvC,MAAM,YAAY,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB;QAE1D,gCAAgC;QAChC,IAAI,CAAC,WAAW;YACd,QAAQ,GAAG,CACT,CAAC,sCAAsC,EAAE,gBAAgB,eAAe,EAAE,UAAU;YAEtF,mBAAmB;QACrB;QAEA,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,kBAAkB,QAAQ;QAAC,OAAO;kBAChC;;;;;;AAGP;GArQgB;;QAKmC,kJAAA,CAAA,iBAAc;QAI7C,0IAAA,CAAA,WAAQ;;;KATZ;AAuQT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MACR;IAEJ;IACA,OAAO;AACT;IARgB", "debugId": null}}, {"offset": {"line": 4522, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/inventory/context/bulk-transfer-context.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, ReactNode, useEffect } from \"react\";\r\nimport { BulkTransferItem } from \"../types/bulk-transfer\";\r\n\r\ninterface BulkTransferContextType {\r\n  transferItems: BulkTransferItem[];\r\n  addTransferItem: (item: BulkTransferItem) => void;\r\n  updateTransferItem: (index: number, item: Partial<BulkTransferItem>) => void;\r\n  removeTransferItem: (index: number) => void;\r\n  clearTransferItems: () => void;\r\n  notes: string;\r\n  setNotes: (notes: string) => void;\r\n  isInitialized: boolean;\r\n}\r\n\r\nconst BulkTransferContext = createContext<BulkTransferContextType | undefined>(undefined);\r\n\r\n// Storage keys\r\nconst TRANSFER_ITEMS_STORAGE_KEY = \"bulk_transfer_items\";\r\nconst TRANSFER_NOTES_STORAGE_KEY = \"bulk_transfer_notes\";\r\n\r\nexport const BulkTransferProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\r\n  const [transferItems, setTransferItems] = useState<BulkTransferItem[]>([]);\r\n  const [notes, setNotes] = useState<string>(\"\");\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n\r\n  // Load data from localStorage on mount\r\n  useEffect(() => {\r\n    try {\r\n      // Only run on client-side\r\n      if (typeof window !== \"undefined\") {\r\n        const storedItems = localStorage.getItem(TRANSFER_ITEMS_STORAGE_KEY);\r\n        const storedNotes = localStorage.getItem(TRANSFER_NOTES_STORAGE_KEY);\r\n\r\n        if (storedItems) {\r\n          setTransferItems(JSON.parse(storedItems));\r\n        }\r\n\r\n        if (storedNotes) {\r\n          setNotes(storedNotes);\r\n        }\r\n\r\n        setIsInitialized(true);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading bulk transfer data from localStorage:\", error);\r\n      setIsInitialized(true);\r\n    }\r\n  }, []);\r\n\r\n  // Save transferItems to localStorage whenever it changes\r\n  useEffect(() => {\r\n    if (isInitialized && typeof window !== \"undefined\" && transferItems.length > 0) {\r\n      localStorage.setItem(TRANSFER_ITEMS_STORAGE_KEY, JSON.stringify(transferItems));\r\n    }\r\n  }, [transferItems, isInitialized]);\r\n\r\n  // Save notes to localStorage whenever it changes\r\n  useEffect(() => {\r\n    if (isInitialized && typeof window !== \"undefined\" && notes !== \"\") {\r\n      localStorage.setItem(TRANSFER_NOTES_STORAGE_KEY, notes);\r\n    }\r\n  }, [notes, isInitialized]);\r\n\r\n  const addTransferItem = (item: BulkTransferItem) => {\r\n    setTransferItems((prev) => [...prev, item]);\r\n  };\r\n\r\n  const updateTransferItem = (index: number, item: Partial<BulkTransferItem>) => {\r\n    setTransferItems((prev) => {\r\n      const newItems = [...prev];\r\n      newItems[index] = { ...newItems[index], ...item };\r\n      return newItems;\r\n    });\r\n  };\r\n\r\n  const removeTransferItem = (index: number) => {\r\n    setTransferItems((prev) => prev.filter((_, i) => i !== index));\r\n  };\r\n\r\n  const clearTransferItems = React.useCallback(() => {\r\n    // Check if there's anything to clear to avoid unnecessary state updates\r\n    if (transferItems.length > 0 || notes !== \"\") {\r\n      // Clear localStorage first to prevent the useEffect from triggering again\r\n      if (typeof window !== \"undefined\") {\r\n        localStorage.removeItem(TRANSFER_ITEMS_STORAGE_KEY);\r\n        localStorage.removeItem(TRANSFER_NOTES_STORAGE_KEY);\r\n      }\r\n\r\n      // Then update state\r\n      setTransferItems([]);\r\n      setNotes(\"\");\r\n    }\r\n  }, [transferItems.length, notes]);\r\n\r\n  return (\r\n    <BulkTransferContext.Provider\r\n      value={{\r\n        transferItems,\r\n        addTransferItem,\r\n        updateTransferItem,\r\n        removeTransferItem,\r\n        clearTransferItems,\r\n        notes,\r\n        setNotes,\r\n        isInitialized,\r\n      }}\r\n    >\r\n      {children}\r\n    </BulkTransferContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useBulkTransfer = () => {\r\n  const context = useContext(BulkTransferContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useBulkTransfer must be used within a BulkTransferProvider\");\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAgBA,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAuC;AAE/E,eAAe;AACf,MAAM,6BAA6B;AACnC,MAAM,6BAA6B;AAE5B,MAAM,uBAA0D,CAAC,EAAE,QAAQ,EAAE;;IAClF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI;gBACF,0BAA0B;gBAC1B,wCAAmC;oBACjC,MAAM,cAAc,aAAa,OAAO,CAAC;oBACzC,MAAM,cAAc,aAAa,OAAO,CAAC;oBAEzC,IAAI,aAAa;wBACf,iBAAiB,KAAK,KAAK,CAAC;oBAC9B;oBAEA,IAAI,aAAa;wBACf,SAAS;oBACX;oBAEA,iBAAiB;gBACnB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uDAAuD;gBACrE,iBAAiB;YACnB;QACF;yCAAG,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,iBAAiB,aAAkB,eAAe,cAAc,MAAM,GAAG,GAAG;gBAC9E,aAAa,OAAO,CAAC,4BAA4B,KAAK,SAAS,CAAC;YAClE;QACF;yCAAG;QAAC;QAAe;KAAc;IAEjC,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,iBAAiB,aAAkB,eAAe,UAAU,IAAI;gBAClE,aAAa,OAAO,CAAC,4BAA4B;YACnD;QACF;yCAAG;QAAC;QAAO;KAAc;IAEzB,MAAM,kBAAkB,CAAC;QACvB,iBAAiB,CAAC,OAAS;mBAAI;gBAAM;aAAK;IAC5C;IAEA,MAAM,qBAAqB,CAAC,OAAe;QACzC,iBAAiB,CAAC;YAChB,MAAM,WAAW;mBAAI;aAAK;YAC1B,QAAQ,CAAC,MAAM,GAAG;gBAAE,GAAG,QAAQ,CAAC,MAAM;gBAAE,GAAG,IAAI;YAAC;YAChD,OAAO;QACT;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACzD;IAEA,MAAM,qBAAqB,6JAAA,CAAA,UAAK,CAAC,WAAW;gEAAC;YAC3C,wEAAwE;YACxE,IAAI,cAAc,MAAM,GAAG,KAAK,UAAU,IAAI;gBAC5C,0EAA0E;gBAC1E,wCAAmC;oBACjC,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;gBAC1B;gBAEA,oBAAoB;gBACpB,iBAAiB,EAAE;gBACnB,SAAS;YACX;QACF;+DAAG;QAAC,cAAc,MAAM;QAAE;KAAM;IAEhC,qBACE,6LAAC,oBAAoB,QAAQ;QAC3B,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GA1Fa;KAAA;AA4FN,MAAM,kBAAkB;;IAC7B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 4664, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/app/providers.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { GlobalSearch } from \"@/components/global-search\";\r\nimport { ApiRedirectProvider } from \"@/components/providers/api-redirect-provider\";\r\nimport { AuthErrorHandler } from \"@/components/providers/auth-error-handler\";\r\nimport {\r\n  LoadingProvider,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\nimport { SearchProvider } from \"@/components/providers/search-provider\";\r\nimport { Toaster } from \"@/components/ui/sonner\";\r\nimport { AuthErrorProvider } from \"@/features/auth/context/auth-error-context\";\r\nimport { PermissionProvider } from \"@/features/auth/context/permission-context\";\r\nimport { BulkTransferProvider } from \"@/features/inventory/context/bulk-transfer-context\";\r\nimport { queryClient } from \"@/lib/react-query\";\r\nimport { QueryClientProvider } from \"@tanstack/react-query\";\r\nimport { usePathname, useSearchParams } from \"next/navigation\";\r\nimport * as React from \"react\";\r\n\r\nexport function Providers({ children }: { children: React.ReactNode }) {\r\n  return (\r\n    <QueryClientProvider client={queryClient}>\r\n      <AuthErrorProvider>\r\n        <AuthErrorHandler>\r\n          <ApiRedirectProvider>\r\n            <LoadingProvider>\r\n              <GlobalLoadingIndicator />\r\n              <PermissionProvider>\r\n                <SearchProvider>\r\n                  <BulkTransferProvider>\r\n                    {children}\r\n                    <GlobalSearch />\r\n                    <Toaster position=\"top-right\" richColors />\r\n                  </BulkTransferProvider>\r\n                </SearchProvider>\r\n              </PermissionProvider>\r\n            </LoadingProvider>\r\n          </ApiRedirectProvider>\r\n        </AuthErrorHandler>\r\n      </AuthErrorProvider>\r\n    </QueryClientProvider>\r\n  );\r\n}\r\n\r\n// Component that monitors React Query loading state and navigation\r\nfunction GlobalLoadingIndicator() {\r\n  const { setLoading } = useLoading();\r\n  const [isAnyQueryLoading, setIsAnyQueryLoading] = React.useState(false);\r\n  const [isNavigating, setIsNavigating] = React.useState(false);\r\n\r\n  // Track navigation state changes\r\n  const pathname = usePathname();\r\n  const searchParams = useSearchParams();\r\n\r\n  // Show loader on navigation\r\n  React.useEffect(() => {\r\n    // When the path or search params change, show the loader\r\n    setIsNavigating(true);\r\n\r\n    // Use setTimeout to ensure this doesn't happen during rendering\r\n    setTimeout(() => {\r\n      setLoading(\"api\", true);\r\n    }, 0);\r\n\r\n    // Hide the loader after a short delay\r\n    const navigationTimeout = setTimeout(() => {\r\n      setIsNavigating(false);\r\n\r\n      // Only hide the loader if there are no active queries\r\n      if (!isAnyQueryLoading) {\r\n        setLoading(\"api\", false);\r\n      }\r\n    }, 300); // Short delay to ensure the loader is visible during navigation\r\n\r\n    return () => {\r\n      clearTimeout(navigationTimeout);\r\n    };\r\n  }, [pathname, searchParams, setLoading, isAnyQueryLoading]);\r\n\r\n  // Check for loading queries periodically\r\n  React.useEffect(() => {\r\n    let timeoutId: NodeJS.Timeout;\r\n\r\n    const checkLoadingState = () => {\r\n      try {\r\n        // Get all queries from the cache\r\n        const queries = queryClient.getQueryCache().getAll();\r\n        const mutations = queryClient.getMutationCache().getAll();\r\n\r\n        // Check if any query or mutation is loading\r\n        const hasLoadingQueries = queries.some(\r\n          (query) => query.state.status === \"loading\"\r\n        );\r\n        const hasLoadingMutations = mutations.some(\r\n          (mutation) => mutation.state.status === \"loading\"\r\n        );\r\n\r\n        // Update loading state\r\n        const isLoading = hasLoadingQueries || hasLoadingMutations;\r\n\r\n        if (isLoading !== isAnyQueryLoading) {\r\n          setIsAnyQueryLoading(isLoading);\r\n\r\n          // Only update the global loading state if we're not navigating\r\n          // (navigation already sets the loading state)\r\n          if (!isNavigating) {\r\n            // Use setTimeout to ensure this doesn't happen during rendering\r\n            setTimeout(() => {\r\n              setLoading(\"api\", isLoading);\r\n            }, 0);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error checking query loading state:\", error);\r\n      }\r\n\r\n      // Schedule next check\r\n      timeoutId = setTimeout(checkLoadingState, 100); // Check every 100ms\r\n    };\r\n\r\n    // Start checking\r\n    checkLoadingState();\r\n\r\n    // Clean up\r\n    return () => {\r\n      clearTimeout(timeoutId);\r\n    };\r\n  }, [setLoading, isAnyQueryLoading, isNavigating]);\r\n\r\n  // Add a global event listener for fetch requests\r\n  React.useEffect(() => {\r\n    // Keep track of active fetch requests\r\n    let activeFetchCount = 0;\r\n\r\n    // Create a proxy for the original fetch function\r\n    const originalFetch = window.fetch;\r\n\r\n    window.fetch = function (...args) {\r\n      // Increment the counter when a fetch request starts\r\n      activeFetchCount++;\r\n      if (activeFetchCount > 0 && !isNavigating && !isAnyQueryLoading) {\r\n        // Use setTimeout to ensure this doesn't happen during rendering\r\n        setTimeout(() => {\r\n          setLoading(\"api\", true);\r\n        }, 0);\r\n      }\r\n\r\n      // Call the original fetch function\r\n      return originalFetch.apply(this, args).finally(() => {\r\n        // Decrement the counter when the fetch request completes\r\n        activeFetchCount--;\r\n        if (activeFetchCount === 0 && !isNavigating && !isAnyQueryLoading) {\r\n          // Small delay to prevent flickering\r\n          setTimeout(() => {\r\n            setLoading(\"api\", false);\r\n          }, 100);\r\n        }\r\n      });\r\n    };\r\n\r\n    // Restore the original fetch function when the component unmounts\r\n    return () => {\r\n      window.fetch = originalFetch;\r\n    };\r\n  }, [setLoading, isNavigating, isAnyQueryLoading]);\r\n\r\n  return null;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAjBA;;;;;;;;;;;;;;AAmBO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ,+HAAA,CAAA,cAAW;kBACtC,cAAA,6LAAC,kKAAA,CAAA,oBAAiB;sBAChB,cAAA,6LAAC,8JAAA,CAAA,mBAAgB;0BACf,cAAA,6LAAC,iKAAA,CAAA,sBAAmB;8BAClB,cAAA,6LAAC,yJAAA,CAAA,kBAAe;;0CACd,6LAAC;;;;;0CACD,6LAAC,+JAAA,CAAA,qBAAkB;0CACjB,cAAA,6LAAC,wJAAA,CAAA,iBAAc;8CACb,cAAA,6LAAC,0KAAA,CAAA,uBAAoB;;4CAClB;0DACD,6LAAC,yIAAA,CAAA,eAAY;;;;;0DACb,6LAAC,qIAAA,CAAA,UAAO;gDAAC,UAAS;gDAAY,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5D;KAvBgB;AAyBhB,mEAAmE;AACnE,SAAS;;IACP,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD;IAChC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEvD,iCAAiC;IACjC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAEnC,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4CAAE;YACd,yDAAyD;YACzD,gBAAgB;YAEhB,gEAAgE;YAChE;oDAAW;oBACT,WAAW,OAAO;gBACpB;mDAAG;YAEH,sCAAsC;YACtC,MAAM,oBAAoB;sEAAW;oBACnC,gBAAgB;oBAEhB,sDAAsD;oBACtD,IAAI,CAAC,mBAAmB;wBACtB,WAAW,OAAO;oBACpB;gBACF;qEAAG,MAAM,gEAAgE;YAEzE;oDAAO;oBACL,aAAa;gBACf;;QACF;2CAAG;QAAC;QAAU;QAAc;QAAY;KAAkB;IAE1D,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4CAAE;YACd,IAAI;YAEJ,MAAM;sEAAoB;oBACxB,IAAI;wBACF,iCAAiC;wBACjC,MAAM,UAAU,+HAAA,CAAA,cAAW,CAAC,aAAa,GAAG,MAAM;wBAClD,MAAM,YAAY,+HAAA,CAAA,cAAW,CAAC,gBAAgB,GAAG,MAAM;wBAEvD,4CAA4C;wBAC5C,MAAM,oBAAoB,QAAQ,IAAI;oGACpC,CAAC,QAAU,MAAM,KAAK,CAAC,MAAM,KAAK;;wBAEpC,MAAM,sBAAsB,UAAU,IAAI;sGACxC,CAAC,WAAa,SAAS,KAAK,CAAC,MAAM,KAAK;;wBAG1C,uBAAuB;wBACvB,MAAM,YAAY,qBAAqB;wBAEvC,IAAI,cAAc,mBAAmB;4BACnC,qBAAqB;4BAErB,+DAA+D;4BAC/D,8CAA8C;4BAC9C,IAAI,CAAC,cAAc;gCACjB,gEAAgE;gCAChE;0FAAW;wCACT,WAAW,OAAO;oCACpB;yFAAG;4BACL;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,uCAAuC;oBACvD;oBAEA,sBAAsB;oBACtB,YAAY,WAAW,mBAAmB,MAAM,oBAAoB;gBACtE;;YAEA,iBAAiB;YACjB;YAEA,WAAW;YACX;oDAAO;oBACL,aAAa;gBACf;;QACF;2CAAG;QAAC;QAAY;QAAmB;KAAa;IAEhD,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4CAAE;YACd,sCAAsC;YACtC,IAAI,mBAAmB;YAEvB,iDAAiD;YACjD,MAAM,gBAAgB,OAAO,KAAK;YAElC,OAAO,KAAK;oDAAG,SAAU,GAAG,IAAI;oBAC9B,oDAAoD;oBACpD;oBACA,IAAI,mBAAmB,KAAK,CAAC,gBAAgB,CAAC,mBAAmB;wBAC/D,gEAAgE;wBAChE;gEAAW;gCACT,WAAW,OAAO;4BACpB;+DAAG;oBACL;oBAEA,mCAAmC;oBACnC,OAAO,cAAc,KAAK,CAAC,IAAI,EAAE,MAAM,OAAO;4DAAC;4BAC7C,yDAAyD;4BACzD;4BACA,IAAI,qBAAqB,KAAK,CAAC,gBAAgB,CAAC,mBAAmB;gCACjE,oCAAoC;gCACpC;wEAAW;wCACT,WAAW,OAAO;oCACpB;uEAAG;4BACL;wBACF;;gBACF;;YAEA,kEAAkE;YAClE;oDAAO;oBACL,OAAO,KAAK,GAAG;gBACjB;;QACF;2CAAG;QAAC;QAAY;QAAc;KAAkB;IAEhD,OAAO;AACT;GA1HS;;QACgB,yJAAA,CAAA,aAAU;QAKhB,qIAAA,CAAA,cAAW;QACP,qIAAA,CAAA,kBAAe;;;MAP7B", "debugId": null}}, {"offset": {"line": 4937, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/client-only.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport function ClientOnly({ children }: { children: React.ReactNode }) {\r\n  const [mounted, setMounted] = useState(false);\r\n  \r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n  \r\n  return mounted ? <>{children}</> : null;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIO,SAAS,WAAW,EAAE,QAAQ,EAAiC;;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,WAAW;QACb;+BAAG,EAAE;IAEL,OAAO,wBAAU;kBAAG;wBAAe;AACrC;GARgB;KAAA", "debugId": null}}]}