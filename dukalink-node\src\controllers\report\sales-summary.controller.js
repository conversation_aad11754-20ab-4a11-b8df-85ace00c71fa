/**
 * Sales Summary Report Controller
 */
const {
  Sale,
  SaleItem,
  Product,
  Branch,
  Region,
  User,
  ProductCategory,
  Location,
  PaymentMethod,
} = require("../../models");
const AppError = require("../../utils/error");
const { Op } = require("sequelize");
const sequelize = require("../../../config/database");
const logger = require("../../utils/logger");
const moment = require("moment");
const ExcelJS = require("exceljs");

/**
 * Get sales summary report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getSalesSummaryReport = async (req, res, next) => {
  try {
    const {
      start_date,
      end_date,
      product_id,
      category_id,
      branch_id,
      region_id,
      location_id,
      format = "json",
    } = req.query;

    // Validate date parameters
    if (!start_date || !end_date) {
      return next(new AppError("Start date and end date are required", 400));
    }

    // Parse dates
    const startDate = moment(start_date).startOf("day").toDate();
    const endDate = moment(end_date).endOf("day").toDate();

    // Validate date range
    if (moment(endDate).diff(moment(startDate), "days") > 90) {
      return next(new AppError("Date range cannot exceed 90 days", 400));
    }

    // Build where clause for sales
    const saleWhereClause = {
      created_at: {
        [Op.between]: [startDate, endDate],
        [Op.lte]: new Date(), // Don't include future dates
      },
      status: "completed",
      deleted_at: null,
    };

    // Add branch filter if provided
    if (branch_id) {
      saleWhereClause.branch_id = branch_id;
    }

    // Handle region filtering - get all branches in the specified region
    if (region_id) {
      const branchesInRegion = await Branch.findAll({
        where: { region_id, deleted_at: null },
        attributes: ["id"],
      });

      const branchIds = branchesInRegion.map((branch) => branch.id);

      if (branchIds.length > 0) {
        // Remove any existing branch_id filter
        delete saleWhereClause.branch_id;

        // Add the branch_id IN clause
        saleWhereClause.branch_id = {
          [Op.in]: branchIds,
        };
      }
    }

    // Build where clause for sale items
    const saleItemWhereClause = {
      // Note: SaleItems don't use soft deletes (paranoid: false), so no deleted_at filter needed
    };

    // Add product filter if provided
    if (product_id) {
      saleItemWhereClause.product_id = product_id;
    }

    // Build include array for product category filter
    const productInclude = [];
    if (category_id) {
      productInclude.push({
        model: ProductCategory,
        where: { id: category_id },
        attributes: ["id", "name"],
      });
    }

    // Build location filter
    const locationFilter = {};
    if (location_id) {
      locationFilter.location_id = location_id;
    }

    // Get sales data
    const sales = await Sale.findAll({
      where: saleWhereClause,
      include: [
        {
          model: SaleItem,
          where: saleItemWhereClause,
          paranoid: false, // Disable paranoid for SaleItem since it doesn't have deleted_at
          include: [
            {
              model: Product,
              attributes: ["id", "name", "sku", "category_id"],
              include: productInclude,
            },
          ],
        },
        {
          model: Branch,
          attributes: ["id", "name", "location", "region_id"],
          where: locationFilter,
          include: [
            {
              model: Region,
              attributes: ["id", "name", "code"],
              required: false,
            },
          ],
        },
        {
          model: User,
          attributes: ["id", "name", "email"],
          required: false, // LEFT JOIN to handle cases where user might be deleted
        },
        {
          model: PaymentMethod,
          attributes: ["id", "name", "code"],
          required: false, // LEFT JOIN to handle cases where payment method might be missing
        },
      ],
      order: [["created_at", "DESC"]],
    });

    // Process sales data
    const salesData = sales.map((sale) => {
      const saleItems = sale.SaleItems.map((item) => ({
        id: item.id,
        product_id: item.product_id,
        product_name: item.Product?.name || "Unknown Product",
        product_sku: item.Product?.sku || "N/A",
        category_id: item.Product?.category_id,
        category_name: item.Product?.ProductCategory?.name || "Uncategorized",
        quantity: item.quantity,
        unit_price: parseFloat(item.unit_price || 0),
        discount_amount: parseFloat(item.discount_amount || 0),
        total_price: parseFloat(item.total_price || 0),
        created_at: item.created_at,
      }));

      // Generate fallback receipt number for sales without one
      let receiptNumber = sale.receipt_number;
      if (!receiptNumber) {
        if (sale.is_dsa) {
          receiptNumber = `DSA-${sale.id}`;
        } else {
          receiptNumber = `SALE-${sale.id}`;
        }
      }

      return {
        id: sale.id,
        receipt_number: receiptNumber,
        branch_id: sale.branch_id,
        branch_name: sale.Branch?.name || "Unknown Branch",
        branch_location: sale.Branch?.location || "Unknown Location",
        region_id: sale.Branch?.region_id || null,
        region_name: sale.Branch?.Region?.name || "Unknown Region",
        user_id: sale.user_id,
        user_name: sale.User?.name || "Unknown User",
        total_amount: parseFloat(sale.total_amount || 0),
        discount_amount: parseFloat(sale.discount_amount || 0),
        net_amount:
          parseFloat(sale.net_amount || 0) ||
          parseFloat(sale.total_amount || 0) -
            parseFloat(sale.discount_amount || 0),
        payment_method: sale.PaymentMethod?.name || "Unknown Payment Method",
        payment_method_code: sale.PaymentMethod?.code || "unknown",
        is_dsa: sale.is_dsa || false,
        status: sale.status || "completed",
        created_at: sale.created_at,
        items: saleItems,
      };
    });

    // Calculate summary statistics
    const summary = {
      total_sales: salesData.length,
      total_amount: salesData.reduce((sum, sale) => sum + sale.total_amount, 0),
      total_discount: salesData.reduce(
        (sum, sale) => sum + sale.discount_amount,
        0
      ),
      net_amount: salesData.reduce((sum, sale) => sum + sale.net_amount, 0),
      total_items: salesData.reduce((sum, sale) => sum + sale.items.length, 0),
      average_sale_value:
        salesData.length > 0
          ? salesData.reduce((sum, sale) => sum + sale.net_amount, 0) /
            salesData.length
          : 0,
    };

    // Group by product
    const productSummary = {};
    salesData.forEach((sale) => {
      sale.items.forEach((item) => {
        if (!productSummary[item.product_id]) {
          productSummary[item.product_id] = {
            product_id: item.product_id,
            product_name: item.product_name,
            product_sku: item.product_sku,
            category_id: item.category_id,
            category_name: item.category_name,
            quantity: 0,
            total_amount: 0,
            discount_amount: 0,
            net_amount: 0,
          };
        }

        productSummary[item.product_id].quantity += item.quantity;
        productSummary[item.product_id].total_amount += item.total_price;
        productSummary[item.product_id].discount_amount += item.discount_amount;
        productSummary[item.product_id].net_amount +=
          item.total_price - item.discount_amount;
      });
    });

    // Convert to array and sort by total amount
    const productSummaryArray = Object.values(productSummary).sort(
      (a, b) => b.total_amount - a.total_amount
    );

    // Group by branch
    const branchSummary = {};
    salesData.forEach((sale) => {
      if (!branchSummary[sale.branch_id]) {
        branchSummary[sale.branch_id] = {
          branch_id: sale.branch_id,
          branch_name: sale.branch_name,
          branch_location: sale.branch_location,
          region_id: sale.region_id,
          region_name: sale.region_name,
          total_sales: 0,
          total_amount: 0,
          discount_amount: 0,
          net_amount: 0,
        };
      }

      branchSummary[sale.branch_id].total_sales += 1;
      branchSummary[sale.branch_id].total_amount += sale.total_amount;
      branchSummary[sale.branch_id].discount_amount += sale.discount_amount;
      branchSummary[sale.branch_id].net_amount += sale.net_amount;
    });

    // Convert to array and sort by total amount
    const branchSummaryArray = Object.values(branchSummary).sort(
      (a, b) => b.total_amount - a.total_amount
    );

    // Prepare response
    const response = {
      status: "success",
      filters: {
        start_date: start_date,
        end_date: end_date,
        product_id: product_id || null,
        category_id: category_id || null,
        branch_id: branch_id || null,
        region_id: region_id || null,
        location_id: location_id || null,
      },
      summary,
      by_product: productSummaryArray,
      by_branch: branchSummaryArray,
      sales: salesData,
    };

    // Return response based on requested format
    if (format === "excel") {
      return await generateExcelReport(response, res);
    }

    return res.status(200).json(response);
  } catch (error) {
    logger.error(`Error getting sales summary report: ${error.message}`);
    next(error);
  }
};

/**
 * Generate Excel report for sales summary
 * @param {Object} data - Report data
 * @param {Object} res - Express response object
 */
const generateExcelReport = async (data, res) => {
  try {
    const workbook = new ExcelJS.Workbook();

    // Add only the sales summary table (not the metrics)
    const summarySheet = workbook.addWorksheet("Sales Summary");
    summarySheet.columns = [
      { header: "ID", key: "id", width: 10 },
      { header: "Receipt Number", key: "receipt_number", width: 20 },
      { header: "Date", key: "created_at", width: 20 },
      { header: "Branch", key: "branch_name", width: 25 },
      { header: "Region", key: "region_name", width: 20 },
      { header: "User", key: "user_name", width: 25 },
      { header: "Total Amount", key: "total_amount", width: 15 },
      { header: "Discount", key: "discount_amount", width: 15 },
      { header: "Net Amount", key: "net_amount", width: 15 },
      { header: "Payment Method", key: "payment_method", width: 20 },
      { header: "DSA Sale", key: "is_dsa", width: 12 },
      { header: "Status", key: "status", width: 12 },
    ];

    // Add sales data with formatted dates and DSA values
    data.sales.forEach((sale) => {
      summarySheet.addRow({
        id: sale.id,
        receipt_number: sale.receipt_number,
        created_at: moment(sale.created_at).format("YYYY-MM-DD HH:mm:ss"),
        branch_name: sale.branch_name,
        region_name: sale.region_name,
        user_name: sale.user_name,
        total_amount: sale.total_amount,
        discount_amount: sale.discount_amount,
        net_amount: sale.net_amount,
        payment_method: sale.payment_method,
        is_dsa: sale.is_dsa ? "Yes" : "No",
        status: sale.status,
      });
    });

    // Style the header row
    const headerRow = summarySheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };

    // Auto-fit columns
    summarySheet.columns.forEach((column) => {
      if (column.width < 10) column.width = 10;
    });

    // Set response headers
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=sales-summary-${moment().format("YYYY-MM-DD")}.xlsx`
    );

    // Write to response
    await workbook.xlsx.write(res);

    return res.end();
  } catch (error) {
    logger.error(`Error generating Excel report: ${error.message}`);
    throw error;
  }
};

module.exports = {
  getSalesSummaryReport,
};
