{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        success:\r\n          \"border-transparent bg-green-500 text-white [a&]:hover:bg-green-600 focus-visible:ring-green-500/20 dark:focus-visible:ring-green-500/40\",\r\n        warning:\r\n          \"border-transparent bg-yellow-500 text-white [a&]:hover:bg-yellow-600 focus-visible:ring-yellow-500/20 dark:focus-visible:ring-yellow-500/40\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/pagination.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport {\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n  MoreHorizontalIcon,\r\n} from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button, buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Pagination({ className, ...props }: React.ComponentProps<\"nav\">) {\r\n  return (\r\n    <nav\r\n      role=\"navigation\"\r\n      aria-label=\"pagination\"\r\n      data-slot=\"pagination\"\r\n      className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"pagination-content\"\r\n      className={cn(\"flex flex-row items-center gap-1\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationItem({ ...props }: React.ComponentProps<\"li\">) {\r\n  return <li data-slot=\"pagination-item\" {...props} />\r\n}\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean\r\n} & Pick<React.ComponentProps<typeof Button>, \"size\"> &\r\n  React.ComponentProps<\"a\">\r\n\r\nfunction PaginationLink({\r\n  className,\r\n  isActive,\r\n  size = \"icon\",\r\n  ...props\r\n}: PaginationLinkProps) {\r\n  return (\r\n    <a\r\n      aria-current={isActive ? \"page\" : undefined}\r\n      data-slot=\"pagination-link\"\r\n      data-active={isActive}\r\n      className={cn(\r\n        buttonVariants({\r\n          variant: isActive ? \"outline\" : \"ghost\",\r\n          size,\r\n        }),\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationPrevious({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) {\r\n  return (\r\n    <PaginationLink\r\n      aria-label=\"Go to previous page\"\r\n      size=\"default\"\r\n      className={cn(\"gap-1 px-2.5 sm:pl-2.5\", className)}\r\n      {...props}\r\n    >\r\n      <ChevronLeftIcon />\r\n      <span className=\"hidden sm:block\">Previous</span>\r\n    </PaginationLink>\r\n  )\r\n}\r\n\r\nfunction PaginationNext({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) {\r\n  return (\r\n    <PaginationLink\r\n      aria-label=\"Go to next page\"\r\n      size=\"default\"\r\n      className={cn(\"gap-1 px-2.5 sm:pr-2.5\", className)}\r\n      {...props}\r\n    >\r\n      <span className=\"hidden sm:block\">Next</span>\r\n      <ChevronRightIcon />\r\n    </PaginationLink>\r\n  )\r\n}\r\n\r\nfunction PaginationEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      aria-hidden\r\n      data-slot=\"pagination-ellipsis\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontalIcon className=\"size-4\" />\r\n      <span className=\"sr-only\">More pages</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationLink,\r\n  PaginationItem,\r\n  PaginationPrevious,\r\n  PaginationNext,\r\n  PaginationEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AAAA;AAAA;AAMA;AACA;;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,MAAK;QACL,cAAW;QACX,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EAAE,GAAG,OAAmC;IAC9D,qBAAO,6LAAC;QAAG,aAAU;QAAmB,GAAG,KAAK;;;;;;AAClD;MAFS;AAST,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,OAAO,MAAM,EACb,GAAG,OACiB;IACpB,qBACE,6LAAC;QACC,gBAAc,WAAW,SAAS;QAClC,aAAU;QACV,eAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YACb,SAAS,WAAW,YAAY;YAChC;QACF,IACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACyC;IAC5C,qBACE,6LAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;0BAET,6LAAC,2NAAA,CAAA,kBAAe;;;;;0BAChB,6LAAC;gBAAK,WAAU;0BAAkB;;;;;;;;;;;;AAGxC;MAfS;AAiBT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACyC;IAC5C,qBACE,6LAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BAAkB;;;;;;0BAClC,6LAAC,6NAAA,CAAA,mBAAgB;;;;;;;;;;;AAGvB;MAfS;AAiBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAW;QACX,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,qBAAkB;gBAAC,WAAU;;;;;;0BAC9B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;MAfS", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 625, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/data-pagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useMemo } from \"react\";\r\nimport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n} from \"@/components/ui/pagination\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { ChevronFirst, ChevronLast } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nexport interface DataPaginationProps {\r\n  // Required props\r\n  currentPage: number;\r\n  totalPages: number;\r\n  onPageChange: (page: number) => void;\r\n\r\n  // Optional props\r\n  pageSize?: number;\r\n  pageSizes?: number[];\r\n  onPageSizeChange?: (pageSize: number) => void;\r\n  totalItems?: number;\r\n  isLoading?: boolean;\r\n  showPageSizeSelector?: boolean;\r\n  showItemsInfo?: boolean;\r\n  showFirstLastButtons?: boolean;\r\n  maxPageButtons?: number;\r\n  className?: string;\r\n  compact?: boolean;\r\n  ariaLabel?: string;\r\n}\r\n\r\nexport function DataPagination({\r\n  currentPage,\r\n  totalPages,\r\n  onPageChange,\r\n  pageSize = 10,\r\n  pageSizes = [10, 25, 50, 100, 250, 500, 1000],\r\n  onPageSizeChange,\r\n  totalItems,\r\n  isLoading = false,\r\n  showPageSizeSelector = true,\r\n  showItemsInfo = true,\r\n  showFirstLastButtons = true,\r\n  maxPageButtons = 5,\r\n  className,\r\n  compact = false,\r\n  ariaLabel = \"Pagination\",\r\n}: DataPaginationProps) {\r\n  // Don't render pagination if there's only one page and no page size selector\r\n  if (totalPages <= 1 && (!showPageSizeSelector || !onPageSizeChange)) {\r\n    return null;\r\n  }\r\n\r\n  // Calculate the current range of items being displayed\r\n  const itemRange = useMemo(() => {\r\n    if (!totalItems) return null;\r\n\r\n    const start = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;\r\n    const end = Math.min(currentPage * pageSize, totalItems);\r\n\r\n    return { start, end };\r\n  }, [currentPage, pageSize, totalItems]);\r\n\r\n  // Generate page numbers to display\r\n  const pageNumbers = useMemo(() => {\r\n    const numbers: (number | string)[] = [];\r\n\r\n    if (totalPages <= maxPageButtons) {\r\n      // If total pages is less than or equal to maxPageButtons, show all pages\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        numbers.push(i);\r\n      }\r\n    } else {\r\n      // Always include first page\r\n      numbers.push(1);\r\n\r\n      // Calculate start and end of page numbers to show\r\n      let start = Math.max(2, currentPage - Math.floor(maxPageButtons / 2) + 1);\r\n      let end = Math.min(totalPages - 1, start + maxPageButtons - 3);\r\n\r\n      // Adjust if we're at the beginning\r\n      if (currentPage <= Math.floor(maxPageButtons / 2)) {\r\n        end = maxPageButtons - 2;\r\n        start = 2;\r\n      }\r\n\r\n      // Adjust if we're at the end\r\n      if (currentPage > totalPages - Math.floor(maxPageButtons / 2)) {\r\n        start = Math.max(2, totalPages - maxPageButtons + 2);\r\n        end = totalPages - 1;\r\n      }\r\n\r\n      // Add ellipsis before middle pages if needed\r\n      if (start > 2) {\r\n        numbers.push(\"ellipsis-start\");\r\n      }\r\n\r\n      // Add middle pages\r\n      for (let i = start; i <= end; i++) {\r\n        numbers.push(i);\r\n      }\r\n\r\n      // Add ellipsis after middle pages if needed\r\n      if (end < totalPages - 1) {\r\n        numbers.push(\"ellipsis-end\");\r\n      }\r\n\r\n      // Always include last page if more than one page\r\n      if (totalPages > 1) {\r\n        numbers.push(totalPages);\r\n      }\r\n    }\r\n\r\n    return numbers;\r\n  }, [currentPage, totalPages, maxPageButtons]);\r\n\r\n  // Handle page click with validation\r\n  const handlePageClick = (page: number, e: React.MouseEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Only trigger page change if it's not the current page and not loading\r\n    if (page !== currentPage && !isLoading) {\r\n      // Force the page to be within valid range\r\n      const validPage = Math.max(1, Math.min(page, totalPages));\r\n      onPageChange(validPage);\r\n    }\r\n  };\r\n\r\n  // Handle page size change\r\n  const handlePageSizeChange = (value: string) => {\r\n    if (onPageSizeChange) {\r\n      onPageSizeChange(parseInt(value, 10));\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex flex-col gap-4 w-full\",\r\n        compact\r\n          ? \"sm:flex-row sm:items-center sm:justify-between\"\r\n          : \"md:flex-row md:items-center md:justify-between\",\r\n        className\r\n      )}\r\n      aria-label={ariaLabel}\r\n    >\r\n      {/* Page size selector */}\r\n      {showPageSizeSelector && onPageSizeChange && (\r\n        <div className=\"flex items-center gap-2\">\r\n          <span className=\"text-sm text-muted-foreground\">Show</span>\r\n          <Select\r\n            value={pageSize.toString()}\r\n            onValueChange={handlePageSizeChange}\r\n            disabled={isLoading}\r\n          >\r\n            <SelectTrigger\r\n              className=\"h-8 w-[70px]\"\r\n              aria-label=\"Select page size\"\r\n            >\r\n              <SelectValue placeholder={pageSize.toString()} />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              {pageSizes.map((size) => (\r\n                <SelectItem key={size} value={size.toString()}>\r\n                  {size}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n          <span className=\"text-sm text-muted-foreground\">per page</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Pagination controls - Always show if we have pagination data */}\r\n      {totalPages >= 1 && (\r\n        <div className=\"bg-muted/50 rounded-md p-1\">\r\n          <Pagination>\r\n            <PaginationContent className=\"flex-wrap justify-center\">\r\n              {/* First page button */}\r\n              {showFirstLastButtons && (\r\n                <PaginationItem>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={(e) => handlePageClick(1, e)}\r\n                    disabled={currentPage === 1 || isLoading}\r\n                    className=\"h-8 w-8\"\r\n                    aria-label=\"Go to first page\"\r\n                  >\r\n                    <ChevronFirst className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </PaginationItem>\r\n              )}\r\n\r\n              {/* Previous page button */}\r\n              <PaginationItem>\r\n                <PaginationPrevious\r\n                  href=\"#\"\r\n                  onClick={(e) => handlePageClick(currentPage - 1, e)}\r\n                  aria-disabled={currentPage === 1 || isLoading}\r\n                  className={cn(\r\n                    currentPage === 1 || isLoading\r\n                      ? \"pointer-events-none opacity-50\"\r\n                      : \"\",\r\n                    \"hover:bg-primary/10 transition-colors\"\r\n                  )}\r\n                />\r\n              </PaginationItem>\r\n\r\n              {/* Page numbers */}\r\n              {!compact &&\r\n                pageNumbers.map((pageNumber, index) => {\r\n                  if (typeof pageNumber === \"string\") {\r\n                    return (\r\n                      <PaginationItem key={pageNumber}>\r\n                        <PaginationEllipsis />\r\n                      </PaginationItem>\r\n                    );\r\n                  }\r\n\r\n                  return (\r\n                    <PaginationItem key={`page-${pageNumber}`}>\r\n                      <PaginationLink\r\n                        href=\"#\"\r\n                        onClick={(e) => handlePageClick(pageNumber, e)}\r\n                        isActive={currentPage === pageNumber}\r\n                        aria-current={\r\n                          currentPage === pageNumber ? \"page\" : undefined\r\n                        }\r\n                      >\r\n                        {pageNumber}\r\n                      </PaginationLink>\r\n                    </PaginationItem>\r\n                  );\r\n                })}\r\n\r\n              {/* Compact view shows current/total instead of page numbers */}\r\n              {compact && (\r\n                <div className=\"flex items-center mx-2\">\r\n                  <span className=\"text-sm\">\r\n                    Page <span className=\"font-bold\">{currentPage}</span> of{\" \"}\r\n                    <span className=\"font-medium\">{totalPages}</span>\r\n                    {isLoading && (\r\n                      <span className=\"ml-1 text-muted-foreground\">\r\n                        (Loading...)\r\n                      </span>\r\n                    )}\r\n                  </span>\r\n                </div>\r\n              )}\r\n\r\n              {/* Next page button */}\r\n              <PaginationItem>\r\n                <PaginationNext\r\n                  href=\"#\"\r\n                  onClick={(e) => handlePageClick(currentPage + 1, e)}\r\n                  aria-disabled={currentPage === totalPages || isLoading}\r\n                  className={cn(\r\n                    currentPage === totalPages || isLoading\r\n                      ? \"pointer-events-none opacity-50\"\r\n                      : \"\",\r\n                    \"hover:bg-primary/10 transition-colors\"\r\n                  )}\r\n                />\r\n              </PaginationItem>\r\n\r\n              {/* Last page button */}\r\n              {showFirstLastButtons && (\r\n                <PaginationItem>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={(e) => handlePageClick(totalPages, e)}\r\n                    disabled={currentPage === totalPages || isLoading}\r\n                    className=\"h-8 w-8\"\r\n                    aria-label=\"Go to last page\"\r\n                  >\r\n                    <ChevronLast className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </PaginationItem>\r\n              )}\r\n            </PaginationContent>\r\n          </Pagination>\r\n        </div>\r\n      )}\r\n\r\n      {/* Items info */}\r\n      {showItemsInfo && totalItems !== undefined && (\r\n        <div className=\"text-sm text-muted-foreground whitespace-nowrap\">\r\n          {totalItems === 0 ? (\r\n            \"No items\"\r\n          ) : (\r\n            <>\r\n              Showing {itemRange?.start} to {itemRange?.end} of {totalItems}{\" \"}\r\n              items\r\n            </>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AAOA;AACA;AAAA;AACA;;;AArBA;;;;;;;AA4CO,SAAS,eAAe,EAC7B,WAAW,EACX,UAAU,EACV,YAAY,EACZ,WAAW,EAAE,EACb,YAAY;IAAC;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;CAAK,EAC7C,gBAAgB,EAChB,UAAU,EACV,YAAY,KAAK,EACjB,uBAAuB,IAAI,EAC3B,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,iBAAiB,CAAC,EAClB,SAAS,EACT,UAAU,KAAK,EACf,YAAY,YAAY,EACJ;;IACpB,6EAA6E;IAC7E,IAAI,cAAc,KAAK,CAAC,CAAC,wBAAwB,CAAC,gBAAgB,GAAG;QACnE,OAAO;IACT;IAEA,uDAAuD;IACvD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE;YACxB,IAAI,CAAC,YAAY,OAAO;YAExB,MAAM,QAAQ,eAAe,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,WAAW;YACpE,MAAM,MAAM,KAAK,GAAG,CAAC,cAAc,UAAU;YAE7C,OAAO;gBAAE;gBAAO;YAAI;QACtB;4CAAG;QAAC;QAAa;QAAU;KAAW;IAEtC,mCAAmC;IACnC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YAC1B,MAAM,UAA+B,EAAE;YAEvC,IAAI,cAAc,gBAAgB;gBAChC,yEAAyE;gBACzE,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;oBACpC,QAAQ,IAAI,CAAC;gBACf;YACF,OAAO;gBACL,4BAA4B;gBAC5B,QAAQ,IAAI,CAAC;gBAEb,kDAAkD;gBAClD,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB,KAAK;gBACvE,IAAI,MAAM,KAAK,GAAG,CAAC,aAAa,GAAG,QAAQ,iBAAiB;gBAE5D,mCAAmC;gBACnC,IAAI,eAAe,KAAK,KAAK,CAAC,iBAAiB,IAAI;oBACjD,MAAM,iBAAiB;oBACvB,QAAQ;gBACV;gBAEA,6BAA6B;gBAC7B,IAAI,cAAc,aAAa,KAAK,KAAK,CAAC,iBAAiB,IAAI;oBAC7D,QAAQ,KAAK,GAAG,CAAC,GAAG,aAAa,iBAAiB;oBAClD,MAAM,aAAa;gBACrB;gBAEA,6CAA6C;gBAC7C,IAAI,QAAQ,GAAG;oBACb,QAAQ,IAAI,CAAC;gBACf;gBAEA,mBAAmB;gBACnB,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;oBACjC,QAAQ,IAAI,CAAC;gBACf;gBAEA,4CAA4C;gBAC5C,IAAI,MAAM,aAAa,GAAG;oBACxB,QAAQ,IAAI,CAAC;gBACf;gBAEA,iDAAiD;gBACjD,IAAI,aAAa,GAAG;oBAClB,QAAQ,IAAI,CAAC;gBACf;YACF;YAEA,OAAO;QACT;8CAAG;QAAC;QAAa;QAAY;KAAe;IAE5C,oCAAoC;IACpC,MAAM,kBAAkB,CAAC,MAAc;QACrC,EAAE,cAAc;QAEhB,wEAAwE;QACxE,IAAI,SAAS,eAAe,CAAC,WAAW;YACtC,0CAA0C;YAC1C,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;YAC7C,aAAa;QACf;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC5B,IAAI,kBAAkB;YACpB,iBAAiB,SAAS,OAAO;QACnC;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8BACA,UACI,mDACA,kDACJ;QAEF,cAAY;;YAGX,wBAAwB,kCACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAgC;;;;;;kCAChD,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,QAAQ;wBACxB,eAAe;wBACf,UAAU;;0CAEV,6LAAC,qIAAA,CAAA,gBAAa;gCACZ,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAa,SAAS,QAAQ;;;;;;;;;;;0CAE7C,6LAAC,qIAAA,CAAA,gBAAa;0CACX,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,qIAAA,CAAA,aAAU;wCAAY,OAAO,KAAK,QAAQ;kDACxC;uCADc;;;;;;;;;;;;;;;;kCAMvB,6LAAC;wBAAK,WAAU;kCAAgC;;;;;;;;;;;;YAKnD,cAAc,mBACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,yIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,yIAAA,CAAA,oBAAiB;wBAAC,WAAU;;4BAE1B,sCACC,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,GAAG;oCACnC,UAAU,gBAAgB,KAAK;oCAC/B,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAM9B,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,yIAAA,CAAA,qBAAkB;oCACjB,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,cAAc,GAAG;oCACjD,iBAAe,gBAAgB,KAAK;oCACpC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBAAgB,KAAK,YACjB,mCACA,IACJ;;;;;;;;;;;4BAML,CAAC,WACA,YAAY,GAAG,CAAC,CAAC,YAAY;gCAC3B,IAAI,OAAO,eAAe,UAAU;oCAClC,qBACE,6LAAC,yIAAA,CAAA,iBAAc;kDACb,cAAA,6LAAC,yIAAA,CAAA,qBAAkB;;;;;uCADA;;;;;gCAIzB;gCAEA,qBACE,6LAAC,yIAAA,CAAA,iBAAc;8CACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;wCACb,MAAK;wCACL,SAAS,CAAC,IAAM,gBAAgB,YAAY;wCAC5C,UAAU,gBAAgB;wCAC1B,gBACE,gBAAgB,aAAa,SAAS;kDAGvC;;;;;;mCATgB,CAAC,KAAK,EAAE,YAAY;;;;;4BAa7C;4BAGD,yBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCAAU;sDACnB,6LAAC;4CAAK,WAAU;sDAAa;;;;;;wCAAmB;wCAAI;sDACzD,6LAAC;4CAAK,WAAU;sDAAe;;;;;;wCAC9B,2BACC,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;;;;;;0CASrD,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;oCACb,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,cAAc,GAAG;oCACjD,iBAAe,gBAAgB,cAAc;oCAC7C,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBAAgB,cAAc,YAC1B,mCACA,IACJ;;;;;;;;;;;4BAML,sCACC,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,YAAY;oCAC5C,UAAU,gBAAgB,cAAc;oCACxC,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUpC,iBAAiB,eAAe,2BAC/B,6LAAC;gBAAI,WAAU;0BACZ,eAAe,IACd,2BAEA;;wBAAE;wBACS,WAAW;wBAAM;wBAAK,WAAW;wBAAI;wBAAK;wBAAY;wBAAI;;;;;;;;;;;;;;AAQjF;GA9QgB;KAAA", "debugId": null}}, {"offset": {"line": 1027, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,6LAAC,sKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/expenses/expenses-data-table.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport { Eye, FileCheck, FileX } from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { DataPagination } from \"@/components/ui/data-pagination\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\ninterface ExpensesDataTableProps {\r\n  data: any[];\r\n  totalCount: number;\r\n  isLoading?: boolean;\r\n  pagination?: {\r\n    currentPage: number;\r\n    onPageChange: (page: number) => void;\r\n    pageSize: number;\r\n    onPageSizeChange?: (pageSize: number) => void;\r\n  };\r\n}\r\n\r\nexport function ExpensesDataTable({\r\n  data,\r\n  totalCount,\r\n  isLoading = false,\r\n  pagination,\r\n}: ExpensesDataTableProps) {\r\n  const router = useRouter();\r\n  const pageSize = pagination?.pageSize || 10;\r\n  const currentPage = pagination?.currentPage || 1;\r\n  const totalPages = Math.ceil(totalCount / pageSize);\r\n\r\n  // Status badge variants\r\n  const statusVariants = {\r\n    pending: \"warning\",\r\n    approved: \"success\",\r\n    partially_approved: \"info\",\r\n    declined: \"destructive\",\r\n  };\r\n\r\n  return (\r\n    <div className=\"rounded-md border\">\r\n      <Table>\r\n        <TableHeader>\r\n          <TableRow>\r\n            <TableHead>ID</TableHead>\r\n            <TableHead>Employee</TableHead>\r\n            <TableHead>Branch</TableHead>\r\n            <TableHead>Category</TableHead>\r\n            <TableHead>Amount</TableHead>\r\n            <TableHead>Status</TableHead>\r\n            <TableHead>POP</TableHead>\r\n            <TableHead>Date</TableHead>\r\n            <TableHead className=\"text-right\">Actions</TableHead>\r\n          </TableRow>\r\n        </TableHeader>\r\n        <TableBody>\r\n          {isLoading && (\r\n            <TableRow>\r\n              <TableCell colSpan={9} className=\"h-24 text-center\">\r\n                <div className=\"flex justify-center items-center\">\r\n                  <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-primary\"></div>\r\n                  <span className=\"ml-2\">Loading expenses...</span>\r\n                </div>\r\n              </TableCell>\r\n            </TableRow>\r\n          )}\r\n          {!isLoading && data.length === 0 && (\r\n            <TableRow>\r\n              <TableCell colSpan={9} className=\"h-24 text-center\">\r\n                No expense requests found.\r\n              </TableCell>\r\n            </TableRow>\r\n          )}\r\n          {!isLoading &&\r\n            data.map((expense) => (\r\n              <TableRow key={expense.id}>\r\n                <TableCell className=\"font-medium\">{expense.id}</TableCell>\r\n                <TableCell>{expense.user?.name || \"Unknown\"}</TableCell>\r\n                <TableCell>{expense.branch?.name || \"Unknown\"}</TableCell>\r\n                <TableCell>\r\n                  {expense.category?.name || \"Uncategorized\"}\r\n                </TableCell>\r\n                <TableCell>{formatCurrency(expense.amount)}</TableCell>\r\n                <TableCell>\r\n                  <Badge\r\n                    variant={\r\n                      statusVariants[\r\n                        expense.status as keyof typeof statusVariants\r\n                      ] as any\r\n                    }\r\n                  >\r\n                    {expense.status.replace(\"_\", \" \")}\r\n                  </Badge>\r\n                </TableCell>\r\n                <TableCell>\r\n                  <TooltipProvider>\r\n                    <Tooltip>\r\n                      <TooltipTrigger asChild>\r\n                        <div className=\"flex items-center justify-center\">\r\n                          {expense.receipt_image ? (\r\n                            <FileCheck className=\"h-5 w-5 text-green-500\" />\r\n                          ) : (\r\n                            <FileX className=\"h-5 w-5 text-red-500\" />\r\n                          )}\r\n                        </div>\r\n                      </TooltipTrigger>\r\n                      <TooltipContent>\r\n                        {expense.receipt_image\r\n                          ? \"Receipt attached\"\r\n                          : \"No receipt attached\"}\r\n                      </TooltipContent>\r\n                    </Tooltip>\r\n                  </TooltipProvider>\r\n                </TableCell>\r\n                <TableCell>\r\n                  {new Date(expense.created_at).toLocaleDateString()}\r\n                </TableCell>\r\n                <TableCell className=\"text-right\">\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={() => router.push(`/expenses/${expense.id}`)}\r\n                  >\r\n                    <Eye className=\"h-4 w-4\" />\r\n                    <span className=\"sr-only\">View details</span>\r\n                  </Button>\r\n                </TableCell>\r\n              </TableRow>\r\n            ))}\r\n        </TableBody>\r\n      </Table>\r\n      {pagination && (\r\n        <div className=\"px-4 py-2 border-t\">\r\n          <DataPagination\r\n            currentPage={currentPage}\r\n            totalPages={totalPages}\r\n            onPageChange={pagination.onPageChange}\r\n            pageSize={pageSize}\r\n            onPageSizeChange={pagination.onPageSizeChange}\r\n            totalItems={totalCount}\r\n            isLoading={isLoading}\r\n            showPageSizeSelector={true}\r\n            showItemsInfo={true}\r\n            showFirstLastButtons={true}\r\n          />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;AAhBA;;;;;;;;;AAmCO,SAAS,kBAAkB,EAChC,IAAI,EACJ,UAAU,EACV,YAAY,KAAK,EACjB,UAAU,EACa;;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,YAAY,YAAY;IACzC,MAAM,cAAc,YAAY,eAAe;IAC/C,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;IAE1C,wBAAwB;IACxB,MAAM,iBAAiB;QACrB,SAAS;QACT,UAAU;QACV,oBAAoB;QACpB,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,oIAAA,CAAA,QAAK;;kCACJ,6LAAC,oIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8CACP,6LAAC,oIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,oIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,oIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,oIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,oIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,oIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,oIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,oIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,oIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAa;;;;;;;;;;;;;;;;;kCAGtC,6LAAC,oIAAA,CAAA,YAAS;;4BACP,2BACC,6LAAC,oIAAA,CAAA,WAAQ;0CACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oCAAC,SAAS;oCAAG,WAAU;8CAC/B,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;;4BAK9B,CAAC,aAAa,KAAK,MAAM,KAAK,mBAC7B,6LAAC,oIAAA,CAAA,WAAQ;0CACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oCAAC,SAAS;oCAAG,WAAU;8CAAmB;;;;;;;;;;;4BAKvD,CAAC,aACA,KAAK,GAAG,CAAC,CAAC,wBACR,6LAAC,oIAAA,CAAA,WAAQ;;sDACP,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAe,QAAQ,EAAE;;;;;;sDAC9C,6LAAC,oIAAA,CAAA,YAAS;sDAAE,QAAQ,IAAI,EAAE,QAAQ;;;;;;sDAClC,6LAAC,oIAAA,CAAA,YAAS;sDAAE,QAAQ,MAAM,EAAE,QAAQ;;;;;;sDACpC,6LAAC,oIAAA,CAAA,YAAS;sDACP,QAAQ,QAAQ,EAAE,QAAQ;;;;;;sDAE7B,6LAAC,oIAAA,CAAA,YAAS;sDAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;sDACzC,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SACE,cAAc,CACZ,QAAQ,MAAM,CACf;0DAGF,QAAQ,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;sDAGjC,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,sIAAA,CAAA,kBAAe;0DACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;sEACN,6LAAC,sIAAA,CAAA,iBAAc;4DAAC,OAAO;sEACrB,cAAA,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,aAAa,iBACpB,6LAAC,mNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;yFAErB,6LAAC,2MAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAIvB,6LAAC,sIAAA,CAAA,iBAAc;sEACZ,QAAQ,aAAa,GAClB,qBACA;;;;;;;;;;;;;;;;;;;;;;sDAKZ,6LAAC,oIAAA,CAAA,YAAS;sDACP,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;;;;;;sDAElD,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;;kEAEpD,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;mCAjDjB,QAAQ,EAAE;;;;;;;;;;;;;;;;;YAwDhC,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iJAAA,CAAA,iBAAc;oBACb,aAAa;oBACb,YAAY;oBACZ,cAAc,WAAW,YAAY;oBACrC,UAAU;oBACV,kBAAkB,WAAW,gBAAgB;oBAC7C,YAAY;oBACZ,WAAW;oBACX,sBAAsB;oBACtB,eAAe;oBACf,sBAAsB;;;;;;;;;;;;;;;;;AAMlC;GAjIgB;;QAMC,qIAAA,CAAA,YAAS;;;KANV", "debugId": null}}, {"offset": {"line": 1511, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1543, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/branches/api/branch-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { Branch, PaginatedResponse, UpdateBranchStatusRequest } from \"@/types\";\r\nimport { CreateBranchRequest, UpdateBranchRequest } from \"@/types/branch\";\r\n\r\nexport const branchService = {\r\n  getBranches: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Branch>> => {\r\n    try {\r\n      console.log(\"Calling API: /branches with params:\", params);\r\n      let response;\r\n\r\n      try {\r\n        // Try the Next.js API route first\r\n        response = await apiClient.get<any>(\"/branches\", { params });\r\n      } catch (error) {\r\n        console.warn(\r\n          \"Error fetching from Next.js API route, trying direct backend:\",\r\n          error\r\n        );\r\n        // If that fails, try the backend directly\r\n        const API_URL = process.env.NEXT_PUBLIC_API_URL;\r\n        const token =\r\n          localStorage.getItem(\"token\") || localStorage.getItem(\"accessToken\");\r\n\r\n        if (!token) {\r\n          throw new Error(\"No authentication token available\");\r\n        }\r\n\r\n        const backendResponse = await fetch(\r\n          `${API_URL}/branches${\r\n            params ? `?${new URLSearchParams(params)}` : \"\"\r\n          }`,\r\n          {\r\n            headers: {\r\n              Authorization: `Bearer ${token}`,\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n          }\r\n        );\r\n\r\n        if (!backendResponse.ok) {\r\n          throw new Error(`Backend API error: ${backendResponse.status}`);\r\n        }\r\n\r\n        response = await backendResponse.json();\r\n      }\r\n\r\n      console.log(\"Raw branches API response:\", response);\r\n\r\n      // Map API response to our Branch type\r\n      const mapApiBranchToBranch = (apiBranch: any): Branch => ({\r\n        ...apiBranch,\r\n      });\r\n\r\n      // If response is an array, convert to paginated format with mapped branches\r\n      if (Array.isArray(response)) {\r\n        console.log(\r\n          \"Branches response is an array, converting to paginated format\"\r\n        );\r\n        const mappedBranches = response.map(mapApiBranchToBranch);\r\n        return {\r\n          data: mappedBranches,\r\n          pagination: {\r\n            total: mappedBranches.length,\r\n            page: 1,\r\n            limit: mappedBranches.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Check if response is already in paginated format\r\n      if (response && response.data && Array.isArray(response.data)) {\r\n        console.log(\"Branches response is already in paginated format\");\r\n        const mappedBranches = response.data.map(mapApiBranchToBranch);\r\n        return {\r\n          data: mappedBranches,\r\n          pagination: response.pagination || {\r\n            total: mappedBranches.length,\r\n            page: 1,\r\n            limit: mappedBranches.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If response has data property but it's not an array, wrap it\r\n      if (response && response.data && !Array.isArray(response.data)) {\r\n        console.log(\r\n          \"Branches response has data property but it's not an array, wrapping it\"\r\n        );\r\n        return {\r\n          data: [mapApiBranchToBranch(response.data)],\r\n          pagination: response.pagination || {\r\n            total: 1,\r\n            page: 1,\r\n            limit: 1,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If response itself is not an array but contains branches directly\r\n      if (response && !Array.isArray(response) && !response.data) {\r\n        console.log(\"Response contains branches directly, converting to array\");\r\n        // Try to extract branches from the response\r\n        const branches = Object.values(response).filter(\r\n          (item) =>\r\n            typeof item === \"object\" &&\r\n            item !== null &&\r\n            \"id\" in item &&\r\n            \"name\" in item\r\n        );\r\n\r\n        if (branches.length > 0) {\r\n          const mappedBranches = branches.map(mapApiBranchToBranch);\r\n          return {\r\n            data: mappedBranches,\r\n            pagination: {\r\n              total: mappedBranches.length,\r\n              page: 1,\r\n              limit: mappedBranches.length,\r\n              totalPages: 1,\r\n            },\r\n          };\r\n        }\r\n      }\r\n\r\n      // Default fallback\r\n      console.log(\"Using default fallback for branches response\");\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error in getBranches:\", error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getBranchById: async (id: number): Promise<Branch> => {\r\n    return apiClient.get(`/branches/${id}`);\r\n  },\r\n\r\n  createBranch: async (branch: CreateBranchRequest): Promise<Branch> => {\r\n    // Send exactly what the API expects according to the guide\r\n    const apiRequest = {\r\n      tenant_id: branch.tenant_id,\r\n      name: branch.name,\r\n      location: branch.location,\r\n      region_id: branch.region_id,\r\n      is_hq: false, // Always set to false as requested\r\n      status: \"active\", // Default status\r\n    };\r\n\r\n    // Add optional fields if they exist\r\n    if (branch.phone) {\r\n      apiRequest.phone_number = branch.phone;\r\n    }\r\n    if (branch.email) {\r\n      apiRequest.email = branch.email;\r\n    }\r\n\r\n    console.log(\"Creating branch with data:\", apiRequest);\r\n    return apiClient.post(\"/branches\", apiRequest);\r\n  },\r\n\r\n  updateBranch: async (\r\n    id: number,\r\n    branch: UpdateBranchRequest\r\n  ): Promise<Branch> => {\r\n    // Ensure we're sending the correct fields to the API\r\n    const apiRequest = {\r\n      name: branch.name,\r\n      location: branch.location,\r\n      region_id: branch.region_id,\r\n      level: branch.level,\r\n    };\r\n\r\n    console.log(\"Updating branch with data:\", apiRequest);\r\n    return apiClient.put(`/branches/${id}`, apiRequest);\r\n  },\r\n\r\n  deleteBranch: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/branches/${id}`);\r\n  },\r\n\r\n  updateBranchStatus: async (\r\n    id: number,\r\n    status: UpdateBranchStatusRequest\r\n  ): Promise<Branch> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      status: status.status, // 'active' or 'inactive'\r\n    };\r\n\r\n    return apiClient.put(`/branches/${id}/status`, apiRequest);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAqBwB;AArBxB;;AAIO,MAAM,gBAAgB;IAC3B,aAAa,OACX;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,uCAAuC;YACnD,IAAI;YAEJ,IAAI;gBACF,kCAAkC;gBAClC,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,aAAa;oBAAE;gBAAO;YAC5D,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CACV,iEACA;gBAEF,0CAA0C;gBAC1C,MAAM;gBACN,MAAM,QACJ,aAAa,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC;gBAExD,IAAI,CAAC,OAAO;oBACV,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,kBAAkB,MAAM,MAC5B,GAAG,QAAQ,SAAS,EAClB,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,SAAS,GAAG,IAC7C,EACF;oBACE,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,OAAO;wBAChC,gBAAgB;oBAClB;gBACF;gBAGF,IAAI,CAAC,gBAAgB,EAAE,EAAE;oBACvB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,gBAAgB,MAAM,EAAE;gBAChE;gBAEA,WAAW,MAAM,gBAAgB,IAAI;YACvC;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,sCAAsC;YACtC,MAAM,uBAAuB,CAAC,YAA2B,CAAC;oBACxD,GAAG,SAAS;gBACd,CAAC;YAED,4EAA4E;YAC5E,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,QAAQ,GAAG,CACT;gBAEF,MAAM,iBAAiB,SAAS,GAAG,CAAC;gBACpC,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,eAAe,MAAM;wBAC5B,MAAM;wBACN,OAAO,eAAe,MAAM;wBAC5B,YAAY;oBACd;gBACF;YACF;YAEA,mDAAmD;YACnD,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC7D,QAAQ,GAAG,CAAC;gBACZ,MAAM,iBAAiB,SAAS,IAAI,CAAC,GAAG,CAAC;gBACzC,OAAO;oBACL,MAAM;oBACN,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO,eAAe,MAAM;wBAC5B,MAAM;wBACN,OAAO,eAAe,MAAM;wBAC5B,YAAY;oBACd;gBACF;YACF;YAEA,+DAA+D;YAC/D,IAAI,YAAY,SAAS,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC9D,QAAQ,GAAG,CACT;gBAEF,OAAO;oBACL,MAAM;wBAAC,qBAAqB,SAAS,IAAI;qBAAE;oBAC3C,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO;wBACP,MAAM;wBACN,OAAO;wBACP,YAAY;oBACd;gBACF;YACF;YAEA,oEAAoE;YACpE,IAAI,YAAY,CAAC,MAAM,OAAO,CAAC,aAAa,CAAC,SAAS,IAAI,EAAE;gBAC1D,QAAQ,GAAG,CAAC;gBACZ,4CAA4C;gBAC5C,MAAM,WAAW,OAAO,MAAM,CAAC,UAAU,MAAM,CAC7C,CAAC,OACC,OAAO,SAAS,YAChB,SAAS,QACT,QAAQ,QACR,UAAU;gBAGd,IAAI,SAAS,MAAM,GAAG,GAAG;oBACvB,MAAM,iBAAiB,SAAS,GAAG,CAAC;oBACpC,OAAO;wBACL,MAAM;wBACN,YAAY;4BACV,OAAO,eAAe,MAAM;4BAC5B,MAAM;4BACN,OAAO,eAAe,MAAM;4BAC5B,YAAY;wBACd;oBACF;gBACF;YACF;YAEA,mBAAmB;YACnB,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,eAAe,OAAO;QACpB,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;IACxC;IAEA,cAAc,OAAO;QACnB,2DAA2D;QAC3D,MAAM,aAAa;YACjB,WAAW,OAAO,SAAS;YAC3B,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS;YAC3B,OAAO;YACP,QAAQ;QACV;QAEA,oCAAoC;QACpC,IAAI,OAAO,KAAK,EAAE;YAChB,WAAW,YAAY,GAAG,OAAO,KAAK;QACxC;QACA,IAAI,OAAO,KAAK,EAAE;YAChB,WAAW,KAAK,GAAG,OAAO,KAAK;QACjC;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,aAAa;IACrC;IAEA,cAAc,OACZ,IACA;QAEA,qDAAqD;QACrD,MAAM,aAAa;YACjB,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS;YAC3B,OAAO,OAAO,KAAK;QACrB;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;IAC1C;IAEA,cAAc,OAAO;QACnB,OAAO,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;IAC3C;IAEA,oBAAoB,OAClB,IACA;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,QAAQ,OAAO,MAAM;QACvB;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,EAAE;IACjD;AACF", "debugId": null}}, {"offset": {"line": 1723, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/branches/hooks/use-branches.ts"], "sourcesContent": ["import { UpdateBranchRequest, UpdateBranchStatusRequest } from \"@/types\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport { branchService } from \"../api/branch-service\";\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { CreateBranchRequest } from \"@/types/branch\";\r\n\r\nexport function useBranches(\r\n  params?: Record<string, any>,\r\n  options?: { enabled?: boolean }\r\n) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n\r\n  return useQuery({\r\n    queryKey: [\"branches\", params],\r\n    queryFn: () => branchService.getBranches(params),\r\n    // Only run query when authentication is ready and enabled option is true (if provided)\r\n    enabled: isInitialized && !!accessToken && (options?.enabled !== false),\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\nexport function useBranch(id: number) {\r\n  return useQuery({\r\n    queryKey: [\"branches\", id],\r\n    queryFn: () => branchService.getBranchById(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateBranch() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (branch: CreateBranchRequest) =>\r\n      branchService.createBranch(branch),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch created\", {\r\n        description: \"The branch has been created successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error creating branch\", {\r\n        description:\r\n          error.message || \"An error occurred while creating the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateBranch(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (branch: UpdateBranchRequest) =>\r\n      branchService.updateBranch(id, branch),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch updated\", {\r\n        description: \"The branch has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating branch\", {\r\n        description:\r\n          error.message || \"An error occurred while updating the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteBranch() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => branchService.deleteBranch(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch deleted\", {\r\n        description: \"The branch has been deleted successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error deleting branch\", {\r\n        description:\r\n          error.message || \"An error occurred while deleting the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateBranchStatus(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (status: UpdateBranchStatusRequest) =>\r\n      branchService.updateBranchStatus(id, status),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch status updated\", {\r\n        description: \"The branch status has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating branch status\", {\r\n        description:\r\n          error.message ||\r\n          \"An error occurred while updating the branch status.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAGO,SAAS,YACd,MAA4B,EAC5B,OAA+B;;IAE/B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAEnD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAO;QAC9B,OAAO;oCAAE,IAAM,0JAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;;QACzC,uFAAuF;QACvF,SAAS,iBAAiB,CAAC,CAAC,eAAgB,SAAS,YAAY;QACjE,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,MAAM;oCAAE,CAAC;gBACP,IAAI,CAAC,MAAM;oBACT,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;oBACF;gBACF;gBACA,OAAO;YACT;;IACF;AACF;GA/BgB;;QAIyB,wIAAA,CAAA,gBAAa;QAE7C,8KAAA,CAAA,WAAQ;;;AA2BV,SAAS,UAAU,EAAU;;IAClC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAG;QAC1B,OAAO;kCAAE,IAAM,0JAAA,CAAA,gBAAa,CAAC,aAAa,CAAC;;QAC3C,SAAS,CAAC,CAAC;IACb;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAOV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,SACX,0JAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;;QAC7B,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa;gBACf;YACF;;QACA,OAAO;2CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IAnBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,SAAS,gBAAgB,EAAU;;IACxC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,SACX,0JAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,IAAI;;QACjC,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa;gBACf;YACF;;QACA,OAAO;2CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IApBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAmBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,KAAe,0JAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;;QACvD,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa;gBACf;YACF;;QACA,OAAO;2CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IAlBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiBb,SAAS,sBAAsB,EAAU;;IAC9C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,CAAC,SACX,0JAAA,CAAA,gBAAa,CAAC,kBAAkB,CAAC,IAAI;;QACvC,SAAS;iDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,yBAAyB;oBACrC,aAAa;gBACf;YACF;;QACA,OAAO;iDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;oBAC1C,aACE,MAAM,OAAO,IACb;gBACJ;YACF;;IACF;AACF;IArBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1959, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/api/reports-api-service.ts"], "sourcesContent": ["/**\r\n * Reports API Service\r\n * Handles API calls for the new reports endpoints from REPORTS_ENDPOINT_GUIDE.md\r\n */\r\n\r\nimport apiClient from \"@/lib/api-client\";\r\nimport { \r\n  StockHistoryParams, \r\n  StockHistoryResponse,\r\n  SalesSummaryParams,\r\n  SalesSummaryResponse,\r\n  BankingTransactionsParams,\r\n  BankingTransactionsResponse,\r\n  ExpenseExportParams,\r\n  ExpenseExportResponse\r\n} from \"@/types/reports-api\";\r\n\r\n/**\r\n * Reports API Service\r\n * Uses the new endpoints specified in REPORTS_ENDPOINT_GUIDE.md\r\n */\r\nconst reportsApiService = {\r\n  /**\r\n   * Get stock history for a specific product\r\n   * Endpoint: GET /api/v1/reports/stock-history\r\n   * Permission: stock_reports:read\r\n   */\r\n  getStockHistory: async (params: StockHistoryParams): Promise<StockHistoryResponse | Blob> => {\r\n    try {\r\n      const config: any = { params };\r\n      \r\n      // If format is excel, expect blob response\r\n      if (params.format === 'excel') {\r\n        config.responseType = 'blob';\r\n        const response = await apiClient.get(\"/reports/stock-history\", config);\r\n        return response as Blob;\r\n      }\r\n      \r\n      const response = await apiClient.get<StockHistoryResponse>(\"/reports/stock-history\", config);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching stock history:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get sales summary report with enhanced filtering\r\n   * Endpoint: GET /api/v1/reports/sales-summary\r\n   * Permission: sales_reports:read\r\n   */\r\n  getSalesSummary: async (params: SalesSummaryParams): Promise<SalesSummaryResponse | Blob> => {\r\n    try {\r\n      const config: any = { params };\r\n      \r\n      // If format is excel, expect blob response\r\n      if (params.format === 'excel') {\r\n        config.responseType = 'blob';\r\n        const response = await apiClient.get(\"/reports/sales-summary\", config);\r\n        return response as Blob;\r\n      }\r\n      \r\n      const response = await apiClient.get<SalesSummaryResponse>(\"/reports/sales-summary\", config);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching sales summary:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get banking transactions report with pagination\r\n   * Endpoint: GET /api/v1/reports/banking-transactions\r\n   * Permission: banking_reports:read\r\n   */\r\n  getBankingTransactions: async (params?: BankingTransactionsParams): Promise<BankingTransactionsResponse | Blob> => {\r\n    try {\r\n      const config: any = { params };\r\n      \r\n      // If format is excel, expect blob response\r\n      if (params?.format === 'excel') {\r\n        config.responseType = 'blob';\r\n        const response = await apiClient.get(\"/reports/banking-transactions\", config);\r\n        return response as Blob;\r\n      }\r\n      \r\n      const response = await apiClient.get<BankingTransactionsResponse>(\"/reports/banking-transactions\", config);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching banking transactions:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Export expenses in Excel format\r\n   * Endpoint: GET /api/v1/expense-analytics/export\r\n   * Permission: expense_reports:read\r\n   */\r\n  exportExpenses: async (params?: ExpenseExportParams): Promise<ExpenseExportResponse> => {\r\n    try {\r\n      const response = await apiClient.get(\"/expense-analytics/export\", {\r\n        params,\r\n        responseType: 'blob',\r\n      });\r\n      return response as Blob;\r\n    } catch (error) {\r\n      console.error('Error exporting expenses:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get stock history summary for dashboard\r\n   * Simplified version for dashboard widgets\r\n   */\r\n  getStockHistorySummary: async (params: { branch_id?: number }): Promise<{\r\n    total_products: number;\r\n    low_stock_count: number;\r\n    total_value: number;\r\n    last_updated: string;\r\n  }> => {\r\n    try {\r\n      // This would be a simplified endpoint for dashboard use\r\n      // For now, we'll use the main endpoint and process the data\r\n      const response = await apiClient.get<any>(\"/reports/stock-summary\", {\r\n        params,\r\n      });\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching stock summary:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get banking transactions summary for dashboard\r\n   * Simplified version for dashboard widgets\r\n   */\r\n  getBankingTransactionsSummary: async (params: { \r\n    start_date?: string; \r\n    end_date?: string; \r\n    branch_id?: number; \r\n  }): Promise<{\r\n    total_transactions: number;\r\n    total_amount: number;\r\n    pending_count: number;\r\n    completed_count: number;\r\n  }> => {\r\n    try {\r\n      // Get summary data from the main endpoint\r\n      const response = await reportsApiService.getBankingTransactions({\r\n        ...params,\r\n        limit: 1, // We only need the summary, not the transactions\r\n      }) as BankingTransactionsResponse;\r\n      \r\n      return {\r\n        total_transactions: response.summary.total_transactions,\r\n        total_amount: response.summary.total_amount,\r\n        pending_count: response.transactions.filter(t => t.status === 'pending').length,\r\n        completed_count: response.transactions.filter(t => t.status === 'completed').length,\r\n      };\r\n    } catch (error) {\r\n      console.error('Error fetching banking summary:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n\r\nexport default reportsApiService;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAYA;;;CAGC,GACD,MAAM,oBAAoB;IACxB;;;;GAIC,GACD,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,SAAc;gBAAE;YAAO;YAE7B,2CAA2C;YAC3C,IAAI,OAAO,MAAM,KAAK,SAAS;gBAC7B,OAAO,YAAY,GAAG;gBACtB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,0BAA0B;gBAC/D,OAAO;YACT;YAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAuB,0BAA0B;YACrF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,SAAc;gBAAE;YAAO;YAE7B,2CAA2C;YAC3C,IAAI,OAAO,MAAM,KAAK,SAAS;gBAC7B,OAAO,YAAY,GAAG;gBACtB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,0BAA0B;gBAC/D,OAAO;YACT;YAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAuB,0BAA0B;YACrF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,wBAAwB,OAAO;QAC7B,IAAI;YACF,MAAM,SAAc;gBAAE;YAAO;YAE7B,2CAA2C;YAC3C,IAAI,QAAQ,WAAW,SAAS;gBAC9B,OAAO,YAAY,GAAG;gBACtB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,iCAAiC;gBACtE,OAAO;YACT;YAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAA8B,iCAAiC;YACnG,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,6BAA6B;gBAChE;gBACA,cAAc;YAChB;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA;;;GAGC,GACD,wBAAwB,OAAO;QAM7B,IAAI;YACF,wDAAwD;YACxD,4DAA4D;YAC5D,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,0BAA0B;gBAClE;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA;;;GAGC,GACD,+BAA+B,OAAO;QAUpC,IAAI;YACF,0CAA0C;YAC1C,MAAM,WAAW,MAAM,kBAAkB,sBAAsB,CAAC;gBAC9D,GAAG,MAAM;gBACT,OAAO;YACT;YAEA,OAAO;gBACL,oBAAoB,SAAS,OAAO,CAAC,kBAAkB;gBACvD,cAAc,SAAS,OAAO,CAAC,YAAY;gBAC3C,eAAe,SAAS,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;gBAC/E,iBAAiB,SAAS,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;YACrF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 2101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/utils/export-handlers.ts"], "sourcesContent": ["/**\r\n * Centralized export handlers for reports\r\n * Provides consistent export experience across all reports\r\n */\r\n\r\nimport { format } from \"date-fns\";\r\nimport { toast } from \"sonner\";\r\nimport reportsApiService from \"../api/reports-api-service\";\r\nimport {\r\n  StockHistoryParams,\r\n  SalesSummaryParams,\r\n  BankingTransactionsParams,\r\n  ExpenseExportParams,\r\n} from \"@/types/reports-api\";\r\n\r\n/**\r\n * Download a blob as a file\r\n */\r\nexport const downloadBlob = (blob: Blob, filename: string) => {\r\n  const url = window.URL.createObjectURL(blob);\r\n  const link = document.createElement(\"a\");\r\n  link.href = url;\r\n  link.download = filename;\r\n  document.body.appendChild(link);\r\n  link.click();\r\n  document.body.removeChild(link);\r\n  window.URL.revokeObjectURL(url);\r\n};\r\n\r\n/**\r\n * Stock History Export Handler\r\n */\r\nexport const handleStockHistoryExport = async (params: StockHistoryParams) => {\r\n  try {\r\n    // Call the API service directly with format=excel\r\n    const exportParams = { ...params, format: \"excel\" as const };\r\n    const blob = (await reportsApiService.getStockHistory(\r\n      exportParams\r\n    )) as Blob;\r\n\r\n    // Generate filename\r\n    const today = format(new Date(), \"yyyy-MM-dd\");\r\n    const dateRange =\r\n      params.start_date && params.end_date\r\n        ? `${params.start_date}-to-${params.end_date}`\r\n        : today;\r\n    const filename = `stock-history-product-${params.product_id}-${dateRange}.xlsx`;\r\n\r\n    // Download the file\r\n    downloadBlob(blob, filename);\r\n\r\n    toast.success(`${filename} has been downloaded successfully.`);\r\n\r\n    return { success: true, filename };\r\n  } catch (error) {\r\n    console.error(\"Stock History export failed:\", error);\r\n    toast.error(\r\n      \"Failed to export Stock History. Please try again or contact support.\"\r\n    );\r\n    return { success: false, error };\r\n  }\r\n};\r\n\r\n/**\r\n * Sales Summary Export Handler\r\n */\r\nexport const handleSalesSummaryExport = async (params: SalesSummaryParams) => {\r\n  try {\r\n    // Call the API service directly with format=excel\r\n    const exportParams = { ...params, format: \"excel\" as const };\r\n    const blob = (await reportsApiService.getSalesSummary(\r\n      exportParams\r\n    )) as Blob;\r\n\r\n    // Generate filename\r\n    const dateRange = `${params.start_date}-to-${params.end_date}`;\r\n    const filters: string[] = [];\r\n    if (params.branch_id) filters.push(`branch-${params.branch_id}`);\r\n    if (params.product_id) filters.push(`product-${params.product_id}`);\r\n    if (params.category_id) filters.push(`category-${params.category_id}`);\r\n\r\n    const filterSuffix = filters.length > 0 ? `-${filters.join(\"-\")}` : \"\";\r\n    const filename = `sales-summary-${dateRange}${filterSuffix}.xlsx`;\r\n\r\n    // Download the file\r\n    downloadBlob(blob, filename);\r\n\r\n    toast.success(`${filename} has been downloaded successfully.`);\r\n\r\n    return { success: true, filename };\r\n  } catch (error) {\r\n    console.error(\"Sales Summary export failed:\", error);\r\n    toast.error(\r\n      \"Failed to export Sales Summary. Please try again or contact support.\"\r\n    );\r\n    return { success: false, error };\r\n  }\r\n};\r\n\r\n/**\r\n * Banking Transactions Export Handler\r\n */\r\nexport const handleBankingTransactionsExport = async (\r\n  params: BankingTransactionsParams\r\n) => {\r\n  try {\r\n    // Call the API service directly with format=excel\r\n    const exportParams = { ...params, format: \"excel\" as const };\r\n    const blob = (await reportsApiService.getBankingTransactions(\r\n      exportParams\r\n    )) as Blob;\r\n\r\n    // Generate filename\r\n    const today = format(new Date(), \"yyyy-MM-dd\");\r\n    const dateRange =\r\n      params.start_date && params.end_date\r\n        ? `${params.start_date}-to-${params.end_date}`\r\n        : today;\r\n\r\n    const filters: string[] = [];\r\n    if (params.branch_id) filters.push(`branch-${params.branch_id}`);\r\n    if (params.banking_method) filters.push(`method-${params.banking_method}`);\r\n    if (params.transaction_type)\r\n      filters.push(`type-${params.transaction_type}`);\r\n    if (params.status) filters.push(`status-${params.status}`);\r\n\r\n    const filterSuffix = filters.length > 0 ? `-${filters.join(\"-\")}` : \"\";\r\n    const filename = `banking-transactions-${dateRange}${filterSuffix}.xlsx`;\r\n\r\n    // Download the file\r\n    downloadBlob(blob, filename);\r\n\r\n    toast.success(`${filename} has been downloaded successfully.`);\r\n\r\n    return { success: true, filename };\r\n  } catch (error) {\r\n    console.error(\"Banking Transactions export failed:\", error);\r\n    toast.error(\r\n      \"Failed to export Banking Transactions. Please try again or contact support.\"\r\n    );\r\n    return { success: false, error };\r\n  }\r\n};\r\n\r\n/**\r\n * Expense Export Handler\r\n */\r\nexport const handleExpenseExport = async (params: ExpenseExportParams) => {\r\n  try {\r\n    // Call the API service directly\r\n    const blob = (await reportsApiService.exportExpenses(params)) as Blob;\r\n\r\n    // Generate filename\r\n    const today = format(new Date(), \"yyyy-MM-dd\");\r\n    const dateRange =\r\n      params.start_date && params.end_date\r\n        ? `${params.start_date}-to-${params.end_date}`\r\n        : today;\r\n\r\n    const reportType = params.report_type || \"detailed\";\r\n    const filters: string[] = [];\r\n    if (params.branch_id) filters.push(`branch-${params.branch_id}`);\r\n    if (params.category_id) filters.push(`category-${params.category_id}`);\r\n    if (params.user_id) filters.push(`user-${params.user_id}`);\r\n    if (params.status) filters.push(`status-${params.status}`);\r\n\r\n    const filterSuffix = filters.length > 0 ? `-${filters.join(\"-\")}` : \"\";\r\n    const filename = `expenses-${reportType}-${dateRange}${filterSuffix}.xlsx`;\r\n\r\n    // Download the file\r\n    downloadBlob(blob, filename);\r\n\r\n    toast.success(`${filename} has been downloaded successfully.`);\r\n\r\n    return { success: true, filename };\r\n  } catch (error) {\r\n    console.error(\"Expense export failed:\", error);\r\n    toast.error(\r\n      \"Failed to export Expense report. Please try again or contact support.\"\r\n    );\r\n    return { success: false, error };\r\n  }\r\n};\r\n\r\n/**\r\n * Bulk export handler for multiple reports\r\n */\r\nexport const handleBulkExport = async (\r\n  exports: Array<{\r\n    type:\r\n      | \"stock-history\"\r\n      | \"sales-summary\"\r\n      | \"banking-transactions\"\r\n      | \"expenses\";\r\n    params: any;\r\n    name: string;\r\n  }>\r\n) => {\r\n  const results: Array<{\r\n    type: string;\r\n    params: any;\r\n    name: string;\r\n    success: boolean;\r\n    filename?: string;\r\n    error?: unknown;\r\n  }> = [];\r\n\r\n  for (const exportItem of exports) {\r\n    try {\r\n      let result: { success: boolean; filename?: string; error?: unknown };\r\n      switch (exportItem.type) {\r\n        case \"stock-history\":\r\n          result = await handleStockHistoryExport(exportItem.params);\r\n          break;\r\n        case \"sales-summary\":\r\n          result = await handleSalesSummaryExport(exportItem.params);\r\n          break;\r\n        case \"banking-transactions\":\r\n          result = await handleBankingTransactionsExport(exportItem.params);\r\n          break;\r\n        case \"expenses\":\r\n          result = await handleExpenseExport(exportItem.params);\r\n          break;\r\n        default:\r\n          throw new Error(`Unknown export type: ${exportItem.type}`);\r\n      }\r\n\r\n      if (result.success && result.filename) {\r\n        results.push({\r\n          ...exportItem,\r\n          success: true,\r\n          filename: result.filename,\r\n        });\r\n      } else {\r\n        results.push({ ...exportItem, success: false, error: result.error });\r\n      }\r\n    } catch (error) {\r\n      results.push({ ...exportItem, success: false, error });\r\n    }\r\n  }\r\n\r\n  const successful = results.filter((r) => r.success).length;\r\n  const failed = results.filter((r) => !r.success).length;\r\n\r\n  if (failed === 0) {\r\n    toast.success(`All ${successful} reports exported successfully.`);\r\n  } else {\r\n    toast.error(`${successful} reports exported, ${failed} failed.`);\r\n  }\r\n\r\n  return results;\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAED;AACA;AACA;;;;AAWO,MAAM,eAAe,CAAC,MAAY;IACvC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;IACvC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;AAC7B;AAKO,MAAM,2BAA2B,OAAO;IAC7C,IAAI;QACF,kDAAkD;QAClD,MAAM,eAAe;YAAE,GAAG,MAAM;YAAE,QAAQ;QAAiB;QAC3D,MAAM,OAAQ,MAAM,iKAAA,CAAA,UAAiB,CAAC,eAAe,CACnD;QAGF,oBAAoB;QACpB,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;QACjC,MAAM,YACJ,OAAO,UAAU,IAAI,OAAO,QAAQ,GAChC,GAAG,OAAO,UAAU,CAAC,IAAI,EAAE,OAAO,QAAQ,EAAE,GAC5C;QACN,MAAM,WAAW,CAAC,sBAAsB,EAAE,OAAO,UAAU,CAAC,CAAC,EAAE,UAAU,KAAK,CAAC;QAE/E,oBAAoB;QACpB,aAAa,MAAM;QAEnB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,SAAS,kCAAkC,CAAC;QAE7D,OAAO;YAAE,SAAS;YAAM;QAAS;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT;QAEF,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAKO,MAAM,2BAA2B,OAAO;IAC7C,IAAI;QACF,kDAAkD;QAClD,MAAM,eAAe;YAAE,GAAG,MAAM;YAAE,QAAQ;QAAiB;QAC3D,MAAM,OAAQ,MAAM,iKAAA,CAAA,UAAiB,CAAC,eAAe,CACnD;QAGF,oBAAoB;QACpB,MAAM,YAAY,GAAG,OAAO,UAAU,CAAC,IAAI,EAAE,OAAO,QAAQ,EAAE;QAC9D,MAAM,UAAoB,EAAE;QAC5B,IAAI,OAAO,SAAS,EAAE,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,SAAS,EAAE;QAC/D,IAAI,OAAO,UAAU,EAAE,QAAQ,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,UAAU,EAAE;QAClE,IAAI,OAAO,WAAW,EAAE,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,OAAO,WAAW,EAAE;QAErE,MAAM,eAAe,QAAQ,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,MAAM,GAAG;QACpE,MAAM,WAAW,CAAC,cAAc,EAAE,YAAY,aAAa,KAAK,CAAC;QAEjE,oBAAoB;QACpB,aAAa,MAAM;QAEnB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,SAAS,kCAAkC,CAAC;QAE7D,OAAO;YAAE,SAAS;YAAM;QAAS;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT;QAEF,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAKO,MAAM,kCAAkC,OAC7C;IAEA,IAAI;QACF,kDAAkD;QAClD,MAAM,eAAe;YAAE,GAAG,MAAM;YAAE,QAAQ;QAAiB;QAC3D,MAAM,OAAQ,MAAM,iKAAA,CAAA,UAAiB,CAAC,sBAAsB,CAC1D;QAGF,oBAAoB;QACpB,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;QACjC,MAAM,YACJ,OAAO,UAAU,IAAI,OAAO,QAAQ,GAChC,GAAG,OAAO,UAAU,CAAC,IAAI,EAAE,OAAO,QAAQ,EAAE,GAC5C;QAEN,MAAM,UAAoB,EAAE;QAC5B,IAAI,OAAO,SAAS,EAAE,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,SAAS,EAAE;QAC/D,IAAI,OAAO,cAAc,EAAE,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,cAAc,EAAE;QACzE,IAAI,OAAO,gBAAgB,EACzB,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE,OAAO,gBAAgB,EAAE;QAChD,IAAI,OAAO,MAAM,EAAE,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;QAEzD,MAAM,eAAe,QAAQ,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,MAAM,GAAG;QACpE,MAAM,WAAW,CAAC,qBAAqB,EAAE,YAAY,aAAa,KAAK,CAAC;QAExE,oBAAoB;QACpB,aAAa,MAAM;QAEnB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,SAAS,kCAAkC,CAAC;QAE7D,OAAO;YAAE,SAAS;YAAM;QAAS;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT;QAEF,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAKO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,gCAAgC;QAChC,MAAM,OAAQ,MAAM,iKAAA,CAAA,UAAiB,CAAC,cAAc,CAAC;QAErD,oBAAoB;QACpB,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;QACjC,MAAM,YACJ,OAAO,UAAU,IAAI,OAAO,QAAQ,GAChC,GAAG,OAAO,UAAU,CAAC,IAAI,EAAE,OAAO,QAAQ,EAAE,GAC5C;QAEN,MAAM,aAAa,OAAO,WAAW,IAAI;QACzC,MAAM,UAAoB,EAAE;QAC5B,IAAI,OAAO,SAAS,EAAE,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,SAAS,EAAE;QAC/D,IAAI,OAAO,WAAW,EAAE,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,OAAO,WAAW,EAAE;QACrE,IAAI,OAAO,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE,OAAO,OAAO,EAAE;QACzD,IAAI,OAAO,MAAM,EAAE,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;QAEzD,MAAM,eAAe,QAAQ,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,MAAM,GAAG;QACpE,MAAM,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,YAAY,aAAa,KAAK,CAAC;QAE1E,oBAAoB;QACpB,aAAa,MAAM;QAEnB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,SAAS,kCAAkC,CAAC;QAE7D,OAAO;YAAE,SAAS;YAAM;QAAS;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT;QAEF,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAKO,MAAM,mBAAmB,OAC9B;IAUA,MAAM,UAOD,EAAE;IAEP,KAAK,MAAM,cAAc,QAAS;QAChC,IAAI;YACF,IAAI;YACJ,OAAQ,WAAW,IAAI;gBACrB,KAAK;oBACH,SAAS,MAAM,yBAAyB,WAAW,MAAM;oBACzD;gBACF,KAAK;oBACH,SAAS,MAAM,yBAAyB,WAAW,MAAM;oBACzD;gBACF,KAAK;oBACH,SAAS,MAAM,gCAAgC,WAAW,MAAM;oBAChE;gBACF,KAAK;oBACH,SAAS,MAAM,oBAAoB,WAAW,MAAM;oBACpD;gBACF;oBACE,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,WAAW,IAAI,EAAE;YAC7D;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,QAAQ,EAAE;gBACrC,QAAQ,IAAI,CAAC;oBACX,GAAG,UAAU;oBACb,SAAS;oBACT,UAAU,OAAO,QAAQ;gBAC3B;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC;oBAAE,GAAG,UAAU;oBAAE,SAAS;oBAAO,OAAO,OAAO,KAAK;gBAAC;YACpE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC;gBAAE,GAAG,UAAU;gBAAE,SAAS;gBAAO;YAAM;QACtD;IACF;IAEA,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAC,IAAM,EAAE,OAAO,EAAE,MAAM;IAC1D,MAAM,SAAS,QAAQ,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,OAAO,EAAE,MAAM;IAEvD,IAAI,WAAW,GAAG;QAChB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,WAAW,+BAA+B,CAAC;IAClE,OAAO;QACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,WAAW,mBAAmB,EAAE,OAAO,QAAQ,CAAC;IACjE;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2313, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/expenses/expenses-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { useBranches } from \"@/features/branches/hooks/use-branches\";\r\nimport { handleExpenseExport } from \"@/features/reports/utils/export-handlers\";\r\nimport { Download, ListFilter, Search } from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\ninterface ExpensesHeaderProps {\r\n  isLoading?: boolean;\r\n  searchQuery?: string;\r\n  onSearchChange?: (query: string) => void;\r\n  statusFilter?: string;\r\n  onStatusChange?: (status: string | undefined) => void;\r\n  branchFilter?: number;\r\n  onBranchChange?: (branchId: number | undefined) => void;\r\n  onPageReset?: () => void;\r\n}\r\n\r\nexport function ExpensesHeader({\r\n  isLoading = false,\r\n  searchQuery = \"\",\r\n  onSearchChange,\r\n  statusFilter,\r\n  onStatusChange,\r\n  branchFilter,\r\n  onBranchChange,\r\n  onPageReset,\r\n}: ExpensesHeaderProps) {\r\n  const router = useRouter();\r\n  const { data: user } = useCurrentUser();\r\n\r\n  // Fetch branches for filtering\r\n  const { data: branchesData } = useBranches({\r\n    limit: 100,\r\n    status: \"active\",\r\n  });\r\n\r\n  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);\r\n  const [isExporting, setIsExporting] = useState(false);\r\n\r\n  // Sync local search query with prop\r\n  useEffect(() => {\r\n    setLocalSearchQuery(searchQuery);\r\n  }, [searchQuery]);\r\n\r\n  // Handle status filter change\r\n  const handleStatusChange = (value: string) => {\r\n    if (value === \"all\") {\r\n      onStatusChange?.(undefined);\r\n    } else {\r\n      onStatusChange?.(value);\r\n    }\r\n    onPageReset?.(); // Reset to page 1 when filters change\r\n  };\r\n\r\n  // Handle branch filter change\r\n  const handleBranchChange = (value: string) => {\r\n    if (value === \"all\") {\r\n      onBranchChange?.(undefined);\r\n    } else {\r\n      onBranchChange?.(parseInt(value, 10));\r\n    }\r\n    onPageReset?.(); // Reset to page 1 when filters change\r\n  };\r\n\r\n  // Handle search\r\n  const handleSearch = () => {\r\n    onSearchChange?.(localSearchQuery);\r\n    onPageReset?.(); // Reset to page 1 when searching\r\n  };\r\n\r\n  // Handle search input keydown\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    if (e.key === \"Enter\") {\r\n      handleSearch();\r\n    }\r\n  };\r\n\r\n  // Handle clear filters\r\n  const handleClearFilters = () => {\r\n    setLocalSearchQuery(\"\");\r\n    onSearchChange?.(\"\");\r\n    onStatusChange?.(undefined);\r\n    onBranchChange?.(undefined);\r\n    onPageReset?.(); // Reset to page 1 when clearing filters\r\n  };\r\n\r\n  // Handle backend export\r\n  const handleExport = async () => {\r\n    setIsExporting(true);\r\n    try {\r\n      await handleExpenseExport({\r\n        branch_id: branchFilter,\r\n        status: statusFilter as\r\n          | \"pending\"\r\n          | \"approved\"\r\n          | \"partially_approved\"\r\n          | \"declined\"\r\n          | undefined,\r\n        report_type: \"detailed\", // Default to detailed report\r\n      });\r\n    } finally {\r\n      setIsExporting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n      <div className=\"flex flex-1 items-center gap-2\">\r\n        <div className=\"relative flex-1 sm:max-w-xs\">\r\n          <Search className=\"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n          <Input\r\n            type=\"search\"\r\n            placeholder=\"Search expenses...\"\r\n            className=\"pl-8\"\r\n            value={localSearchQuery}\r\n            onChange={(e) => setLocalSearchQuery(e.target.value)}\r\n            onKeyDown={handleKeyDown}\r\n          />\r\n        </div>\r\n        <Button variant=\"outline\" onClick={handleSearch}>\r\n          Search\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"flex items-center gap-2\">\r\n        {/* Branch Filter - Only show for company_admin and super_admin */}\r\n        {user?.role_name === \"company_admin\" && (\r\n          <Select\r\n            value={branchFilter ? branchFilter.toString() : \"all\"}\r\n            onValueChange={handleBranchChange}\r\n          >\r\n            <SelectTrigger className=\"w-[180px]\">\r\n              <SelectValue placeholder=\"Filter by branch\" />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"all\">All Branches</SelectItem>\r\n              {branchesData?.data?.map((branch) => (\r\n                <SelectItem key={branch.id} value={branch.id.toString()}>\r\n                  {branch.name}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n        )}\r\n\r\n        <Select\r\n          value={statusFilter || \"all\"}\r\n          onValueChange={handleStatusChange}\r\n        >\r\n          <SelectTrigger className=\"w-[180px]\">\r\n            <SelectValue placeholder=\"Filter by status\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Statuses</SelectItem>\r\n            <SelectItem value=\"pending\">Pending</SelectItem>\r\n            <SelectItem value=\"approved\">Approved</SelectItem>\r\n            <SelectItem value=\"partially_approved\">\r\n              Partially Approved\r\n            </SelectItem>\r\n            <SelectItem value=\"declined\">Declined</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n\r\n        <Button variant=\"outline\" onClick={handleClearFilters}>\r\n          Clear Filters\r\n        </Button>\r\n\r\n        <Button\r\n          onClick={handleExport}\r\n          variant=\"outline\"\r\n          size=\"default\"\r\n          disabled={isLoading || isExporting}\r\n        >\r\n          {isExporting ? (\r\n            <div className=\"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600\" />\r\n          ) : (\r\n            <Download className=\"h-4 w-4 mr-2\" />\r\n          )}\r\n          {isExporting ? \"Exporting...\" : \"Export to Excel\"}\r\n        </Button>\r\n\r\n        {user?.role_name === \"company_admin\" && (\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={() => router.push(\"/expenses/categories\")}\r\n          >\r\n            <ListFilter className=\"h-4 w-4 mr-2\" />\r\n            Categories\r\n          </Button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AAhBA;;;;;;;;;;AA6BO,SAAS,eAAe,EAC7B,YAAY,KAAK,EACjB,cAAc,EAAE,EAChB,cAAc,EACd,YAAY,EACZ,cAAc,EACd,YAAY,EACZ,cAAc,EACd,WAAW,EACS;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAEpC,+BAA+B;IAC/B,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,EAAE;QACzC,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,oBAAoB;QACtB;mCAAG;QAAC;KAAY;IAEhB,8BAA8B;IAC9B,MAAM,qBAAqB,CAAC;QAC1B,IAAI,UAAU,OAAO;YACnB,iBAAiB;QACnB,OAAO;YACL,iBAAiB;QACnB;QACA,iBAAiB,sCAAsC;IACzD;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB,CAAC;QAC1B,IAAI,UAAU,OAAO;YACnB,iBAAiB;QACnB,OAAO;YACL,iBAAiB,SAAS,OAAO;QACnC;QACA,iBAAiB,sCAAsC;IACzD;IAEA,gBAAgB;IAChB,MAAM,eAAe;QACnB,iBAAiB;QACjB,iBAAiB,iCAAiC;IACpD;IAEA,8BAA8B;IAC9B,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB;QACzB,oBAAoB;QACpB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB,wCAAwC;IAC3D;IAEA,wBAAwB;IACxB,MAAM,eAAe;QACnB,eAAe;QACf,IAAI;YACF,MAAM,CAAA,GAAA,4JAAA,CAAA,sBAAmB,AAAD,EAAE;gBACxB,WAAW;gBACX,QAAQ;gBAMR,aAAa;YACf;QACF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAW;;;;;;;;;;;;kCAGf,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;kCAAc;;;;;;;;;;;;0BAKnD,6LAAC;gBAAI,WAAU;;oBAEZ,MAAM,cAAc,iCACnB,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO,eAAe,aAAa,QAAQ,KAAK;wBAChD,eAAe;;0CAEf,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kDACZ,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;oCACvB,cAAc,MAAM,IAAI,CAAC,uBACxB,6LAAC,qIAAA,CAAA,aAAU;4CAAiB,OAAO,OAAO,EAAE,CAAC,QAAQ;sDAClD,OAAO,IAAI;2CADG,OAAO,EAAE;;;;;;;;;;;;;;;;;kCAQlC,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO,gBAAgB;wBACvB,eAAe;;0CAEf,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kDACZ,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;kDAC7B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAqB;;;;;;kDAGvC,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;;;;;;;;;;;;;kCAIjC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;kCAAoB;;;;;;kCAIvD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,SAAQ;wBACR,MAAK;wBACL,UAAU,aAAa;;4BAEtB,4BACC,6LAAC;gCAAI,WAAU;;;;;qDAEf,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAErB,cAAc,iBAAiB;;;;;;;oBAGjC,MAAM,cAAc,iCACnB,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS,IAAM,OAAO,IAAI,CAAC;;0CAE3B,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAOnD;GAhLgB;;QAUC,qIAAA,CAAA,YAAS;QACD,kJAAA,CAAA,iBAAc;QAGN,0JAAA,CAAA,cAAW;;;KAd5B", "debugId": null}}, {"offset": {"line": 2666, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/page-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { ChevronLeft } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\n\r\ninterface PageHeaderProps {\r\n  title: string;\r\n  description?: string;\r\n  backHref?: string;\r\n  actions?: React.ReactNode;\r\n}\r\n\r\nexport function PageHeader({\r\n  title,\r\n  description,\r\n  backHref,\r\n  actions,\r\n}: PageHeaderProps) {\r\n  return (\r\n    <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n      <div className=\"space-y-1\">\r\n        <div className=\"flex items-center gap-2\">\r\n          {backHref && (\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"h-8 w-8\"\r\n              asChild\r\n            >\r\n              <Link href={backHref}>\r\n                <ChevronLeft className=\"h-4 w-4\" />\r\n                <span className=\"sr-only\">Back</span>\r\n              </Link>\r\n            </Button>\r\n          )}\r\n          <h1 className=\"text-2xl font-bold tracking-tight\">{title}</h1>\r\n        </div>\r\n        {description && (\r\n          <p className=\"text-muted-foreground\">{description}</p>\r\n        )}\r\n      </div>\r\n      {actions && <div className=\"flex items-center gap-2\">{actions}</div>}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAaO,SAAS,WAAW,EACzB,KAAK,EACL,WAAW,EACX,QAAQ,EACR,OAAO,EACS;IAChB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,0BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,OAAO;0CAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM;;sDACV,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAIhC,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;;;;;;;oBAEpD,6BACC,6LAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;YAGzC,yBAAW,6LAAC;gBAAI,WAAU;0BAA2B;;;;;;;;;;;;AAG5D;KAhCgB", "debugId": null}}, {"offset": {"line": 2777, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/hooks/use-permissions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useCurrentUser } from \"./use-auth\";\r\nimport { usePermissionContext } from \"../context/permission-context\";\r\n\r\n// Define constants for resources and actions\r\nexport const RESOURCES = {\r\n  USERS: \"users\",\r\n  ROLES: \"roles\",\r\n  PERMISSIONS: \"permissions\",\r\n  PRODUCTS: \"products\",\r\n  CATEGORIES: \"categories\",\r\n  BRANDS: \"brands\",\r\n  INVENTORY: \"inventory\",\r\n  STOCK_ITEMS: \"stock_items\",\r\n  BRANCHES: \"branches\",\r\n  EMPLOYEES: \"employees\",\r\n  BANKING: \"banking\",\r\n  EXPENSES: \"expenses\",\r\n  EXPENSE_FIRST_APPROVAL: \"expense_first_approval\",\r\n  EXPENSE_SECOND_APPROVAL: \"expense_second_approval\",\r\n  SALES: \"sales\",\r\n  POS_SESSIONS: \"pos_sessions\",\r\n  CUSTOMERS: \"customers\",\r\n  PROFILE: \"profile\",\r\n  STOCK_LOCATIONS: \"stock_locations\",\r\n};\r\n\r\nexport const ACTIONS = {\r\n  CREATE: \"create\",\r\n  READ: \"read\",\r\n  UPDATE: \"update\",\r\n  DELETE: \"delete\",\r\n};\r\n\r\nexport const SCOPE = {\r\n  ANY: \"any\",\r\n  OWN: \"own\",\r\n};\r\n\r\nexport function usePermissions() {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const {\r\n    permissions,\r\n    isLoading: isPermissionsLoading,\r\n    hasPermission,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n  } = usePermissionContext();\r\n\r\n  // Check if the user is a company admin\r\n  const isCompanyAdmin = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the company_admin role\r\n    return (\r\n      user.role_name === \"company_admin\" ||\r\n      user.role_name === \"tenant_admin\" ||\r\n      user.role_name === \"super_admin\"\r\n    );\r\n  };\r\n\r\n  // Check if the user is a branch manager\r\n  const isBranchManager = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the branch_manager role\r\n    return user.role_name === \"branch_manager\";\r\n  };\r\n\r\n  // Check if the user can manage stock locations (create, update, delete)\r\n  const canManageStockLocations = (): boolean => {\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.CREATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.UPDATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.DELETE, SCOPE.ANY) ||\r\n      isCompanyAdmin()\r\n    );\r\n  };\r\n\r\n  // Check if the user can view stock locations\r\n  const canViewStockLocations = (): boolean => {\r\n    // Check for explicit permission or fallback to any authenticated user\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.READ, SCOPE.ANY) ||\r\n      !!user\r\n    );\r\n  };\r\n\r\n  // Check if the user can perform an action on a resource\r\n  const can = (\r\n    resource: string,\r\n    action: string,\r\n    scope: \"any\" | \"own\" = \"any\"\r\n  ): boolean => {\r\n    return hasPermission(resource, action, scope);\r\n  };\r\n\r\n  // Check if the user has any of the specified permissions\r\n  const hasAnyPermission = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.some(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  // Check if the user has all of the specified permissions\r\n  const hasAllPermissions = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.every(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  return {\r\n    isCompanyAdmin,\r\n    isBranchManager,\r\n    canManageStockLocations,\r\n    canViewStockLocations,\r\n    can,\r\n    hasPermission,\r\n    hasAnyPermission,\r\n    hasAllPermissions,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n    permissions, // Expose the raw permissions for debugging\r\n    isLoading: isUserLoading || isPermissionsLoading,\r\n    RESOURCES,\r\n    ACTIONS,\r\n    SCOPE,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;AAHA;;;AAMO,MAAM,YAAY;IACvB,OAAO;IACP,OAAO;IACP,aAAa;IACb,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,WAAW;IACX,aAAa;IACb,UAAU;IACV,WAAW;IACX,SAAS;IACT,UAAU;IACV,wBAAwB;IACxB,yBAAyB;IACzB,OAAO;IACP,cAAc;IACd,WAAW;IACX,SAAS;IACT,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEO,MAAM,QAAQ;IACnB,KAAK;IACL,KAAK;AACP;AAEO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EACJ,WAAW,EACX,WAAW,oBAAoB,EAC/B,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,CAAA,GAAA,+JAAA,CAAA,uBAAoB,AAAD;IAEvB,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,+CAA+C;QAC/C,OACE,KAAK,SAAS,KAAK,mBACnB,KAAK,SAAS,KAAK,kBACnB,KAAK,SAAS,KAAK;IAEvB;IAEA,wCAAwC;IACxC,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAElB,gDAAgD;QAChD,OAAO,KAAK,SAAS,KAAK;IAC5B;IAEA,wEAAwE;IACxE,MAAM,0BAA0B;QAC9B,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE;IAEJ;IAEA,6CAA6C;IAC7C,MAAM,wBAAwB;QAC5B,sEAAsE;QACtE,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,KAChE,CAAC,CAAC;IAEN;IAEA,wDAAwD;IACxD,MAAM,MAAM,CACV,UACA,QACA,QAAuB,KAAK;QAE5B,OAAO,cAAc,UAAU,QAAQ;IACzC;IAEA,yDAAyD;IACzD,MAAM,mBAAmB,CACvB;QAMA,OAAO,YAAY,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC1D,cAAc,UAAU,QAAQ;IAEpC;IAEA,yDAAyD;IACzD,MAAM,oBAAoB,CACxB;QAMA,OAAO,YAAY,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC3D,cAAc,UAAU,QAAQ;IAEpC;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,iBAAiB;QAC5B;QACA;QACA;IACF;AACF;GArGgB;;QACmC,kJAAA,CAAA,iBAAc;QAO3D,+JAAA,CAAA,uBAAoB", "debugId": null}}, {"offset": {"line": 2890, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/route-permissions.ts"], "sourcesContent": ["/**\r\n * Route Permission Mapping\r\n *\r\n * This file maps routes to the permissions required to access them.\r\n * Each key represents a route pattern, and the value is the permission required.\r\n */\r\n\r\nimport { NavigationPermission } from \"./navigation-permissions\";\r\n\r\nexport const routePermissions: Record<string, NavigationPermission> = {\r\n  // Dashboard routes\r\n  \"/dashboard\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/company\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/branch\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/finance\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/operations\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/stock\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/float\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/pos\": { resource: \"dashboard\", action: \"view\" },\r\n\r\n  // User management routes\r\n  \"/users\": { resource: \"users\", action: \"view\" },\r\n  \"/users/create\": { resource: \"users\", action: \"create\" },\r\n  \"/users/[id]\": { resource: \"users\", action: \"view\" },\r\n  \"/users/[id]/edit\": { resource: \"users\", action: \"update\" },\r\n\r\n  // Role management routes\r\n  \"/roles\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/create\": { resource: \"roles\", action: \"create\" },\r\n  \"/roles/[id]\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/[id]/edit\": { resource: \"roles\", action: \"update\" },\r\n\r\n  // RBAC routes\r\n  \"/rbac\": { resource: \"permissions\", action: \"manage\" },\r\n\r\n  // Branch management routes\r\n  \"/branches\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/create\": { resource: \"branches\", action: \"create\" },\r\n  \"/branches/[id]\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/[id]/edit\": { resource: \"branches\", action: \"update\" },\r\n\r\n  // Location management routes\r\n  \"/locations\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/locations/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n  \"/location-guides\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/location-guides/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n\r\n  // Employee management routes\r\n  \"/employees\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/create\": { resource: \"employees\", action: \"create\" },\r\n  \"/employees/[id]\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/[id]/edit\": { resource: \"employees\", action: \"update\" },\r\n\r\n  // Product management routes\r\n  \"/products\": { resource: \"products\", action: \"view\" },\r\n  \"/products/create\": { resource: \"products\", action: \"create\" },\r\n  \"/products/[id]\": { resource: \"products\", action: \"view\" },\r\n  \"/products/[id]/edit\": { resource: \"products\", action: \"update\" },\r\n\r\n  // Category management routes\r\n  \"/categories\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/create\": { resource: \"categories\", action: \"create\" },\r\n  \"/categories/[id]\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/[id]/edit\": { resource: \"categories\", action: \"update\" },\r\n\r\n  // Brand management routes\r\n  \"/brands\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/create\": { resource: \"brands\", action: \"create\" },\r\n  \"/brands/[id]\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/[id]/edit\": { resource: \"brands\", action: \"update\" },\r\n\r\n  // Inventory management routes\r\n  \"/inventory\": { resource: \"inventory\", action: \"view\" },\r\n  \"/inventory/transfers\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/bulk-transfer\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/reports\": { resource: \"inventory\", action: \"report\" },\r\n  \"/inventory/stock-cards\": { resource: \"inventory\", action: \"view\" },\r\n\r\n  // Banking routes\r\n  \"/banking\": { resource: \"banking\", action: \"view\" },\r\n  \"/banking/summary\": { resource: \"banking\", action: \"view\" },\r\n\r\n  // MPESA routes\r\n  \"/mpesa\": { resource: \"mpesa\", action: \"view\" },\r\n  \"/mpesa/transactions\": { resource: \"mpesa\", action: \"view\" },\r\n\r\n  // Float management routes\r\n  \"/float\": { resource: \"float\", action: \"view\" },\r\n  \"/float/movements\": { resource: \"float\", action: \"view\" },\r\n  \"/float/reconciliations\": { resource: \"float\", action: \"view\" },\r\n  \"/float/topups\": { resource: \"float\", action: \"view\" },\r\n\r\n  // DSA routes\r\n  \"/dsa\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/agents\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/assignments\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/reconciliations\": { resource: \"dsa\", action: \"view\" },\r\n\r\n  // Phone repairs routes\r\n  \"/phone-repairs\": { resource: \"phone_repairs\", action: \"view\" },\r\n  \"/phone-repairs/[id]\": { resource: \"phone_repairs\", action: \"view\" },\r\n\r\n  // Expense management routes\r\n  \"/expenses\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/create\": { resource: \"expenses\", action: \"create\" },\r\n  \"/expenses/[id]\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/[id]/edit\": { resource: \"expenses\", action: \"update\" },\r\n  \"/expense-analytics\": { resource: \"expenses\", action: \"view\" },\r\n\r\n  // Credit Note routes\r\n  \"/credit-notes\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/new\": { resource: \"invoices\", action: \"create\" },\r\n  \"/credit-notes/[id]\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/[id]/edit\": { resource: \"invoices\", action: \"update\" },\r\n\r\n  // Report routes\r\n  \"/reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-summary\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-item\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-category\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-employee\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-payment-type\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-banking\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/cash-status\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/running-balances\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-transactions\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/phone-repairs\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/expense-reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/dsa-sales\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/receipts\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/shifts\": { resource: \"reports\", action: \"view\" },\r\n\r\n  // Settings routes\r\n  \"/settings\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/system\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/company\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/payment-methods\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/health\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // Profile route\r\n  \"/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // POS routes\r\n  \"/pos\": { resource: \"pos\", action: \"view\" },\r\n  \"/pos/sessions\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]/edit\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/close\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/shift-closing\": {\r\n    resource: \"pos_sessions\",\r\n    action: \"view\",\r\n  },\r\n\r\n  // Sales routes\r\n  \"/sales\": { resource: \"sales\", action: \"view\" },\r\n  \"/sales/[id]\": { resource: \"sales\", action: \"view\" },\r\n\r\n  // Customer routes\r\n  \"/customers\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/create\": { resource: \"customers\", action: \"create\" },\r\n  \"/customers/[id]\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/[id]/edit\": { resource: \"customers\", action: \"update\" },\r\n\r\n  // Procurement routes\r\n  \"/procurement\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests/new\": { resource: \"procurement\", action: \"create\" },\r\n  \"/procurement/receipts\": { resource: \"procurement\", action: \"view\" },\r\n\r\n  // Supplier routes\r\n  \"/suppliers\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/create\": { resource: \"suppliers\", action: \"create\" },\r\n  \"/suppliers/[id]\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/[id]/edit\": { resource: \"suppliers\", action: \"update\" },\r\n\r\n  // Tenant routes (Super Admin only)\r\n  \"/tenants\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/create\": { resource: \"tenants\", action: \"create\" },\r\n  \"/tenants/[id]\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/[id]/edit\": { resource: \"tenants\", action: \"update\" },\r\n\r\n  // Super Admin routes\r\n  \"/super-admin\": { resource: \"tenants\", action: \"view\" },\r\n\r\n  // Debug routes\r\n  \"/permission-debug\": { resource: \"settings\", action: \"manage\" },\r\n};\r\n\r\n/**\r\n * Get the permission required for a route\r\n * @param route The route to check\r\n * @returns The permission required for the route, or null if no permission is required\r\n */\r\nexport function getRoutePermission(route: string): NavigationPermission | null {\r\n  // First try exact match\r\n  if (routePermissions[route]) {\r\n    return routePermissions[route];\r\n  }\r\n\r\n  // Then try dynamic routes\r\n  for (const [pattern, permission] of Object.entries(routePermissions)) {\r\n    if (pattern.includes(\"[\") && matchDynamicRoute(route, pattern)) {\r\n      return permission;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Check if a route matches a dynamic route pattern\r\n * @param route The route to check\r\n * @param pattern The pattern to match against\r\n * @returns Whether the route matches the pattern\r\n */\r\nfunction matchDynamicRoute(route: string, pattern: string): boolean {\r\n  const routeParts = route.split(\"/\");\r\n  const patternParts = pattern.split(\"/\");\r\n\r\n  if (routeParts.length !== patternParts.length) {\r\n    return false;\r\n  }\r\n\r\n  for (let i = 0; i < patternParts.length; i++) {\r\n    if (patternParts[i].startsWith(\"[\") && patternParts[i].endsWith(\"]\")) {\r\n      // This is a dynamic part, so it matches anything\r\n      continue;\r\n    }\r\n\r\n    if (patternParts[i] !== routeParts[i]) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  return true;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAIM,MAAM,mBAAyD;IACpE,mBAAmB;IACnB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC7D,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,kBAAkB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,cAAc;IACd,SAAS;QAAE,UAAU;QAAe,QAAQ;IAAS;IAErD,2BAA2B;IAC3B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAClE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,2BAA2B;QAAE,UAAU;QAAa,QAAQ;IAAS;IACrE,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,8BAA8B;QAAE,UAAU;QAAa,QAAQ;IAAS;IAExE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,eAAe;QAAE,UAAU;QAAc,QAAQ;IAAO;IACxD,sBAAsB;QAAE,UAAU;QAAc,QAAQ;IAAS;IACjE,oBAAoB;QAAE,UAAU;QAAc,QAAQ;IAAO;IAC7D,yBAAyB;QAAE,UAAU;QAAc,QAAQ;IAAS;IAEpE,0BAA0B;IAC1B,WAAW;QAAE,UAAU;QAAU,QAAQ;IAAO;IAChD,kBAAkB;QAAE,UAAU;QAAU,QAAQ;IAAS;IACzD,gBAAgB;QAAE,UAAU;QAAU,QAAQ;IAAO;IACrD,qBAAqB;QAAE,UAAU;QAAU,QAAQ;IAAS;IAE5D,8BAA8B;IAC9B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAW;IACpE,4BAA4B;QAAE,UAAU;QAAa,QAAQ;IAAW;IACxE,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAChE,0BAA0B;QAAE,UAAU;QAAa,QAAQ;IAAO;IAElE,iBAAiB;IACjB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,oBAAoB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE1D,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,uBAAuB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAE3D,0BAA0B;IAC1B,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAO;IACxD,0BAA0B;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9D,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAErD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,eAAe;QAAE,UAAU;QAAO,QAAQ;IAAO;IACjD,oBAAoB;QAAE,UAAU;QAAO,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAO,QAAQ;IAAO;IAE1D,uBAAuB;IACvB,kBAAkB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAC9D,uBAAuB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAEnE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAChE,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAE7D,qBAAqB;IACrB,iBAAiB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACxD,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC7D,2BAA2B;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEpE,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,kCAAkC;QAAE,UAAU;QAAW,QAAQ;IAAO;IACxE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,wBAAwB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC9D,6BAA6B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACnE,+BAA+B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACrE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,4BAA4B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClE,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC5D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC3D,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEzD,kBAAkB;IAClB,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,6BAA6B;QAAE,UAAU;QAAY,QAAQ;IAAS;IACtE,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC3D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE3D,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAElD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,iBAAiB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IAC5D,sBAAsB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IACjE,2BAA2B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACxE,4BAA4B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACzE,oCAAoC;QAClC,UAAU;QACV,QAAQ;IACV;IAEA,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IAEnD,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAC1D,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IACnE,6BAA6B;QAAE,UAAU;QAAe,QAAQ;IAAS;IACzE,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAEnE,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,mCAAmC;IACnC,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAC3D,iBAAiB;QAAE,UAAU;QAAW,QAAQ;IAAO;IACvD,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAE9D,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEtD,eAAe;IACf,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;AAChE;AAOO,SAAS,mBAAmB,KAAa;IAC9C,wBAAwB;IACxB,IAAI,gBAAgB,CAAC,MAAM,EAAE;QAC3B,OAAO,gBAAgB,CAAC,MAAM;IAChC;IAEA,0BAA0B;IAC1B,KAAK,MAAM,CAAC,SAAS,WAAW,IAAI,OAAO,OAAO,CAAC,kBAAmB;QACpE,IAAI,QAAQ,QAAQ,CAAC,QAAQ,kBAAkB,OAAO,UAAU;YAC9D,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,kBAAkB,KAAa,EAAE,OAAe;IACvD,MAAM,aAAa,MAAM,KAAK,CAAC;IAC/B,MAAM,eAAe,QAAQ,KAAK,CAAC;IAEnC,IAAI,WAAW,MAAM,KAAK,aAAa,MAAM,EAAE;QAC7C,OAAO;IACT;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,IAAI,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM;YAEpE;QACF;QAEA,IAAI,YAAY,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE;YACrC,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3457, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/role-guard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, useEffect, useState } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\nimport { getRoutePermission } from \"@/lib/route-permissions\";\r\nimport { hasRouteAccess } from \"@/lib/role-utils\"; // Keep for backward compatibility\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\nfunction mapActionToGrantAction(action: string): string {\r\n  switch (action) {\r\n    case \"view\":\r\n      return \"read:any\";\r\n    case \"create\":\r\n      return \"create:any\";\r\n    case \"update\":\r\n      return \"update:any\";\r\n    case \"delete\":\r\n      return \"delete:any\";\r\n    case \"manage\":\r\n      return \"update:any\"; // Manage maps to update:any\r\n    case \"transfer\":\r\n      return \"update:any\"; // Transfer maps to update:any\r\n    case \"report\":\r\n      return \"read:any\"; // Report maps to read:any\r\n    default:\r\n      return `${action}:any`;\r\n  }\r\n}\r\n\r\ninterface RoleGuardProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function RoleGuard({ children }: RoleGuardProps) {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const { hasPermission, isLoading: isPermissionsLoading } = usePermissions();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Track if this is the initial mount\r\n  const [isInitialMount, setIsInitialMount] = useState(true);\r\n\r\n  // Effect to mark initial mount as complete\r\n  useEffect(() => {\r\n    setIsInitialMount(false);\r\n  }, []);\r\n\r\n  // Effect to check access rights\r\n  useEffect(() => {\r\n    // Skip check if on auth pages\r\n    if (\r\n      pathname.startsWith(\"/login\") ||\r\n      pathname.startsWith(\"/forgot-password\") ||\r\n      pathname.startsWith(\"/reset-password\")\r\n    ) {\r\n      setLoading(\"role\", false);\r\n      return;\r\n    }\r\n\r\n    // Only show loading indicator on initial mount or when user data is loading\r\n    // and we haven't shown the loading screen before in this session\r\n    if (isInitialMount && isUserLoading && !hasShownLoading.role) {\r\n      setLoading(\"role\", true);\r\n      return;\r\n    } else {\r\n      setLoading(\"role\", false);\r\n    }\r\n\r\n    // If user data and permissions are loaded, check access rights\r\n    if (!isUserLoading && !isPermissionsLoading && user) {\r\n      // Safely access role_name with type checking\r\n      const roleName =\r\n        user && typeof user === \"object\" && \"role_name\" in user\r\n          ? user.role_name\r\n          : \"\";\r\n\r\n      // First try permission-based access control\r\n      const routePermission = getRoutePermission(pathname);\r\n\r\n      let hasAccess = true;\r\n      let accessMethod = \"default\";\r\n\r\n      // For MVP, prioritize role-based access control\r\n      hasAccess = hasRouteAccess(pathname, roleName);\r\n      accessMethod = \"role\";\r\n\r\n      // Add detailed debug logging\r\n      console.log(`[RoleGuard] Route: ${pathname}`);\r\n      console.log(`[RoleGuard] User role: ${roleName}`);\r\n      console.log(`[RoleGuard] Using role-based access (MVP approach)`);\r\n      console.log(\r\n        `[RoleGuard] Access ${hasAccess ? \"GRANTED\" : \"DENIED\"} by role check`\r\n      );\r\n\r\n      // If we have a permission mapping, check it too (for debugging purposes only)\r\n      if (routePermission) {\r\n        // Map the action to the format used in grants (e.g., \"view\" -> \"read:any\")\r\n        const grantAction = mapActionToGrantAction(routePermission.action);\r\n\r\n        // Check if user has the required permission (but don't use the result)\r\n        const permissionAccess = hasPermission(\r\n          routePermission.resource,\r\n          grantAction,\r\n          routePermission.scope\r\n        );\r\n\r\n        // Log the permission check result for debugging\r\n        console.log(\r\n          `[RoleGuard] Permission check (not used): ${\r\n            routePermission.resource\r\n          }:${routePermission.action}:${routePermission.scope || \"any\"}`\r\n        );\r\n        console.log(`[RoleGuard] Mapped to grant action: ${grantAction}`);\r\n        console.log(\r\n          `[RoleGuard] Permission would be ${\r\n            permissionAccess ? \"GRANTED\" : \"DENIED\"\r\n          } (for future reference)`\r\n        );\r\n      }\r\n\r\n      if (!hasAccess) {\r\n        console.log(\r\n          `[RoleGuard] Access denied for ${pathname}, redirecting to dashboard (method: ${accessMethod})`\r\n        );\r\n\r\n        // Redirect to the appropriate dashboard based on role\r\n        if (roleName === \"accountant\" || roleName === \"finance_manager\") {\r\n          console.log(`[RoleGuard] Redirecting to finance dashboard`);\r\n          router.replace(\"/dashboard\");\r\n          // router.replace(\"/dashboard/finance\");\r\n        } else if (roleName === \"stock_admin\") {\r\n          console.log(`[RoleGuard] Redirecting to stock dashboard`);\r\n          router.replace(\"/dashboard\");\r\n\r\n          // router.replace(\"/dashboard/stock\");\r\n        } else if (\r\n          roleName === \"operations\" ||\r\n          roleName === \"operations_manager\"\r\n        ) {\r\n          console.log(`[RoleGuard] Redirecting to operations dashboard`);\r\n          router.replace(\"/dashboard\");\r\n          // router.replace(\"/dashboard/operations\");\r\n        } else if (roleName === \"float_manager\") {\r\n          console.log(`[RoleGuard] Redirecting to float dashboard`);\r\n          // router.replace(\"/dashboard/float\");\r\n          router.replace(\"/dashboard\");\r\n        } else if (\r\n          roleName === \"pos_operator\" ||\r\n          roleName === \"shop_attendant\"\r\n        ) {\r\n          console.log(`[RoleGuard] Redirecting to POS dashboard`);\r\n          // router.replace(\"/dashboard/pos\");\r\n          router.replace(\"/dashboard\");\r\n        } else if (roleName === \"company_admin\") {\r\n          console.log(`[RoleGuard] Redirecting to company dashboard`);\r\n          // router.replace(\"/dashboard/company\");\r\n          router.replace(\"/dashboard\");\r\n        } else {\r\n          console.log(`[RoleGuard] Redirecting to main dashboard`);\r\n          router.replace(\"/dashboard\");\r\n        }\r\n      }\r\n\r\n      // User data is loaded, we can stop checking and mark as shown\r\n      setLoading(\"role\", false);\r\n      markLoadingShown(\"role\");\r\n    } else if (!isUserLoading && !isPermissionsLoading) {\r\n      // No user data and not loading, stop checking\r\n      setLoading(\"role\", false);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    user,\r\n    isUserLoading,\r\n    isPermissionsLoading,\r\n    pathname,\r\n    router,\r\n    isInitialMount,\r\n    hasShownLoading.role,\r\n    hasPermission,\r\n  ]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in checking state\r\n  useEffect(() => {\r\n    let timeoutId: NodeJS.Timeout | null = null;\r\n\r\n    if (isLoading.role) {\r\n      timeoutId = setTimeout(() => {\r\n        setLoading(\"role\", false);\r\n        markLoadingShown(\"role\");\r\n      }, 1500); // 1.5 second timeout for better UX\r\n    }\r\n\r\n    return () => {\r\n      if (timeoutId) clearTimeout(timeoutId);\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.role]);\r\n\r\n  // Show loading state only when actively checking role access\r\n  if (isLoading.role) {\r\n    return <LoadingScreen message=\"Checking access...\" />;\r\n  }\r\n\r\n  // Don't block rendering, the useEffect will handle redirects\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA,2NAAmD,kCAAkC;AACrF;;;AARA;;;;;;;;AAaA;;;;CAIC,GACD,SAAS,uBAAuB,MAAc;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,cAAc,4BAA4B;QACnD,KAAK;YACH,OAAO,cAAc,8BAA8B;QACrD,KAAK;YACH,OAAO,YAAY,0BAA0B;QAC/C;YACE,OAAO,GAAG,OAAO,IAAI,CAAC;IAC1B;AACF;AAMO,SAAS,UAAU,EAAE,QAAQ,EAAkB;;IACpD,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EAAE,aAAa,EAAE,WAAW,oBAAoB,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD;IACxE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD;IAEX,qCAAqC;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,kBAAkB;QACpB;8BAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,8BAA8B;YAC9B,IACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,uBACpB,SAAS,UAAU,CAAC,oBACpB;gBACA,WAAW,QAAQ;gBACnB;YACF;YAEA,4EAA4E;YAC5E,iEAAiE;YACjE,IAAI,kBAAkB,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;gBAC5D,WAAW,QAAQ;gBACnB;YACF,OAAO;gBACL,WAAW,QAAQ;YACrB;YAEA,+DAA+D;YAC/D,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,MAAM;gBACnD,6CAA6C;gBAC7C,MAAM,WACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;gBAEN,4CAA4C;gBAC5C,MAAM,kBAAkB,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAE3C,IAAI,YAAY;gBAChB,IAAI,eAAe;gBAEnB,gDAAgD;gBAChD,YAAY,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBACrC,eAAe;gBAEf,6BAA6B;gBAC7B,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;gBAC5C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU;gBAChD,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;gBAChE,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,YAAY,YAAY,SAAS,cAAc,CAAC;gBAGxE,8EAA8E;gBAC9E,IAAI,iBAAiB;oBACnB,2EAA2E;oBAC3E,MAAM,cAAc,uBAAuB,gBAAgB,MAAM;oBAEjE,uEAAuE;oBACvE,MAAM,mBAAmB,cACvB,gBAAgB,QAAQ,EACxB,aACA,gBAAgB,KAAK;oBAGvB,gDAAgD;oBAChD,QAAQ,GAAG,CACT,CAAC,yCAAyC,EACxC,gBAAgB,QAAQ,CACzB,CAAC,EAAE,gBAAgB,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,IAAI,OAAO;oBAEhE,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,aAAa;oBAChE,QAAQ,GAAG,CACT,CAAC,gCAAgC,EAC/B,mBAAmB,YAAY,SAChC,uBAAuB,CAAC;gBAE7B;gBAEA,IAAI,CAAC,WAAW;oBACd,QAAQ,GAAG,CACT,CAAC,8BAA8B,EAAE,SAAS,oCAAoC,EAAE,aAAa,CAAC,CAAC;oBAGjG,sDAAsD;oBACtD,IAAI,aAAa,gBAAgB,aAAa,mBAAmB;wBAC/D,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;wBAC1D,OAAO,OAAO,CAAC;oBACf,wCAAwC;oBAC1C,OAAO,IAAI,aAAa,eAAe;wBACrC,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC;wBACxD,OAAO,OAAO,CAAC;oBAEf,sCAAsC;oBACxC,OAAO,IACL,aAAa,gBACb,aAAa,sBACb;wBACA,QAAQ,GAAG,CAAC,CAAC,+CAA+C,CAAC;wBAC7D,OAAO,OAAO,CAAC;oBACf,2CAA2C;oBAC7C,OAAO,IAAI,aAAa,iBAAiB;wBACvC,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC;wBACxD,sCAAsC;wBACtC,OAAO,OAAO,CAAC;oBACjB,OAAO,IACL,aAAa,kBACb,aAAa,kBACb;wBACA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,CAAC;wBACtD,oCAAoC;wBACpC,OAAO,OAAO,CAAC;oBACjB,OAAO,IAAI,aAAa,iBAAiB;wBACvC,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;wBAC1D,wCAAwC;wBACxC,OAAO,OAAO,CAAC;oBACjB,OAAO;wBACL,QAAQ,GAAG,CAAC,CAAC,yCAAyC,CAAC;wBACvD,OAAO,OAAO,CAAC;oBACjB;gBACF;gBAEA,8DAA8D;gBAC9D,WAAW,QAAQ;gBACnB,iBAAiB;YACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;gBAClD,8CAA8C;gBAC9C,WAAW,QAAQ;YACrB;QACA,uDAAuD;QACzD;8BAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB,IAAI;QACpB;KACD;IAED,kEAAkE;IAClE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,YAAmC;YAEvC,IAAI,UAAU,IAAI,EAAE;gBAClB,YAAY;2CAAW;wBACrB,WAAW,QAAQ;wBACnB,iBAAiB;oBACnB;0CAAG,OAAO,mCAAmC;YAC/C;YAEA;uCAAO;oBACL,IAAI,WAAW,aAAa;gBAC9B;;QACA,uDAAuD;QACzD;8BAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6DAA6D;IAC7D,IAAI,UAAU,IAAI,EAAE;QAClB,qBAAO,6LAAC,yJAAA,CAAA,gBAAa;YAAC,SAAQ;;;;;;IAChC;IAEA,6DAA6D;IAC7D,qBAAO;kBAAG;;AACZ;GA/KgB;;QACmC,kJAAA,CAAA,iBAAc;QACJ,yJAAA,CAAA,iBAAc;QAC1D,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QAE1B,yJAAA,CAAA,aAAU;;;KANE", "debugId": null}}, {"offset": {"line": 3669, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;YACvE,MAAM;kDAAW;oBACf,YAAY,OAAO,UAAU,GAAG;gBAClC;;YACA,IAAI,gBAAgB,CAAC,UAAU;YAC/B,YAAY,OAAO,UAAU,GAAG;YAChC;yCAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;QACjD;gCAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX;GAdgB", "debugId": null}}, {"offset": {"line": 3706, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 3742, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 3938, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 3969, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { VariantProps, cva } from \"class-variance-authority\";\r\nimport { PanelLeftIcon } from \"lucide-react\";\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\r\nconst SIDEBAR_WIDTH = \"16rem\";\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\";\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  openMobile: boolean;\r\n  setOpenMobile: (open: boolean) => void;\r\n  isMobile: boolean;\r\n  toggleSidebar: () => void;\r\n};\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext);\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\");\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n}) {\r\n  const isMobile = useIsMobile();\r\n  const [openMobile, setOpenMobile] = React.useState(false);\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen);\r\n  const open = openProp ?? _open;\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value;\r\n      if (setOpenProp) {\r\n        setOpenProp(openState);\r\n      } else {\r\n        _setOpen(openState);\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\r\n    },\r\n    [setOpenProp, open]\r\n  );\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\r\n  }, [isMobile, setOpen, setOpenMobile]);\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault();\r\n        toggleSidebar();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [toggleSidebar]);\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\";\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  );\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  );\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\";\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\";\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\";\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event);\r\n        toggleSidebar();\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  );\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  isActive?: boolean;\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n  const { isMobile, state } = useSidebar();\r\n\r\n  // Use useMemo to prevent recreating the button on every render\r\n  const button = React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, variant, className, props]);\r\n\r\n  // Use useMemo for tooltipContent to prevent recalculation on every render\r\n  const tooltipContent = React.useMemo(() => {\r\n    if (!tooltip) return null;\r\n    return typeof tooltip === \"string\" ? { children: tooltip } : tooltip;\r\n  }, [tooltip]);\r\n\r\n  if (!tooltip) {\r\n    return button;\r\n  }\r\n\r\n  // Use useMemo for the hidden prop to prevent recalculation on every render\r\n  const isHidden = React.useMemo(() => {\r\n    return state !== \"collapsed\" || isMobile;\r\n  }, [state, isMobile]);\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={isHidden}\r\n        {...tooltipContent}\r\n      />\r\n    </Tooltip>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  showOnHover?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, className, showOnHover, props]);\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [className, props]);\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean;\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`;\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean;\r\n  size?: \"sm\" | \"md\";\r\n  isActive?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, className, props]);\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AApBA;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;gDAC9B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;QACpG;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;sDAAE;YACtC,OAAO,WAAW;8DAAc,CAAC,OAAS,CAAC;+DAAQ;8DAAQ,CAAC,OAAS,CAAC;;QACxE;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;qCAAE;YACd,MAAM;2DAAgB,CAAC;oBACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;wBACA,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;iDAC/B,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6LAAC,sIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6LAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;IAhGS;;QAaU,gIAAA,CAAA,cAAW;;;KAbrB;AAkGT,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6LAAC,oIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6LAAC,oIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,oIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,6LAAC,oIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;IApGS;;QAYgD;;;MAZhD;AAsGT,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,gBAAa;;;;;0BACd,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IAxBS;;QAKmB;;;MALnB;AA0BT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;IAvBS;;QACmB;;;MADnB;AAyBT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,6LAAC,wIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;OAnBS;AAqBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OArBS;AAuBT,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,MAAM,4BAA4B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAClC,+rBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;;IAChD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,+DAA+D;IAC/D,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;6CAAE,kBAC3B,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,aAAW;gBACX,eAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;oBAAE;oBAAS;gBAAK,IAAI;gBAC3D,GAAG,KAAK;;;;;;4CAEV;QAAC;QAAM;QAAM;QAAU;QAAS;QAAW;KAAM;IAEpD,0EAA0E;IAC1E,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qDAAE;YACnC,IAAI,CAAC,SAAS,OAAO;YACrB,OAAO,OAAO,YAAY,WAAW;gBAAE,UAAU;YAAQ,IAAI;QAC/D;oDAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,2EAA2E;IAC3E,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;+CAAE;YAC7B,OAAO,UAAU,eAAe;QAClC;8CAAG;QAAC;QAAO;KAAS;IAEpB,qBACE,6LAAC,sIAAA,CAAA,UAAO;;0BACN,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6LAAC,sIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ;gBACP,GAAG,cAAc;;;;;;;;;;;;AAI1B;IAtDS;;QAcqB;;;OAdrB;AAwDT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qCAAE,kBACnB,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;gBAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;gBAED,GAAG,KAAK;;;;;;oCAEV;QAAC;QAAM;QAAW;QAAa;KAAM;AAC1C;IA/BS;OAAA;AAiCT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;;IAC5B,kEAAkE;IAClE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;oCAAE,kBACnB,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;gBAED,GAAG,KAAK;;;;;;mCAEV;QAAC;QAAW;KAAM;AACvB;IArBS;OAAA;AAuBT,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;8CAAE;YAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;QAClD;6CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;IApCS;OAAA;AAsCT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OAbS;AAeT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;wCAAE,kBACnB,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,aAAW;gBACX,eAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;gBAED,GAAG,KAAK;;;;;;uCAEV;QAAC;QAAM;QAAM;QAAU;QAAW;KAAM;AAC7C;IA/BS;OAAA", "debugId": null}}, {"offset": {"line": 4775, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/navigation-link.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { ReactNode, MouseEvent, useMemo } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface NavigationLinkProps {\r\n  href: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n  onClick?: (e: MouseEvent<HTMLAnchorElement>) => void;\r\n  replace?: boolean;\r\n  prefetch?: boolean;\r\n  scroll?: boolean;\r\n  exact?: boolean;\r\n}\r\n\r\n/**\r\n * NavigationLink component that ensures client-side navigation\r\n * This component wraps Next.js Link component and provides a consistent way to navigate\r\n */\r\nexport function NavigationLink({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(className)}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n      aria-current={isActive ? \"page\" : undefined}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n\r\n/**\r\n * NavigationButton component that looks like a button but navigates like a link\r\n */\r\nexport function NavigationButton({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(\r\n        \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n        className\r\n      )}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAsBO,SAAS,eAAe,EAC7B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;QACzD;2CAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;QACb,gBAAc,WAAW,SAAS;kBAEjC;;;;;;AAGP;GA5CgB;;QAUC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAXd;AAiDT,SAAS,iBAAiB,EAC/B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;QACzD;6CAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0RACA;QAEF,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;kBAEZ;;;;;;AAGP;IA9CgB;;QAUC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;MAXd", "debugId": null}}, {"offset": {"line": 4896, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot=\"collapsible-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot=\"collapsible-content\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,6LAAC,0KAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,6LAAC,0KAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,6LAAC,0KAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS", "debugId": null}}, {"offset": {"line": 4953, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-main.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\nimport { ChevronRight, CircleIcon, type LucideIcon } from \"lucide-react\";\r\n\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport {\r\n  SidebarGroup,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavMain({\r\n  items,\r\n}: {\r\n  items: {\r\n    title: string;\r\n    url: string;\r\n    icon?: LucideIcon;\r\n    isActive?: boolean;\r\n    items?: {\r\n      title: string;\r\n      url: string;\r\n    }[];\r\n  }[];\r\n}) {\r\n  return (\r\n    <SidebarGroup>\r\n      <SidebarMenu>\r\n        {items.map((item) => {\r\n          // Special case for Dashboard - make it a direct link\r\n          if (item.title === \"Dashboard\") {\r\n            return (\r\n              <SidebarMenuItem key={item.title}>\r\n                <SidebarMenuButton\r\n                  asChild\r\n                  tooltip={item.title}\r\n                  isActive={item.isActive}\r\n                  className=\"nav-main-item\"\r\n                >\r\n                  <NavigationLink href={item.url}>\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                  </NavigationLink>\r\n                </SidebarMenuButton>\r\n              </SidebarMenuItem>\r\n            );\r\n          }\r\n\r\n          // For all other items, use the collapsible dropdown\r\n          return (\r\n            <Collapsible\r\n              key={item.title}\r\n              asChild\r\n              defaultOpen={item.isActive}\r\n              className=\"group/collapsible mb-2\"\r\n            >\r\n              <SidebarMenuItem>\r\n                <CollapsibleTrigger asChild>\r\n                  <SidebarMenuButton\r\n                    tooltip={item.title}\r\n                    isActive={item.isActive}\r\n                    className=\"nav-main-item\"\r\n                  >\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                    <ChevronRight className=\"ml-auto h-2 w-2 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 nav-main-item-chevron\" />\r\n                  </SidebarMenuButton>\r\n                </CollapsibleTrigger>\r\n                <CollapsibleContent>\r\n                  <SidebarMenuSub className=\"nav-main-sub mt-2\">\r\n                    {item.items?.map((subItem) => (\r\n                      <SidebarMenuSubItem key={subItem.title}>\r\n                        <SidebarMenuSubButton asChild>\r\n                          <NavigationLink href={subItem.url} exact={true}>\r\n                            {/* No circle icon for child items */}\r\n                            <span className=\"text-xs\">{subItem.title}</span>\r\n                          </NavigationLink>\r\n                        </SidebarMenuSubButton>\r\n                      </SidebarMenuSubItem>\r\n                    ))}\r\n                  </SidebarMenuSub>\r\n                </CollapsibleContent>\r\n              </SidebarMenuItem>\r\n            </Collapsible>\r\n          );\r\n        })}\r\n      </SidebarMenu>\r\n    </SidebarGroup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAKA;AAVA;;;;;;AAoBO,SAAS,QAAQ,EACtB,KAAK,EAYN;IACC,qBACE,6LAAC,sIAAA,CAAA,eAAY;kBACX,cAAA,6LAAC,sIAAA,CAAA,cAAW;sBACT,MAAM,GAAG,CAAC,CAAC;gBACV,qDAAqD;gBACrD,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kCACd,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,SAAS,KAAK,KAAK;4BACnB,UAAU,KAAK,QAAQ;4BACvB,WAAU;sCAEV,cAAA,6LAAC,iJAAA,CAAA,iBAAc;gCAAC,MAAM,KAAK,GAAG;;kDAC5B,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI,kBAAI,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CAClC,KAAK,QAAQ,kBACZ,6LAAC,6MAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAG1B,6LAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAfG,KAAK,KAAK;;;;;gBAqBpC;gBAEA,oDAAoD;gBACpD,qBACE,6LAAC,0IAAA,CAAA,cAAW;oBAEV,OAAO;oBACP,aAAa,KAAK,QAAQ;oBAC1B,WAAU;8BAEV,cAAA,6LAAC,sIAAA,CAAA,kBAAe;;0CACd,6LAAC,0IAAA,CAAA,qBAAkB;gCAAC,OAAO;0CACzB,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;oCAChB,SAAS,KAAK,KAAK;oCACnB,UAAU,KAAK,QAAQ;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI,kBAAI,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDAClC,KAAK,QAAQ,kBACZ,6LAAC,6MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAG1B,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;sDAEb,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG5B,6LAAC,0IAAA,CAAA,qBAAkB;0CACjB,cAAA,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,6LAAC,sIAAA,CAAA,qBAAkB;sDACjB,cAAA,6LAAC,sIAAA,CAAA,uBAAoB;gDAAC,OAAO;0DAC3B,cAAA,6LAAC,iJAAA,CAAA,iBAAc;oDAAC,MAAM,QAAQ,GAAG;oDAAE,OAAO;8DAExC,cAAA,6LAAC;wDAAK,WAAU;kEAAW,QAAQ,KAAK;;;;;;;;;;;;;;;;2CAJrB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;mBA3BzC,KAAK,KAAK;;;;;YAyCrB;;;;;;;;;;;AAIR;KA5FgB", "debugId": null}}, {"offset": {"line": 5171, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 5233, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 5529, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-user.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Chev<PERSON>UpDown, LogOut } from \"lucide-react\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavUser({\r\n  user,\r\n}: {\r\n  user: {\r\n    name: string;\r\n    email: string;\r\n    avatar: string;\r\n  };\r\n}) {\r\n  const { isMobile } = useSidebar();\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground nav-user\"\r\n            >\r\n              <Avatar className=\"h-6 w-6 rounded-lg nav-user-avatar\">\r\n                <AvatarImage src={user.avatar} alt={user.name} />\r\n                <AvatarFallback className=\"rounded-lg text-xs\">\r\n                  {user.name.charAt(0)}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left leading-tight nav-user-details\">\r\n                <span className=\"truncate text-xs font-medium\">\r\n                  {user.name}\r\n                </span>\r\n                <span className=\"truncate text-[10px]\">{user.email}</span>\r\n              </div>\r\n              <ChevronsUpDown className=\"ml-auto h-3 w-3 nav-user-chevron\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                  <AvatarImage src={user.avatar} alt={user.name} />\r\n                  <AvatarFallback className=\"rounded-lg\">CN</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-medium\">{user.name}</span>\r\n                  <span className=\"truncate text-xs\">{user.email}</span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuGroup>\r\n              <DropdownMenuItem>\r\n                <BadgeCheck className=\"mr-2 h-3.5 w-3.5\" />\r\n                <span className=\"text-xs\">Account</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem>\r\n              <LogOut className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"text-xs\">Log out</span>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AACA;AASA;;;AAdA;;;;;AAqBO,SAAS,QAAQ,EACtB,IAAI,EAOL;;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IAE9B,qBACE,6LAAC,sIAAA,CAAA,cAAW;kBACV,cAAA,6LAAC,sIAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC,+IAAA,CAAA,eAAY;;kCACX,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,6LAAC,qIAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM;4CAAE,KAAK,KAAK,IAAI;;;;;;sDAC7C,6LAAC,qIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,KAAK,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;8CAGtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDACb,KAAK,IAAI;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAAwB,KAAK,KAAK;;;;;;;;;;;;8CAEpD,6LAAC,iOAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,6LAAC,+IAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,6LAAC,+IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM;oDAAE,KAAK,KAAK,IAAI;;;;;;8DAC7C,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAa;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI;;;;;;8DACjD,6LAAC;oDAAK,WAAU;8DAAoB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAIpD,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0CACtB,6LAAC,+IAAA,CAAA,oBAAiB;0CAChB,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;;sDACf,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0CACtB,6LAAC,+IAAA,CAAA,mBAAgB;;kDACf,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;GAtEgB;;QASO,sIAAA,CAAA,aAAU;;;KATjB", "debugId": null}}, {"offset": {"line": 5801, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport \"@/components/ui/sidebar-collapsed.css\";\r\nimport \"@/components/ui/sidebar-compact.css\";\r\nimport {\r\n  Banknote,\r\n  BarChart3 as BarChart3Icon,\r\n  Building as BuildingIcon,\r\n  CreditCard,\r\n  FileText,\r\n  GalleryVerticalEnd,\r\n  Home as HomeIcon,\r\n  Landmark,\r\n  Package,\r\n  Settings,\r\n  ShoppingCart,\r\n  Smartphone,\r\n  UserCheck,\r\n} from \"lucide-react\";\r\n\r\nimport { NavMain } from \"@/components/nav-main\";\r\nimport { NavUser } from \"@/components/nav-user\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n// Import usePermissions for future use\r\n// import { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\n// import { getRoutePermission } from \"@/lib/route-permissions\";\r\n// import { navigationPermissions } from \"@/lib/navigation-permissions\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { usePathname } from \"next/navigation\";\r\n\r\ntype NavItem = {\r\n  title: string;\r\n  url: string;\r\n  icon?: any;\r\n  isActive?: boolean;\r\n  items?: { title: string; url: string }[];\r\n};\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * This function is commented out for MVP, will be used in the future\r\n * when the permission system is fully implemented\r\n *\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\n// function mapActionToGrantAction(action: string): string {\r\n//   switch (action) {\r\n//     case \"view\":\r\n//       return \"read:any\";\r\n//     case \"create\":\r\n//       return \"create:any\";\r\n//     case \"update\":\r\n//       return \"update:any\";\r\n//     case \"delete\":\r\n//       return \"delete:any\";\r\n//     case \"manage\":\r\n//       return \"update:any\"; // Manage maps to update:any\r\n//     case \"transfer\":\r\n//       return \"update:any\"; // Transfer maps to update:any\r\n//     case \"report\":\r\n//       return \"read:any\"; // Report maps to read:any\r\n//     default:\r\n//       return `${action}:any`;\r\n//   }\r\n// }\r\n\r\nexport function AppSidebar() {\r\n  const { data: user } = useCurrentUser();\r\n  const pathname = usePathname();\r\n  // Permissions will be used in the future\r\n  // const { hasPermission, permissions } = usePermissions();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  // Define all navigation items\r\n  const getAllNavigationItems = (): NavItem[] => [\r\n    {\r\n      title: \"Dashboard\",\r\n      url: \"/dashboard\",\r\n      icon: HomeIcon,\r\n      isActive: pathname === \"/dashboard\" || pathname === \"/\",\r\n    },\r\n    {\r\n      title: \"Reports\",\r\n      url: \"/reports/sales-summary\",\r\n      icon: BarChart3Icon,\r\n      isActive:\r\n        pathname.startsWith(\"/reports\") ||\r\n        pathname.startsWith(\"/expense-analytics\"),\r\n      items: [\r\n        {\r\n          title: \"Sales Summary\",\r\n          url: \"/reports/sales-summary\",\r\n        },\r\n        {\r\n          title: \"Sales by Item\",\r\n          url: \"/reports/sales-by-item\",\r\n        },\r\n        {\r\n          title: \"Sales by Category\",\r\n          url: \"/reports/sales-by-category\",\r\n        },\r\n        {\r\n          title: \"Current Stock Levels\",\r\n          url: \"/reports/current-stock-levels\",\r\n        },\r\n        {\r\n          title: \"Stock History\",\r\n          url: \"/reports/stock-history\",\r\n        },\r\n        {\r\n          title: \"Banking Transactions\",\r\n          url: \"/reports/banking-transactions\",\r\n        },\r\n        {\r\n          title: \"Tax Report\",\r\n          url: \"/reports/tax-report\",\r\n        },\r\n        {\r\n          title: \"Banking Summary\",\r\n          url: \"/banking?tab=summary\",\r\n        },\r\n        {\r\n          title: \"M-Pesa Transactions\",\r\n          url: \"/reports/mpesa-transactions\",\r\n        },\r\n        {\r\n          title: \"Running Balances\",\r\n          url: \"/reports/running-balances\",\r\n        },\r\n        {\r\n          title: \"Cash Status\",\r\n          url: \"/reports/cash-status\",\r\n        },\r\n        {\r\n          title: \"Cash Float\",\r\n          url: \"/reports/cash-float\",\r\n        },\r\n        {\r\n          title: \"DSA Sales\",\r\n          url: \"/reports/dsa-sales\",\r\n        },\r\n        {\r\n          title: \"Receipts\",\r\n          url: \"/reports/receipts\",\r\n        },\r\n        {\r\n          title: \"Shifts\",\r\n          url: \"/reports/shifts\",\r\n        },\r\n        {\r\n          title: \"Phone Repairs\",\r\n          url: \"/reports/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"Stock Valuation\",\r\n          url: \"/inventory/valuation\",\r\n        },\r\n        {\r\n          title: \"Stock Aging\",\r\n          url: \"/reports/stock-aging\",\r\n        },\r\n        {\r\n          title: \"Stock Movement\",\r\n          url: \"/reports/stock-movement\",\r\n        },\r\n        {\r\n          title: \"Expense Analytics\",\r\n          url: \"/expense-analytics\",\r\n        },\r\n        {\r\n          title: \"Customers\",\r\n          url: \"/customers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Administration\",\r\n      url: \"/users\",\r\n      icon: BuildingIcon,\r\n      isActive:\r\n        pathname.startsWith(\"/users\") ||\r\n        pathname.startsWith(\"/roles\") ||\r\n        pathname.startsWith(\"/rbac\") ||\r\n        pathname.startsWith(\"/branches\") ||\r\n        pathname.startsWith(\"/locations\") ||\r\n        pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        // Tenants menu item removed\r\n        {\r\n          title: \"Users\",\r\n          url: \"/users\",\r\n        },\r\n        {\r\n          title: \"Roles\",\r\n          url: \"/roles\",\r\n        },\r\n        {\r\n          title: \"RBAC\",\r\n          url: \"/rbac\",\r\n        },\r\n        {\r\n          title: \"Branches\",\r\n          url: \"/branches\",\r\n        },\r\n        {\r\n          title: \"Locations\",\r\n          url: \"/locations\",\r\n        },\r\n        {\r\n          title: \"Employees\",\r\n          url: \"/employees\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Stock & Inventory\",\r\n      url: \"/products\",\r\n      icon: Package,\r\n      isActive:\r\n        pathname.startsWith(\"/products\") ||\r\n        pathname.startsWith(\"/categories\") ||\r\n        pathname.startsWith(\"/brands\") ||\r\n        pathname.startsWith(\"/inventory\") ||\r\n        pathname.includes(\"/inventory/stock-cards\") ||\r\n        pathname.includes(\"/inventory/purchases\"),\r\n      items: [\r\n        {\r\n          title: \"Products\",\r\n          url: \"/products\",\r\n        },\r\n        {\r\n          title: \"Categories\",\r\n          url: \"/categories\",\r\n        },\r\n        {\r\n          title: \"Brands\",\r\n          url: \"/brands\",\r\n        },\r\n        {\r\n          title: \"Inventory\",\r\n          url: \"/inventory\",\r\n        },\r\n        // Commented out Stock Locations as requested\r\n        // {\r\n        //   title: \"Stock Locations\",\r\n        //   url: \"/inventory/locations\",\r\n        // },\r\n        {\r\n          title: \"Purchases\",\r\n          url: \"/inventory/purchases\",\r\n        },\r\n        {\r\n          title: \"Create Purchase\",\r\n          url: \"/inventory/purchases/new\",\r\n        },\r\n        {\r\n          title: \"Stock Requests\",\r\n          url: \"/inventory/stock-requests\",\r\n        },\r\n        {\r\n          title: \"Stock Transfers\",\r\n          url: \"/inventory/transfers\",\r\n        },\r\n        {\r\n          title: \"Make Stock Transfer\",\r\n          url: \"/inventory/bulk-transfer\",\r\n        },\r\n        {\r\n          title: \"Stock Transfer History\",\r\n          url: \"/inventory/bulk-transfers\",\r\n        },\r\n        {\r\n          title: \"Stock Cards\",\r\n          url: \"/inventory/stock-cards\",\r\n        },\r\n        {\r\n          title: \"Inventory Reports\",\r\n          url: \"/inventory/reports\",\r\n        },\r\n        {\r\n          title: \"Excel Import/Export\",\r\n          url: \"/inventory/excel\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Float Management\",\r\n      url: \"/float\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/float\"),\r\n      items: [\r\n        {\r\n          title: \"Float Dashboard\",\r\n          url: \"/float\",\r\n        },\r\n        {\r\n          title: \"Float Movements\",\r\n          url: \"/float/movements\",\r\n        },\r\n        {\r\n          title: \"Float Reconciliations\",\r\n          url: \"/float/reconciliations\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Banking Management\",\r\n      url: \"/banking\",\r\n      icon: Landmark,\r\n      isActive: pathname.startsWith(\"/banking\"),\r\n      items: [\r\n        {\r\n          title: \"Banking Records\",\r\n          url: \"/banking\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Expenses Management\",\r\n      url: \"/expenses\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/expenses\"),\r\n      items: [\r\n        {\r\n          title: \"Expenses\",\r\n          url: \"/expenses\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Invoice Management\",\r\n      url: \"/invoices\",\r\n      icon: FileText,\r\n      isActive:\r\n        pathname.startsWith(\"/invoices\") ||\r\n        pathname.startsWith(\"/credit-notes\"),\r\n      items: [\r\n        {\r\n          title: \"All Invoices\",\r\n          url: \"/invoices\",\r\n        },\r\n        {\r\n          title: \"Credit Notes\",\r\n          url: \"/credit-notes\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"DSA Management\",\r\n      url: \"/dsa\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/dsa\"),\r\n      items: [\r\n        {\r\n          title: \"DSA Dashboard\",\r\n          url: \"/dsa\",\r\n        },\r\n        {\r\n          title: \"DSA Agents\",\r\n          url: \"/dsa/customers\",\r\n        },\r\n        {\r\n          title: \"Stock Assignments\",\r\n          url: \"/dsa/assignments\",\r\n        },\r\n        // Commented out DSA Reconciliations as requested\r\n        // {\r\n        //   title: \"DSA Reconciliations\",\r\n        //   url: \"/dsa/reconciliations\",\r\n        // },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Procurement\",\r\n      url: \"/procurement\",\r\n      icon: ShoppingCart,\r\n      isActive:\r\n        pathname.startsWith(\"/procurement\") ||\r\n        pathname.startsWith(\"/suppliers\"),\r\n      items: [\r\n        {\r\n          title: \"Procurement Dashboard\",\r\n          url: \"/procurement\",\r\n        },\r\n        {\r\n          title: \"Procurement Requests\",\r\n          url: \"/procurement/requests\",\r\n        },\r\n        {\r\n          title: \"Create Request\",\r\n          url: \"/procurement/requests/new\",\r\n        },\r\n        {\r\n          title: \"Procurement Receipts\",\r\n          url: \"/procurement/receipts\",\r\n        },\r\n        {\r\n          title: \"Suppliers\",\r\n          url: \"/suppliers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Phone Repairs\",\r\n      url: \"/phone-repairs\",\r\n      icon: Smartphone,\r\n      isActive:\r\n        pathname === \"/phone-repairs\" || pathname.startsWith(\"/phone-repairs/\")\r\n          ? true\r\n          : false,\r\n      items: [\r\n        {\r\n          title: \"All Repairs\",\r\n          url: \"/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"New Repair\",\r\n          url: \"/phone-repairs/new\",\r\n        },\r\n      ],\r\n    },\r\n    // Settings section completely hidden for production - not implemented\r\n    // {\r\n    //   title: \"Settings\",\r\n    //   url: \"/settings\",\r\n    //   icon: Settings,\r\n    //   isActive:\r\n    //     pathname.startsWith(\"/settings\") || pathname.startsWith(\"/profile\"),\r\n    //   items: [\r\n    //     {\r\n    //       title: \"System Settings\",\r\n    //       url: \"/settings/system\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Company Settings\",\r\n    //       url: \"/settings/company\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Payment Methods\",\r\n    //       url: \"/settings/payment-methods\",\r\n    //     },\r\n    //     {\r\n    //       title: \"System Health\",\r\n    //       url: \"/settings/health\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Profile\",\r\n    //       url: \"/profile\",\r\n    //     },\r\n    //   ],\r\n    // },\r\n    // Permissions Demo section removed\r\n  ];\r\n\r\n  // Filter navigation items based on role (MVP approach)\r\n  const getFilteredNavigationItemsByRole = (): NavItem[] => {\r\n    const allItems = getAllNavigationItems();\r\n\r\n    // Create a custom Employees section for branch managers\r\n    const employeesSection: NavItem = {\r\n      title: \"Employees\",\r\n      url: \"/employees\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        {\r\n          title: \"Manage Employees\",\r\n          url: \"/employees\",\r\n        },\r\n        {\r\n          title: \"Add Employee\",\r\n          url: \"/employees/create\",\r\n        },\r\n      ],\r\n    };\r\n\r\n    // For MVP, use role-based filtering as the default approach\r\n    console.log(\r\n      `[Navigation] ${userRoleName} role detected - using role-based filtering (MVP approach)`\r\n    );\r\n\r\n    // Filter navigation items based on user role\r\n    const filteredItems = allItems.filter((item) => {\r\n      // For super_admin and company_admin, show all items\r\n      if (userRoleName === \"super_admin\" || userRoleName === \"company_admin\") {\r\n        console.log(`[Navigation] Admin role detected - showing all items`);\r\n        return true;\r\n      }\r\n\r\n      // For branch_admin, show most items\r\n      if (userRoleName === \"branch_admin\") {\r\n        // Hide Tenants section\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n        return true;\r\n      }\r\n\r\n      // For accountant, show finance-related items\r\n      if (userRoleName === \"accountant\") {\r\n        // Allow Dashboard\r\n        // Hide Tenants section\r\n        if (item.title === \"Dashboard\") {\r\n          return true;\r\n        }\r\n\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Administration routes for accountant\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow Reports, Banking, Expenses, Float\r\n        if (\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide DSA Management\r\n        if (item.title === \"DSA Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Phone Repairs\r\n        if (item.title === \"Phone Repairs\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow POS Management for viewing sales data\r\n        if (item.title === \"POS Management\") {\r\n          return true;\r\n        }\r\n\r\n        // Hide Settings for accountant\r\n        if (item.title === \"Settings\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For auditor, show reporting and read-only sections\r\n      if (userRoleName === \"auditor\") {\r\n        // Allow Reports, Banking, Expenses, Float (read-only)\r\n        if (\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"POS Management\" ||\r\n          item.title === \"Products & Inventory\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For finance_manager, show finance-related sections\r\n      if (userRoleName === \"finance_manager\") {\r\n        // Allow Reports, Banking, Expenses, Float\r\n        if (\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"POS Management\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For float_manager, show float-related sections\r\n      if (userRoleName === \"float_manager\") {\r\n        if (item.title === \"Dashboard\") {\r\n          return true;\r\n        }\r\n\r\n        // Allow Float Management, Banking, and Reports\r\n        if (\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Reports\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Allow POS Management but only for Cash Balance access\r\n        if (item.title === \"POS Management\") {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For operations and operations_manager, show operations-related sections\r\n      if (\r\n        userRoleName === \"operations\" ||\r\n        userRoleName === \"operations_manager\"\r\n      ) {\r\n        // Hide Administration routes\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Tenants section\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow everything else\r\n        return true;\r\n      }\r\n\r\n      // For stock_admin, show inventory-related sections\r\n      if (userRoleName === \"stock_admin\") {\r\n        // Allow Products & Inventory\r\n        if (\r\n          item.title === \"Products & Inventory\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Procurement\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // Branch Manager role removed as they don't login via the web\r\n\r\n      // For pos_operator and shop_attendant, hide admin sections\r\n      if (\r\n        userRoleName === \"pos_operator\" ||\r\n        userRoleName === \"shop_attendant\"\r\n      ) {\r\n        // Hide Administration routes\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Float Management\r\n        if (item.title === \"Float Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Banking Management\r\n        if (item.title === \"Banking Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Expenses Management\r\n        if (item.title === \"Expenses Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide DSA Management\r\n        if (item.title === \"DSA Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow POS Management\r\n        if (item.title === \"POS Management\") {\r\n          return true;\r\n        }\r\n\r\n        // Allow Products & Inventory (read-only)\r\n        if (item.title === \"Products & Inventory\") {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For other roles, restrict access to only essential items\r\n      console.log(\r\n        `[Navigation] Unknown role detected: ${userRoleName} - restricting access`\r\n      );\r\n\r\n      // Allow Dashboard only\r\n      if (item.title === \"Dashboard\") {\r\n        return true;\r\n      }\r\n\r\n      // Hide Settings and everything else for unknown roles\r\n      return false;\r\n    });\r\n\r\n    // Filter subitems based on role\r\n    const itemsWithFilteredSubitems = filteredItems.map((item) => {\r\n      // If the item has subitems, filter them based on role\r\n      if (item.items && item.items.length > 0) {\r\n        console.log(\r\n          `[Navigation] Checking subitems for \"${item.title}\" section`\r\n        );\r\n\r\n        const filteredSubItems = item.items.filter((subItem) => {\r\n          // For Administration, filter RBAC items for non-super_admin users\r\n          if (item.title === \"Administration\") {\r\n            // Only super_admin can see RBAC items\r\n            if (subItem.title === \"RBAC\") {\r\n              return userRoleName === \"super_admin\";\r\n            }\r\n            // All other admin roles can see other items\r\n            return true;\r\n          }\r\n\r\n          // For Settings, only show Profile for most roles\r\n          if (item.title === \"Settings\") {\r\n            // Admin roles can see all settings\r\n            if (\r\n              userRoleName === \"super_admin\" ||\r\n              userRoleName === \"company_admin\" ||\r\n              userRoleName === \"branch_admin\"\r\n            ) {\r\n              return true;\r\n            }\r\n\r\n            // Accountant can see Profile and Payment Methods\r\n            if (userRoleName === \"accountant\") {\r\n              return (\r\n                subItem.title === \"Profile\" ||\r\n                subItem.title === \"Payment Methods\"\r\n              );\r\n            }\r\n\r\n            // Other roles can only see Profile\r\n            return subItem.title === \"Profile\";\r\n          }\r\n\r\n          // For POS Management, restrict access based on role\r\n          if (item.title === \"POS Management\") {\r\n            // For float_manager, only show Cash Balance\r\n            if (userRoleName === \"float_manager\") {\r\n              return subItem.title === \"Cash Balance\";\r\n            }\r\n\r\n            // For other roles, restrict Cash Balance to admin roles\r\n            if (subItem.title === \"Cash Balance\") {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"accountant\" ||\r\n                userRoleName === \"finance_manager\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // For Products & Inventory, restrict Categories and Brands for non-admin roles\r\n          if (\r\n            item.title === \"Products & Inventory\" &&\r\n            (subItem.title.includes(\"Categories\") ||\r\n              subItem.title.includes(\"Brands\"))\r\n          ) {\r\n            return (\r\n              userRoleName === \"super_admin\" ||\r\n              userRoleName === \"company_admin\" ||\r\n              userRoleName === \"branch_admin\" ||\r\n              userRoleName === \"stock_admin\"\r\n            );\r\n          }\r\n\r\n          // For Products & Inventory, restrict inventory management for shop_attendant and pos_operator\r\n          if (\r\n            item.title === \"Products & Inventory\" &&\r\n            (subItem.title.includes(\"Transfer\") ||\r\n              subItem.title.includes(\"Stock Cards\") ||\r\n              subItem.title.includes(\"Inventory Reports\") ||\r\n              subItem.title.includes(\"Excel\"))\r\n          ) {\r\n            return (\r\n              userRoleName !== \"pos_operator\" &&\r\n              userRoleName !== \"shop_attendant\"\r\n            );\r\n          }\r\n\r\n          // For Reports, restrict certain reports based on role\r\n          if (item.title === \"Reports\") {\r\n            // Finance-related reports\r\n            if (\r\n              subItem.title.includes(\"Banking\") ||\r\n              subItem.title.includes(\"Cash\") ||\r\n              subItem.title.includes(\"Float\") ||\r\n              subItem.title.includes(\"Expense\")\r\n            ) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"accountant\" ||\r\n                userRoleName === \"finance_manager\" ||\r\n                userRoleName === \"auditor\"\r\n              );\r\n            }\r\n\r\n            // Inventory-related reports\r\n            if (\r\n              subItem.title.includes(\"Stock\") ||\r\n              subItem.title.includes(\"Inventory\")\r\n            ) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"stock_admin\" ||\r\n                userRoleName === \"operations_manager\" ||\r\n                userRoleName === \"operations\" ||\r\n                userRoleName === \"auditor\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // For Procurement, restrict certain operations based on role\r\n          if (item.title === \"Procurement\") {\r\n            // Creating and approving procurement requests\r\n            if (subItem.title.includes(\"Create\")) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"stock_admin\" ||\r\n                userRoleName === \"operations_manager\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // For Float Management, restrict certain operations based on role\r\n          if (item.title === \"Float Management\") {\r\n            // Float reconciliations\r\n            if (subItem.title.includes(\"Reconciliation\")) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"accountant\" ||\r\n                userRoleName === \"finance_manager\" ||\r\n                userRoleName === \"float_manager\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // Allow all other subitems by default\r\n          return true;\r\n        });\r\n\r\n        // Update the item's subitems with the filtered list\r\n        item.items = filteredSubItems;\r\n      }\r\n\r\n      return item;\r\n    });\r\n\r\n    // Only return items that have at least one subitem (if they had subitems to begin with)\r\n    const result = itemsWithFilteredSubitems.filter(\r\n      (item) => !item.items || item.items.length > 0\r\n    );\r\n\r\n    // Add the Employees section at the beginning of the filtered items for branch managers\r\n    if (userRoleName === \"branch_manager\") {\r\n      return [employeesSection, ...result];\r\n    }\r\n\r\n    return result;\r\n  };\r\n\r\n  // Filter navigation items based on permissions (for future use)\r\n  // This function is commented out for MVP, will be implemented in the future\r\n  // when the permission system is fully implemented\r\n\r\n  // Sample data for the sidebar\r\n  const data = {\r\n    user: {\r\n      name: user?.name || \"User\",\r\n      email: user?.email || \"<EMAIL>\",\r\n      avatar: \"/placeholder-avatar.jpg\",\r\n    },\r\n    teams: [\r\n      {\r\n        name: teamData.name,\r\n        logo: GalleryVerticalEnd,\r\n        plan: teamData.plan,\r\n      },\r\n    ],\r\n    // For MVP, use role-based filtering\r\n    navMain: getFilteredNavigationItemsByRole(),\r\n    // For future: navMain: getFilteredNavigationItems(),\r\n  };\r\n\r\n  const { state, isMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\" && !isMobile;\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"h-full flex flex-col sidebar-compact\",\r\n        isCollapsed && \"sidebar-collapsed\"\r\n      )}\r\n    >\r\n      <div className=\"flex-1 overflow-y-auto py-2\">\r\n        <div className={cn(\"px-3\", isCollapsed && \"px-2\")}>\r\n          <NavMain items={data.navMain} />\r\n        </div>\r\n      </div>\r\n      <div className={cn(\"p-3 border-t\", isCollapsed && \"p-2\")}>\r\n        <NavUser user={data.user} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AACA,uCAAuC;AACvC,0EAA0E;AAC1E,gEAAgE;AAChE,wEAAwE;AACxE;AACA;;;AA7BA;;;;;;;;;;AAoEO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,yCAAyC;IACzC,2DAA2D;IAE3D,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,8BAA8B;IAC9B,MAAM,wBAAwB,IAAiB;YAC7C;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,sMAAA,CAAA,OAAQ;gBACd,UAAU,aAAa,gBAAgB,aAAa;YACtD;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,qNAAA,CAAA,YAAa;gBACnB,UACE,SAAS,UAAU,CAAC,eACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAY;gBAClB,UACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,YACpB,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL,4BAA4B;oBAC5B;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,2MAAA,CAAA,UAAO;gBACb,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,QAAQ,CAAC,6BAClB,SAAS,QAAQ,CAAC;gBACpB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,6CAA6C;oBAC7C,IAAI;oBACJ,8BAA8B;oBAC9B,iCAAiC;oBACjC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,iNAAA,CAAA,WAAQ;gBACd,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,mNAAA,CAAA,YAAS;gBACf,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBAMD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,yNAAA,CAAA,eAAY;gBAClB,UACE,SAAS,UAAU,CAAC,mBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,iNAAA,CAAA,aAAU;gBAChB,UACE,aAAa,oBAAoB,SAAS,UAAU,CAAC,qBACjD,OACA;gBACN,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;SAgCD;IAED,uDAAuD;IACvD,MAAM,mCAAmC;QACvC,MAAM,WAAW;QAEjB,wDAAwD;QACxD,MAAM,mBAA4B;YAChC,OAAO;YACP,KAAK;YACL,MAAM,mNAAA,CAAA,YAAS;YACf,UAAU,SAAS,UAAU,CAAC;YAC9B,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;QAEA,4DAA4D;QAC5D,QAAQ,GAAG,CACT,CAAC,aAAa,EAAE,aAAa,0DAA0D,CAAC;QAG1F,6CAA6C;QAC7C,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC;YACrC,oDAAoD;YACpD,IAAI,iBAAiB,iBAAiB,iBAAiB,iBAAiB;gBACtE,QAAQ,GAAG,CAAC,CAAC,oDAAoD,CAAC;gBAClE,OAAO;YACT;YAEA,oCAAoC;YACpC,IAAI,iBAAiB,gBAAgB;gBACnC,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBACA,OAAO;YACT;YAEA,6CAA6C;YAC7C,IAAI,iBAAiB,cAAc;gBACjC,kBAAkB;gBAClB,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,OAAO;gBACT;gBAEA,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBAEA,4CAA4C;gBAC5C,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,0CAA0C;gBAC1C,IACE,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,oBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,qBAAqB;gBACrB,IAAI,KAAK,KAAK,KAAK,iBAAiB;oBAClC,OAAO;gBACT;gBAEA,8CAA8C;gBAC9C,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,+BAA+B;gBAC/B,IAAI,KAAK,KAAK,KAAK,YAAY;oBAC7B,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,qDAAqD;YACrD,IAAI,iBAAiB,WAAW;gBAC9B,sDAAsD;gBACtD,IACE,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,oBACf,KAAK,KAAK,KAAK,wBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,qDAAqD;YACrD,IAAI,iBAAiB,mBAAmB;gBACtC,0CAA0C;gBAC1C,IACE,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,kBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,iDAAiD;YACjD,IAAI,iBAAiB,iBAAiB;gBACpC,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,OAAO;gBACT;gBAEA,+CAA+C;gBAC/C,IACE,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,WACf;oBACA,OAAO;gBACT;gBAEA,wDAAwD;gBACxD,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,0EAA0E;YAC1E,IACE,iBAAiB,gBACjB,iBAAiB,sBACjB;gBACA,6BAA6B;gBAC7B,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,OAAO;YACT;YAEA,mDAAmD;YACnD,IAAI,iBAAiB,eAAe;gBAClC,6BAA6B;gBAC7B,IACE,KAAK,KAAK,KAAK,0BACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,eACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,8DAA8D;YAE9D,2DAA2D;YAC3D,IACE,iBAAiB,kBACjB,iBAAiB,kBACjB;gBACA,6BAA6B;gBAC7B,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,IAAI,KAAK,KAAK,KAAK,oBAAoB;oBACrC,OAAO;gBACT;gBAEA,0BAA0B;gBAC1B,IAAI,KAAK,KAAK,KAAK,sBAAsB;oBACvC,OAAO;gBACT;gBAEA,2BAA2B;gBAC3B,IAAI,KAAK,KAAK,KAAK,uBAAuB;oBACxC,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,yCAAyC;gBACzC,IAAI,KAAK,KAAK,KAAK,wBAAwB;oBACzC,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,2DAA2D;YAC3D,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,aAAa,qBAAqB,CAAC;YAG5E,uBAAuB;YACvB,IAAI,KAAK,KAAK,KAAK,aAAa;gBAC9B,OAAO;YACT;YAEA,sDAAsD;YACtD,OAAO;QACT;QAEA,gCAAgC;QAChC,MAAM,4BAA4B,cAAc,GAAG,CAAC,CAAC;YACnD,sDAAsD;YACtD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;gBACvC,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC;gBAG9D,MAAM,mBAAmB,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1C,kEAAkE;oBAClE,IAAI,KAAK,KAAK,KAAK,kBAAkB;wBACnC,sCAAsC;wBACtC,IAAI,QAAQ,KAAK,KAAK,QAAQ;4BAC5B,OAAO,iBAAiB;wBAC1B;wBACA,4CAA4C;wBAC5C,OAAO;oBACT;oBAEA,iDAAiD;oBACjD,IAAI,KAAK,KAAK,KAAK,YAAY;wBAC7B,mCAAmC;wBACnC,IACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,gBACjB;4BACA,OAAO;wBACT;wBAEA,iDAAiD;wBACjD,IAAI,iBAAiB,cAAc;4BACjC,OACE,QAAQ,KAAK,KAAK,aAClB,QAAQ,KAAK,KAAK;wBAEtB;wBAEA,mCAAmC;wBACnC,OAAO,QAAQ,KAAK,KAAK;oBAC3B;oBAEA,oDAAoD;oBACpD,IAAI,KAAK,KAAK,KAAK,kBAAkB;wBACnC,4CAA4C;wBAC5C,IAAI,iBAAiB,iBAAiB;4BACpC,OAAO,QAAQ,KAAK,KAAK;wBAC3B;wBAEA,wDAAwD;wBACxD,IAAI,QAAQ,KAAK,KAAK,gBAAgB;4BACpC,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,gBACjB,iBAAiB;wBAErB;oBACF;oBAEA,+EAA+E;oBAC/E,IACE,KAAK,KAAK,KAAK,0BACf,CAAC,QAAQ,KAAK,CAAC,QAAQ,CAAC,iBACtB,QAAQ,KAAK,CAAC,QAAQ,CAAC,SAAS,GAClC;wBACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB;oBAErB;oBAEA,8FAA8F;oBAC9F,IACE,KAAK,KAAK,KAAK,0BACf,CAAC,QAAQ,KAAK,CAAC,QAAQ,CAAC,eACtB,QAAQ,KAAK,CAAC,QAAQ,CAAC,kBACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,wBACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,QAAQ,GACjC;wBACA,OACE,iBAAiB,kBACjB,iBAAiB;oBAErB;oBAEA,sDAAsD;oBACtD,IAAI,KAAK,KAAK,KAAK,WAAW;wBAC5B,0BAA0B;wBAC1B,IACE,QAAQ,KAAK,CAAC,QAAQ,CAAC,cACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,WACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB;4BACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,gBACjB,iBAAiB,qBACjB,iBAAiB;wBAErB;wBAEA,4BAA4B;wBAC5B,IACE,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,cACvB;4BACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,iBACjB,iBAAiB,wBACjB,iBAAiB,gBACjB,iBAAiB;wBAErB;oBACF;oBAEA,6DAA6D;oBAC7D,IAAI,KAAK,KAAK,KAAK,eAAe;wBAChC,8CAA8C;wBAC9C,IAAI,QAAQ,KAAK,CAAC,QAAQ,CAAC,WAAW;4BACpC,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,iBACjB,iBAAiB;wBAErB;oBACF;oBAEA,kEAAkE;oBAClE,IAAI,KAAK,KAAK,KAAK,oBAAoB;wBACrC,wBAAwB;wBACxB,IAAI,QAAQ,KAAK,CAAC,QAAQ,CAAC,mBAAmB;4BAC5C,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,gBACjB,iBAAiB,qBACjB,iBAAiB;wBAErB;oBACF;oBAEA,sCAAsC;oBACtC,OAAO;gBACT;gBAEA,oDAAoD;gBACpD,KAAK,KAAK,GAAG;YACf;YAEA,OAAO;QACT;QAEA,wFAAwF;QACxF,MAAM,SAAS,0BAA0B,MAAM,CAC7C,CAAC,OAAS,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG;QAG/C,uFAAuF;QACvF,IAAI,iBAAiB,kBAAkB;YACrC,OAAO;gBAAC;mBAAqB;aAAO;QACtC;QAEA,OAAO;IACT;IAEA,gEAAgE;IAChE,4EAA4E;IAC5E,kDAAkD;IAElD,8BAA8B;IAC9B,MAAM,OAAO;QACX,MAAM;YACJ,MAAM,MAAM,QAAQ;YACpB,OAAO,MAAM,SAAS;YACtB,QAAQ;QACV;QACA,OAAO;YACL;gBACE,MAAM,SAAS,IAAI;gBACnB,MAAM,yOAAA,CAAA,qBAAkB;gBACxB,MAAM,SAAS,IAAI;YACrB;SACD;QACD,oCAAoC;QACpC,SAAS;IAEX;IAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IACrC,MAAM,cAAc,UAAU,eAAe,CAAC;IAE9C,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wCACA,eAAe;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,eAAe;8BACxC,cAAA,6LAAC,oIAAA,CAAA,UAAO;wBAAC,OAAO,KAAK,OAAO;;;;;;;;;;;;;;;;0BAGhC,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,eAAe;0BAChD,cAAA,6LAAC,oIAAA,CAAA,UAAO;oBAAC,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;AAIhC;GAj4BgB;;QACS,kJAAA,CAAA,iBAAc;QACpB,qIAAA,CAAA,cAAW;QA22BA,sIAAA,CAAA,aAAU;;;KA72BxB", "debugId": null}}, {"offset": {"line": 6547, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/team-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// import { GalleryVerticalEnd } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n\r\nexport function TeamHeader() {\r\n  const { data: user } = useCurrentUser();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <div className=\"bg-white text-primary flex aspect-square size-6 items-center justify-center rounded-lg\">\r\n        {/* <GalleryVerticalEnd className=\"size-3\" /> */}\r\n        <Image\r\n          src={\"/images/simbatelecomlogo.png\"}\r\n          alt=\"Simba Telecom Logo\"\r\n          className=\"object-contain\"\r\n          width={16}\r\n          height={16}\r\n        />\r\n      </div>\r\n      <div className=\"grid flex-1 text-left leading-tight\">\r\n        <span className=\"truncate text-sm font-semibold text-primary-foreground\">\r\n          {teamData.name}\r\n        </span>\r\n        <span className=\"truncate text-xs text-primary-foreground/90 font-medium\">\r\n          {teamData.plan}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,qDAAqD;AACrD;AACA;;;AAJA;;;AAMO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAEpC,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAI;oBACJ,WAAU;oBACV,OAAO;oBACP,QAAQ;;;;;;;;;;;0BAGZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;kCAEhB,6LAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;;;;;;;;;;;;;AAKxB;GAhEgB;;QACS,kJAAA,CAAA,iBAAc;;;KADvB", "debugId": null}}, {"offset": {"line": 6662, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/search-button.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearch } from \"@/components/providers/search-provider\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\n\r\nexport function SearchButton() {\r\n  const { openSearch } = useSearch();\r\n\r\n  return (\r\n    <Button\r\n      variant=\"outline\"\r\n      size=\"sm\"\r\n      onClick={openSearch}\r\n      className=\"relative h-9 w-full justify-start bg-background text-sm font-normal text-muted-foreground shadow-none sm:pr-12 md:w-40 lg:w-64\"\r\n    >\r\n      <span className=\"hidden lg:inline-flex\">Search anything...</span>\r\n      <span className=\"inline-flex lg:hidden\">Search...</span>\r\n      <kbd className=\"pointer-events-none absolute right-1.5 top-1.5 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex\">\r\n        <span className=\"text-xs\">⌘</span>K\r\n      </kbd>\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD;IAE/B,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;;0BAEV,6LAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,6LAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAU;;;;;;oBAAQ;;;;;;;;;;;;;AAI1C;GAjBgB;;QACS,wJAAA,CAAA,YAAS;;;KADlB", "debugId": null}}, {"offset": {"line": 6740, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/top-navigation.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport {\r\n  Bell,\r\n  ChevronDown,\r\n  ChevronLeft,\r\n  LogOut,\r\n  Settings,\r\n  User,\r\n  PanelLeftIcon,\r\n  PanelRightIcon,\r\n  Home,\r\n} from \"lucide-react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser, useLogout } from \"@/features/auth/hooks/use-auth\";\r\nimport { TeamHeader } from \"@/components/team-header\";\r\nimport { SearchButton } from \"@/components/search-button\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport Link from \"next/link\";\r\n\r\nexport function TopNavigation() {\r\n  const { data: user } = useCurrentUser();\r\n  const logout = useLogout();\r\n  const { toggleSidebar, state, isMobile, setOpenMobile } = useSidebar();\r\n  const isExpanded = state === \"expanded\";\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Check if we're on the stock requests page\r\n  const isStockRequestsPage = pathname.includes('/inventory/stock-requests') &&\r\n    !pathname.includes('/create') &&\r\n    !pathname.match(/\\/inventory\\/stock-requests\\/\\d+/);\r\n\r\n  const handleLogout = () => {\r\n    logout.mutate();\r\n  };\r\n\r\n  const handleBackClick = () => {\r\n    router.back();\r\n  };\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-30 flex h-14 shrink-0 items-center gap-2 border-b border-primary-foreground/10 bg-primary text-primary-foreground transition-[width,height] ease-linear\">\r\n      <div className=\"flex flex-1 items-center justify-between px-4 w-full\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {isMobile && isStockRequestsPage ? (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={handleBackClick}\r\n                aria-label=\"Go back\"\r\n                title=\"Go back\"\r\n              >\r\n                <ChevronLeft className=\"h-5 w-5\" />\r\n                <span className=\"sr-only\">Go Back</span>\r\n              </Button>\r\n            ) : (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={() => (isMobile ? setOpenMobile(true) : toggleSidebar())}\r\n                data-mobile={isMobile ? \"true\" : \"false\"}\r\n                aria-label=\"Toggle sidebar\"\r\n                title={isExpanded ? \"Collapse sidebar\" : \"Expand sidebar\"}\r\n              >\r\n                {isExpanded ? (\r\n                  <PanelLeftIcon className=\"h-5 w-5\" />\r\n                ) : (\r\n                  <PanelRightIcon className=\"h-5 w-5\" />\r\n                )}\r\n                <span className=\"sr-only\">Toggle Sidebar</span>\r\n              </Button>\r\n            )}\r\n            <TeamHeader />\r\n\r\n            <div className=\"h-6 border-r border-primary-foreground/20 mx-2\"></div>\r\n\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n              asChild\r\n            >\r\n              <Link href=\"/dashboard\">\r\n                <Home className=\"h-4 w-4 mr-1\" />\r\n                Dashboard\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-center\">\r\n          <SearchButton />\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"relative h-8 w-8 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n            aria-label=\"Notifications\"\r\n          >\r\n            <Bell className=\"h-5 w-5\" />\r\n            <span className=\"absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-[10px] font-bold text-primary\">\r\n              3\r\n            </span>\r\n          </Button>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"relative h-8 gap-2 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                aria-label=\"Account\"\r\n              >\r\n                <Avatar className=\"h-7 w-7\">\r\n                  <AvatarImage\r\n                    src=\"/placeholder-avatar.jpg\"\r\n                    alt={user?.name || \"User\"}\r\n                  />\r\n                  <AvatarFallback className=\"text-xs font-semibold\">\r\n                    {user?.name ? user.name.charAt(0) : \"U\"}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <span className=\"hidden text-sm font-semibold md:inline-block\">\r\n                  {user?.name || \"User\"}\r\n                </span>\r\n                <ChevronDown className=\"h-4 w-4 text-primary-foreground/80\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-56 rounded-lg\">\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <div className=\"flex flex-col space-y-1\">\r\n                  <p className=\"text-sm font-semibold leading-none\">\r\n                    {user?.name || \"User\"}\r\n                  </p>\r\n                  <p className=\"text-xs leading-none text-muted-foreground\">\r\n                    {user?.email || \"<EMAIL>\"}\r\n                  </p>\r\n                </div>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuGroup>\r\n                <DropdownMenuItem>\r\n                  <User className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Profile</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem>\r\n                  <Settings className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Settings</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuGroup>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem onClick={handleLogout}>\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                <span className=\"text-sm\">Log out</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AAEA;AACA;AASA;AACA;AACA;;;AA/BA;;;;;;;;;;;AAiCO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IACnE,MAAM,aAAa,UAAU;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,4CAA4C;IAC5C,MAAM,sBAAsB,SAAS,QAAQ,CAAC,gCAC5C,CAAC,SAAS,QAAQ,CAAC,cACnB,CAAC,SAAS,KAAK,CAAC;IAElB,MAAM,eAAe;QACnB,OAAO,MAAM;IACf;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI;IACb;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,YAAY,oCACX,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,cAAW;gCACX,OAAM;;kDAEN,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;qDAG5B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAO,WAAW,cAAc,QAAQ;gCACjD,eAAa,WAAW,SAAS;gCACjC,cAAW;gCACX,OAAO,aAAa,qBAAqB;;oCAExC,2BACC,6LAAC,uNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;6DAEzB,6LAAC,yNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAE5B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG9B,6LAAC,uIAAA,CAAA,aAAU;;;;;0CAEX,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,OAAO;0CAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,6LAAC,sMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,yIAAA,CAAA,eAAY;;;;;;;;;;8BAGf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,cAAW;;8CAEX,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAA6H;;;;;;;;;;;;sCAK/I,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,cAAW;;0DAEX,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,qIAAA,CAAA,cAAW;wDACV,KAAI;wDACJ,KAAK,MAAM,QAAQ;;;;;;kEAErB,6LAAC,qIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,MAAM,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAGxC,6LAAC;gDAAK,WAAU;0DACb,MAAM,QAAQ;;;;;;0DAEjB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG3B,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,6LAAC,+IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ;;;;;;kEAEjB,6LAAC;wDAAE,WAAU;kEACV,MAAM,SAAS;;;;;;;;;;;;;;;;;sDAItB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sDACtB,6LAAC,+IAAA,CAAA,oBAAiB;;8DAChB,6LAAC,+IAAA,CAAA,mBAAgB;;sEACf,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC,+IAAA,CAAA,mBAAgB;;sEACf,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAG9B,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sDACtB,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C;GAlJgB;;QACS,kJAAA,CAAA,iBAAc;QACtB,kJAAA,CAAA,YAAS;QACkC,sIAAA,CAAA,aAAU;QAErD,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KANd", "debugId": null}}, {"offset": {"line": 7192, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn(\"hover:text-foreground transition-colors\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn(\"text-foreground font-normal\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"[&>svg]:size-3.5\", className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,6LAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;KAFS;AAIT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MAhBS;AAkBT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;MAhBS;AAkBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,6LAAC,mNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;MAhBS", "debugId": null}}, {"offset": {"line": 7342, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/breadcrumbs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  B<PERSON><PERSON>rumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from \"@/components/ui/breadcrumb\";\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\n\r\nexport function Breadcrumbs() {\r\n  const pathname = usePathname();\r\n\r\n  // Get the current page name from the pathname\r\n  const getPageName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length === 0) return \"Dashboard\";\r\n\r\n    // Get the last segment of the path and capitalize it\r\n    const lastSegment = path[path.length - 1];\r\n    return (\r\n      lastSegment.charAt(0).toUpperCase() +\r\n      lastSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  // Get the parent path for breadcrumb\r\n  const getParentPath = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Remove the last segment to get the parent path\r\n    return \"/\" + path.slice(0, path.length - 1).join(\"/\");\r\n  };\r\n\r\n  // Get the parent name for breadcrumb\r\n  const getParentName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Get the second-to-last segment and capitalize it\r\n    const parentSegment = path[path.length - 2];\r\n    return (\r\n      parentSegment.charAt(0).toUpperCase() +\r\n      parentSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  const parentPath = getParentPath();\r\n  const parentName = getParentName();\r\n  const pageName = getPageName();\r\n\r\n  return (\r\n    <div className=\"mb-4\">\r\n      <Breadcrumb>\r\n        <BreadcrumbList>\r\n          {parentPath && parentName && (\r\n            <>\r\n              <BreadcrumbItem className=\"hidden md:block\">\r\n                <BreadcrumbLink asChild>\r\n                  <NavigationLink\r\n                    href={parentPath}\r\n                    className=\"text-sm font-medium text-muted-foreground hover:text-foreground\"\r\n                  >\r\n                    {parentName}\r\n                  </NavigationLink>\r\n                </BreadcrumbLink>\r\n              </BreadcrumbItem>\r\n              <BreadcrumbSeparator className=\"hidden md:block h-3 w-3\" />\r\n            </>\r\n          )}\r\n          <BreadcrumbItem>\r\n            <BreadcrumbPage className=\"text-sm font-semibold\">\r\n              {pageName}\r\n            </BreadcrumbPage>\r\n          </BreadcrumbItem>\r\n        </BreadcrumbList>\r\n      </Breadcrumb>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;;;AAXA;;;;AAaO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,8CAA8C;IAC9C,MAAM,cAAc;QAClB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;QAE9B,qDAAqD;QACrD,MAAM,cAAc,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzC,OACE,YAAY,MAAM,CAAC,GAAG,WAAW,KACjC,YAAY,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEvC;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,iDAAiD;QACjD,OAAO,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC;IACnD;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,mDAAmD;QACnD,MAAM,gBAAgB,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC3C,OACE,cAAc,MAAM,CAAC,GAAG,WAAW,KACnC,cAAc,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEzC;IAEA,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,WAAW;IAEjB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,yIAAA,CAAA,aAAU;sBACT,cAAA,6LAAC,yIAAA,CAAA,iBAAc;;oBACZ,cAAc,4BACb;;0CACE,6LAAC,yIAAA,CAAA,iBAAc;gCAAC,WAAU;0CACxB,cAAA,6LAAC,yIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC,iJAAA,CAAA,iBAAc;wCACb,MAAM;wCACN,WAAU;kDAET;;;;;;;;;;;;;;;;0CAIP,6LAAC,yIAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;;;kCAGnC,6LAAC,yIAAA,CAAA,iBAAc;kCACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAtEgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 7471, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/main-layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { ReactNode, Suspense, useEffect } from \"react\";\r\nimport { RoleGuard } from \"./role-guard\";\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\nimport { SidebarProvider, useSidebar } from \"@/components/ui/sidebar\";\r\nimport { AppSidebar } from \"@/components/app-sidebar\";\r\nimport { TopNavigation } from \"@/components/top-navigation\";\r\nimport { Breadcrumbs } from \"@/components/breadcrumbs\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\";\r\n\r\ninterface MainLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function MainLayout({ children }: MainLayoutProps) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Use useEffect for redirects to avoid hydration issues\r\n  useEffect(() => {\r\n    // Only show loading on the very first render if:\r\n    // 1. Auth is not initialized yet\r\n    // 2. We haven't shown the loading screen before in this session\r\n    if (!isInitialized && !hasShownLoading.main) {\r\n      setLoading(\"main\", true);\r\n    } else {\r\n      setLoading(\"main\", false);\r\n    }\r\n\r\n    if (!isInitialized) {\r\n      return; // Wait until auth state is initialized\r\n    }\r\n\r\n    // After auth state is initialized, we can hide the loading screen\r\n    setLoading(\"main\", false);\r\n\r\n    // If we have an access token, mark that we've shown loading\r\n    // This prevents showing loading on subsequent navigations\r\n    if (accessToken) {\r\n      markLoadingShown(\"main\");\r\n    }\r\n\r\n    // We're disabling client-side redirects to avoid redirect loops\r\n    // The middleware will handle redirects instead\r\n    if (!accessToken) {\r\n      // We're not redirecting here anymore\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [accessToken, isInitialized, hasShownLoading.main]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in loading state\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (isLoading.main) {\r\n        setLoading(\"main\", false);\r\n        markLoadingShown(\"main\");\r\n      }\r\n    }, 1500); // 1.5 second timeout for better UX\r\n\r\n    return () => clearTimeout(timeoutId);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.main]);\r\n\r\n  // Don't render anything if not authenticated\r\n  if (!accessToken) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <SidebarProvider>\r\n      <RoleGuard>\r\n        <MainLayoutContent>{children}</MainLayoutContent>\r\n      </RoleGuard>\r\n    </SidebarProvider>\r\n  );\r\n}\r\n\r\nfunction MainLayoutContent({ children }: { children: ReactNode }) {\r\n  const { state, openMobile, setOpenMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\";\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-screen w-full bg-gray-50\">\r\n      <TopNavigation />\r\n      <div className=\"flex h-[calc(100vh-3.5rem)] overflow-hidden\">\r\n        {/* Desktop sidebar */}\r\n        <aside\r\n          className={cn(\r\n            \"border-r bg-white transition-all duration-300 shrink-0 hidden md:block\",\r\n            isCollapsed ? \"w-16\" : \"w-64\"\r\n          )}\r\n        >\r\n          <AppSidebar />\r\n        </aside>\r\n\r\n        {/* Mobile sidebar */}\r\n        <div className=\"md:hidden\">\r\n          <Sheet open={openMobile} onOpenChange={setOpenMobile}>\r\n            <SheetContent\r\n              side=\"left\"\r\n              className=\"p-0 w-[280px] border-r bg-white\"\r\n            >\r\n              <div className=\"h-full overflow-y-auto\">\r\n                <AppSidebar />\r\n              </div>\r\n            </SheetContent>\r\n          </Sheet>\r\n        </div>\r\n\r\n        <main className=\"flex flex-col w-full overflow-y-auto overflow-x-hidden\">\r\n          <div className=\"p-6\">\r\n            <Suspense fallback={<div>Loading content...</div>}>\r\n              <Breadcrumbs />\r\n              {children}\r\n            </Suspense>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;AAoBO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD;IAEX,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,iDAAiD;YACjD,iCAAiC;YACjC,gEAAgE;YAChE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;gBAC3C,WAAW,QAAQ;YACrB,OAAO;gBACL,WAAW,QAAQ;YACrB;YAEA,IAAI,CAAC,eAAe;gBAClB,QAAQ,uCAAuC;YACjD;YAEA,kEAAkE;YAClE,WAAW,QAAQ;YAEnB,4DAA4D;YAC5D,0DAA0D;YAC1D,IAAI,aAAa;gBACf,iBAAiB;YACnB;YAEA,gEAAgE;YAChE,+CAA+C;YAC/C,IAAI,CAAC,aAAa;YAChB,qCAAqC;YACvC;QACA,uDAAuD;QACzD;+BAAG;QAAC;QAAa;QAAe,gBAAgB,IAAI;KAAC;IAErD,iEAAiE;IACjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,YAAY;kDAAW;oBAC3B,IAAI,UAAU,IAAI,EAAE;wBAClB,WAAW,QAAQ;wBACnB,iBAAiB;oBACnB;gBACF;iDAAG,OAAO,mCAAmC;YAE7C;wCAAO,IAAM,aAAa;;QAC1B,uDAAuD;QACzD;+BAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6CAA6C;IAC7C,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,iJAAA,CAAA,YAAS;sBACR,cAAA,6LAAC;0BAAmB;;;;;;;;;;;;;;;;AAI5B;GA9DgB;;QACyB,wIAAA,CAAA,gBAAa;QAElD,yJAAA,CAAA,aAAU;;;KAHE;AAgEhB,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;;IAC9D,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IACtD,MAAM,cAAc,UAAU;IAE9B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,gBAAa;;;;;0BACd,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA,cAAc,SAAS;kCAGzB,cAAA,6LAAC,uIAAA,CAAA,aAAU;;;;;;;;;;kCAIb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAY,cAAc;sCACrC,cAAA,6LAAC,oIAAA,CAAA,eAAY;gCACX,MAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6JAAA,CAAA,WAAQ;gCAAC,wBAAU,6LAAC;8CAAI;;;;;;;kDACvB,6LAAC,oIAAA,CAAA,cAAW;;;;;oCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;IA3CS;;QACsC,sIAAA,CAAA,aAAU;;;MADhD", "debugId": null}}, {"offset": {"line": 7713, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/api/expenses.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\n\r\n// Get all expense categories\r\nexport async function getExpenseCategories(params?: { grouped?: boolean }) {\r\n  try {\r\n    const response = await apiClient.get(\"/expense-categories\", { params });\r\n    return response.data || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching expense categories:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\n// Get all expense category groups\r\nexport async function getExpenseCategoryGroups(params?: {\r\n  include_categories?: boolean;\r\n}) {\r\n  try {\r\n    const response = await apiClient.get(\"/expense-category-groups\", {\r\n      params,\r\n    });\r\n    return response.data || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching expense category groups:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\n// Create expense category\r\nexport async function createExpenseCategory(data: {\r\n  name: string;\r\n  description?: string;\r\n  is_active?: boolean;\r\n  is_shop_allowed?: boolean;\r\n  group_id?: number | null;\r\n}) {\r\n  try {\r\n    const response = await apiClient.post(\"/expense-categories\", data);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error creating expense category:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Update expense category\r\nexport async function updateExpenseCategory(\r\n  id: number,\r\n  data: {\r\n    name?: string;\r\n    description?: string;\r\n    is_active?: boolean;\r\n    is_shop_allowed?: boolean;\r\n    group_id?: number | null;\r\n  }\r\n) {\r\n  try {\r\n    const response = await apiClient.put(`/expense-categories/${id}`, data);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error updating expense category:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Get all expenses with pagination and filtering\r\nexport async function getExpenses({\r\n  page = 1,\r\n  limit = 10,\r\n  status,\r\n  search,\r\n  branch_id,\r\n}: {\r\n  page?: number;\r\n  limit?: number;\r\n  status?: string;\r\n  search?: string;\r\n  branch_id?: number;\r\n}) {\r\n  try {\r\n    // Build query parameters\r\n    const params: Record<string, any> = {\r\n      limit: limit,\r\n      offset: (page - 1) * limit,\r\n    };\r\n\r\n    if (status) {\r\n      params.status = status;\r\n    }\r\n\r\n    if (search) {\r\n      params.search = search;\r\n    }\r\n\r\n    if (branch_id) {\r\n      params.branch_id = branch_id;\r\n    }\r\n\r\n    // Fetch expenses using the global API client\r\n    const response = (await apiClient.get(\"/expenses\", { params })) as any;\r\n\r\n    return {\r\n      expenses: response.data || [],\r\n      totalCount: response.total || 0,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching expenses:\", error);\r\n    return {\r\n      expenses: [],\r\n      totalCount: 0,\r\n    };\r\n  }\r\n}\r\n\r\n// Get expense by ID\r\nexport async function getExpenseById(id: string) {\r\n  try {\r\n    const response = (await apiClient.get(`/expenses/${id}`)) as any;\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(`Error fetching expense with ID ${id}:`, error);\r\n    return null;\r\n  }\r\n}\r\n\r\n// Legacy approve expense (redirects to appropriate approval level)\r\nexport async function approveExpense(\r\n  id: string,\r\n  data: {\r\n    approved_amount: number;\r\n    approval_notes?: string;\r\n  }\r\n) {\r\n  try {\r\n    return await apiClient.put(`/expenses/${id}/approve`, data);\r\n  } catch (error) {\r\n    console.error(\"Error approving expense:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Legacy decline expense (redirects to appropriate decline level)\r\nexport async function declineExpense(\r\n  id: string,\r\n  data: {\r\n    approval_notes?: string;\r\n  }\r\n) {\r\n  try {\r\n    return await apiClient.put(`/expenses/${id}/decline`, data);\r\n  } catch (error) {\r\n    console.error(\"Error declining expense:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// First level approval (Accountants, OPM, Assistant OPM)\r\nexport async function firstLevelApproveExpense(\r\n  id: string,\r\n  data: {\r\n    approved_amount: number;\r\n    approval_notes?: string;\r\n  }\r\n) {\r\n  try {\r\n    return await apiClient.put(`/expenses/${id}/first-level-approve`, data);\r\n  } catch (error) {\r\n    console.error(\"Error approving expense at first level:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// First level decline (Accountants, OPM, Assistant OPM)\r\nexport async function firstLevelDeclineExpense(\r\n  id: string,\r\n  data: {\r\n    approval_notes?: string;\r\n  }\r\n) {\r\n  try {\r\n    return await apiClient.put(`/expenses/${id}/first-level-decline`, data);\r\n  } catch (error) {\r\n    console.error(\"Error declining expense at first level:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Final approval (Company Admin only)\r\nexport async function finalApproveExpense(\r\n  id: string,\r\n  data: {\r\n    approved_amount: number;\r\n    approval_notes?: string;\r\n  }\r\n) {\r\n  try {\r\n    return await apiClient.post(`/expenses/${id}/final-approve`, data);\r\n  } catch (error) {\r\n    console.error(\"Error giving final approval to expense:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Final decline (Company Admin only)\r\nexport async function finalDeclineExpense(\r\n  id: string,\r\n  data: {\r\n    approval_notes?: string;\r\n  }\r\n) {\r\n  try {\r\n    return await apiClient.post(`/expenses/${id}/final-decline`, data);\r\n  } catch (error) {\r\n    console.error(\"Error declining expense at final level:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Legacy second level approval functions - deprecated\r\n// These are kept for backward compatibility but should not be used in new code\r\nexport async function secondLevelApproveExpense(\r\n  id: string,\r\n  data: {\r\n    approved_amount: number;\r\n    approval_notes?: string;\r\n  }\r\n) {\r\n  console.warn(\r\n    \"secondLevelApproveExpense is deprecated, use finalApproveExpense instead\"\r\n  );\r\n  return finalApproveExpense(id, data);\r\n}\r\n\r\nexport async function secondLevelDeclineExpense(\r\n  id: string,\r\n  data: {\r\n    approval_notes?: string;\r\n  }\r\n) {\r\n  console.warn(\r\n    \"secondLevelDeclineExpense is deprecated, use finalDeclineExpense instead\"\r\n  );\r\n  return finalDeclineExpense(id, data);\r\n}\r\n\r\n// Upload receipt image for an expense\r\nexport async function uploadReceiptImage(id: string, file: File) {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append(\"receipt_image\", file);\r\n\r\n    const response = await apiClient.post(`/expenses/${id}/receipt`, formData, {\r\n      headers: {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      },\r\n    });\r\n\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error uploading receipt image:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Create a new expense\r\nexport async function createExpense(data: {\r\n  amount: number;\r\n  description: string;\r\n  category_id: number;\r\n  branch_id: number;\r\n  pos_session_id?: number;\r\n  document_image?: File;\r\n}) {\r\n  try {\r\n    // Check if document_image is provided\r\n    if (data.document_image) {\r\n      // Use FormData to send the file along with other data\r\n      const formData = new FormData();\r\n      formData.append(\"amount\", data.amount.toString());\r\n      formData.append(\"description\", data.description);\r\n      formData.append(\"category_id\", data.category_id.toString());\r\n      formData.append(\"branch_id\", data.branch_id.toString());\r\n\r\n      if (data.pos_session_id) {\r\n        formData.append(\"pos_session_id\", data.pos_session_id.toString());\r\n      }\r\n\r\n      // Make sure to use the exact field name expected by the backend controller\r\n      // The controller expects \"document_image\" as the field name\r\n      formData.append(\"document_image\", data.document_image);\r\n\r\n      // Log the FormData for debugging\r\n      console.log(\"FormData entries:\");\r\n      for (const pair of (formData as any).entries()) {\r\n        console.log(\r\n          `${pair[0]}: ${\r\n            pair[1] instanceof File\r\n              ? `File: ${pair[1].name} (${pair[1].type}, ${pair[1].size} bytes)`\r\n              : pair[1]\r\n          }`\r\n        );\r\n      }\r\n\r\n      // Send the FormData with the file\r\n      const response = await apiClient.post(\"/expenses\", formData, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n      });\r\n\r\n      return (response as any).data;\r\n    } else {\r\n      // If no receipt_image, send a regular JSON object\r\n      const expenseData = {\r\n        amount: data.amount,\r\n        description: data.description,\r\n        category_id: data.category_id,\r\n        branch_id: data.branch_id,\r\n        pos_session_id: data.pos_session_id,\r\n      };\r\n\r\n      // Send the data directly without FormData\r\n      const response = await apiClient.post(\"/expenses\", expenseData);\r\n      return (response as any).data;\r\n    }\r\n  } catch (error: any) {\r\n    console.error(\"Error creating expense:\", error);\r\n\r\n    // Log more detailed error information\r\n    if (error.response) {\r\n      console.error(\"Response data:\", error.response.data);\r\n      console.error(\"Response status:\", error.response.status);\r\n      console.error(\"Response headers:\", error.response.headers);\r\n    } else if (error.request) {\r\n      console.error(\r\n        \"Request was made but no response received:\",\r\n        error.request\r\n      );\r\n    } else {\r\n      console.error(\"Error message:\", error.message);\r\n    }\r\n\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Update an expense\r\nexport async function updateExpense(\r\n  id: string,\r\n  data: {\r\n    amount?: number;\r\n    description?: string;\r\n    category_id?: number;\r\n  }\r\n) {\r\n  try {\r\n    const response = await apiClient.put(`/expenses/${id}`, data);\r\n    return (response as any).data;\r\n  } catch (error) {\r\n    console.error(\"Error updating expense:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Upload invoice image for an expense\r\nexport async function uploadInvoiceImage(id: string, file: File) {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append(\"invoice_image\", file);\r\n\r\n    const response = await apiClient.post(`/expenses/${id}/invoice`, formData, {\r\n      headers: {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      },\r\n    });\r\n\r\n    return (response as any).data;\r\n  } catch (error) {\r\n    console.error(\"Error uploading invoice image:\", error);\r\n    throw error;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,eAAe,qBAAqB,MAA8B;IACvE,IAAI;QACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,uBAAuB;YAAE;QAAO;QACrE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO,EAAE;IACX;AACF;AAGO,eAAe,yBAAyB,MAE9C;IACC,IAAI;QACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,4BAA4B;YAC/D;QACF;QACA,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO,EAAE;IACX;AACF;AAGO,eAAe,sBAAsB,IAM3C;IACC,IAAI;QACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;QAC7D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAGO,eAAe,sBACpB,EAAU,EACV,IAMC;IAED,IAAI;QACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;QAClE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAGO,eAAe,YAAY,EAChC,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,MAAM,EACN,MAAM,EACN,SAAS,EAOV;IACC,IAAI;QACF,yBAAyB;QACzB,MAAM,SAA8B;YAClC,OAAO;YACP,QAAQ,CAAC,OAAO,CAAC,IAAI;QACvB;QAEA,IAAI,QAAQ;YACV,OAAO,MAAM,GAAG;QAClB;QAEA,IAAI,QAAQ;YACV,OAAO,MAAM,GAAG;QAClB;QAEA,IAAI,WAAW;YACb,OAAO,SAAS,GAAG;QACrB;QAEA,6CAA6C;QAC7C,MAAM,WAAY,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAE5D,OAAO;YACL,UAAU,SAAS,IAAI,IAAI,EAAE;YAC7B,YAAY,SAAS,KAAK,IAAI;QAChC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YACL,UAAU,EAAE;YACZ,YAAY;QACd;IACF;AACF;AAGO,eAAe,eAAe,EAAU;IAC7C,IAAI;QACF,MAAM,WAAY,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACvD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;QACvD,OAAO;IACT;AACF;AAGO,eAAe,eACpB,EAAU,EACV,IAGC;IAED,IAAI;QACF,OAAO,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,QAAQ,CAAC,EAAE;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAGO,eAAe,eACpB,EAAU,EACV,IAEC;IAED,IAAI;QACF,OAAO,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,QAAQ,CAAC,EAAE;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAGO,eAAe,yBACpB,EAAU,EACV,IAGC;IAED,IAAI;QACF,OAAO,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,oBAAoB,CAAC,EAAE;IACpE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,MAAM;IACR;AACF;AAGO,eAAe,yBACpB,EAAU,EACV,IAEC;IAED,IAAI;QACF,OAAO,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,oBAAoB,CAAC,EAAE;IACpE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,MAAM;IACR;AACF;AAGO,eAAe,oBACpB,EAAU,EACV,IAGC;IAED,IAAI;QACF,OAAO,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,GAAG,cAAc,CAAC,EAAE;IAC/D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,MAAM;IACR;AACF;AAGO,eAAe,oBACpB,EAAU,EACV,IAEC;IAED,IAAI;QACF,OAAO,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,GAAG,cAAc,CAAC,EAAE;IAC/D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,MAAM;IACR;AACF;AAIO,eAAe,0BACpB,EAAU,EACV,IAGC;IAED,QAAQ,IAAI,CACV;IAEF,OAAO,oBAAoB,IAAI;AACjC;AAEO,eAAe,0BACpB,EAAU,EACV,IAEC;IAED,QAAQ,IAAI,CACV;IAEF,OAAO,oBAAoB,IAAI;AACjC;AAGO,eAAe,mBAAmB,EAAU,EAAE,IAAU;IAC7D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,iBAAiB;QAEjC,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,GAAG,QAAQ,CAAC,EAAE,UAAU;YACzE,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAGO,eAAe,cAAc,IAOnC;IACC,IAAI;QACF,sCAAsC;QACtC,IAAI,KAAK,cAAc,EAAE;YACvB,sDAAsD;YACtD,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,UAAU,KAAK,MAAM,CAAC,QAAQ;YAC9C,SAAS,MAAM,CAAC,eAAe,KAAK,WAAW;YAC/C,SAAS,MAAM,CAAC,eAAe,KAAK,WAAW,CAAC,QAAQ;YACxD,SAAS,MAAM,CAAC,aAAa,KAAK,SAAS,CAAC,QAAQ;YAEpD,IAAI,KAAK,cAAc,EAAE;gBACvB,SAAS,MAAM,CAAC,kBAAkB,KAAK,cAAc,CAAC,QAAQ;YAChE;YAEA,2EAA2E;YAC3E,4DAA4D;YAC5D,SAAS,MAAM,CAAC,kBAAkB,KAAK,cAAc;YAErD,iCAAiC;YACjC,QAAQ,GAAG,CAAC;YACZ,KAAK,MAAM,QAAQ,AAAC,SAAiB,OAAO,GAAI;gBAC9C,QAAQ,GAAG,CACT,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,EACX,IAAI,CAAC,EAAE,YAAY,OACf,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,GAChE,IAAI,CAAC,EAAE,EACX;YAEN;YAEA,kCAAkC;YAClC,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,aAAa,UAAU;gBAC3D,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,OAAO,AAAC,SAAiB,IAAI;QAC/B,OAAO;YACL,kDAAkD;YAClD,MAAM,cAAc;gBAClB,QAAQ,KAAK,MAAM;gBACnB,aAAa,KAAK,WAAW;gBAC7B,aAAa,KAAK,WAAW;gBAC7B,WAAW,KAAK,SAAS;gBACzB,gBAAgB,KAAK,cAAc;YACrC;YAEA,0CAA0C;YAC1C,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,aAAa;YACnD,OAAO,AAAC,SAAiB,IAAI;QAC/B;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,sCAAsC;QACtC,IAAI,MAAM,QAAQ,EAAE;YAClB,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YACnD,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ,CAAC,MAAM;YACvD,QAAQ,KAAK,CAAC,qBAAqB,MAAM,QAAQ,CAAC,OAAO;QAC3D,OAAO,IAAI,MAAM,OAAO,EAAE;YACxB,QAAQ,KAAK,CACX,8CACA,MAAM,OAAO;QAEjB,OAAO;YACL,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;QAC/C;QAEA,MAAM;IACR;AACF;AAGO,eAAe,cACpB,EAAU,EACV,IAIC;IAED,IAAI;QACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;QACxD,OAAO,AAAC,SAAiB,IAAI;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAGO,eAAe,mBAAmB,EAAU,EAAE,IAAU;IAC7D,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,iBAAiB;QAEjC,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,GAAG,QAAQ,CAAC,EAAE,UAAU;YACzE,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,OAAO,AAAC,SAAiB,IAAI;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 7976, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/expenses/hooks/use-expenses.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport * as expensesApi from \"@/lib/api/expenses\";\r\n\r\n/**\r\n * Hook to fetch expenses with pagination and filtering\r\n */\r\nexport const useExpenses = (params?: {\r\n  page?: number;\r\n  limit?: number;\r\n  status?: string;\r\n  search?: string;\r\n  branch_id?: number;\r\n}) => {\r\n  return useQuery({\r\n    queryKey: [\"expenses\", params],\r\n    queryFn: () => expensesApi.getExpenses(params || {}),\r\n    placeholderData: \"keep\" as any,\r\n    retry: 2,\r\n    refetchOnWindowFocus: false,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to create a new expense\r\n */\r\nexport const useCreateExpense = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: {\r\n      amount: number;\r\n      description: string;\r\n      category_id: number;\r\n      branch_id: number;\r\n      pos_session_id?: number;\r\n      document_image?: File;\r\n    }) => expensesApi.createExpense(data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"expenses\"] });\r\n      toast.success(\"Expense created successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(error.message || \"Failed to create expense\");\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to update an expense\r\n */\r\nexport const useUpdateExpense = (id: string) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: {\r\n      amount?: number;\r\n      description?: string;\r\n      category_id?: number;\r\n    }) => expensesApi.updateExpense(id, data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"expenses\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"expenses\"] });\r\n      toast.success(\"Expense updated successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(error.message || \"Failed to update expense\");\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to upload a receipt image for an expense\r\n */\r\nexport const useUploadReceiptImage = (id: string) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (file: File) => expensesApi.uploadReceiptImage(id, file),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"expenses\", id] });\r\n      toast.success(\"Receipt image uploaded successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(error.message || \"Failed to upload receipt image\");\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to upload an invoice image for an expense\r\n */\r\nexport const useUploadInvoiceImage = (id: string) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (file: File) => expensesApi.uploadInvoiceImage(id, file),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"expenses\", id] });\r\n      toast.success(\"Invoice image uploaded successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(error.message || \"Failed to upload invoice image\");\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AACA;AACA;;;;;AAKO,MAAM,cAAc,CAAC;;IAO1B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAO;QAC9B,OAAO;oCAAE,IAAM,CAAA,GAAA,gIAAA,CAAA,cAAuB,AAAD,EAAE,UAAU,CAAC;;QAClD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;IACxB;AACF;GAda;;QAOJ,8KAAA,CAAA,WAAQ;;;AAYV,MAAM,mBAAmB;;IAC9B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,CAAC,OAOP,CAAA,GAAA,gIAAA,CAAA,gBAAyB,AAAD,EAAE;;QAChC,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;4CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IApBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAsBb,MAAM,mBAAmB,CAAC;;IAC/B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,CAAC,OAIP,CAAA,GAAA,gIAAA,CAAA,gBAAyB,AAAD,EAAE,IAAI;;QACpC,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;4CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAlBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAoBb,MAAM,wBAAwB,CAAC;;IACpC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,CAAC,OAAe,CAAA,GAAA,gIAAA,CAAA,qBAA8B,AAAD,EAAE,IAAI;;QAC/D,SAAS;iDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;iDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAba;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAeb,MAAM,wBAAwB,CAAC;;IACpC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,CAAC,OAAe,CAAA,GAAA,gIAAA,CAAA,qBAA8B,AAAD,EAAE,IAAI;;QAC/D,SAAS;iDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;iDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAba;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 8149, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/app/expenses/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ExpensesDataTable } from \"@/components/expenses/expenses-data-table\";\r\nimport { ExpensesHeader } from \"@/components/expenses/expenses-header\";\r\nimport { PageHeader } from \"@/components/page-header\";\r\nimport { MainLayout } from \"@/components/layouts/main-layout\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { useExpenses } from \"@/features/expenses/hooks/use-expenses\";\r\n\r\nexport default function ExpensesPage() {\r\n  const router = useRouter();\r\n  const { data: user, isLoading: userLoading } = useCurrentUser();\r\n\r\n  // Pure React state management (SPA pattern)\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [pageSize, setPageSize] = useState(10);\r\n  const [statusFilter, setStatusFilter] = useState<string | undefined>(\r\n    undefined\r\n  );\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [branchFilter, setBranchFilter] = useState<number | undefined>(\r\n    undefined\r\n  );\r\n\r\n  // Build query parameters for API call\r\n  const queryParams = {\r\n    page: currentPage,\r\n    limit: pageSize,\r\n    status: statusFilter,\r\n    search: searchQuery || undefined,\r\n    branch_id: branchFilter,\r\n  };\r\n\r\n  // Use React Query hook for data fetching\r\n  const { data, isLoading } = useExpenses(queryParams);\r\n\r\n  // Extract data from the response\r\n  const expenses = data?.expenses || [];\r\n  const totalCount = data?.totalCount || 0;\r\n\r\n  // Handle page changes\r\n  const handlePageChange = (page: number) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  // Handle page size changes\r\n  const handlePageSizeChange = (newPageSize: number) => {\r\n    setPageSize(newPageSize);\r\n    setCurrentPage(1); // Reset to first page when changing page size\r\n  };\r\n\r\n  // Check user permissions\r\n  useEffect(() => {\r\n    if (!userLoading && user) {\r\n      if (\r\n        user.role_name !== \"company_admin\" &&\r\n        user.role_name !== \"branch_admin\" &&\r\n        user.role_name !== \"branch_manager\" &&\r\n        user.role_name !== \"accountant\"\r\n      ) {\r\n        router.replace(\"/dashboard\");\r\n      }\r\n    }\r\n  }, [user, userLoading, router]);\r\n\r\n  if (userLoading) {\r\n    return (\r\n      <MainLayout>\r\n        <div className=\"flex justify-center items-center h-64\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\r\n          <span className=\"ml-2\">Loading...</span>\r\n        </div>\r\n      </MainLayout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"flex flex-col gap-4\">\r\n        <PageHeader\r\n          title=\"Expenses Management\"\r\n          description=\"Review and manage expense requests from employees\"\r\n        />\r\n\r\n        <ExpensesHeader\r\n          isLoading={isLoading}\r\n          searchQuery={searchQuery}\r\n          onSearchChange={setSearchQuery}\r\n          statusFilter={statusFilter}\r\n          onStatusChange={setStatusFilter}\r\n          branchFilter={branchFilter}\r\n          onBranchChange={setBranchFilter}\r\n          onPageReset={() => setCurrentPage(1)}\r\n        />\r\n\r\n        <ExpensesDataTable\r\n          data={expenses}\r\n          totalCount={totalCount}\r\n          isLoading={isLoading}\r\n          pagination={{\r\n            currentPage,\r\n            onPageChange: handlePageChange,\r\n            pageSize,\r\n            onPageSizeChange: handlePageSizeChange,\r\n          }}\r\n        />\r\n      </div>\r\n    </MainLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAE5D,4CAA4C;IAC5C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC7C;IAEF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC7C;IAGF,sCAAsC;IACtC,MAAM,cAAc;QAClB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ,eAAe;QACvB,WAAW;IACb;IAEA,yCAAyC;IACzC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,EAAE;IAExC,iCAAiC;IACjC,MAAM,WAAW,MAAM,YAAY,EAAE;IACrC,MAAM,aAAa,MAAM,cAAc;IAEvC,sBAAsB;IACtB,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,2BAA2B;IAC3B,MAAM,uBAAuB,CAAC;QAC5B,YAAY;QACZ,eAAe,IAAI,8CAA8C;IACnE;IAEA,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,eAAe,MAAM;gBACxB,IACE,KAAK,SAAS,KAAK,mBACnB,KAAK,SAAS,KAAK,kBACnB,KAAK,SAAS,KAAK,oBACnB,KAAK,SAAS,KAAK,cACnB;oBACA,OAAO,OAAO,CAAC;gBACjB;YACF;QACF;iCAAG;QAAC;QAAM;QAAa;KAAO;IAE9B,IAAI,aAAa;QACf,qBACE,6LAAC,kJAAA,CAAA,aAAU;sBACT,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAK,WAAU;kCAAO;;;;;;;;;;;;;;;;;IAI/B;IAEA,qBACE,6LAAC,kJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,uIAAA,CAAA,aAAU;oBACT,OAAM;oBACN,aAAY;;;;;;8BAGd,6LAAC,uJAAA,CAAA,iBAAc;oBACb,WAAW;oBACX,aAAa;oBACb,gBAAgB;oBAChB,cAAc;oBACd,gBAAgB;oBAChB,cAAc;oBACd,gBAAgB;oBAChB,aAAa,IAAM,eAAe;;;;;;8BAGpC,6LAAC,8JAAA,CAAA,oBAAiB;oBAChB,MAAM;oBACN,YAAY;oBACZ,WAAW;oBACX,YAAY;wBACV;wBACA,cAAc;wBACd;wBACA,kBAAkB;oBACpB;;;;;;;;;;;;;;;;;AAKV;GApGwB;;QACP,qIAAA,CAAA,YAAS;QACuB,kJAAA,CAAA,iBAAc;QAuBjC,0JAAA,CAAA,cAAW;;;KAzBjB", "debugId": null}}]}