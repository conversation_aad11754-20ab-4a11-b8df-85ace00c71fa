"use client";

import { MainLayout } from "@/components/layouts/main-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  useHQInventory,
  useInventoryReportSummary,
} from "@/features/inventory/hooks/use-inventory";
import { ReportChart } from "@/features/reports/components/report-chart";
import { ReportDataTable } from "@/features/reports/components/report-data-table";
import { ReportFilters } from "@/features/reports/components/report-filters";
import {
  useStockLevelsExport,
  useStockLevelsReport,
} from "@/features/reports/hooks/use-stock-levels";
import { formatCurrency } from "@/lib/utils";
import { StockLevelProduct } from "@/types/stock-levels";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import {
  AlertCircle,
  <PERSON><PERSON><PERSON>riangle,
  CheckCircle,
  Download,
  Package,
  RefreshCw,
  TrendingDown,
  TrendingUp,
  Wifi,
  WifiOff,
} from "lucide-react";
import { useState } from "react";

export default function CurrentStockLevelsPage() {
  const [filters, setFilters] = useState({
    branch_id: undefined as number | undefined,
    region_id: undefined as number | undefined,
    category_id: undefined as number | undefined,
    search: "",
    page: 1,
    limit: 50,
    include_zero_stock: true,
    sort_by: "name" as const,
    sort_direction: "asc" as const,
  });

  // Use the new comprehensive stock levels endpoint
  const {
    data: stockLevelsData,
    isLoading,
    error,
    refetch,
  } = useStockLevelsReport(filters);

  // Excel export mutation
  const exportMutation = useStockLevelsExport();

  // Error state management
  const isNetworkError =
    error?.message?.includes("Network") || error?.message?.includes("fetch");
  const isPermissionError =
    error?.message?.includes("permission") || error?.message?.includes("403");
  const isServerError =
    error?.message?.includes("Server") || error?.message?.includes("500");
  const isEndpointNotFound =
    error?.message?.includes("404") || error?.message?.includes("not found");

  // Fallback to old endpoints when new endpoint is not available
  const shouldUseFallback = isEndpointNotFound || isServerError;

  // Fallback hooks (only enabled when needed)
  const { data: fallbackStockData, isLoading: isFallbackStockLoading } =
    useHQInventory(
      { ...filters, limit: filters.limit || 50 },
      shouldUseFallback
    );

  const { data: fallbackSummaryData, isLoading: isFallbackSummaryLoading } =
    useInventoryReportSummary({
      branch_id: filters.branch_id,
      category_id: filters.category_id,
    });

  const handleFilterChange = (newFilters: any) => {
    setFilters((prev) => ({ ...prev, ...newFilters, page: 1 }));
  };

  const handleExport = async () => {
    try {
      await exportMutation.mutateAsync({
        branch_id: filters.branch_id,
        region_id: filters.region_id,
        category_id: filters.category_id,
        search: filters.search,
        include_zero_stock: filters.include_zero_stock,
      });
    } catch (error) {
      console.error("Export failed:", error);
    }
  };

  const handleRetry = () => {
    refetch();
  };

  // Error display component
  const ErrorDisplay = () => {
    if (!error) return null;

    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-3">
            {isNetworkError ? (
              <WifiOff className="h-8 w-8 text-red-500" />
            ) : isPermissionError ? (
              <AlertCircle className="h-8 w-8 text-red-500" />
            ) : isServerError ? (
              <AlertTriangle className="h-8 w-8 text-red-500" />
            ) : (
              <AlertCircle className="h-8 w-8 text-red-500" />
            )}

            <div className="flex-1">
              <h3 className="font-semibold text-red-800">
                {isNetworkError && "Network Connection Error"}
                {isPermissionError && "Access Denied"}
                {isServerError && "Server Error"}
                {isEndpointNotFound && "Feature Not Available"}
                {!isNetworkError &&
                  !isPermissionError &&
                  !isServerError &&
                  !isEndpointNotFound &&
                  "Error Loading Data"}
              </h3>
              <p className="text-sm text-red-600 mt-1">
                {isNetworkError &&
                  "Please check your internet connection and try again."}
                {isPermissionError &&
                  "You don't have permission to view stock reports. Contact your administrator."}
                {isServerError &&
                  "The server is experiencing issues. Please try again later."}
                {isEndpointNotFound &&
                  "The stock levels feature is not available. Please contact support."}
                {!isNetworkError &&
                  !isPermissionError &&
                  !isServerError &&
                  !isEndpointNotFound &&
                  error.message}
              </p>
            </div>

            <Button
              onClick={handleRetry}
              variant="outline"
              size="sm"
              className="border-red-300 text-red-700 hover:bg-red-100"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Column definitions for fallback data structure
  const fallbackStockColumns: ColumnDef<any>[] = [
    {
      accessorKey: "product.name",
      header: "Product Name",
      cell: ({ row }) => (
        <div>
          <div className="font-medium">
            {row.original.product?.name || "Unknown Product"}
          </div>
          <div className="text-sm text-muted-foreground">
            SKU: {row.original.product?.sku || "N/A"}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "product.category.name",
      header: "Category",
      cell: ({ row }) =>
        row.original.product?.category?.name || "Uncategorized",
    },
    {
      accessorKey: "quantity",
      header: "Current Stock",
      cell: ({ row }) => {
        const quantity = row.getValue("quantity") as number;
        const minStock = row.original.product?.min_stock_level || 0;

        return (
          <div className="flex items-center gap-2">
            <span
              className={`font-medium ${
                quantity <= 0
                  ? "text-red-600"
                  : quantity <= minStock
                  ? "text-yellow-600"
                  : "text-green-600"
              }`}
            >
              {quantity}
            </span>
            {quantity <= 0 && (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
            {quantity > 0 && quantity <= minStock && (
              <TrendingDown className="h-4 w-4 text-yellow-500" />
            )}
            {quantity > minStock && (
              <CheckCircle className="h-4 w-4 text-green-500" />
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "buying_price",
      header: "Unit Cost",
      cell: ({ row }) => formatCurrency(row.getValue("buying_price") || 0),
    },
    {
      accessorKey: "selling_price",
      header: "Unit Price",
      cell: ({ row }) => formatCurrency(row.getValue("selling_price") || 0),
    },
    {
      header: "Total Value",
      cell: ({ row }) => {
        const quantity = row.original.quantity || 0;
        const price = row.original.buying_price || 0;
        return formatCurrency(quantity * price);
      },
    },
    {
      accessorKey: "branch.name",
      header: "Branch",
      cell: ({ row }) => row.original.branch?.name || "Unknown Branch",
    },
    {
      header: "Region",
      cell: ({ row }) => row.original.branch?.region?.name || "Unknown Region",
    },
    {
      header: "Stock Status",
      cell: ({ row }) => {
        const quantity = row.original.quantity || 0;
        const minStock = row.original.product?.min_stock_level || 0;

        let status = "In Stock";
        let className = "bg-green-100 text-green-800";

        if (quantity <= 0) {
          status = "Out of Stock";
          className = "bg-red-100 text-red-800";
        } else if (quantity <= minStock) {
          status = "Low Stock";
          className = "bg-yellow-100 text-yellow-800";
        }

        return (
          <span
            className={`px-2 py-1 rounded text-xs font-medium ${className}`}
          >
            {status}
          </span>
        );
      },
    },
  ];

  // Column definitions for current stock levels using new data structure
  const stockColumns: ColumnDef<StockLevelProduct>[] = [
    {
      accessorKey: "name",
      header: "Product Name",
      cell: ({ row }) => (
        <div>
          <div className="font-medium">
            {row.original.name || "Unknown Product"}
          </div>
          <div className="text-sm text-muted-foreground">
            SKU: {row.original.sku || "N/A"}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "category.name",
      header: "Category",
      cell: ({ row }) => row.original.category?.name || "Uncategorized",
    },
    {
      accessorKey: "stock_info.current_quantity",
      header: "Current Stock",
      cell: ({ row }) => {
        const quantity = row.original.stock_info.current_quantity;
        const minStock = row.original.stock_info.min_stock_level;

        return (
          <div className="flex items-center gap-2">
            <span
              className={`font-medium ${
                quantity <= 0
                  ? "text-red-600"
                  : quantity <= minStock
                  ? "text-yellow-600"
                  : "text-green-600"
              }`}
            >
              {quantity}
            </span>
            {quantity <= 0 && (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
            {quantity > 0 && quantity <= minStock && (
              <TrendingDown className="h-4 w-4 text-yellow-500" />
            )}
            {quantity > minStock && (
              <CheckCircle className="h-4 w-4 text-green-500" />
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "pricing.buying_price",
      header: "Unit Cost",
      cell: ({ row }) => formatCurrency(row.original.pricing.buying_price || 0),
    },
    {
      accessorKey: "pricing.selling_price",
      header: "Unit Price",
      cell: ({ row }) =>
        formatCurrency(row.original.pricing.selling_price || 0),
    },
    {
      header: "Total Value",
      cell: ({ row }) => {
        const totalValue = parseFloat(row.original.pricing.total_value || "0");
        return formatCurrency(totalValue);
      },
    },
    {
      accessorKey: "location.branch_name",
      header: "Branch",
      cell: ({ row }) => (
        <div>
          <div className="font-medium">
            {row.original.location.branch_name || "Unknown Branch"}
          </div>
          <div className="text-sm text-muted-foreground">
            {row.original.location.branch_location || ""}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "location.region_name",
      header: "Region",
      cell: ({ row }) => row.original.location.region_name || "Unknown Region",
    },
    {
      accessorKey: "brand.name",
      header: "Brand",
      cell: ({ row }) => row.original.brand?.name || "No Brand",
    },
    {
      accessorKey: "pricing.margin_percentage",
      header: "Margin %",
      cell: ({ row }) => {
        const margin = parseFloat(
          row.original.pricing.margin_percentage || "0"
        );
        return (
          <span
            className={`font-medium ${
              margin >= 30
                ? "text-green-600"
                : margin >= 15
                ? "text-yellow-600"
                : "text-red-600"
            }`}
          >
            {margin.toFixed(1)}%
          </span>
        );
      },
    },
    {
      accessorKey: "stock_info.last_restocked",
      header: "Last Restocked",
      cell: ({ row }) => {
        const lastRestocked = row.original.stock_info.last_restocked;
        return lastRestocked
          ? format(new Date(lastRestocked), "MMM dd, yyyy")
          : "Never";
      },
    },
    {
      header: "Stock Status",
      cell: ({ row }) => {
        const status = row.original.stock_info.stock_status;

        let displayStatus = "In Stock";
        let className = "bg-green-100 text-green-800";

        if (status === "out_of_stock") {
          displayStatus = "Out of Stock";
          className = "bg-red-100 text-red-800";
        } else if (status === "low_stock") {
          displayStatus = "Low Stock";
          className = "bg-yellow-100 text-yellow-800";
        }

        return (
          <span
            className={`px-2 py-1 rounded text-xs font-medium ${className}`}
          >
            {displayStatus}
          </span>
        );
      },
    },
  ];

  // Process chart data using new data structure or fallback
  const processChartData = () => {
    if (isUsingFallback) {
      // Fallback: process data from old endpoint
      if (!fallbackStockData?.data) return { labels: [], datasets: [] };

      const categoryData = fallbackStockData.data.reduce(
        (acc: any, item: any) => {
          const category = item.product?.category?.name || "Uncategorized";
          if (!acc[category]) {
            acc[category] = { quantity: 0, value: 0 };
          }
          acc[category].quantity += item.quantity || 0;
          return acc;
        },
        {}
      );

      return {
        labels: Object.keys(categoryData),
        datasets: [
          {
            label: "Stock Quantity by Category",
            data: Object.values(categoryData).map((cat: any) => cat.quantity),
            backgroundColor: [
              "#3B82F6",
              "#EF4444",
              "#10B981",
              "#F59E0B",
              "#8B5CF6",
              "#EC4899",
              "#06B6D4",
              "#84CC16",
            ],
          },
        ],
      };
    }

    if (!stockLevelsData?.by_category) return { labels: [], datasets: [] };

    // Use the category breakdown from the new endpoint
    const categoryData = stockLevelsData.by_category;

    return {
      labels: categoryData.map((cat) => cat.category_name),
      datasets: [
        {
          label: "Stock Quantity by Category",
          data: categoryData.map((cat) => cat.total_quantity),
          backgroundColor: [
            "#3B82F6",
            "#EF4444",
            "#10B981",
            "#F59E0B",
            "#8B5CF6",
            "#EC4899",
            "#06B6D4",
            "#84CC16",
          ],
        },
      ],
    };
  };

  // Process branch chart data
  const processBranchChartData = () => {
    if (isUsingFallback) {
      // Fallback: process data from old endpoint
      if (!fallbackStockData?.data) return { labels: [], datasets: [] };

      const branchData = fallbackStockData.data.reduce(
        (acc: any, item: any) => {
          const branch = item.branch?.name || "Unknown Branch";
          if (!acc[branch]) {
            acc[branch] = { quantity: 0, value: 0 };
          }
          acc[branch].quantity += item.quantity || 0;
          return acc;
        },
        {}
      );

      return {
        labels: Object.keys(branchData),
        datasets: [
          {
            label: "Stock Quantity by Branch",
            data: Object.values(branchData).map(
              (branch: any) => branch.quantity
            ),
            backgroundColor: [
              "#10B981",
              "#3B82F6",
              "#F59E0B",
              "#EF4444",
              "#8B5CF6",
              "#EC4899",
            ],
          },
        ],
      };
    }

    if (!stockLevelsData?.by_branch) return { labels: [], datasets: [] };

    // Use the branch breakdown from the new endpoint
    const branchData = stockLevelsData.by_branch;

    return {
      labels: branchData.map((branch) => branch.branch_name),
      datasets: [
        {
          label: "Stock Quantity by Branch",
          data: branchData.map((branch) => branch.total_quantity),
          backgroundColor: [
            "#10B981",
            "#3B82F6",
            "#F59E0B",
            "#EF4444",
            "#8B5CF6",
            "#EC4899",
          ],
        },
      ],
    };
  };

  // Extract data from new endpoint structure or fallback
  const isUsingFallback = shouldUseFallback && !stockLevelsData;
  const currentIsLoading = isUsingFallback
    ? isFallbackStockLoading || isFallbackSummaryLoading
    : isLoading;

  // Add unique keys to stock items to prevent duplicate key errors
  const stockItems = isUsingFallback
    ? (fallbackStockData?.data || []).map((item: any, index: number) => ({
        ...item,
        uniqueKey: `${item.product?.id || index}-${item.branch?.id || index}`,
      }))
    : (stockLevelsData?.products || []).map((item: any) => ({
        ...item,
        uniqueKey: `${item.id}-${item.location.branch_id}`,
      }));

  const totalItems = isUsingFallback
    ? fallbackStockData?.pagination?.total || 0
    : stockLevelsData?.pagination?.total || 0;

  const summary = isUsingFallback
    ? {
        total_products: fallbackSummaryData?.total_products || 0,
        total_value: fallbackSummaryData?.total_value || 0,
        total_quantity: 0, // Not available in fallback
        in_stock_count:
          (fallbackSummaryData?.total_products || 0) -
          (fallbackSummaryData?.out_of_stock_count || 0),
        low_stock_count: fallbackSummaryData?.low_stock_count || 0,
        out_of_stock_count: fallbackSummaryData?.out_of_stock_count || 0,
        categories_count: fallbackSummaryData?.by_category?.length || 0,
        branches_count: fallbackSummaryData?.by_branch?.length || 0,
        last_updated: new Date().toISOString(),
      }
    : stockLevelsData?.summary;

  const stockAlerts = isUsingFallback ? null : stockLevelsData?.stock_alerts;

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              Current Stock Levels
            </h1>
            <p className="text-muted-foreground">
              View real-time stock levels for all products across your locations
            </p>
          </div>
          <Button
            onClick={handleExport}
            disabled={exportMutation.isPending}
            variant="outline"
          >
            {exportMutation.isPending ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export to Excel
              </>
            )}
          </Button>
        </div>

        <ReportFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          showProductFilter={false}
          showBranchFilter={true}
          showRegionFilter={true}
          showTimeFilter={false}
          showUserFilter={false}
          showPaymentMethodFilter={false}
          showCategoryFilter={true}
        />

        {/* Error Display */}
        <ErrorDisplay />

        {/* Fallback Notice */}
        {isUsingFallback && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <Wifi className="h-6 w-6 text-blue-500" />
                <div>
                  <h3 className="font-semibold text-blue-800">
                    Using Basic Stock View
                  </h3>
                  <p className="text-sm text-blue-600">
                    Advanced features are temporarily unavailable. Showing basic
                    stock information.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Show content only if no error or using fallback */}
        {(!error || isUsingFallback) && (
          <>
            {currentIsLoading ? (
              <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="h-32" />
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Products
                    </CardTitle>
                    <Package className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {summary?.total_products || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Products in inventory
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Value
                    </CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(summary?.total_value || 0)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Current inventory value
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      In Stock
                    </CardTitle>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">
                      {summary?.in_stock_count || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Items in stock
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Low Stock Items
                    </CardTitle>
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-yellow-600">
                      {summary?.low_stock_count || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Items below minimum level
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Out of Stock
                    </CardTitle>
                    <TrendingDown className="h-4 w-4 text-red-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-red-600">
                      {summary?.out_of_stock_count || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Items with zero stock
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Stock Alerts Section */}
            {stockAlerts &&
              (stockAlerts.critical_low_stock.length > 0 ||
                stockAlerts.out_of_stock.length > 0) && (
                <div className="space-y-4">
                  <h2 className="text-lg font-semibold text-red-600 flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    Stock Alerts
                  </h2>

                  <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                    {/* Critical Low Stock Alerts */}
                    {stockAlerts.critical_low_stock.length > 0 && (
                      <Card className="border-yellow-200 bg-yellow-50">
                        <CardHeader>
                          <CardTitle className="text-yellow-800 flex items-center gap-2">
                            <TrendingDown className="h-4 w-4" />
                            Critical Low Stock (
                            {stockAlerts.critical_low_stock.length})
                          </CardTitle>
                          <CardDescription>
                            Products that urgently need restocking
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3 max-h-64 overflow-y-auto">
                            {stockAlerts.critical_low_stock.map((alert) => (
                              <div
                                key={`${alert.product_id}-${alert.branch_name}`}
                                className="flex items-center justify-between p-3 bg-white rounded-lg border"
                              >
                                <div className="flex-1">
                                  <div className="font-medium text-sm">
                                    {alert.product_name}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    SKU: {alert.sku}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {alert.branch_name}
                                  </div>
                                </div>
                                <div className="text-right">
                                  <div className="text-sm font-medium text-red-600">
                                    {alert.current_quantity} /{" "}
                                    {alert.min_stock_level}
                                  </div>
                                  <div
                                    className={`text-xs px-2 py-1 rounded ${
                                      alert.urgency_level === "critical"
                                        ? "bg-red-100 text-red-800"
                                        : alert.urgency_level === "high"
                                        ? "bg-orange-100 text-orange-800"
                                        : "bg-yellow-100 text-yellow-800"
                                    }`}
                                  >
                                    {alert.urgency_level}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Out of Stock Alerts */}
                    {stockAlerts.out_of_stock.length > 0 && (
                      <Card className="border-red-200 bg-red-50">
                        <CardHeader>
                          <CardTitle className="text-red-800 flex items-center gap-2">
                            <Package className="h-4 w-4" />
                            Out of Stock ({stockAlerts.out_of_stock.length})
                          </CardTitle>
                          <CardDescription>
                            Products that are completely out of stock
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3 max-h-64 overflow-y-auto">
                            {stockAlerts.out_of_stock.map((alert) => (
                              <div
                                key={`${alert.product_id}-${alert.branch_name}`}
                                className="flex items-center justify-between p-3 bg-white rounded-lg border"
                              >
                                <div className="flex-1">
                                  <div className="font-medium text-sm">
                                    {alert.product_name}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    SKU: {alert.sku}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {alert.branch_name}
                                  </div>
                                </div>
                                <div className="text-right">
                                  <div className="text-sm font-medium text-red-600">
                                    {alert.days_out_of_stock} days
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {alert.last_sale_date
                                      ? `Last sale: ${format(
                                          new Date(alert.last_sale_date),
                                          "MMM dd"
                                        )}`
                                      : "No recent sales"}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </div>
              )}

            {/* Stock Levels Charts */}
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <ReportChart
                title="Stock Distribution by Category"
                description="Current stock quantities grouped by product category"
                data={processChartData()}
                chartTypes={["bar", "pie"]}
                defaultChartType="bar"
              />

              <ReportChart
                title="Stock Distribution by Branch"
                description="Current stock quantities across different branches"
                data={processBranchChartData()}
                chartTypes={["bar", "pie"]}
                defaultChartType="pie"
              />
            </div>

            {/* Stock Levels Table */}
            {currentIsLoading ? (
              <Skeleton className="h-96" />
            ) : (
              <ReportDataTable
                columns={isUsingFallback ? fallbackStockColumns : stockColumns}
                data={stockItems}
                title="Current Stock Levels"
                description={`Showing ${
                  stockItems.length
                } of ${totalItems} products${
                  isUsingFallback ? " (Basic View)" : ""
                }`}
                searchColumn={isUsingFallback ? "product.name" : "name"}
                searchPlaceholder="Search products..."
                exportFilename="current-stock-levels"
                showExport={false}
              />
            )}
          </>
        )}
      </div>
    </MainLayout>
  );
}
