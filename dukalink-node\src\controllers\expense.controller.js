/**
 * Expense controller
 */
const Expense = require("../models/expense.model");
const User = require("../models/user.model");
const Branch = require("../models/branch.model");
const Region = require("../models/region.model");
const ExpenseCategory = require("../models/expense-category.model");
const PosSession = require("../models/pos-session.model");
const AppError = require("../utils/error");
const path = require("path");
const fs = require("fs");
const { Op } = require("sequelize");
const logger = require("../utils/logger");
const { deleteFile } = require("../utils/file-upload");
const { sendExpenseNotification } = require("../services/resend-email.service");

// Create a new expense request
exports.createExpense = async (req, res, next) => {
  try {
    logger.info("Create expense request received");
    logger.info("Request body:", req.body);
    logger.info("Request headers:", req.headers);

    if (req.files) logger.info("Request files:", req.files);

    const contentType = req.headers["content-type"] || "";
    if (
      contentType.includes("multipart/form-data") &&
      (!req.body || Object.keys(req.body).length === 0)
    ) {
      logger.warn("Multipart request with empty body detected");
      if (!req.body) req.body = {};
      req.body.branch_id = req.user.branch_id;
      req.body.user_id = req.user.id;

      if (!req.body.amount || !req.body.description) {
        return next(
          new AppError(
            "Form parsing failed. Ensure amount and description are included in the form data.",
            400
          )
        );
      }
    }

    const amount = req.body?.amount;
    const description = req.body?.description;
    const branch_id = req.body?.branch_id || req.user.branch_id;
    const user_id = req.body?.user_id || req.user.id;
    const category_id = req.body?.category_id || null;

    const amountIsValid =
      amount !== null && amount !== undefined && String(amount).trim() !== "";
    const descriptionIsValid =
      description !== null &&
      description !== undefined &&
      String(description).trim() !== "";

    if (!amountIsValid || !descriptionIsValid) {
      if (req.files) {
        const allFiles = Object.values(req.files).flat();
        for (const file of allFiles) {
          fs.unlinkSync(file.path);
          logger.info(
            `Deleted uploaded file due to validation error: ${file.path}`
          );
        }
      }

      const missingFields = [];
      if (!amountIsValid) missingFields.push("amount");
      if (!descriptionIsValid) missingFields.push("description");

      return next(
        new AppError(
          `Missing required fields: ${missingFields.join(", ")}.`,
          400
        )
      );
    }

    let activeSession =
      (await PosSession.findOne({
        where: { user_id, branch_id, status: "open" },
      })) ||
      (await PosSession.findOne({
        where: { branch_id, status: "open" },
      }));

    let expenseStatus = "pending";
    if (category_id) {
      const category = await ExpenseCategory.findByPk(category_id);
      if (category?.is_shop_allowed) {
        expenseStatus = "approved";
        logger.info(`Auto-approving Shop Allowed category: ${category_id}`);
      }
    }

    const expense = await Expense.create({
      user_id,
      branch_id,
      amount: parseFloat(amount),
      description,
      status: expenseStatus,
      category_id,
      pos_session_id: activeSession?.id || null,
    });

    // Handle file uploads from req.files (multi-field upload)
    let hasDocumentImage = false;
    const documentFile =
      (req.files?.document_image && req.files.document_image[0]) ||
      (req.files?.invoice_image && req.files.invoice_image[0]);

    if (documentFile) {
      logger.info(`Document file uploaded: ${JSON.stringify(documentFile)}`);
      try {
        // Check if the field name is document_image or invoice_image (for backward compatibility)
        if (
          req.file.fieldname === "document_image" ||
          req.file.fieldname === "invoice_image"
        ) {
          logger.info(`Processing ${req.file.fieldname} field`);
          // Update expense with document image path
          expense.document_image = `/uploads/documents/${req.file.filename}`;
          await expense.save();
          logger.info(
            `Document image saved for expense ${expense.id}: ${expense.document_image}`
          );
        }
        // Handle receipt_image field
        else if (req.file.fieldname === "receipt_image") {
          logger.info("Processing receipt_image field");
          // Update expense with image path
          expense.receipt_image = `/uploads/receipts/${req.file.filename}`;
          await expense.save();
          hasReceiptImage = true;
          logger.info(
            `Receipt image saved for expense ${expense.id}: ${expense.receipt_image}`
          );
        } else {
          logger.warn(
            `Unknown field name in file upload: ${req.file.fieldname}`
          );
        }
      } catch (err) {
        logger.error(`Failed to save document reference: ${err.message}`);
      }
    }

    const expenseWithRelations = await Expense.findByPk(expense.id, {
      include: [
        { model: User, as: "user", attributes: ["id", "name", "email"] },
        { model: Branch, as: "branch", attributes: ["id", "name", "location"] },
        {
          model: ExpenseCategory,
          as: "category",
          attributes: ["id", "name", "description", "is_shop_allowed"],
        },
      ],
    });

    // Always send email notification for all expenses regardless of category or document status
    const emailSubject = hasDocumentImage
      ? `Document Uploaded for Expense: ${new Intl.NumberFormat("en-KE", {
          style: "currency",
          currency: "KES",
        }).format(expense.amount)}`
      : `New Expense Created: ${new Intl.NumberFormat("en-KE", {
          style: "currency",
          currency: "KES",
        }).format(expense.amount)}`;

    const emailOptions = hasDocumentImage
      ? {
          isDocumentUpload: true,
          documentPath: expense.document_image,
          customSubject: emailSubject,
        }
      : { customSubject: emailSubject };

    sendExpenseNotification(expenseWithRelations, emailOptions)
      .then(() => logger.info(`Notification sent for expense ${expense.id}`))
      .catch((err) => logger.error(`Failed to send email: ${err.message}`));

    const responseData = {
      status: "success",
      data: expense,
      linked_to_session: !!activeSession,
    };

    if (activeSession) {
      responseData.session_info = {
        id: activeSession.id,
        user_id: activeSession.user_id,
        branch_id: activeSession.branch_id,
        start_time: activeSession.start_time,
      };
    }

    return res.status(201).json(responseData);
  } catch (error) {
    logger.error(`Error creating expense: ${error.message}`);
    next(error);
  }
};

// Get all expenses with filtering
exports.getAllExpenses = async (req, res, next) => {
  try {
    const {
      status,
      start_date,
      end_date,
      user_id,
      branch_id,
      region_id,
      pos_session_id,
      limit = 100,
      offset = 0,
    } = req.query;

    // Build where clause
    const whereClause = {};

    // Filter by status if provided
    if (status) {
      whereClause.status = status;
    }

    // Filter by date range if provided
    if (start_date && end_date) {
      whereClause.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)],
      };
    } else if (start_date) {
      whereClause.created_at = {
        [Op.gte]: new Date(start_date),
      };
    } else if (end_date) {
      whereClause.created_at = {
        [Op.lte]: new Date(end_date),
      };
    }

    // Filter by user if provided
    if (user_id) {
      whereClause.user_id = user_id;
    }

    // Filter by branch if provided
    if (branch_id) {
      whereClause.branch_id = branch_id;
    }

    // Handle region filtering - get all branches in the specified region
    if (region_id) {
      const branchesInRegion = await Branch.findAll({
        where: { region_id, deleted_at: null },
        attributes: ["id"],
      });

      const branchIds = branchesInRegion.map((branch) => branch.id);

      if (branchIds.length > 0) {
        // Remove any existing branch_id filter
        delete whereClause.branch_id;

        // Add the branch_id IN clause
        whereClause.branch_id = {
          [Op.in]: branchIds,
        };
      }
    }

    // Filter by POS session if provided
    if (pos_session_id) {
      whereClause.pos_session_id = pos_session_id;
    }

    // Regular users can only see their own expenses
    if (
      req.user.role_name !== "company_admin" &&
      req.user.role_name !== "branch_admin" &&
      req.user.role_name !== "branch_manager" &&
      req.user.role_name !== "operations_manager" &&
      req.user.role_name !== "assistant_operations_manager" &&
      req.user.role_name !== "accountant"
    ) {
      whereClause.user_id = req.user.id;
    }

    // Branch-level roles can only see expenses from their branch
    if (
      req.user.role_name === "branch_admin" ||
      req.user.role_name === "branch_manager" ||
      req.user.role_name === "operations_manager" ||
      req.user.role_name === "assistant_operations_manager"
    ) {
      whereClause.branch_id = req.user.branch_id;
    }

    // Get expenses
    const expenses = await Expense.findAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: User,
          as: "first_approver",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: User,
          as: "final_approver",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "name", "location", "region_id"],
          include: [
            {
              model: Region,
              attributes: ["id", "name", "code"],
              required: false,
            },
          ],
        },
        {
          model: ExpenseCategory,
          as: "category",
          attributes: ["id", "name", "description"],
        },
        {
          model: PosSession,
          as: "pos_session",
          attributes: ["id", "start_time", "end_time", "status"],
        },
      ],
      order: [["created_at", "DESC"]],
      limit: parseInt(limit),
      offset: parseInt(offset),
    });

    // Get total count for pagination
    const totalCount = await Expense.count({ where: whereClause });

    // Add invoice_image/document_image fields for backward compatibility
    const expensesData = expenses.map((expense) => {
      const expenseData = expense.toJSON();

      // Handle the case where the migration hasn't been run yet
      if (
        expenseData.document_image === undefined &&
        expenseData.invoice_image !== undefined
      ) {
        // Migration not run yet, add document_image field
        expenseData.document_image = expenseData.invoice_image;
      }
      // Handle the case where the migration has been run
      else if (
        expenseData.document_image !== undefined &&
        expenseData.invoice_image === undefined
      ) {
        // Migration has been run, add invoice_image field
        expenseData.invoice_image = expenseData.document_image;
      }

      return expenseData;
    });

    return res.status(200).json({
      status: "success",
      count: expenses.length,
      total: totalCount,
      data: expensesData,
    });
  } catch (error) {
    logger.error(`Error fetching expenses: ${error.message}`);
    next(error);
  }
};

// Get a specific expense
exports.getExpenseById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const expense = await Expense.findByPk(id, {
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: User,
          as: "first_approver",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: User,
          as: "final_approver",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "name", "location"],
        },
        {
          model: ExpenseCategory,
          as: "category",
          attributes: ["id", "name", "description"],
        },
        {
          model: PosSession,
          as: "pos_session",
          attributes: ["id", "start_time", "end_time", "status"],
        },
      ],
    });

    if (!expense) {
      return next(new AppError("Expense not found", 404));
    }

    // Check if user has permission to view this expense
    const canViewAnyExpenseInBranch = [
      "branch_admin",
      "operations_manager",
      "assistant_operations_manager",
    ].includes(req.user.role_name);

    const canViewAnyExpense = ["company_admin", "accountant"].includes(
      req.user.role_name
    );

    if (
      !canViewAnyExpense &&
      req.user.id !== expense.user_id &&
      (!canViewAnyExpenseInBranch || req.user.branch_id !== expense.branch_id)
    ) {
      return next(
        new AppError("You do not have permission to view this expense", 403)
      );
    }

    // Add invoice_image/document_image fields for backward compatibility
    const expenseData = expense.toJSON();

    // Handle the case where the migration hasn't been run yet
    if (
      expenseData.document_image === undefined &&
      expenseData.invoice_image !== undefined
    ) {
      // Migration not run yet, add document_image field
      expenseData.document_image = expenseData.invoice_image;
    }
    // Handle the case where the migration has been run
    else if (
      expenseData.document_image !== undefined &&
      expenseData.invoice_image === undefined
    ) {
      // Migration has been run, add invoice_image field
      expenseData.invoice_image = expenseData.document_image;
    }

    return res.status(200).json({
      status: "success",
      data: expenseData,
    });
  } catch (error) {
    logger.error(`Error fetching expense by ID: ${error.message}`);
    next(error);
  }
};

// Approve or partially approve an expense (Legacy - redirects to new approval flow)
exports.approveExpense = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { approved_amount, approval_notes } = req.body;

    // Determine which approval endpoint to use based on user role
    if (
      req.user.role_name === "operations_manager" ||
      req.user.role_name === "assistant_operations_manager"
    ) {
      // Redirect to first level approval
      logger.info(`Redirecting to first level approval for expense ${id}`);

      // Import the first level approval controller
      const expenseApprovalController = require("./expense-approval.controller");

      // Call the first level approval function
      return expenseApprovalController.firstLevelApprove(req, res, next);
    } else if (
      req.user.role_name === "accountant" ||
      req.user.role_name === "company_admin"
    ) {
      // Redirect to final approval
      logger.info(`Redirecting to final approval for expense ${id}`);

      // Import the expense approval controller
      const expenseApprovalController = require("./expense-approval.controller");

      // Call the final approval function
      return expenseApprovalController.finalApprove(req, res, next);
    } else {
      // User doesn't have permission to approve expenses
      return next(
        new AppError(
          "You do not have permission to approve expenses. Only Operations Manager, Assistant Operations Manager, Accountant, or Company Admin can approve expenses.",
          403
        )
      );
    }
  } catch (error) {
    logger.error(`Error in expense approval redirection: ${error.message}`);
    next(error);
  }
};

// Decline an expense (Legacy - redirects to new approval flow)
exports.declineExpense = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { approval_notes } = req.body;

    // Determine which decline endpoint to use based on user role
    if (
      req.user.role_name === "operations_manager" ||
      req.user.role_name === "assistant_operations_manager"
    ) {
      // Redirect to first level decline
      logger.info(`Redirecting to first level decline for expense ${id}`);

      // Import the first level decline controller
      const expenseApprovalController = require("./expense-approval.controller");

      // Call the first level decline function
      return expenseApprovalController.firstLevelDecline(req, res, next);
    } else if (
      req.user.role_name === "accountant" ||
      req.user.role_name === "company_admin"
    ) {
      // Redirect to final decline
      logger.info(`Redirecting to final decline for expense ${id}`);

      // Import the expense approval controller
      const expenseApprovalController = require("./expense-approval.controller");

      // Call the final decline function
      return expenseApprovalController.finalDecline(req, res, next);
    } else {
      // User doesn't have permission to decline expenses
      return next(
        new AppError(
          "You do not have permission to decline expenses. Only Operations Manager, Assistant Operations Manager, Accountant, or Company Admin can decline expenses.",
          403
        )
      );
    }
  } catch (error) {
    logger.error(`Error in expense decline redirection: ${error.message}`);
    next(error);
  }
};

// Upload receipt image for an existing expense (POP after approval)
exports.uploadReceiptImage = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Log the request details for debugging
    logger.info(`Upload receipt image request for expense ID: ${id}`);
    logger.info(`Request headers: ${JSON.stringify(req.headers)}`);
    logger.info(
      `Request file: ${req.file ? JSON.stringify(req.file) : "No file"}`
    );

    // Check if expense exists
    const expense = await Expense.findByPk(id);
    if (!expense) {
      // If there's an uploaded file, delete it
      if (req.file) {
        await deleteFile(req.file.path);
        logger.info(`Deleted uploaded file because expense ${id} not found`);
      }
      return next(new AppError("Expense not found", 404));
    }

    // Check if user has permission to upload receipt
    // Only the user who created the expense or admins can upload receipts
    if (
      req.user.id !== expense.user_id &&
      req.user.role_name !== "company_admin" &&
      req.user.role_name !== "branch_admin" &&
      req.user.role_name !== "branch_manager"
    ) {
      // If there's an uploaded file, delete it
      if (req.file) {
        await deleteFile(req.file.path);
        logger.info(
          `Deleted uploaded file because user ${req.user.id} doesn't have permission`
        );
      }
      return next(
        new AppError(
          "You do not have permission to upload a receipt for this expense",
          403
        )
      );
    }

    // Check if file was uploaded
    if (!req.file) {
      logger.error("No file was received in the request");
      return next(
        new AppError(
          'No receipt image uploaded. Please ensure you are sending a file with field name "receipt_image".',
          400
        )
      );
    }

    logger.info(
      `File received: ${req.file.filename}, size: ${req.file.size}, mimetype: ${req.file.mimetype}`
    );

    // If expense already has a receipt image, delete the old one
    if (expense.receipt_image) {
      const oldFilePath = path.join(
        __dirname,
        "../../uploads",
        expense.receipt_image.replace(/^\/uploads\//, "")
      );
      if (fs.existsSync(oldFilePath)) {
        await deleteFile(oldFilePath);
        logger.info(`Deleted old receipt image: ${oldFilePath}`);
      } else {
        logger.warn(`Old receipt image file not found: ${oldFilePath}`);
      }
    }

    // Update expense with new image path
    expense.receipt_image = `/uploads/receipts/${req.file.filename}`;
    await expense.save();

    logger.info(
      `Receipt image updated for expense ${expense.id}: ${expense.receipt_image}`
    );

    // Fetch the expense with related data for the email notification
    const expenseWithRelations = await Expense.findByPk(expense.id, {
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email"],
        },
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "name", "location"],
        },
        {
          model: ExpenseCategory,
          as: "category",
          attributes: ["id", "name", "description", "is_shop_allowed"],
        },
      ],
    });

    // Check if this is a Shop Allowed expense
    const isShopAllowed =
      expenseWithRelations.category?.is_shop_allowed || false;

    // Send email notification for both Shop Allowed and Non-Shop Allowed expenses when receipt is uploaded
    // For Non-Shop Allowed, this is the first notification
    // For Shop Allowed, this is a follow-up notification that a receipt was uploaded

    // Customize the subject based on expense type
    const emailSubject = isShopAllowed
      ? `Receipt Uploaded for Shop Allowed Expense: ${new Intl.NumberFormat(
          "en-KE",
          {
            style: "currency",
            currency: "KES",
          }
        ).format(expense.amount)}`
      : `Receipt Uploaded for Expense: ${new Intl.NumberFormat("en-KE", {
          style: "currency",
          currency: "KES",
        }).format(expense.amount)}`;

    // Send email notification with receipt attachment (don't await to avoid blocking the response)
    sendExpenseNotification(expenseWithRelations, {
      isReceiptUpload: true,
      receiptPath: expense.receipt_image,
      customSubject: emailSubject,
    })
      .then(() =>
        logger.info(
          `Email notification sent for ${isShopAllowed ? "Shop Allowed" : "Non-Shop Allowed"} expense ${expense.id} with receipt attachment`
        )
      )
      .catch((err) =>
        logger.error(
          `Error sending email notification with receipt: ${err.message}`
        )
      );

    return res.status(200).json({
      status: "success",
      message: "Receipt image uploaded successfully",
      data: {
        id: expense.id,
        receipt_image: expense.receipt_image,
        document_image: expense.document_image,
        invoice_image: expense.document_image, // For backward compatibility
      },
    });
  } catch (error) {
    logger.error(`Error uploading receipt image: ${error.message}`);
    logger.error(`Error stack: ${error.stack}`);
    next(error);
  }
};

// Upload document image for an existing expense (formerly invoice image)
exports.uploadDocumentImage = async (req, res, next) => {
  // Keep backward compatibility
  exports.uploadInvoiceImage = exports.uploadDocumentImage;
  try {
    const { id } = req.params;

    // Log the request details for debugging
    logger.info(`Upload document image request for expense ID: ${id}`);
    logger.info(`Request headers: ${JSON.stringify(req.headers)}`);
    logger.info(
      `Request file: ${req.file ? JSON.stringify(req.file) : "No file"}`
    );

    // Check if expense exists
    const expense = await Expense.findByPk(id);
    if (!expense) {
      // If there's an uploaded file, delete it
      if (req.file) {
        await deleteFile(req.file.path);
        logger.info(`Deleted uploaded file because expense ${id} not found`);
      }
      return next(new AppError("Expense not found", 404));
    }

    // Check if user has permission to upload invoice
    // Only the user who created the expense or admins can upload invoices
    if (
      req.user.id !== expense.user_id &&
      req.user.role_name !== "company_admin" &&
      req.user.role_name !== "branch_admin" &&
      req.user.role_name !== "branch_manager"
    ) {
      // If there's an uploaded file, delete it
      if (req.file) {
        await deleteFile(req.file.path);
        logger.info(
          `Deleted uploaded file because user ${req.user.id} doesn't have permission`
        );
      }
      return next(
        new AppError(
          "You do not have permission to upload a document for this expense",
          403
        )
      );
    }

    // Check if file was uploaded
    if (!req.file) {
      logger.error("No file was received in the request");
      return next(
        new AppError(
          'No document image uploaded. Please ensure you are sending a file with field name "document_image" or "invoice_image" (for backward compatibility).',
          400
        )
      );
    }

    logger.info(
      `File received: ${req.file.filename}, size: ${req.file.size}, mimetype: ${req.file.mimetype}`
    );

    // If expense already has a document image, delete the old one
    if (expense.document_image) {
      const oldFilePath = path.join(
        __dirname,
        "../../uploads",
        expense.document_image.replace(/^\/uploads\//, "")
      );
      if (fs.existsSync(oldFilePath)) {
        await deleteFile(oldFilePath);
        logger.info(`Deleted old document image: ${oldFilePath}`);
      } else {
        logger.warn(`Old document image file not found: ${oldFilePath}`);
      }
    }
    // For backward compatibility, also check invoice_image
    else if (expense.invoice_image) {
      const oldFilePath = path.join(
        __dirname,
        "../../uploads",
        expense.invoice_image.replace(/^\/uploads\//, "")
      );
      if (fs.existsSync(oldFilePath)) {
        await deleteFile(oldFilePath);
        logger.info(`Deleted old invoice image: ${oldFilePath}`);
      } else {
        logger.warn(`Old invoice image file not found: ${oldFilePath}`);
      }
    }

    // Update expense with new image path
    expense.document_image = `/uploads/documents/${req.file.filename}`;
    await expense.save();

    logger.info(
      `Document image updated for expense ${expense.id}: ${expense.document_image}`
    );

    // Get expense with relations for email notification
    const expenseWithRelations = await Expense.findByPk(expense.id, {
      include: [
        { model: User, as: "user" },
        { model: Branch, as: "branch" },
        { model: ExpenseCategory, as: "category" },
      ],
    });

    // Send email notification for document upload
    if (expenseWithRelations) {
      try {
        await sendExpenseNotification(expenseWithRelations, {
          isDocumentUpload: true,
          documentPath: expense.document_image,
          customSubject: `Document Uploaded for Expense: ${new Intl.NumberFormat(
            "en-KE",
            {
              style: "currency",
              currency: "KES",
            }
          ).format(expense.amount)}`,
        });
        logger.info(
          `Document upload notification sent for expense ${expense.id}`
        );
      } catch (emailError) {
        logger.error(
          `Error sending document upload notification: ${emailError.message}`
        );
        // Don't fail the request if email fails
      }
    }

    return res.status(200).json({
      status: "success",
      message: "Document image uploaded successfully",
      data: {
        id: expense.id,
        document_image: expense.document_image,
        invoice_image: expense.document_image, // For backward compatibility
      },
    });
  } catch (error) {
    logger.error(`Error uploading document image: ${error.message}`);
    logger.error(`Error stack: ${error.stack}`);
    next(error);
  }
};

// Get expense summary statistics
exports.getExpenseSummary = async (req, res, next) => {
  try {
    const { start_date, end_date, branch_id, pos_session_id } = req.query;

    // Build where clause
    const whereClause = {};

    // Filter by date range if provided
    if (start_date && end_date) {
      whereClause.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)],
      };
    } else if (start_date) {
      whereClause.created_at = {
        [Op.gte]: new Date(start_date),
      };
    } else if (end_date) {
      whereClause.created_at = {
        [Op.lte]: new Date(end_date),
      };
    }

    // Filter by branch if provided
    if (branch_id) {
      whereClause.branch_id = branch_id;
    }

    // Filter by POS session if provided
    if (pos_session_id) {
      whereClause.pos_session_id = pos_session_id;
    }

    // Branch-level roles can only see expenses from their branch
    if (
      req.user.role_name === "branch_admin" ||
      req.user.role_name === "operations_manager" ||
      req.user.role_name === "assistant_operations_manager"
    ) {
      whereClause.branch_id = req.user.branch_id;
    }

    // Get summary statistics
    const summary = {
      total_requested:
        (await Expense.sum("amount", { where: whereClause })) || 0,
      total_approved:
        (await Expense.sum("final_approved_amount", {
          where: {
            ...whereClause,
            status: ["approved", "partially_approved"],
          },
        })) || 0,
      count_pending: await Expense.count({
        where: {
          ...whereClause,
          status: "pending",
        },
      }),
      count_pending_first_approval: await Expense.count({
        where: {
          ...whereClause,
          status: "pending_first_approval",
        },
      }),
      count_first_approved: await Expense.count({
        where: {
          ...whereClause,
          status: "first_approved",
        },
      }),
      count_first_declined: await Expense.count({
        where: {
          ...whereClause,
          status: "first_declined",
        },
      }),
      count_pending_final_approval: await Expense.count({
        where: {
          ...whereClause,
          status: "pending_final_approval",
        },
      }),
      count_approved: await Expense.count({
        where: {
          ...whereClause,
          status: "approved",
        },
      }),
      count_partially_approved: await Expense.count({
        where: {
          ...whereClause,
          status: "partially_approved",
        },
      }),
      count_declined: await Expense.count({
        where: {
          ...whereClause,
          status: "declined",
        },
      }),
      total_count: await Expense.count({ where: whereClause }),
    };

    return res.status(200).json({
      status: "success",
      data: summary,
    });
  } catch (error) {
    logger.error(`Error fetching expense summary: ${error.message}`);
    next(error);
  }
};
