// Expense Category Group interface
export interface ExpenseCategoryGroup {
  id: number;
  name: string;
  description: string | null;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
  categories?: ExpenseCategory[];
}

// Expense Category interface
export interface ExpenseCategory {
  id: number;
  name: string;
  description: string | null;
  is_active: boolean;
  is_shop_allowed?: boolean; // Added for shop allowed categorization
  group_id?: number | null;
  created_at?: string;
  updated_at?: string;
  group?: ExpenseCategoryGroup;
}

// Create Expense Category Request
export interface CreateExpenseCategoryRequest {
  name: string;
  description?: string;
  is_active?: boolean;
  is_shop_allowed?: boolean; // Added for shop allowed categorization
  group_id?: number | null;
}

// Update Expense Category Request
export interface UpdateExpenseCategoryRequest {
  name?: string;
  description?: string;
  is_active?: boolean;
  is_shop_allowed?: boolean; // Added for shop allowed categorization
  group_id?: number | null;
}

// Expense interface
export interface Expense {
  id: number;
  user_id: number;
  branch_id: number;
  category_id: number;
  amount: number;
  description: string;
  status: string;
  first_approved_by: number | null;
  second_approved_by: number | null;
  first_approved_at: string | null;
  second_approved_at: string | null;
  approved_amount: number | null;
  approval_notes: string | null;
  created_at: string;
  updated_at: string;
  category?: ExpenseCategory;
  user?: {
    id: number;
    name: string;
    email: string;
  };
  branch?: {
    id: number;
    name: string;
    region_id?: number;
    Region?: {
      id: number;
      name: string;
      code: string;
    };
  };
  first_approver?: {
    id: number;
    name: string;
    email: string;
  };
  second_approver?: {
    id: number;
    name: string;
    email: string;
  };
}
