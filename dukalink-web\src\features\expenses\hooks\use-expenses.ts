import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import * as expensesApi from "@/lib/api/expenses";

/**
 * Hook to fetch expenses with pagination and filtering
 */
export const useExpenses = (params?: {
  page?: number;
  limit?: number;
  status?: string;
  search?: string;
  branch_id?: number;
  region_id?: number;
}) => {
  return useQuery({
    queryKey: ["expenses", params],
    queryFn: () => expensesApi.getExpenses(params || {}),
    placeholderData: "keep" as any,
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook to create a new expense
 */
export const useCreateExpense = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      amount: number;
      description: string;
      category_id: number;
      branch_id: number;
      pos_session_id?: number;
      document_image?: File;
    }) => expensesApi.createExpense(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["expenses"] });
      toast.success("Expense created successfully");
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to create expense");
    },
  });
};

/**
 * Hook to update an expense
 */
export const useUpdateExpense = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      amount?: number;
      description?: string;
      category_id?: number;
    }) => expensesApi.updateExpense(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["expenses", id] });
      queryClient.invalidateQueries({ queryKey: ["expenses"] });
      toast.success("Expense updated successfully");
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to update expense");
    },
  });
};

/**
 * Hook to upload a receipt image for an expense
 */
export const useUploadReceiptImage = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (file: File) => expensesApi.uploadReceiptImage(id, file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["expenses", id] });
      toast.success("Receipt image uploaded successfully");
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to upload receipt image");
    },
  });
};

/**
 * Hook to upload an invoice image for an expense
 */
export const useUploadInvoiceImage = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (file: File) => expensesApi.uploadInvoiceImage(id, file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["expenses", id] });
      toast.success("Invoice image uploaded successfully");
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to upload invoice image");
    },
  });
};
