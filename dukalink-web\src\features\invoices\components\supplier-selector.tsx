"use client";

import React, { useState, useMemo, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSuppliers, Supplier } from "@/features/suppliers/hooks/use-suppliers";

interface SupplierSelectorProps {
  value?: number;
  onValueChange: (value: number | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
}

export function SupplierSelector({
  value,
  onValueChange,
  placeholder = "Select invoice to",
  disabled = false,
}: SupplierSelectorProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch suppliers with search
  const { data: suppliersData, isLoading } = useSuppliers({
    search: debouncedSearchTerm || undefined,
  });
  

  // Debug logging to see the actual data structure
  console.log("Search term:", searchTerm);
  console.log("Debounced search term:", debouncedSearchTerm);
  console.log("Raw suppliersData:", suppliersData);
  console.log("suppliersData type:", typeof suppliersData);
  console.log("suppliersData.data:", (suppliersData as any)?.data);

  // Extract suppliers from the API response
  const suppliers = useMemo(() => {
    // Handle the standard paginated response format: { data: [...], pagination: {...} }
    if (suppliersData && typeof suppliersData === 'object' && 'data' in suppliersData) {
      const extractedSuppliers = Array.isArray(suppliersData.data) ? suppliersData.data : [];
      console.log("Extracted suppliers from .data:", extractedSuppliers);
      return extractedSuppliers as Supplier[];
    }

    // Fallback for direct array (shouldn't happen based on your API response)
    if (Array.isArray(suppliersData)) {
      console.log("Extracted suppliers as direct array:", suppliersData);
      return suppliersData as Supplier[];
    }

    console.log("No suppliers found, returning empty array");
    return [];
  }, [suppliersData]);

  // Find selected supplier
  const selectedSupplier = useMemo(() => {
    return suppliers.find((supplier) => supplier.id === value);
  }, [suppliers, value]);

  const handleSelect = (supplierId: number) => {
    onValueChange(supplierId === value ? undefined : supplierId);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={disabled}
        >
          {selectedSupplier ? (
            <div className="flex flex-col items-start">
              <span className="font-medium">{selectedSupplier.name}</span>
              {selectedSupplier.email && (
                <span className="text-xs text-muted-foreground">
                  {selectedSupplier.email}
                </span>
              )}
            </div>
          ) : (
            placeholder
          )}
          {isLoading ? (
            <Loader2 className="ml-2 h-4 w-4 animate-spin" />
          ) : (
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0">
        <Command key={`suppliers-${suppliers.length}-${debouncedSearchTerm}`}>
          <CommandInput
            placeholder="Search invoice to..."
            value={searchTerm}
            onValueChange={setSearchTerm}
          />
          <CommandList>
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span>Loading recipients...</span>
              </div>
            ) : (
              <>
                <CommandEmpty>
                  {searchTerm ? (
                    <div className="text-center p-4">
                      <p>No invoice recipients found matching &ldquo;{searchTerm}&rdquo;</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        Try a different search term or add a new recipient
                      </p>
                    </div>
                  ) : (
                    <div className="text-center p-4">
                      <p>No invoice recipients found</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        Add a new recipient to get started
                      </p>
                    </div>
                  )}
                </CommandEmpty>
                <CommandGroup>
                  {suppliers.map((supplier) => (
                    <CommandItem
                      key={supplier.id}
                      value={supplier.id.toString()}
                      onSelect={() => handleSelect(supplier.id)}
                      className="flex items-center justify-between p-3"
                    >
                      <div className="flex items-center">
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            value === supplier.id ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <div className="flex flex-col">
                          <span className="font-medium">{supplier.name}</span>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            {supplier.phone && (
                              <span>📞 {supplier.phone}</span>
                            )}
                            {supplier.krapin ? (
                              <span className="text-green-600">
                                PIN: {supplier.krapin}
                              </span>
                            ) : (
                              <span className="text-orange-600">⚠️ No PIN</span>
                            )}
                          </div>
                          {supplier.email && (
                            <span className="text-xs text-muted-foreground">
                              {supplier.email}
                            </span>
                          )}
                        </div>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
