/**
 * Expense Approval Controller
 * Handles the simplified two-level approval process for expenses:
 * 1. First level approval by accountant, operations manager, or assistant operations manager
 * 2. Final approval by company_admin
 */
const Expense = require("../models/expense.model");
const User = require("../models/user.model");
const Branch = require("../models/branch.model");
const ExpenseCategory = require("../models/expense-category.model");
const PosSession = require("../models/pos-session.model");
const AppError = require("../utils/error");
const logger = require("../utils/logger");
const rbacService = require("../services/rbac.service");
const {
  sendExpenseApprovalNotification,
} = require("../services/resend-email.service");

/**
 * First level approval by accountant, operations manager, or assistant operations manager
 */
exports.firstLevelApprove = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { approved_amount, approval_notes } = req.body;

    // Permission check is now handled by RBAC middleware
    // No need to check role_name here

    const expense = await Expense.findByPk(id, {
      include: [
        {
          model: ExpenseCategory,
          as: "category",
          attributes: ["id", "name", "is_shop_allowed"],
        },
      ],
    });

    if (!expense) {
      return next(new AppError("Expense not found", 404));
    }

    // Check if expense is already processed
    if (
      expense.status !== "pending" &&
      expense.status !== "pending_first_approval"
    ) {
      return next(
        new AppError(`This expense is already ${expense.status}`, 400)
      );
    }

    // Document requirement removed - approvers can approve expenses without requiring documents to be uploaded first

    // Check if the expense belongs to the approver's branch or if user has permission to approve expenses from any branch
    if (expense.branch_id !== req.user.branch_id) {
      try {
        // Use RBAC to check if user has createAny permission for expense_first_approval
        // First get the AccessControl instance
        const ac = await rbacService.getAccessControl();

        // Get the user's role (with backward compatibility mapping)
        let mappedRole = req.user.role_name;
        if (mappedRole === "branch_admin") mappedRole = "branch_manager";
        if (mappedRole === "admin") mappedRole = "super_admin";

        // Check permission using AccessControl
        const hasPermission = ac
          .can(mappedRole)
          .createAny("expense_first_approval");

        if (!hasPermission.granted) {
          return next(
            new AppError("You can only approve expenses from your branch", 403)
          );
        }

        // Log that user is approving an expense from another branch
        logger.info(
          `User ${req.user.id} (${req.user.role_name}) is approving expense ${expense.id} from branch ${expense.branch_id} using createAny permission`
        );
      } catch (error) {
        logger.error(`Error checking RBAC permission: ${error.message}`);
        return next(new AppError("Error checking permissions", 500));
      }
    }

    // Validate approved amount
    if (!approved_amount || approved_amount <= 0) {
      return next(
        new AppError("Approved amount must be greater than zero", 400)
      );
    }

    // Update expense with first level approval
    await expense.update({
      status: "pending_final_approval",
      first_approved_amount: parseFloat(approved_amount),
      first_approved_by: req.user.id,
      first_approval_date: new Date(),
      first_approval_notes: approval_notes || null,
    });

    // Get updated expense with associations
    const updatedExpense = await Expense.findByPk(id, {
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: User,
          as: "first_approver",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: User,
          as: "final_approver",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "name", "location"],
        },
        {
          model: ExpenseCategory,
          as: "category",
          attributes: ["id", "name", "description"],
        },
        {
          model: PosSession,
          as: "pos_session",
          attributes: ["id", "start_time", "end_time", "status"],
        },
      ],
    });

    // Send email notification to expense creator
    sendExpenseApprovalNotification(updatedExpense, {
      isApproved: true,
      isFinalApproval: false,
      approverName: req.user.name,
      approvalNotes: approval_notes,
      approvedAmount: parseFloat(approved_amount),
    })
      .then(() => {
        logger.info(
          `Approval notification email sent to expense creator for expense ${id}`
        );
      })
      .catch((err) => {
        logger.error(
          `Error sending approval notification email: ${err.message}`
        );
      });

    return res.status(200).json({
      status: "success",
      message: "Expense approved at first level and sent for final approval",
      data: updatedExpense,
    });
  } catch (error) {
    logger.error(`Error in first level approval: ${error.message}`);
    next(error);
  }
};

/**
 * First level decline by accountant, operations manager, or assistant operations manager
 */
exports.firstLevelDecline = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { approval_notes } = req.body;

    // Permission check is now handled by RBAC middleware
    // No need to check role_name here

    const expense = await Expense.findByPk(id);

    if (!expense) {
      return next(new AppError("Expense not found", 404));
    }

    // Check if expense is already processed
    if (
      expense.status !== "pending" &&
      expense.status !== "pending_first_approval"
    ) {
      return next(
        new AppError(`This expense is already ${expense.status}`, 400)
      );
    }

    // Check if the expense belongs to the approver's branch or if user has permission to decline expenses from any branch
    if (expense.branch_id !== req.user.branch_id) {
      try {
        // Use RBAC to check if user has createAny permission for expense_first_approval
        // First get the AccessControl instance
        const ac = await rbacService.getAccessControl();

        // Get the user's role (with backward compatibility mapping)
        let mappedRole = req.user.role_name;
        if (mappedRole === "branch_admin") mappedRole = "branch_manager";
        if (mappedRole === "admin") mappedRole = "super_admin";

        // Check permission using AccessControl
        const hasPermission = ac
          .can(mappedRole)
          .createAny("expense_first_approval");

        if (!hasPermission.granted) {
          return next(
            new AppError("You can only decline expenses from your branch", 403)
          );
        }

        // Log that user is declining an expense from another branch
        logger.info(
          `User ${req.user.id} (${req.user.role_name}) is declining expense ${expense.id} from branch ${expense.branch_id} using createAny permission`
        );
      } catch (error) {
        logger.error(`Error checking RBAC permission: ${error.message}`);
        return next(new AppError("Error checking permissions", 500));
      }
    }

    // Update expense with first level decline
    await expense.update({
      status: "first_declined",
      first_approved_amount: 0,
      first_approved_by: req.user.id,
      first_approval_date: new Date(),
      first_approval_notes: approval_notes || null,
    });

    // Get updated expense with associations
    const updatedExpense = await Expense.findByPk(id, {
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: User,
          as: "first_approver",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "name", "location"],
        },
        {
          model: ExpenseCategory,
          as: "category",
          attributes: ["id", "name", "description"],
        },
        {
          model: PosSession,
          as: "pos_session",
          attributes: ["id", "start_time", "end_time", "status"],
        },
      ],
    });

    // Send email notification to expense creator
    sendExpenseApprovalNotification(updatedExpense, {
      isApproved: false,
      isFinalApproval: false,
      approverName: req.user.name,
      approvalNotes: approval_notes,
    })
      .then(() => {
        logger.info(
          `Decline notification email sent to expense creator for expense ${id}`
        );
      })
      .catch((err) => {
        logger.error(
          `Error sending decline notification email: ${err.message}`
        );
      });

    return res.status(200).json({
      status: "success",
      message: "Expense declined at first level",
      data: updatedExpense,
    });
  } catch (error) {
    logger.error(`Error in first level decline: ${error.message}`);
    next(error);
  }
};

/**
 * Final approval by Company Admin
 */
exports.finalApprove = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { approved_amount, approval_notes } = req.body;

    // Permission check is now handled by RBAC middleware
    // No need to check role_name here

    const expense = await Expense.findByPk(id, {
      include: [
        {
          model: ExpenseCategory,
          as: "category",
          attributes: ["id", "name", "is_shop_allowed"],
        },
      ],
    });

    if (!expense) {
      return next(new AppError("Expense not found", 404));
    }

    // For company admins, allow direct approval from any status except terminal statuses
    if (req.user.role_name === "company_admin") {
      // Check if expense is in a terminal status
      if (
        [
          "approved",
          "partially_approved",
          "first_declined",
          "final_declined",
        ].includes(expense.status)
      ) {
        return next(
          new AppError(
            `This expense is already in a terminal status: ${expense.status}`,
            400
          )
        );
      }
    } else {
      // For non-company admins, check if expense has first level approval
      if (expense.status !== "pending_final_approval") {
        return next(
          new AppError(
            `This expense must be approved at the first level before final approval (current status: ${expense.status})`,
            400
          )
        );
      }
    }

    // Check if user is a company_admin
    if (req.user.role_name !== "company_admin") {
      return next(
        new AppError(
          "Only company administrators can give final approval for expenses",
          403
        )
      );
    }

    // Document requirement removed for final approval - company admins can approve expenses without requiring documents to be uploaded first

    // Validate approved amount
    if (!approved_amount || approved_amount <= 0) {
      return next(
        new AppError("Approved amount must be greater than zero", 400)
      );
    }

    // Determine if this is a full or partial approval
    const status =
      parseFloat(approved_amount) < parseFloat(expense.amount)
        ? "partially_approved"
        : "approved";

    // Update expense with final approval
    await expense.update({
      status,
      final_approved_amount: parseFloat(approved_amount),
      final_approved_by: req.user.id,
      final_approval_date: new Date(),
      final_approval_notes: approval_notes || null,
    });

    // Get updated expense with associations
    const updatedExpense = await Expense.findByPk(id, {
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: User,
          as: "first_approver",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: User,
          as: "final_approver",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "name", "location"],
        },
        {
          model: ExpenseCategory,
          as: "category",
          attributes: ["id", "name", "description"],
        },
        {
          model: PosSession,
          as: "pos_session",
          attributes: ["id", "start_time", "end_time", "status"],
        },
      ],
    });

    // Send email notification to expense creator
    sendExpenseApprovalNotification(updatedExpense, {
      isApproved: true,
      isFinalApproval: true,
      approverName: req.user.name,
      approvalNotes: approval_notes,
      approvedAmount: parseFloat(approved_amount),
    })
      .then(() => {
        logger.info(
          `Final approval notification email sent to expense creator for expense ${id}`
        );
      })
      .catch((err) => {
        logger.error(
          `Error sending final approval notification email: ${err.message}`
        );
      });

    return res.status(200).json({
      status: "success",
      message: "Expense has received final approval",
      data: updatedExpense,
    });
  } catch (error) {
    logger.error(`Error in final approval: ${error.message}`);
    next(error);
  }
};

/**
 * Final decline by Company Admin
 */
exports.finalDecline = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { approval_notes } = req.body;

    // Permission check is now handled by RBAC middleware
    // No need to check role_name here

    const expense = await Expense.findByPk(id);

    if (!expense) {
      return next(new AppError("Expense not found", 404));
    }

    // For company admins, allow direct decline from any status except terminal statuses
    if (req.user.role_name === "company_admin") {
      // Check if expense is in a terminal status
      if (
        [
          "approved",
          "partially_approved",
          "first_declined",
          "final_declined",
        ].includes(expense.status)
      ) {
        return next(
          new AppError(
            `This expense is already in a terminal status: ${expense.status}`,
            400
          )
        );
      }
    } else {
      // For non-company admins, check if expense has first level approval
      if (expense.status !== "pending_final_approval") {
        return next(
          new AppError(
            `This expense must be approved at the first level before it can be declined at the final level (current status: ${expense.status})`,
            400
          )
        );
      }
    }

    // Check if user is a company_admin
    if (req.user.role_name !== "company_admin") {
      return next(
        new AppError(
          "Only company administrators can decline expenses at the final level",
          403
        )
      );
    }

    // Update expense with final decline
    await expense.update({
      status: "final_declined",
      final_approved_amount: 0,
      final_approved_by: req.user.id,
      final_approval_date: new Date(),
      final_approval_notes: approval_notes || null,
    });

    // Get updated expense with associations
    const updatedExpense = await Expense.findByPk(id, {
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: User,
          as: "first_approver",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: User,
          as: "final_approver",
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "name", "location"],
        },
        {
          model: ExpenseCategory,
          as: "category",
          attributes: ["id", "name", "description"],
        },
        {
          model: PosSession,
          as: "pos_session",
          attributes: ["id", "start_time", "end_time", "status"],
        },
      ],
    });

    // Send email notification to expense creator
    sendExpenseApprovalNotification(updatedExpense, {
      isApproved: false,
      isFinalApproval: true,
      approverName: req.user.name,
      approvalNotes: approval_notes,
    })
      .then(() => {
        logger.info(
          `Final decline notification email sent to expense creator for expense ${id}`
        );
      })
      .catch((err) => {
        logger.error(
          `Error sending final decline notification email: ${err.message}`
        );
      });

    return res.status(200).json({
      status: "success",
      message: "Expense has been declined at the final level",
      data: updatedExpense,
    });
  } catch (error) {
    logger.error(`Error in final decline: ${error.message}`);
    next(error);
  }
};
