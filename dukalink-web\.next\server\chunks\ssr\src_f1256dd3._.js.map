{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/hooks/use-permissions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useCurrentUser } from \"./use-auth\";\r\nimport { usePermissionContext } from \"../context/permission-context\";\r\n\r\n// Define constants for resources and actions\r\nexport const RESOURCES = {\r\n  USERS: \"users\",\r\n  ROLES: \"roles\",\r\n  PERMISSIONS: \"permissions\",\r\n  PRODUCTS: \"products\",\r\n  CATEGORIES: \"categories\",\r\n  BRANDS: \"brands\",\r\n  INVENTORY: \"inventory\",\r\n  STOCK_ITEMS: \"stock_items\",\r\n  BRANCHES: \"branches\",\r\n  EMPLOYEES: \"employees\",\r\n  BANKING: \"banking\",\r\n  EXPENSES: \"expenses\",\r\n  EXPENSE_FIRST_APPROVAL: \"expense_first_approval\",\r\n  EXPENSE_SECOND_APPROVAL: \"expense_second_approval\",\r\n  SALES: \"sales\",\r\n  POS_SESSIONS: \"pos_sessions\",\r\n  CUSTOMERS: \"customers\",\r\n  PROFILE: \"profile\",\r\n  STOCK_LOCATIONS: \"stock_locations\",\r\n};\r\n\r\nexport const ACTIONS = {\r\n  CREATE: \"create\",\r\n  READ: \"read\",\r\n  UPDATE: \"update\",\r\n  DELETE: \"delete\",\r\n};\r\n\r\nexport const SCOPE = {\r\n  ANY: \"any\",\r\n  OWN: \"own\",\r\n};\r\n\r\nexport function usePermissions() {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const {\r\n    permissions,\r\n    isLoading: isPermissionsLoading,\r\n    hasPermission,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n  } = usePermissionContext();\r\n\r\n  // Check if the user is a company admin\r\n  const isCompanyAdmin = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the company_admin role\r\n    return (\r\n      user.role_name === \"company_admin\" ||\r\n      user.role_name === \"tenant_admin\" ||\r\n      user.role_name === \"super_admin\"\r\n    );\r\n  };\r\n\r\n  // Check if the user is a branch manager\r\n  const isBranchManager = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the branch_manager role\r\n    return user.role_name === \"branch_manager\";\r\n  };\r\n\r\n  // Check if the user can manage stock locations (create, update, delete)\r\n  const canManageStockLocations = (): boolean => {\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.CREATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.UPDATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.DELETE, SCOPE.ANY) ||\r\n      isCompanyAdmin()\r\n    );\r\n  };\r\n\r\n  // Check if the user can view stock locations\r\n  const canViewStockLocations = (): boolean => {\r\n    // Check for explicit permission or fallback to any authenticated user\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.READ, SCOPE.ANY) ||\r\n      !!user\r\n    );\r\n  };\r\n\r\n  // Check if the user can perform an action on a resource\r\n  const can = (\r\n    resource: string,\r\n    action: string,\r\n    scope: \"any\" | \"own\" = \"any\"\r\n  ): boolean => {\r\n    return hasPermission(resource, action, scope);\r\n  };\r\n\r\n  // Check if the user has any of the specified permissions\r\n  const hasAnyPermission = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.some(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  // Check if the user has all of the specified permissions\r\n  const hasAllPermissions = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.every(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  return {\r\n    isCompanyAdmin,\r\n    isBranchManager,\r\n    canManageStockLocations,\r\n    canViewStockLocations,\r\n    can,\r\n    hasPermission,\r\n    hasAnyPermission,\r\n    hasAllPermissions,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n    permissions, // Expose the raw permissions for debugging\r\n    isLoading: isUserLoading || isPermissionsLoading,\r\n    RESOURCES,\r\n    ACTIONS,\r\n    SCOPE,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;AAMO,MAAM,YAAY;IACvB,OAAO;IACP,OAAO;IACP,aAAa;IACb,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,WAAW;IACX,aAAa;IACb,UAAU;IACV,WAAW;IACX,SAAS;IACT,UAAU;IACV,wBAAwB;IACxB,yBAAyB;IACzB,OAAO;IACP,cAAc;IACd,WAAW;IACX,SAAS;IACT,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEO,MAAM,QAAQ;IACnB,KAAK;IACL,KAAK;AACP;AAEO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EACJ,WAAW,EACX,WAAW,oBAAoB,EAC/B,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,CAAA,GAAA,4JAAA,CAAA,uBAAoB,AAAD;IAEvB,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,+CAA+C;QAC/C,OACE,KAAK,SAAS,KAAK,mBACnB,KAAK,SAAS,KAAK,kBACnB,KAAK,SAAS,KAAK;IAEvB;IAEA,wCAAwC;IACxC,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAElB,gDAAgD;QAChD,OAAO,KAAK,SAAS,KAAK;IAC5B;IAEA,wEAAwE;IACxE,MAAM,0BAA0B;QAC9B,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE;IAEJ;IAEA,6CAA6C;IAC7C,MAAM,wBAAwB;QAC5B,sEAAsE;QACtE,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,KAChE,CAAC,CAAC;IAEN;IAEA,wDAAwD;IACxD,MAAM,MAAM,CACV,UACA,QACA,QAAuB,KAAK;QAE5B,OAAO,cAAc,UAAU,QAAQ;IACzC;IAEA,yDAAyD;IACzD,MAAM,mBAAmB,CACvB;QAMA,OAAO,YAAY,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC1D,cAAc,UAAU,QAAQ;IAEpC;IAEA,yDAAyD;IACzD,MAAM,oBAAoB,CACxB;QAMA,OAAO,YAAY,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC3D,cAAc,UAAU,QAAQ;IAEpC;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,iBAAiB;QAC5B;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/route-permissions.ts"], "sourcesContent": ["/**\r\n * Route Permission Mapping\r\n *\r\n * This file maps routes to the permissions required to access them.\r\n * Each key represents a route pattern, and the value is the permission required.\r\n */\r\n\r\nimport { NavigationPermission } from \"./navigation-permissions\";\r\n\r\nexport const routePermissions: Record<string, NavigationPermission> = {\r\n  // Dashboard routes\r\n  \"/dashboard\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/company\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/branch\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/finance\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/operations\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/stock\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/float\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/pos\": { resource: \"dashboard\", action: \"view\" },\r\n\r\n  // User management routes\r\n  \"/users\": { resource: \"users\", action: \"view\" },\r\n  \"/users/create\": { resource: \"users\", action: \"create\" },\r\n  \"/users/[id]\": { resource: \"users\", action: \"view\" },\r\n  \"/users/[id]/edit\": { resource: \"users\", action: \"update\" },\r\n\r\n  // Role management routes\r\n  \"/roles\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/create\": { resource: \"roles\", action: \"create\" },\r\n  \"/roles/[id]\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/[id]/edit\": { resource: \"roles\", action: \"update\" },\r\n\r\n  // RBAC routes\r\n  \"/rbac\": { resource: \"permissions\", action: \"manage\" },\r\n\r\n  // Branch management routes\r\n  \"/branches\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/create\": { resource: \"branches\", action: \"create\" },\r\n  \"/branches/[id]\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/[id]/edit\": { resource: \"branches\", action: \"update\" },\r\n\r\n  // Location management routes\r\n  \"/locations\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/locations/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n  \"/location-guides\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/location-guides/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n\r\n  // Employee management routes\r\n  \"/employees\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/create\": { resource: \"employees\", action: \"create\" },\r\n  \"/employees/[id]\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/[id]/edit\": { resource: \"employees\", action: \"update\" },\r\n\r\n  // Product management routes\r\n  \"/products\": { resource: \"products\", action: \"view\" },\r\n  \"/products/create\": { resource: \"products\", action: \"create\" },\r\n  \"/products/[id]\": { resource: \"products\", action: \"view\" },\r\n  \"/products/[id]/edit\": { resource: \"products\", action: \"update\" },\r\n\r\n  // Category management routes\r\n  \"/categories\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/create\": { resource: \"categories\", action: \"create\" },\r\n  \"/categories/[id]\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/[id]/edit\": { resource: \"categories\", action: \"update\" },\r\n\r\n  // Brand management routes\r\n  \"/brands\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/create\": { resource: \"brands\", action: \"create\" },\r\n  \"/brands/[id]\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/[id]/edit\": { resource: \"brands\", action: \"update\" },\r\n\r\n  // Inventory management routes\r\n  \"/inventory\": { resource: \"inventory\", action: \"view\" },\r\n  \"/inventory/transfers\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/bulk-transfer\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/reports\": { resource: \"inventory\", action: \"report\" },\r\n  \"/inventory/stock-cards\": { resource: \"inventory\", action: \"view\" },\r\n\r\n  // Banking routes\r\n  \"/banking\": { resource: \"banking\", action: \"view\" },\r\n  \"/banking/summary\": { resource: \"banking\", action: \"view\" },\r\n\r\n  // MPESA routes\r\n  \"/mpesa\": { resource: \"mpesa\", action: \"view\" },\r\n  \"/mpesa/transactions\": { resource: \"mpesa\", action: \"view\" },\r\n\r\n  // Float management routes\r\n  \"/float\": { resource: \"float\", action: \"view\" },\r\n  \"/float/movements\": { resource: \"float\", action: \"view\" },\r\n  \"/float/reconciliations\": { resource: \"float\", action: \"view\" },\r\n  \"/float/topups\": { resource: \"float\", action: \"view\" },\r\n\r\n  // DSA routes\r\n  \"/dsa\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/agents\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/assignments\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/reconciliations\": { resource: \"dsa\", action: \"view\" },\r\n\r\n  // Phone repairs routes\r\n  \"/phone-repairs\": { resource: \"phone_repairs\", action: \"view\" },\r\n  \"/phone-repairs/[id]\": { resource: \"phone_repairs\", action: \"view\" },\r\n\r\n  // Expense management routes\r\n  \"/expenses\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/create\": { resource: \"expenses\", action: \"create\" },\r\n  \"/expenses/[id]\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/[id]/edit\": { resource: \"expenses\", action: \"update\" },\r\n  \"/expense-analytics\": { resource: \"expenses\", action: \"view\" },\r\n\r\n  // Credit Note routes\r\n  \"/credit-notes\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/new\": { resource: \"invoices\", action: \"create\" },\r\n  \"/credit-notes/[id]\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/[id]/edit\": { resource: \"invoices\", action: \"update\" },\r\n\r\n  // Report routes\r\n  \"/reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-summary\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-item\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-category\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-employee\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-payment-type\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-banking\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/cash-status\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/running-balances\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-transactions\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/phone-repairs\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/expense-reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/dsa-sales\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/receipts\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/shifts\": { resource: \"reports\", action: \"view\" },\r\n\r\n  // Settings routes\r\n  \"/settings\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/system\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/company\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/payment-methods\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/health\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // Profile route\r\n  \"/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // POS routes\r\n  \"/pos\": { resource: \"pos\", action: \"view\" },\r\n  \"/pos/sessions\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]/edit\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/close\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/shift-closing\": {\r\n    resource: \"pos_sessions\",\r\n    action: \"view\",\r\n  },\r\n\r\n  // Sales routes\r\n  \"/sales\": { resource: \"sales\", action: \"view\" },\r\n  \"/sales/[id]\": { resource: \"sales\", action: \"view\" },\r\n\r\n  // Customer routes\r\n  \"/customers\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/create\": { resource: \"customers\", action: \"create\" },\r\n  \"/customers/[id]\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/[id]/edit\": { resource: \"customers\", action: \"update\" },\r\n\r\n  // Procurement routes\r\n  \"/procurement\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests/new\": { resource: \"procurement\", action: \"create\" },\r\n  \"/procurement/receipts\": { resource: \"procurement\", action: \"view\" },\r\n\r\n  // Supplier routes\r\n  \"/suppliers\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/create\": { resource: \"suppliers\", action: \"create\" },\r\n  \"/suppliers/[id]\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/[id]/edit\": { resource: \"suppliers\", action: \"update\" },\r\n\r\n  // Tenant routes (Super Admin only)\r\n  \"/tenants\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/create\": { resource: \"tenants\", action: \"create\" },\r\n  \"/tenants/[id]\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/[id]/edit\": { resource: \"tenants\", action: \"update\" },\r\n\r\n  // Super Admin routes\r\n  \"/super-admin\": { resource: \"tenants\", action: \"view\" },\r\n\r\n  // Debug routes\r\n  \"/permission-debug\": { resource: \"settings\", action: \"manage\" },\r\n};\r\n\r\n/**\r\n * Get the permission required for a route\r\n * @param route The route to check\r\n * @returns The permission required for the route, or null if no permission is required\r\n */\r\nexport function getRoutePermission(route: string): NavigationPermission | null {\r\n  // First try exact match\r\n  if (routePermissions[route]) {\r\n    return routePermissions[route];\r\n  }\r\n\r\n  // Then try dynamic routes\r\n  for (const [pattern, permission] of Object.entries(routePermissions)) {\r\n    if (pattern.includes(\"[\") && matchDynamicRoute(route, pattern)) {\r\n      return permission;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Check if a route matches a dynamic route pattern\r\n * @param route The route to check\r\n * @param pattern The pattern to match against\r\n * @returns Whether the route matches the pattern\r\n */\r\nfunction matchDynamicRoute(route: string, pattern: string): boolean {\r\n  const routeParts = route.split(\"/\");\r\n  const patternParts = pattern.split(\"/\");\r\n\r\n  if (routeParts.length !== patternParts.length) {\r\n    return false;\r\n  }\r\n\r\n  for (let i = 0; i < patternParts.length; i++) {\r\n    if (patternParts[i].startsWith(\"[\") && patternParts[i].endsWith(\"]\")) {\r\n      // This is a dynamic part, so it matches anything\r\n      continue;\r\n    }\r\n\r\n    if (patternParts[i] !== routeParts[i]) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  return true;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAIM,MAAM,mBAAyD;IACpE,mBAAmB;IACnB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC7D,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,kBAAkB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,cAAc;IACd,SAAS;QAAE,UAAU;QAAe,QAAQ;IAAS;IAErD,2BAA2B;IAC3B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAClE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,2BAA2B;QAAE,UAAU;QAAa,QAAQ;IAAS;IACrE,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,8BAA8B;QAAE,UAAU;QAAa,QAAQ;IAAS;IAExE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,eAAe;QAAE,UAAU;QAAc,QAAQ;IAAO;IACxD,sBAAsB;QAAE,UAAU;QAAc,QAAQ;IAAS;IACjE,oBAAoB;QAAE,UAAU;QAAc,QAAQ;IAAO;IAC7D,yBAAyB;QAAE,UAAU;QAAc,QAAQ;IAAS;IAEpE,0BAA0B;IAC1B,WAAW;QAAE,UAAU;QAAU,QAAQ;IAAO;IAChD,kBAAkB;QAAE,UAAU;QAAU,QAAQ;IAAS;IACzD,gBAAgB;QAAE,UAAU;QAAU,QAAQ;IAAO;IACrD,qBAAqB;QAAE,UAAU;QAAU,QAAQ;IAAS;IAE5D,8BAA8B;IAC9B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAW;IACpE,4BAA4B;QAAE,UAAU;QAAa,QAAQ;IAAW;IACxE,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAChE,0BAA0B;QAAE,UAAU;QAAa,QAAQ;IAAO;IAElE,iBAAiB;IACjB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,oBAAoB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE1D,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,uBAAuB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAE3D,0BAA0B;IAC1B,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAO;IACxD,0BAA0B;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9D,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAErD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,eAAe;QAAE,UAAU;QAAO,QAAQ;IAAO;IACjD,oBAAoB;QAAE,UAAU;QAAO,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAO,QAAQ;IAAO;IAE1D,uBAAuB;IACvB,kBAAkB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAC9D,uBAAuB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAEnE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAChE,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAE7D,qBAAqB;IACrB,iBAAiB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACxD,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC7D,2BAA2B;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEpE,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,kCAAkC;QAAE,UAAU;QAAW,QAAQ;IAAO;IACxE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,wBAAwB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC9D,6BAA6B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACnE,+BAA+B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACrE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,4BAA4B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClE,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC5D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC3D,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEzD,kBAAkB;IAClB,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,6BAA6B;QAAE,UAAU;QAAY,QAAQ;IAAS;IACtE,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC3D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE3D,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAElD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,iBAAiB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IAC5D,sBAAsB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IACjE,2BAA2B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACxE,4BAA4B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACzE,oCAAoC;QAClC,UAAU;QACV,QAAQ;IACV;IAEA,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IAEnD,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAC1D,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IACnE,6BAA6B;QAAE,UAAU;QAAe,QAAQ;IAAS;IACzE,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAEnE,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,mCAAmC;IACnC,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAC3D,iBAAiB;QAAE,UAAU;QAAW,QAAQ;IAAO;IACvD,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAE9D,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEtD,eAAe;IACf,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;AAChE;AAOO,SAAS,mBAAmB,KAAa;IAC9C,wBAAwB;IACxB,IAAI,gBAAgB,CAAC,MAAM,EAAE;QAC3B,OAAO,gBAAgB,CAAC,MAAM;IAChC;IAEA,0BAA0B;IAC1B,KAAK,MAAM,CAAC,SAAS,WAAW,IAAI,OAAO,OAAO,CAAC,kBAAmB;QACpE,IAAI,QAAQ,QAAQ,CAAC,QAAQ,kBAAkB,OAAO,UAAU;YAC9D,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,kBAAkB,KAAa,EAAE,OAAe;IACvD,MAAM,aAAa,MAAM,KAAK,CAAC;IAC/B,MAAM,eAAe,QAAQ,KAAK,CAAC;IAEnC,IAAI,WAAW,MAAM,KAAK,aAAa,MAAM,EAAE;QAC7C,OAAO;IACT;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,IAAI,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM;YAEpE;QACF;QAEA,IAAI,YAAY,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE;YACrC,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/role-guard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, useEffect, useState } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\nimport { getRoutePermission } from \"@/lib/route-permissions\";\r\nimport { hasRouteAccess } from \"@/lib/role-utils\"; // Keep for backward compatibility\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\nfunction mapActionToGrantAction(action: string): string {\r\n  switch (action) {\r\n    case \"view\":\r\n      return \"read:any\";\r\n    case \"create\":\r\n      return \"create:any\";\r\n    case \"update\":\r\n      return \"update:any\";\r\n    case \"delete\":\r\n      return \"delete:any\";\r\n    case \"manage\":\r\n      return \"update:any\"; // Manage maps to update:any\r\n    case \"transfer\":\r\n      return \"update:any\"; // Transfer maps to update:any\r\n    case \"report\":\r\n      return \"read:any\"; // Report maps to read:any\r\n    default:\r\n      return `${action}:any`;\r\n  }\r\n}\r\n\r\ninterface RoleGuardProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function RoleGuard({ children }: RoleGuardProps) {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const { hasPermission, isLoading: isPermissionsLoading } = usePermissions();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Track if this is the initial mount\r\n  const [isInitialMount, setIsInitialMount] = useState(true);\r\n\r\n  // Effect to mark initial mount as complete\r\n  useEffect(() => {\r\n    setIsInitialMount(false);\r\n  }, []);\r\n\r\n  // Effect to check access rights\r\n  useEffect(() => {\r\n    // Skip check if on auth pages\r\n    if (\r\n      pathname.startsWith(\"/login\") ||\r\n      pathname.startsWith(\"/forgot-password\") ||\r\n      pathname.startsWith(\"/reset-password\")\r\n    ) {\r\n      setLoading(\"role\", false);\r\n      return;\r\n    }\r\n\r\n    // Only show loading indicator on initial mount or when user data is loading\r\n    // and we haven't shown the loading screen before in this session\r\n    if (isInitialMount && isUserLoading && !hasShownLoading.role) {\r\n      setLoading(\"role\", true);\r\n      return;\r\n    } else {\r\n      setLoading(\"role\", false);\r\n    }\r\n\r\n    // If user data and permissions are loaded, check access rights\r\n    if (!isUserLoading && !isPermissionsLoading && user) {\r\n      // Safely access role_name with type checking\r\n      const roleName =\r\n        user && typeof user === \"object\" && \"role_name\" in user\r\n          ? user.role_name\r\n          : \"\";\r\n\r\n      // First try permission-based access control\r\n      const routePermission = getRoutePermission(pathname);\r\n\r\n      let hasAccess = true;\r\n      let accessMethod = \"default\";\r\n\r\n      // For MVP, prioritize role-based access control\r\n      hasAccess = hasRouteAccess(pathname, roleName);\r\n      accessMethod = \"role\";\r\n\r\n      // Add detailed debug logging\r\n      console.log(`[RoleGuard] Route: ${pathname}`);\r\n      console.log(`[RoleGuard] User role: ${roleName}`);\r\n      console.log(`[RoleGuard] Using role-based access (MVP approach)`);\r\n      console.log(\r\n        `[RoleGuard] Access ${hasAccess ? \"GRANTED\" : \"DENIED\"} by role check`\r\n      );\r\n\r\n      // If we have a permission mapping, check it too (for debugging purposes only)\r\n      if (routePermission) {\r\n        // Map the action to the format used in grants (e.g., \"view\" -> \"read:any\")\r\n        const grantAction = mapActionToGrantAction(routePermission.action);\r\n\r\n        // Check if user has the required permission (but don't use the result)\r\n        const permissionAccess = hasPermission(\r\n          routePermission.resource,\r\n          grantAction,\r\n          routePermission.scope\r\n        );\r\n\r\n        // Log the permission check result for debugging\r\n        console.log(\r\n          `[RoleGuard] Permission check (not used): ${\r\n            routePermission.resource\r\n          }:${routePermission.action}:${routePermission.scope || \"any\"}`\r\n        );\r\n        console.log(`[RoleGuard] Mapped to grant action: ${grantAction}`);\r\n        console.log(\r\n          `[RoleGuard] Permission would be ${\r\n            permissionAccess ? \"GRANTED\" : \"DENIED\"\r\n          } (for future reference)`\r\n        );\r\n      }\r\n\r\n      if (!hasAccess) {\r\n        console.log(\r\n          `[RoleGuard] Access denied for ${pathname}, redirecting to dashboard (method: ${accessMethod})`\r\n        );\r\n\r\n        // Redirect to the appropriate dashboard based on role\r\n        if (roleName === \"accountant\" || roleName === \"finance_manager\") {\r\n          console.log(`[RoleGuard] Redirecting to finance dashboard`);\r\n          router.replace(\"/dashboard\");\r\n          // router.replace(\"/dashboard/finance\");\r\n        } else if (roleName === \"stock_admin\") {\r\n          console.log(`[RoleGuard] Redirecting to stock dashboard`);\r\n          router.replace(\"/dashboard\");\r\n\r\n          // router.replace(\"/dashboard/stock\");\r\n        } else if (\r\n          roleName === \"operations\" ||\r\n          roleName === \"operations_manager\"\r\n        ) {\r\n          console.log(`[RoleGuard] Redirecting to operations dashboard`);\r\n          router.replace(\"/dashboard\");\r\n          // router.replace(\"/dashboard/operations\");\r\n        } else if (roleName === \"float_manager\") {\r\n          console.log(`[RoleGuard] Redirecting to float dashboard`);\r\n          // router.replace(\"/dashboard/float\");\r\n          router.replace(\"/dashboard\");\r\n        } else if (\r\n          roleName === \"pos_operator\" ||\r\n          roleName === \"shop_attendant\"\r\n        ) {\r\n          console.log(`[RoleGuard] Redirecting to POS dashboard`);\r\n          // router.replace(\"/dashboard/pos\");\r\n          router.replace(\"/dashboard\");\r\n        } else if (roleName === \"company_admin\") {\r\n          console.log(`[RoleGuard] Redirecting to company dashboard`);\r\n          // router.replace(\"/dashboard/company\");\r\n          router.replace(\"/dashboard\");\r\n        } else {\r\n          console.log(`[RoleGuard] Redirecting to main dashboard`);\r\n          router.replace(\"/dashboard\");\r\n        }\r\n      }\r\n\r\n      // User data is loaded, we can stop checking and mark as shown\r\n      setLoading(\"role\", false);\r\n      markLoadingShown(\"role\");\r\n    } else if (!isUserLoading && !isPermissionsLoading) {\r\n      // No user data and not loading, stop checking\r\n      setLoading(\"role\", false);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    user,\r\n    isUserLoading,\r\n    isPermissionsLoading,\r\n    pathname,\r\n    router,\r\n    isInitialMount,\r\n    hasShownLoading.role,\r\n    hasPermission,\r\n  ]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in checking state\r\n  useEffect(() => {\r\n    let timeoutId: NodeJS.Timeout | null = null;\r\n\r\n    if (isLoading.role) {\r\n      timeoutId = setTimeout(() => {\r\n        setLoading(\"role\", false);\r\n        markLoadingShown(\"role\");\r\n      }, 1500); // 1.5 second timeout for better UX\r\n    }\r\n\r\n    return () => {\r\n      if (timeoutId) clearTimeout(timeoutId);\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.role]);\r\n\r\n  // Show loading state only when actively checking role access\r\n  if (isLoading.role) {\r\n    return <LoadingScreen message=\"Checking access...\" />;\r\n  }\r\n\r\n  // Don't block rendering, the useEffect will handle redirects\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA,qNAAmD,kCAAkC;AACrF;AARA;;;;;;;;;AAaA;;;;CAIC,GACD,SAAS,uBAAuB,MAAc;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,cAAc,4BAA4B;QACnD,KAAK;YACH,OAAO,cAAc,8BAA8B;QACrD,KAAK;YACH,OAAO,YAAY,0BAA0B;QAC/C;YACE,OAAO,GAAG,OAAO,IAAI,CAAC;IAC1B;AACF;AAMO,SAAS,UAAU,EAAE,QAAQ,EAAkB;IACpD,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EAAE,aAAa,EAAE,WAAW,oBAAoB,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD;IACxE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD;IAEX,qCAAqC;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB;IACpB,GAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8BAA8B;QAC9B,IACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,uBACpB,SAAS,UAAU,CAAC,oBACpB;YACA,WAAW,QAAQ;YACnB;QACF;QAEA,4EAA4E;QAC5E,iEAAiE;QACjE,IAAI,kBAAkB,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;YAC5D,WAAW,QAAQ;YACnB;QACF,OAAO;YACL,WAAW,QAAQ;QACrB;QAEA,+DAA+D;QAC/D,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,MAAM;YACnD,6CAA6C;YAC7C,MAAM,WACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;YAEN,4CAA4C;YAC5C,MAAM,kBAAkB,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;YAE3C,IAAI,YAAY;YAChB,IAAI,eAAe;YAEnB,gDAAgD;YAChD,YAAY,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;YACrC,eAAe;YAEf,6BAA6B;YAC7B,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;YAC5C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU;YAChD,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;YAChE,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,YAAY,YAAY,SAAS,cAAc,CAAC;YAGxE,8EAA8E;YAC9E,IAAI,iBAAiB;gBACnB,2EAA2E;gBAC3E,MAAM,cAAc,uBAAuB,gBAAgB,MAAM;gBAEjE,uEAAuE;gBACvE,MAAM,mBAAmB,cACvB,gBAAgB,QAAQ,EACxB,aACA,gBAAgB,KAAK;gBAGvB,gDAAgD;gBAChD,QAAQ,GAAG,CACT,CAAC,yCAAyC,EACxC,gBAAgB,QAAQ,CACzB,CAAC,EAAE,gBAAgB,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,IAAI,OAAO;gBAEhE,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,aAAa;gBAChE,QAAQ,GAAG,CACT,CAAC,gCAAgC,EAC/B,mBAAmB,YAAY,SAChC,uBAAuB,CAAC;YAE7B;YAEA,IAAI,CAAC,WAAW;gBACd,QAAQ,GAAG,CACT,CAAC,8BAA8B,EAAE,SAAS,oCAAoC,EAAE,aAAa,CAAC,CAAC;gBAGjG,sDAAsD;gBACtD,IAAI,aAAa,gBAAgB,aAAa,mBAAmB;oBAC/D,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;oBAC1D,OAAO,OAAO,CAAC;gBACf,wCAAwC;gBAC1C,OAAO,IAAI,aAAa,eAAe;oBACrC,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC;oBACxD,OAAO,OAAO,CAAC;gBAEf,sCAAsC;gBACxC,OAAO,IACL,aAAa,gBACb,aAAa,sBACb;oBACA,QAAQ,GAAG,CAAC,CAAC,+CAA+C,CAAC;oBAC7D,OAAO,OAAO,CAAC;gBACf,2CAA2C;gBAC7C,OAAO,IAAI,aAAa,iBAAiB;oBACvC,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC;oBACxD,sCAAsC;oBACtC,OAAO,OAAO,CAAC;gBACjB,OAAO,IACL,aAAa,kBACb,aAAa,kBACb;oBACA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,CAAC;oBACtD,oCAAoC;oBACpC,OAAO,OAAO,CAAC;gBACjB,OAAO,IAAI,aAAa,iBAAiB;oBACvC,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;oBAC1D,wCAAwC;oBACxC,OAAO,OAAO,CAAC;gBACjB,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,yCAAyC,CAAC;oBACvD,OAAO,OAAO,CAAC;gBACjB;YACF;YAEA,8DAA8D;YAC9D,WAAW,QAAQ;YACnB,iBAAiB;QACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;YAClD,8CAA8C;YAC9C,WAAW,QAAQ;QACrB;IACA,uDAAuD;IACzD,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB,IAAI;QACpB;KACD;IAED,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAmC;QAEvC,IAAI,UAAU,IAAI,EAAE;YAClB,YAAY,WAAW;gBACrB,WAAW,QAAQ;gBACnB,iBAAiB;YACnB,GAAG,OAAO,mCAAmC;QAC/C;QAEA,OAAO;YACL,IAAI,WAAW,aAAa;QAC9B;IACA,uDAAuD;IACzD,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6DAA6D;IAC7D,IAAI,UAAU,IAAI,EAAE;QAClB,qBAAO,8OAAC,sJAAA,CAAA,gBAAa;YAAC,SAAQ;;;;;;IAChC;IAEA,6DAA6D;IAC7D,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 883, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 909, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 939, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1111, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1136, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,mKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { VariantProps, cva } from \"class-variance-authority\";\r\nimport { PanelLeftIcon } from \"lucide-react\";\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\r\nconst SIDEBAR_WIDTH = \"16rem\";\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\";\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  openMobile: boolean;\r\n  setOpenMobile: (open: boolean) => void;\r\n  isMobile: boolean;\r\n  toggleSidebar: () => void;\r\n};\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext);\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\");\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n}) {\r\n  const isMobile = useIsMobile();\r\n  const [openMobile, setOpenMobile] = React.useState(false);\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen);\r\n  const open = openProp ?? _open;\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value;\r\n      if (setOpenProp) {\r\n        setOpenProp(openState);\r\n      } else {\r\n        _setOpen(openState);\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\r\n    },\r\n    [setOpenProp, open]\r\n  );\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\r\n  }, [isMobile, setOpen, setOpenMobile]);\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault();\r\n        toggleSidebar();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [toggleSidebar]);\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\";\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  );\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  );\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\";\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\";\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\";\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event);\r\n        toggleSidebar();\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  );\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  isActive?: boolean;\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n  const { isMobile, state } = useSidebar();\r\n\r\n  // Use useMemo to prevent recreating the button on every render\r\n  const button = React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, variant, className, props]);\r\n\r\n  // Use useMemo for tooltipContent to prevent recalculation on every render\r\n  const tooltipContent = React.useMemo(() => {\r\n    if (!tooltip) return null;\r\n    return typeof tooltip === \"string\" ? { children: tooltip } : tooltip;\r\n  }, [tooltip]);\r\n\r\n  if (!tooltip) {\r\n    return button;\r\n  }\r\n\r\n  // Use useMemo for the hidden prop to prevent recalculation on every render\r\n  const isHidden = React.useMemo(() => {\r\n    return state !== \"collapsed\" || isMobile;\r\n  }, [state, isMobile]);\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={isHidden}\r\n        {...tooltipContent}\r\n      />\r\n    </Tooltip>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  showOnHover?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, className, showOnHover, props]);\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [className, props]);\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean;\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`;\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean;\r\n  size?: \"sm\" | \"md\";\r\n  isActive?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, className, props]);\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AApBA;;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WAAW,cAAc,CAAC,OAAS,CAAC,QAAQ,QAAQ,CAAC,OAAS,CAAC;IACxE,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,8OAAC,iIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,iIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,oNAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,8OAAC,qIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,+rBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;IAChD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,+DAA+D;IAC/D,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBAC3B,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,aAAW;YACX,eAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;gBAAE;gBAAS;YAAK,IAAI;YAC3D,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAM;QAAU;QAAS;QAAW;KAAM;IAEpD,0EAA0E;IAC1E,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACnC,IAAI,CAAC,SAAS,OAAO;QACrB,OAAO,OAAO,YAAY,WAAW;YAAE,UAAU;QAAQ,IAAI;IAC/D,GAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,2EAA2E;IAC3E,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC7B,OAAO,UAAU,eAAe;IAClC,GAAG;QAAC;QAAO;KAAS;IAEpB,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ;gBACP,GAAG,cAAc;;;;;;;;;;;;AAI1B;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;YAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAW;QAAa;KAAM;AAC1C;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAW;KAAM;AACvB;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,aAAW;YACX,eAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAM;QAAU;QAAW;KAAM;AAC7C", "debugId": null}}, {"offset": {"line": 1906, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/navigation-link.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { ReactNode, MouseEvent, useMemo } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface NavigationLinkProps {\r\n  href: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n  onClick?: (e: MouseEvent<HTMLAnchorElement>) => void;\r\n  replace?: boolean;\r\n  prefetch?: boolean;\r\n  scroll?: boolean;\r\n  exact?: boolean;\r\n}\r\n\r\n/**\r\n * NavigationLink component that ensures client-side navigation\r\n * This component wraps Next.js Link component and provides a consistent way to navigate\r\n */\r\nexport function NavigationLink({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(className)}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n      aria-current={isActive ? \"page\" : undefined}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n\r\n/**\r\n * NavigationButton component that looks like a button but navigates like a link\r\n */\r\nexport function NavigationButton({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(\r\n        \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n        className\r\n      )}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAsBO,SAAS,eAAe,EAC7B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;IACzD,GAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;QACb,gBAAc,WAAW,SAAS;kBAEjC;;;;;;AAGP;AAKO,SAAS,iBAAiB,EAC/B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;IACzD,GAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0RACA;QAEF,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;kBAEZ;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 2000, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot=\"collapsible-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot=\"collapsible-content\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,uKAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2047, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-main.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\nimport { ChevronRight, CircleIcon, type LucideIcon } from \"lucide-react\";\r\n\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport {\r\n  SidebarGroup,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavMain({\r\n  items,\r\n}: {\r\n  items: {\r\n    title: string;\r\n    url: string;\r\n    icon?: LucideIcon;\r\n    isActive?: boolean;\r\n    items?: {\r\n      title: string;\r\n      url: string;\r\n    }[];\r\n  }[];\r\n}) {\r\n  return (\r\n    <SidebarGroup>\r\n      <SidebarMenu>\r\n        {items.map((item) => {\r\n          // Special case for Dashboard - make it a direct link\r\n          if (item.title === \"Dashboard\") {\r\n            return (\r\n              <SidebarMenuItem key={item.title}>\r\n                <SidebarMenuButton\r\n                  asChild\r\n                  tooltip={item.title}\r\n                  isActive={item.isActive}\r\n                  className=\"nav-main-item\"\r\n                >\r\n                  <NavigationLink href={item.url}>\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                  </NavigationLink>\r\n                </SidebarMenuButton>\r\n              </SidebarMenuItem>\r\n            );\r\n          }\r\n\r\n          // For all other items, use the collapsible dropdown\r\n          return (\r\n            <Collapsible\r\n              key={item.title}\r\n              asChild\r\n              defaultOpen={item.isActive}\r\n              className=\"group/collapsible mb-2\"\r\n            >\r\n              <SidebarMenuItem>\r\n                <CollapsibleTrigger asChild>\r\n                  <SidebarMenuButton\r\n                    tooltip={item.title}\r\n                    isActive={item.isActive}\r\n                    className=\"nav-main-item\"\r\n                  >\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                    <ChevronRight className=\"ml-auto h-2 w-2 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 nav-main-item-chevron\" />\r\n                  </SidebarMenuButton>\r\n                </CollapsibleTrigger>\r\n                <CollapsibleContent>\r\n                  <SidebarMenuSub className=\"nav-main-sub mt-2\">\r\n                    {item.items?.map((subItem) => (\r\n                      <SidebarMenuSubItem key={subItem.title}>\r\n                        <SidebarMenuSubButton asChild>\r\n                          <NavigationLink href={subItem.url} exact={true}>\r\n                            {/* No circle icon for child items */}\r\n                            <span className=\"text-xs\">{subItem.title}</span>\r\n                          </NavigationLink>\r\n                        </SidebarMenuSubButton>\r\n                      </SidebarMenuSubItem>\r\n                    ))}\r\n                  </SidebarMenuSub>\r\n                </CollapsibleContent>\r\n              </SidebarMenuItem>\r\n            </Collapsible>\r\n          );\r\n        })}\r\n      </SidebarMenu>\r\n    </SidebarGroup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAKA;AAVA;;;;;;AAoBO,SAAS,QAAQ,EACtB,KAAK,EAYN;IACC,qBACE,8OAAC,mIAAA,CAAA,eAAY;kBACX,cAAA,8OAAC,mIAAA,CAAA,cAAW;sBACT,MAAM,GAAG,CAAC,CAAC;gBACV,qDAAqD;gBACrD,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,qBACE,8OAAC,mIAAA,CAAA,kBAAe;kCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,SAAS,KAAK,KAAK;4BACnB,UAAU,KAAK,QAAQ;4BACvB,WAAU;sCAEV,cAAA,8OAAC,8IAAA,CAAA,iBAAc;gCAAC,MAAM,KAAK,GAAG;;kDAC5B,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI,kBAAI,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CAClC,KAAK,QAAQ,kBACZ,8OAAC,0MAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAG1B,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAfG,KAAK,KAAK;;;;;gBAqBpC;gBAEA,oDAAoD;gBACpD,qBACE,8OAAC,uIAAA,CAAA,cAAW;oBAEV,OAAO;oBACP,aAAa,KAAK,QAAQ;oBAC1B,WAAU;8BAEV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;;0CACd,8OAAC,uIAAA,CAAA,qBAAkB;gCAAC,OAAO;0CACzB,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;oCAChB,SAAS,KAAK,KAAK;oCACnB,UAAU,KAAK,QAAQ;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI,kBAAI,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDAClC,KAAK,QAAQ,kBACZ,8OAAC,0MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAG1B,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;sDAEb,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG5B,8OAAC,uIAAA,CAAA,qBAAkB;0CACjB,cAAA,8OAAC,mIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,8OAAC,mIAAA,CAAA,qBAAkB;sDACjB,cAAA,8OAAC,mIAAA,CAAA,uBAAoB;gDAAC,OAAO;0DAC3B,cAAA,8OAAC,8IAAA,CAAA,iBAAc;oDAAC,MAAM,QAAQ,GAAG;oDAAE,OAAO;8DAExC,cAAA,8OAAC;wDAAK,WAAU;kEAAW,QAAQ,KAAK;;;;;;;;;;;;;;;;2CAJrB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;mBA3BzC,KAAK,KAAK;;;;;YAyCrB;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 2259, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2311, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2573, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-user.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Chev<PERSON>UpDown, LogOut } from \"lucide-react\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavUser({\r\n  user,\r\n}: {\r\n  user: {\r\n    name: string;\r\n    email: string;\r\n    avatar: string;\r\n  };\r\n}) {\r\n  const { isMobile } = useSidebar();\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground nav-user\"\r\n            >\r\n              <Avatar className=\"h-6 w-6 rounded-lg nav-user-avatar\">\r\n                <AvatarImage src={user.avatar} alt={user.name} />\r\n                <AvatarFallback className=\"rounded-lg text-xs\">\r\n                  {user.name.charAt(0)}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left leading-tight nav-user-details\">\r\n                <span className=\"truncate text-xs font-medium\">\r\n                  {user.name}\r\n                </span>\r\n                <span className=\"truncate text-[10px]\">{user.email}</span>\r\n              </div>\r\n              <ChevronsUpDown className=\"ml-auto h-3 w-3 nav-user-chevron\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                  <AvatarImage src={user.avatar} alt={user.name} />\r\n                  <AvatarFallback className=\"rounded-lg\">CN</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-medium\">{user.name}</span>\r\n                  <span className=\"truncate text-xs\">{user.email}</span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuGroup>\r\n              <DropdownMenuItem>\r\n                <BadgeCheck className=\"mr-2 h-3.5 w-3.5\" />\r\n                <span className=\"text-xs\">Account</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem>\r\n              <LogOut className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"text-xs\">Log out</span>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AACA;AASA;AAdA;;;;;;AAqBO,SAAS,QAAQ,EACtB,IAAI,EAOL;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAE9B,qBACE,8OAAC,mIAAA,CAAA,cAAW;kBACV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kIAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM;4CAAE,KAAK,KAAK,IAAI;;;;;;sDAC7C,8OAAC,kIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,KAAK,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDACb,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAAwB,KAAK,KAAK;;;;;;;;;;;;8CAEpD,8OAAC,8NAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,8OAAC,4IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM;oDAAE,KAAK,KAAK,IAAI;;;;;;8DAC7C,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAa;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI;;;;;;8DACjD,8OAAC;oDAAK,WAAU;8DAAoB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAIpD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,4IAAA,CAAA,oBAAiB;0CAChB,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;;sDACf,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,4IAAA,CAAA,mBAAgB;;kDACf,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}, {"offset": {"line": 2832, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport \"@/components/ui/sidebar-collapsed.css\";\r\nimport \"@/components/ui/sidebar-compact.css\";\r\nimport {\r\n  Banknote,\r\n  BarChart3 as BarChart3Icon,\r\n  Building as BuildingIcon,\r\n  CreditCard,\r\n  FileText,\r\n  GalleryVerticalEnd,\r\n  Home as HomeIcon,\r\n  Landmark,\r\n  Package,\r\n  Settings,\r\n  ShoppingCart,\r\n  Smartphone,\r\n  UserCheck,\r\n} from \"lucide-react\";\r\n\r\nimport { NavMain } from \"@/components/nav-main\";\r\nimport { NavUser } from \"@/components/nav-user\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n// Import usePermissions for future use\r\n// import { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\n// import { getRoutePermission } from \"@/lib/route-permissions\";\r\n// import { navigationPermissions } from \"@/lib/navigation-permissions\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { usePathname } from \"next/navigation\";\r\n\r\ntype NavItem = {\r\n  title: string;\r\n  url: string;\r\n  icon?: any;\r\n  isActive?: boolean;\r\n  items?: { title: string; url: string }[];\r\n};\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * This function is commented out for MVP, will be used in the future\r\n * when the permission system is fully implemented\r\n *\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\n// function mapActionToGrantAction(action: string): string {\r\n//   switch (action) {\r\n//     case \"view\":\r\n//       return \"read:any\";\r\n//     case \"create\":\r\n//       return \"create:any\";\r\n//     case \"update\":\r\n//       return \"update:any\";\r\n//     case \"delete\":\r\n//       return \"delete:any\";\r\n//     case \"manage\":\r\n//       return \"update:any\"; // Manage maps to update:any\r\n//     case \"transfer\":\r\n//       return \"update:any\"; // Transfer maps to update:any\r\n//     case \"report\":\r\n//       return \"read:any\"; // Report maps to read:any\r\n//     default:\r\n//       return `${action}:any`;\r\n//   }\r\n// }\r\n\r\nexport function AppSidebar() {\r\n  const { data: user } = useCurrentUser();\r\n  const pathname = usePathname();\r\n  // Permissions will be used in the future\r\n  // const { hasPermission, permissions } = usePermissions();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  // Define all navigation items\r\n  const getAllNavigationItems = (): NavItem[] => [\r\n    {\r\n      title: \"Dashboard\",\r\n      url: \"/dashboard\",\r\n      icon: HomeIcon,\r\n      isActive: pathname === \"/dashboard\" || pathname === \"/\",\r\n    },\r\n    {\r\n      title: \"Reports\",\r\n      url: \"/reports/sales-summary\",\r\n      icon: BarChart3Icon,\r\n      isActive:\r\n        pathname.startsWith(\"/reports\") ||\r\n        pathname.startsWith(\"/expense-analytics\"),\r\n      items: [\r\n        {\r\n          title: \"Sales Summary\",\r\n          url: \"/reports/sales-summary\",\r\n        },\r\n        {\r\n          title: \"Sales by Item\",\r\n          url: \"/reports/sales-by-item\",\r\n        },\r\n        {\r\n          title: \"Sales by Category\",\r\n          url: \"/reports/sales-by-category\",\r\n        },\r\n        {\r\n          title: \"Current Stock Levels\",\r\n          url: \"/reports/current-stock-levels\",\r\n        },\r\n        {\r\n          title: \"Stock History\",\r\n          url: \"/reports/stock-history\",\r\n        },\r\n        {\r\n          title: \"Banking Transactions\",\r\n          url: \"/reports/banking-transactions\",\r\n        },\r\n        {\r\n          title: \"Tax Report\",\r\n          url: \"/reports/tax-report\",\r\n        },\r\n        {\r\n          title: \"Banking Summary\",\r\n          url: \"/banking?tab=summary\",\r\n        },\r\n        {\r\n          title: \"M-Pesa Transactions\",\r\n          url: \"/reports/mpesa-transactions\",\r\n        },\r\n        {\r\n          title: \"Running Balances\",\r\n          url: \"/reports/running-balances\",\r\n        },\r\n        {\r\n          title: \"Cash Status\",\r\n          url: \"/reports/cash-status\",\r\n        },\r\n        {\r\n          title: \"Cash Float\",\r\n          url: \"/reports/cash-float\",\r\n        },\r\n        {\r\n          title: \"DSA Sales\",\r\n          url: \"/reports/dsa-sales\",\r\n        },\r\n        {\r\n          title: \"Receipts\",\r\n          url: \"/reports/receipts\",\r\n        },\r\n        {\r\n          title: \"Shifts\",\r\n          url: \"/reports/shifts\",\r\n        },\r\n        {\r\n          title: \"Phone Repairs\",\r\n          url: \"/reports/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"Stock Valuation\",\r\n          url: \"/inventory/valuation\",\r\n        },\r\n        {\r\n          title: \"Stock Aging\",\r\n          url: \"/reports/stock-aging\",\r\n        },\r\n        {\r\n          title: \"Stock Movement\",\r\n          url: \"/reports/stock-movement\",\r\n        },\r\n        {\r\n          title: \"Expense Analytics\",\r\n          url: \"/expense-analytics\",\r\n        },\r\n        {\r\n          title: \"Customers\",\r\n          url: \"/customers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Administration\",\r\n      url: \"/users\",\r\n      icon: BuildingIcon,\r\n      isActive:\r\n        pathname.startsWith(\"/users\") ||\r\n        pathname.startsWith(\"/roles\") ||\r\n        pathname.startsWith(\"/rbac\") ||\r\n        pathname.startsWith(\"/branches\") ||\r\n        pathname.startsWith(\"/locations\") ||\r\n        pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        // Tenants menu item removed\r\n        {\r\n          title: \"Users\",\r\n          url: \"/users\",\r\n        },\r\n        {\r\n          title: \"Roles\",\r\n          url: \"/roles\",\r\n        },\r\n        {\r\n          title: \"RBAC\",\r\n          url: \"/rbac\",\r\n        },\r\n        {\r\n          title: \"Branches\",\r\n          url: \"/branches\",\r\n        },\r\n        {\r\n          title: \"Locations\",\r\n          url: \"/locations\",\r\n        },\r\n        {\r\n          title: \"Employees\",\r\n          url: \"/employees\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Stock & Inventory\",\r\n      url: \"/products\",\r\n      icon: Package,\r\n      isActive:\r\n        pathname.startsWith(\"/products\") ||\r\n        pathname.startsWith(\"/categories\") ||\r\n        pathname.startsWith(\"/brands\") ||\r\n        pathname.startsWith(\"/inventory\") ||\r\n        pathname.includes(\"/inventory/stock-cards\") ||\r\n        pathname.includes(\"/inventory/purchases\"),\r\n      items: [\r\n        {\r\n          title: \"Products\",\r\n          url: \"/products\",\r\n        },\r\n        {\r\n          title: \"Categories\",\r\n          url: \"/categories\",\r\n        },\r\n        {\r\n          title: \"Brands\",\r\n          url: \"/brands\",\r\n        },\r\n        {\r\n          title: \"Inventory\",\r\n          url: \"/inventory\",\r\n        },\r\n        // Commented out Stock Locations as requested\r\n        // {\r\n        //   title: \"Stock Locations\",\r\n        //   url: \"/inventory/locations\",\r\n        // },\r\n        {\r\n          title: \"Purchases\",\r\n          url: \"/inventory/purchases\",\r\n        },\r\n        {\r\n          title: \"Create Purchase\",\r\n          url: \"/inventory/purchases/new\",\r\n        },\r\n        {\r\n          title: \"Stock Requests\",\r\n          url: \"/inventory/stock-requests\",\r\n        },\r\n        {\r\n          title: \"Stock Transfers\",\r\n          url: \"/inventory/transfers\",\r\n        },\r\n        {\r\n          title: \"Make Stock Transfer\",\r\n          url: \"/inventory/bulk-transfer\",\r\n        },\r\n        {\r\n          title: \"Stock Transfer History\",\r\n          url: \"/inventory/bulk-transfers\",\r\n        },\r\n        {\r\n          title: \"Stock Cards\",\r\n          url: \"/inventory/stock-cards\",\r\n        },\r\n        {\r\n          title: \"Inventory Reports\",\r\n          url: \"/inventory/reports\",\r\n        },\r\n        {\r\n          title: \"Excel Import/Export\",\r\n          url: \"/inventory/excel\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Float Management\",\r\n      url: \"/float\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/float\"),\r\n      items: [\r\n        {\r\n          title: \"Float Dashboard\",\r\n          url: \"/float\",\r\n        },\r\n        {\r\n          title: \"Float Movements\",\r\n          url: \"/float/movements\",\r\n        },\r\n        {\r\n          title: \"Float Reconciliations\",\r\n          url: \"/float/reconciliations\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Banking Management\",\r\n      url: \"/banking\",\r\n      icon: Landmark,\r\n      isActive: pathname.startsWith(\"/banking\"),\r\n      items: [\r\n        {\r\n          title: \"Banking Records\",\r\n          url: \"/banking\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Expenses Management\",\r\n      url: \"/expenses\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/expenses\"),\r\n      items: [\r\n        {\r\n          title: \"Expenses\",\r\n          url: \"/expenses\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Invoice Management\",\r\n      url: \"/invoices\",\r\n      icon: FileText,\r\n      isActive:\r\n        pathname.startsWith(\"/invoices\") ||\r\n        pathname.startsWith(\"/credit-notes\"),\r\n      items: [\r\n        {\r\n          title: \"All Invoices\",\r\n          url: \"/invoices\",\r\n        },\r\n        {\r\n          title: \"Credit Notes\",\r\n          url: \"/credit-notes\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"DSA Management\",\r\n      url: \"/dsa\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/dsa\"),\r\n      items: [\r\n        {\r\n          title: \"DSA Dashboard\",\r\n          url: \"/dsa\",\r\n        },\r\n        {\r\n          title: \"DSA Agents\",\r\n          url: \"/dsa/customers\",\r\n        },\r\n        {\r\n          title: \"Stock Assignments\",\r\n          url: \"/dsa/assignments\",\r\n        },\r\n        // Commented out DSA Reconciliations as requested\r\n        // {\r\n        //   title: \"DSA Reconciliations\",\r\n        //   url: \"/dsa/reconciliations\",\r\n        // },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Procurement\",\r\n      url: \"/procurement\",\r\n      icon: ShoppingCart,\r\n      isActive:\r\n        pathname.startsWith(\"/procurement\") ||\r\n        pathname.startsWith(\"/suppliers\"),\r\n      items: [\r\n        {\r\n          title: \"Procurement Dashboard\",\r\n          url: \"/procurement\",\r\n        },\r\n        {\r\n          title: \"Procurement Requests\",\r\n          url: \"/procurement/requests\",\r\n        },\r\n        {\r\n          title: \"Create Request\",\r\n          url: \"/procurement/requests/new\",\r\n        },\r\n        {\r\n          title: \"Procurement Receipts\",\r\n          url: \"/procurement/receipts\",\r\n        },\r\n        {\r\n          title: \"Suppliers\",\r\n          url: \"/suppliers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Phone Repairs\",\r\n      url: \"/phone-repairs\",\r\n      icon: Smartphone,\r\n      isActive:\r\n        pathname === \"/phone-repairs\" || pathname.startsWith(\"/phone-repairs/\")\r\n          ? true\r\n          : false,\r\n      items: [\r\n        {\r\n          title: \"All Repairs\",\r\n          url: \"/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"New Repair\",\r\n          url: \"/phone-repairs/new\",\r\n        },\r\n      ],\r\n    },\r\n    // Settings section completely hidden for production - not implemented\r\n    // {\r\n    //   title: \"Settings\",\r\n    //   url: \"/settings\",\r\n    //   icon: Settings,\r\n    //   isActive:\r\n    //     pathname.startsWith(\"/settings\") || pathname.startsWith(\"/profile\"),\r\n    //   items: [\r\n    //     {\r\n    //       title: \"System Settings\",\r\n    //       url: \"/settings/system\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Company Settings\",\r\n    //       url: \"/settings/company\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Payment Methods\",\r\n    //       url: \"/settings/payment-methods\",\r\n    //     },\r\n    //     {\r\n    //       title: \"System Health\",\r\n    //       url: \"/settings/health\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Profile\",\r\n    //       url: \"/profile\",\r\n    //     },\r\n    //   ],\r\n    // },\r\n    // Permissions Demo section removed\r\n  ];\r\n\r\n  // Filter navigation items based on role (MVP approach)\r\n  const getFilteredNavigationItemsByRole = (): NavItem[] => {\r\n    const allItems = getAllNavigationItems();\r\n\r\n    // Create a custom Employees section for branch managers\r\n    const employeesSection: NavItem = {\r\n      title: \"Employees\",\r\n      url: \"/employees\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        {\r\n          title: \"Manage Employees\",\r\n          url: \"/employees\",\r\n        },\r\n        {\r\n          title: \"Add Employee\",\r\n          url: \"/employees/create\",\r\n        },\r\n      ],\r\n    };\r\n\r\n    // For MVP, use role-based filtering as the default approach\r\n    console.log(\r\n      `[Navigation] ${userRoleName} role detected - using role-based filtering (MVP approach)`\r\n    );\r\n\r\n    // Filter navigation items based on user role\r\n    const filteredItems = allItems.filter((item) => {\r\n      // For super_admin and company_admin, show all items\r\n      if (userRoleName === \"super_admin\" || userRoleName === \"company_admin\") {\r\n        console.log(`[Navigation] Admin role detected - showing all items`);\r\n        return true;\r\n      }\r\n\r\n      // For branch_admin, show most items\r\n      if (userRoleName === \"branch_admin\") {\r\n        // Hide Tenants section\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n        return true;\r\n      }\r\n\r\n      // For accountant, show finance-related items\r\n      if (userRoleName === \"accountant\") {\r\n        // Allow Dashboard\r\n        // Hide Tenants section\r\n        if (item.title === \"Dashboard\") {\r\n          return true;\r\n        }\r\n\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Administration routes for accountant\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow Reports, Banking, Expenses, Float\r\n        if (\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide DSA Management\r\n        if (item.title === \"DSA Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Phone Repairs\r\n        if (item.title === \"Phone Repairs\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow POS Management for viewing sales data\r\n        if (item.title === \"POS Management\") {\r\n          return true;\r\n        }\r\n\r\n        // Hide Settings for accountant\r\n        if (item.title === \"Settings\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For auditor, show reporting and read-only sections\r\n      if (userRoleName === \"auditor\") {\r\n        // Allow Reports, Banking, Expenses, Float (read-only)\r\n        if (\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"POS Management\" ||\r\n          item.title === \"Products & Inventory\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For finance_manager, show finance-related sections\r\n      if (userRoleName === \"finance_manager\") {\r\n        // Allow Reports, Banking, Expenses, Float\r\n        if (\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"POS Management\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For float_manager, show float-related sections\r\n      if (userRoleName === \"float_manager\") {\r\n        if (item.title === \"Dashboard\") {\r\n          return true;\r\n        }\r\n\r\n        // Allow Float Management, Banking, and Reports\r\n        if (\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Reports\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Allow POS Management but only for Cash Balance access\r\n        if (item.title === \"POS Management\") {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For operations and operations_manager, show operations-related sections\r\n      if (\r\n        userRoleName === \"operations\" ||\r\n        userRoleName === \"operations_manager\"\r\n      ) {\r\n        // Hide Administration routes\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Tenants section\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow everything else\r\n        return true;\r\n      }\r\n\r\n      // For stock_admin, show inventory-related sections\r\n      if (userRoleName === \"stock_admin\") {\r\n        // Allow Products & Inventory\r\n        if (\r\n          item.title === \"Products & Inventory\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Procurement\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // Branch Manager role removed as they don't login via the web\r\n\r\n      // For pos_operator and shop_attendant, hide admin sections\r\n      if (\r\n        userRoleName === \"pos_operator\" ||\r\n        userRoleName === \"shop_attendant\"\r\n      ) {\r\n        // Hide Administration routes\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Float Management\r\n        if (item.title === \"Float Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Banking Management\r\n        if (item.title === \"Banking Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Expenses Management\r\n        if (item.title === \"Expenses Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide DSA Management\r\n        if (item.title === \"DSA Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow POS Management\r\n        if (item.title === \"POS Management\") {\r\n          return true;\r\n        }\r\n\r\n        // Allow Products & Inventory (read-only)\r\n        if (item.title === \"Products & Inventory\") {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For other roles, restrict access to only essential items\r\n      console.log(\r\n        `[Navigation] Unknown role detected: ${userRoleName} - restricting access`\r\n      );\r\n\r\n      // Allow Dashboard only\r\n      if (item.title === \"Dashboard\") {\r\n        return true;\r\n      }\r\n\r\n      // Hide Settings and everything else for unknown roles\r\n      return false;\r\n    });\r\n\r\n    // Filter subitems based on role\r\n    const itemsWithFilteredSubitems = filteredItems.map((item) => {\r\n      // If the item has subitems, filter them based on role\r\n      if (item.items && item.items.length > 0) {\r\n        console.log(\r\n          `[Navigation] Checking subitems for \"${item.title}\" section`\r\n        );\r\n\r\n        const filteredSubItems = item.items.filter((subItem) => {\r\n          // For Administration, filter RBAC items for non-super_admin users\r\n          if (item.title === \"Administration\") {\r\n            // Only super_admin can see RBAC items\r\n            if (subItem.title === \"RBAC\") {\r\n              return userRoleName === \"super_admin\";\r\n            }\r\n            // All other admin roles can see other items\r\n            return true;\r\n          }\r\n\r\n          // For Settings, only show Profile for most roles\r\n          if (item.title === \"Settings\") {\r\n            // Admin roles can see all settings\r\n            if (\r\n              userRoleName === \"super_admin\" ||\r\n              userRoleName === \"company_admin\" ||\r\n              userRoleName === \"branch_admin\"\r\n            ) {\r\n              return true;\r\n            }\r\n\r\n            // Accountant can see Profile and Payment Methods\r\n            if (userRoleName === \"accountant\") {\r\n              return (\r\n                subItem.title === \"Profile\" ||\r\n                subItem.title === \"Payment Methods\"\r\n              );\r\n            }\r\n\r\n            // Other roles can only see Profile\r\n            return subItem.title === \"Profile\";\r\n          }\r\n\r\n          // For POS Management, restrict access based on role\r\n          if (item.title === \"POS Management\") {\r\n            // For float_manager, only show Cash Balance\r\n            if (userRoleName === \"float_manager\") {\r\n              return subItem.title === \"Cash Balance\";\r\n            }\r\n\r\n            // For other roles, restrict Cash Balance to admin roles\r\n            if (subItem.title === \"Cash Balance\") {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"accountant\" ||\r\n                userRoleName === \"finance_manager\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // For Products & Inventory, restrict Categories and Brands for non-admin roles\r\n          if (\r\n            item.title === \"Products & Inventory\" &&\r\n            (subItem.title.includes(\"Categories\") ||\r\n              subItem.title.includes(\"Brands\"))\r\n          ) {\r\n            return (\r\n              userRoleName === \"super_admin\" ||\r\n              userRoleName === \"company_admin\" ||\r\n              userRoleName === \"branch_admin\" ||\r\n              userRoleName === \"stock_admin\"\r\n            );\r\n          }\r\n\r\n          // For Products & Inventory, restrict inventory management for shop_attendant and pos_operator\r\n          if (\r\n            item.title === \"Products & Inventory\" &&\r\n            (subItem.title.includes(\"Transfer\") ||\r\n              subItem.title.includes(\"Stock Cards\") ||\r\n              subItem.title.includes(\"Inventory Reports\") ||\r\n              subItem.title.includes(\"Excel\"))\r\n          ) {\r\n            return (\r\n              userRoleName !== \"pos_operator\" &&\r\n              userRoleName !== \"shop_attendant\"\r\n            );\r\n          }\r\n\r\n          // For Reports, restrict certain reports based on role\r\n          if (item.title === \"Reports\") {\r\n            // Finance-related reports\r\n            if (\r\n              subItem.title.includes(\"Banking\") ||\r\n              subItem.title.includes(\"Cash\") ||\r\n              subItem.title.includes(\"Float\") ||\r\n              subItem.title.includes(\"Expense\")\r\n            ) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"accountant\" ||\r\n                userRoleName === \"finance_manager\" ||\r\n                userRoleName === \"auditor\"\r\n              );\r\n            }\r\n\r\n            // Inventory-related reports\r\n            if (\r\n              subItem.title.includes(\"Stock\") ||\r\n              subItem.title.includes(\"Inventory\")\r\n            ) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"stock_admin\" ||\r\n                userRoleName === \"operations_manager\" ||\r\n                userRoleName === \"operations\" ||\r\n                userRoleName === \"auditor\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // For Procurement, restrict certain operations based on role\r\n          if (item.title === \"Procurement\") {\r\n            // Creating and approving procurement requests\r\n            if (subItem.title.includes(\"Create\")) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"stock_admin\" ||\r\n                userRoleName === \"operations_manager\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // For Float Management, restrict certain operations based on role\r\n          if (item.title === \"Float Management\") {\r\n            // Float reconciliations\r\n            if (subItem.title.includes(\"Reconciliation\")) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"accountant\" ||\r\n                userRoleName === \"finance_manager\" ||\r\n                userRoleName === \"float_manager\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // Allow all other subitems by default\r\n          return true;\r\n        });\r\n\r\n        // Update the item's subitems with the filtered list\r\n        item.items = filteredSubItems;\r\n      }\r\n\r\n      return item;\r\n    });\r\n\r\n    // Only return items that have at least one subitem (if they had subitems to begin with)\r\n    const result = itemsWithFilteredSubitems.filter(\r\n      (item) => !item.items || item.items.length > 0\r\n    );\r\n\r\n    // Add the Employees section at the beginning of the filtered items for branch managers\r\n    if (userRoleName === \"branch_manager\") {\r\n      return [employeesSection, ...result];\r\n    }\r\n\r\n    return result;\r\n  };\r\n\r\n  // Filter navigation items based on permissions (for future use)\r\n  // This function is commented out for MVP, will be implemented in the future\r\n  // when the permission system is fully implemented\r\n\r\n  // Sample data for the sidebar\r\n  const data = {\r\n    user: {\r\n      name: user?.name || \"User\",\r\n      email: user?.email || \"<EMAIL>\",\r\n      avatar: \"/placeholder-avatar.jpg\",\r\n    },\r\n    teams: [\r\n      {\r\n        name: teamData.name,\r\n        logo: GalleryVerticalEnd,\r\n        plan: teamData.plan,\r\n      },\r\n    ],\r\n    // For MVP, use role-based filtering\r\n    navMain: getFilteredNavigationItemsByRole(),\r\n    // For future: navMain: getFilteredNavigationItems(),\r\n  };\r\n\r\n  const { state, isMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\" && !isMobile;\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"h-full flex flex-col sidebar-compact\",\r\n        isCollapsed && \"sidebar-collapsed\"\r\n      )}\r\n    >\r\n      <div className=\"flex-1 overflow-y-auto py-2\">\r\n        <div className={cn(\"px-3\", isCollapsed && \"px-2\")}>\r\n          <NavMain items={data.navMain} />\r\n        </div>\r\n      </div>\r\n      <div className={cn(\"p-3 border-t\", isCollapsed && \"p-2\")}>\r\n        <NavUser user={data.user} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AACA,uCAAuC;AACvC,0EAA0E;AAC1E,gEAAgE;AAChE,wEAAwE;AACxE;AACA;AA7BA;;;;;;;;;;;AAoEO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,yCAAyC;IACzC,2DAA2D;IAE3D,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,8BAA8B;IAC9B,MAAM,wBAAwB,IAAiB;YAC7C;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,mMAAA,CAAA,OAAQ;gBACd,UAAU,aAAa,gBAAgB,aAAa;YACtD;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,kNAAA,CAAA,YAAa;gBACnB,UACE,SAAS,UAAU,CAAC,eACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAY;gBAClB,UACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,YACpB,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL,4BAA4B;oBAC5B;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,wMAAA,CAAA,UAAO;gBACb,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,QAAQ,CAAC,6BAClB,SAAS,QAAQ,CAAC;gBACpB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,6CAA6C;oBAC7C,IAAI;oBACJ,8BAA8B;oBAC9B,iCAAiC;oBACjC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,8MAAA,CAAA,WAAQ;gBACd,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,gNAAA,CAAA,YAAS;gBACf,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBAMD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,sNAAA,CAAA,eAAY;gBAClB,UACE,SAAS,UAAU,CAAC,mBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,8MAAA,CAAA,aAAU;gBAChB,UACE,aAAa,oBAAoB,SAAS,UAAU,CAAC,qBACjD,OACA;gBACN,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;SAgCD;IAED,uDAAuD;IACvD,MAAM,mCAAmC;QACvC,MAAM,WAAW;QAEjB,wDAAwD;QACxD,MAAM,mBAA4B;YAChC,OAAO;YACP,KAAK;YACL,MAAM,gNAAA,CAAA,YAAS;YACf,UAAU,SAAS,UAAU,CAAC;YAC9B,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;QAEA,4DAA4D;QAC5D,QAAQ,GAAG,CACT,CAAC,aAAa,EAAE,aAAa,0DAA0D,CAAC;QAG1F,6CAA6C;QAC7C,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC;YACrC,oDAAoD;YACpD,IAAI,iBAAiB,iBAAiB,iBAAiB,iBAAiB;gBACtE,QAAQ,GAAG,CAAC,CAAC,oDAAoD,CAAC;gBAClE,OAAO;YACT;YAEA,oCAAoC;YACpC,IAAI,iBAAiB,gBAAgB;gBACnC,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBACA,OAAO;YACT;YAEA,6CAA6C;YAC7C,IAAI,iBAAiB,cAAc;gBACjC,kBAAkB;gBAClB,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,OAAO;gBACT;gBAEA,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBAEA,4CAA4C;gBAC5C,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,0CAA0C;gBAC1C,IACE,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,oBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,qBAAqB;gBACrB,IAAI,KAAK,KAAK,KAAK,iBAAiB;oBAClC,OAAO;gBACT;gBAEA,8CAA8C;gBAC9C,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,+BAA+B;gBAC/B,IAAI,KAAK,KAAK,KAAK,YAAY;oBAC7B,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,qDAAqD;YACrD,IAAI,iBAAiB,WAAW;gBAC9B,sDAAsD;gBACtD,IACE,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,oBACf,KAAK,KAAK,KAAK,wBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,qDAAqD;YACrD,IAAI,iBAAiB,mBAAmB;gBACtC,0CAA0C;gBAC1C,IACE,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,kBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,iDAAiD;YACjD,IAAI,iBAAiB,iBAAiB;gBACpC,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,OAAO;gBACT;gBAEA,+CAA+C;gBAC/C,IACE,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,WACf;oBACA,OAAO;gBACT;gBAEA,wDAAwD;gBACxD,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,0EAA0E;YAC1E,IACE,iBAAiB,gBACjB,iBAAiB,sBACjB;gBACA,6BAA6B;gBAC7B,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,OAAO;YACT;YAEA,mDAAmD;YACnD,IAAI,iBAAiB,eAAe;gBAClC,6BAA6B;gBAC7B,IACE,KAAK,KAAK,KAAK,0BACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,eACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,8DAA8D;YAE9D,2DAA2D;YAC3D,IACE,iBAAiB,kBACjB,iBAAiB,kBACjB;gBACA,6BAA6B;gBAC7B,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,IAAI,KAAK,KAAK,KAAK,oBAAoB;oBACrC,OAAO;gBACT;gBAEA,0BAA0B;gBAC1B,IAAI,KAAK,KAAK,KAAK,sBAAsB;oBACvC,OAAO;gBACT;gBAEA,2BAA2B;gBAC3B,IAAI,KAAK,KAAK,KAAK,uBAAuB;oBACxC,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,yCAAyC;gBACzC,IAAI,KAAK,KAAK,KAAK,wBAAwB;oBACzC,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,2DAA2D;YAC3D,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,aAAa,qBAAqB,CAAC;YAG5E,uBAAuB;YACvB,IAAI,KAAK,KAAK,KAAK,aAAa;gBAC9B,OAAO;YACT;YAEA,sDAAsD;YACtD,OAAO;QACT;QAEA,gCAAgC;QAChC,MAAM,4BAA4B,cAAc,GAAG,CAAC,CAAC;YACnD,sDAAsD;YACtD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;gBACvC,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC;gBAG9D,MAAM,mBAAmB,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1C,kEAAkE;oBAClE,IAAI,KAAK,KAAK,KAAK,kBAAkB;wBACnC,sCAAsC;wBACtC,IAAI,QAAQ,KAAK,KAAK,QAAQ;4BAC5B,OAAO,iBAAiB;wBAC1B;wBACA,4CAA4C;wBAC5C,OAAO;oBACT;oBAEA,iDAAiD;oBACjD,IAAI,KAAK,KAAK,KAAK,YAAY;wBAC7B,mCAAmC;wBACnC,IACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,gBACjB;4BACA,OAAO;wBACT;wBAEA,iDAAiD;wBACjD,IAAI,iBAAiB,cAAc;4BACjC,OACE,QAAQ,KAAK,KAAK,aAClB,QAAQ,KAAK,KAAK;wBAEtB;wBAEA,mCAAmC;wBACnC,OAAO,QAAQ,KAAK,KAAK;oBAC3B;oBAEA,oDAAoD;oBACpD,IAAI,KAAK,KAAK,KAAK,kBAAkB;wBACnC,4CAA4C;wBAC5C,IAAI,iBAAiB,iBAAiB;4BACpC,OAAO,QAAQ,KAAK,KAAK;wBAC3B;wBAEA,wDAAwD;wBACxD,IAAI,QAAQ,KAAK,KAAK,gBAAgB;4BACpC,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,gBACjB,iBAAiB;wBAErB;oBACF;oBAEA,+EAA+E;oBAC/E,IACE,KAAK,KAAK,KAAK,0BACf,CAAC,QAAQ,KAAK,CAAC,QAAQ,CAAC,iBACtB,QAAQ,KAAK,CAAC,QAAQ,CAAC,SAAS,GAClC;wBACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB;oBAErB;oBAEA,8FAA8F;oBAC9F,IACE,KAAK,KAAK,KAAK,0BACf,CAAC,QAAQ,KAAK,CAAC,QAAQ,CAAC,eACtB,QAAQ,KAAK,CAAC,QAAQ,CAAC,kBACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,wBACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,QAAQ,GACjC;wBACA,OACE,iBAAiB,kBACjB,iBAAiB;oBAErB;oBAEA,sDAAsD;oBACtD,IAAI,KAAK,KAAK,KAAK,WAAW;wBAC5B,0BAA0B;wBAC1B,IACE,QAAQ,KAAK,CAAC,QAAQ,CAAC,cACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,WACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB;4BACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,gBACjB,iBAAiB,qBACjB,iBAAiB;wBAErB;wBAEA,4BAA4B;wBAC5B,IACE,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,cACvB;4BACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,iBACjB,iBAAiB,wBACjB,iBAAiB,gBACjB,iBAAiB;wBAErB;oBACF;oBAEA,6DAA6D;oBAC7D,IAAI,KAAK,KAAK,KAAK,eAAe;wBAChC,8CAA8C;wBAC9C,IAAI,QAAQ,KAAK,CAAC,QAAQ,CAAC,WAAW;4BACpC,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,iBACjB,iBAAiB;wBAErB;oBACF;oBAEA,kEAAkE;oBAClE,IAAI,KAAK,KAAK,KAAK,oBAAoB;wBACrC,wBAAwB;wBACxB,IAAI,QAAQ,KAAK,CAAC,QAAQ,CAAC,mBAAmB;4BAC5C,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,gBACjB,iBAAiB,qBACjB,iBAAiB;wBAErB;oBACF;oBAEA,sCAAsC;oBACtC,OAAO;gBACT;gBAEA,oDAAoD;gBACpD,KAAK,KAAK,GAAG;YACf;YAEA,OAAO;QACT;QAEA,wFAAwF;QACxF,MAAM,SAAS,0BAA0B,MAAM,CAC7C,CAAC,OAAS,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG;QAG/C,uFAAuF;QACvF,IAAI,iBAAiB,kBAAkB;YACrC,OAAO;gBAAC;mBAAqB;aAAO;QACtC;QAEA,OAAO;IACT;IAEA,gEAAgE;IAChE,4EAA4E;IAC5E,kDAAkD;IAElD,8BAA8B;IAC9B,MAAM,OAAO;QACX,MAAM;YACJ,MAAM,MAAM,QAAQ;YACpB,OAAO,MAAM,SAAS;YACtB,QAAQ;QACV;QACA,OAAO;YACL;gBACE,MAAM,SAAS,IAAI;gBACnB,MAAM,sOAAA,CAAA,qBAAkB;gBACxB,MAAM,SAAS,IAAI;YACrB;SACD;QACD,oCAAoC;QACpC,SAAS;IAEX;IAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACrC,MAAM,cAAc,UAAU,eAAe,CAAC;IAE9C,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wCACA,eAAe;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,eAAe;8BACxC,cAAA,8OAAC,iIAAA,CAAA,UAAO;wBAAC,OAAO,KAAK,OAAO;;;;;;;;;;;;;;;;0BAGhC,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,eAAe;0BAChD,cAAA,8OAAC,iIAAA,CAAA,UAAO;oBAAC,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;AAIhC", "debugId": null}}, {"offset": {"line": 3563, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/team-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// import { GalleryVerticalEnd } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n\r\nexport function TeamHeader() {\r\n  const { data: user } = useCurrentUser();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <div className=\"bg-white text-primary flex aspect-square size-6 items-center justify-center rounded-lg\">\r\n        {/* <GalleryVerticalEnd className=\"size-3\" /> */}\r\n        <Image\r\n          src={\"/images/simbatelecomlogo.png\"}\r\n          alt=\"Simba Telecom Logo\"\r\n          className=\"object-contain\"\r\n          width={16}\r\n          height={16}\r\n        />\r\n      </div>\r\n      <div className=\"grid flex-1 text-left leading-tight\">\r\n        <span className=\"truncate text-sm font-semibold text-primary-foreground\">\r\n          {teamData.name}\r\n        </span>\r\n        <span className=\"truncate text-xs text-primary-foreground/90 font-medium\">\r\n          {teamData.plan}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,qDAAqD;AACrD;AACA;AAJA;;;;AAMO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAEpC,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAI;oBACJ,WAAU;oBACV,OAAO;oBACP,QAAQ;;;;;;;;;;;0BAGZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;kCAEhB,8OAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;;;;;;;;;;;;;AAKxB", "debugId": null}}, {"offset": {"line": 3665, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/search-button.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearch } from \"@/components/providers/search-provider\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\n\r\nexport function SearchButton() {\r\n  const { openSearch } = useSearch();\r\n\r\n  return (\r\n    <Button\r\n      variant=\"outline\"\r\n      size=\"sm\"\r\n      onClick={openSearch}\r\n      className=\"relative h-9 w-full justify-start bg-background text-sm font-normal text-muted-foreground shadow-none sm:pr-12 md:w-40 lg:w-64\"\r\n    >\r\n      <span className=\"hidden lg:inline-flex\">Search anything...</span>\r\n      <span className=\"inline-flex lg:hidden\">Search...</span>\r\n      <kbd className=\"pointer-events-none absolute right-1.5 top-1.5 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex\">\r\n        <span className=\"text-xs\">⌘</span>K\r\n      </kbd>\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKO,SAAS;IACd,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,YAAS,AAAD;IAE/B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;;0BAEV,8OAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,8OAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAU;;;;;;oBAAQ;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 3730, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/top-navigation.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport {\r\n  Bell,\r\n  ChevronDown,\r\n  ChevronLeft,\r\n  LogOut,\r\n  Settings,\r\n  User,\r\n  PanelLeftIcon,\r\n  PanelRightIcon,\r\n  Home,\r\n} from \"lucide-react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser, useLogout } from \"@/features/auth/hooks/use-auth\";\r\nimport { TeamHeader } from \"@/components/team-header\";\r\nimport { SearchButton } from \"@/components/search-button\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport Link from \"next/link\";\r\n\r\nexport function TopNavigation() {\r\n  const { data: user } = useCurrentUser();\r\n  const logout = useLogout();\r\n  const { toggleSidebar, state, isMobile, setOpenMobile } = useSidebar();\r\n  const isExpanded = state === \"expanded\";\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Check if we're on the stock requests page\r\n  const isStockRequestsPage = pathname.includes('/inventory/stock-requests') &&\r\n    !pathname.includes('/create') &&\r\n    !pathname.match(/\\/inventory\\/stock-requests\\/\\d+/);\r\n\r\n  const handleLogout = () => {\r\n    logout.mutate();\r\n  };\r\n\r\n  const handleBackClick = () => {\r\n    router.back();\r\n  };\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-30 flex h-14 shrink-0 items-center gap-2 border-b border-primary-foreground/10 bg-primary text-primary-foreground transition-[width,height] ease-linear\">\r\n      <div className=\"flex flex-1 items-center justify-between px-4 w-full\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {isMobile && isStockRequestsPage ? (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={handleBackClick}\r\n                aria-label=\"Go back\"\r\n                title=\"Go back\"\r\n              >\r\n                <ChevronLeft className=\"h-5 w-5\" />\r\n                <span className=\"sr-only\">Go Back</span>\r\n              </Button>\r\n            ) : (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={() => (isMobile ? setOpenMobile(true) : toggleSidebar())}\r\n                data-mobile={isMobile ? \"true\" : \"false\"}\r\n                aria-label=\"Toggle sidebar\"\r\n                title={isExpanded ? \"Collapse sidebar\" : \"Expand sidebar\"}\r\n              >\r\n                {isExpanded ? (\r\n                  <PanelLeftIcon className=\"h-5 w-5\" />\r\n                ) : (\r\n                  <PanelRightIcon className=\"h-5 w-5\" />\r\n                )}\r\n                <span className=\"sr-only\">Toggle Sidebar</span>\r\n              </Button>\r\n            )}\r\n            <TeamHeader />\r\n\r\n            <div className=\"h-6 border-r border-primary-foreground/20 mx-2\"></div>\r\n\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n              asChild\r\n            >\r\n              <Link href=\"/dashboard\">\r\n                <Home className=\"h-4 w-4 mr-1\" />\r\n                Dashboard\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-center\">\r\n          <SearchButton />\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"relative h-8 w-8 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n            aria-label=\"Notifications\"\r\n          >\r\n            <Bell className=\"h-5 w-5\" />\r\n            <span className=\"absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-[10px] font-bold text-primary\">\r\n              3\r\n            </span>\r\n          </Button>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"relative h-8 gap-2 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                aria-label=\"Account\"\r\n              >\r\n                <Avatar className=\"h-7 w-7\">\r\n                  <AvatarImage\r\n                    src=\"/placeholder-avatar.jpg\"\r\n                    alt={user?.name || \"User\"}\r\n                  />\r\n                  <AvatarFallback className=\"text-xs font-semibold\">\r\n                    {user?.name ? user.name.charAt(0) : \"U\"}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <span className=\"hidden text-sm font-semibold md:inline-block\">\r\n                  {user?.name || \"User\"}\r\n                </span>\r\n                <ChevronDown className=\"h-4 w-4 text-primary-foreground/80\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-56 rounded-lg\">\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <div className=\"flex flex-col space-y-1\">\r\n                  <p className=\"text-sm font-semibold leading-none\">\r\n                    {user?.name || \"User\"}\r\n                  </p>\r\n                  <p className=\"text-xs leading-none text-muted-foreground\">\r\n                    {user?.email || \"<EMAIL>\"}\r\n                  </p>\r\n                </div>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuGroup>\r\n                <DropdownMenuItem>\r\n                  <User className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Profile</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem>\r\n                  <Settings className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Settings</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuGroup>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem onClick={handleLogout}>\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                <span className=\"text-sm\">Log out</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AAEA;AACA;AASA;AACA;AACA;AA/BA;;;;;;;;;;;;AAiCO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACnE,MAAM,aAAa,UAAU;IAC7B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,4CAA4C;IAC5C,MAAM,sBAAsB,SAAS,QAAQ,CAAC,gCAC5C,CAAC,SAAS,QAAQ,CAAC,cACnB,CAAC,SAAS,KAAK,CAAC;IAElB,MAAM,eAAe;QACnB,OAAO,MAAM;IACf;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI;IACb;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,YAAY,oCACX,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,cAAW;gCACX,OAAM;;kDAEN,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;qDAG5B,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAO,WAAW,cAAc,QAAQ;gCACjD,eAAa,WAAW,SAAS;gCACjC,cAAW;gCACX,OAAO,aAAa,qBAAqB;;oCAExC,2BACC,8OAAC,oNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;6DAEzB,8OAAC,sNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAE5B,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG9B,8OAAC,oIAAA,CAAA,aAAU;;;;;0CAEX,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,OAAO;0CAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,mMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,sIAAA,CAAA,eAAY;;;;;;;;;;8BAGf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,cAAW;;8CAEX,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAA6H;;;;;;;;;;;;sCAK/I,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,cAAW;;0DAEX,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDACV,KAAI;wDACJ,KAAK,MAAM,QAAQ;;;;;;kEAErB,8OAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,MAAM,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAGxC,8OAAC;gDAAK,WAAU;0DACb,MAAM,QAAQ;;;;;;0DAEjB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG3B,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,8OAAC,4IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ;;;;;;kEAEjB,8OAAC;wDAAE,WAAU;kEACV,MAAM,SAAS;;;;;;;;;;;;;;;;;sDAItB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,oBAAiB;;8DAChB,8OAAC,4IAAA,CAAA,mBAAgB;;sEACf,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC,4IAAA,CAAA,mBAAgB;;sEACf,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAG9B,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C", "debugId": null}}, {"offset": {"line": 4165, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn(\"hover:text-foreground transition-colors\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn(\"text-foreground font-normal\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"[&>svg]:size-3.5\", className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,8OAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,8OAAC,sNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 4297, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/breadcrumbs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  B<PERSON><PERSON>rumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from \"@/components/ui/breadcrumb\";\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\n\r\nexport function Breadcrumbs() {\r\n  const pathname = usePathname();\r\n\r\n  // Get the current page name from the pathname\r\n  const getPageName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length === 0) return \"Dashboard\";\r\n\r\n    // Get the last segment of the path and capitalize it\r\n    const lastSegment = path[path.length - 1];\r\n    return (\r\n      lastSegment.charAt(0).toUpperCase() +\r\n      lastSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  // Get the parent path for breadcrumb\r\n  const getParentPath = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Remove the last segment to get the parent path\r\n    return \"/\" + path.slice(0, path.length - 1).join(\"/\");\r\n  };\r\n\r\n  // Get the parent name for breadcrumb\r\n  const getParentName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Get the second-to-last segment and capitalize it\r\n    const parentSegment = path[path.length - 2];\r\n    return (\r\n      parentSegment.charAt(0).toUpperCase() +\r\n      parentSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  const parentPath = getParentPath();\r\n  const parentName = getParentName();\r\n  const pageName = getPageName();\r\n\r\n  return (\r\n    <div className=\"mb-4\">\r\n      <Breadcrumb>\r\n        <BreadcrumbList>\r\n          {parentPath && parentName && (\r\n            <>\r\n              <BreadcrumbItem className=\"hidden md:block\">\r\n                <BreadcrumbLink asChild>\r\n                  <NavigationLink\r\n                    href={parentPath}\r\n                    className=\"text-sm font-medium text-muted-foreground hover:text-foreground\"\r\n                  >\r\n                    {parentName}\r\n                  </NavigationLink>\r\n                </BreadcrumbLink>\r\n              </BreadcrumbItem>\r\n              <BreadcrumbSeparator className=\"hidden md:block h-3 w-3\" />\r\n            </>\r\n          )}\r\n          <BreadcrumbItem>\r\n            <BreadcrumbPage className=\"text-sm font-semibold\">\r\n              {pageName}\r\n            </BreadcrumbPage>\r\n          </BreadcrumbItem>\r\n        </BreadcrumbList>\r\n      </Breadcrumb>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AAXA;;;;;AAaO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,8CAA8C;IAC9C,MAAM,cAAc;QAClB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;QAE9B,qDAAqD;QACrD,MAAM,cAAc,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzC,OACE,YAAY,MAAM,CAAC,GAAG,WAAW,KACjC,YAAY,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEvC;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,iDAAiD;QACjD,OAAO,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC;IACnD;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,mDAAmD;QACnD,MAAM,gBAAgB,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC3C,OACE,cAAc,MAAM,CAAC,GAAG,WAAW,KACnC,cAAc,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEzC;IAEA,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,WAAW;IAEjB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;sBACT,cAAA,8OAAC,sIAAA,CAAA,iBAAc;;oBACZ,cAAc,4BACb;;0CACE,8OAAC,sIAAA,CAAA,iBAAc;gCAAC,WAAU;0CACxB,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,8IAAA,CAAA,iBAAc;wCACb,MAAM;wCACN,WAAU;kDAET;;;;;;;;;;;;;;;;0CAIP,8OAAC,sIAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;;;kCAGnC,8OAAC,sIAAA,CAAA,iBAAc;kCACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 4413, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/main-layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { ReactNode, Suspense, useEffect } from \"react\";\r\nimport { RoleGuard } from \"./role-guard\";\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\nimport { SidebarProvider, useSidebar } from \"@/components/ui/sidebar\";\r\nimport { AppSidebar } from \"@/components/app-sidebar\";\r\nimport { TopNavigation } from \"@/components/top-navigation\";\r\nimport { Breadcrumbs } from \"@/components/breadcrumbs\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\";\r\n\r\ninterface MainLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function MainLayout({ children }: MainLayoutProps) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Use useEffect for redirects to avoid hydration issues\r\n  useEffect(() => {\r\n    // Only show loading on the very first render if:\r\n    // 1. Auth is not initialized yet\r\n    // 2. We haven't shown the loading screen before in this session\r\n    if (!isInitialized && !hasShownLoading.main) {\r\n      setLoading(\"main\", true);\r\n    } else {\r\n      setLoading(\"main\", false);\r\n    }\r\n\r\n    if (!isInitialized) {\r\n      return; // Wait until auth state is initialized\r\n    }\r\n\r\n    // After auth state is initialized, we can hide the loading screen\r\n    setLoading(\"main\", false);\r\n\r\n    // If we have an access token, mark that we've shown loading\r\n    // This prevents showing loading on subsequent navigations\r\n    if (accessToken) {\r\n      markLoadingShown(\"main\");\r\n    }\r\n\r\n    // We're disabling client-side redirects to avoid redirect loops\r\n    // The middleware will handle redirects instead\r\n    if (!accessToken) {\r\n      // We're not redirecting here anymore\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [accessToken, isInitialized, hasShownLoading.main]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in loading state\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (isLoading.main) {\r\n        setLoading(\"main\", false);\r\n        markLoadingShown(\"main\");\r\n      }\r\n    }, 1500); // 1.5 second timeout for better UX\r\n\r\n    return () => clearTimeout(timeoutId);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.main]);\r\n\r\n  // Don't render anything if not authenticated\r\n  if (!accessToken) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <SidebarProvider>\r\n      <RoleGuard>\r\n        <MainLayoutContent>{children}</MainLayoutContent>\r\n      </RoleGuard>\r\n    </SidebarProvider>\r\n  );\r\n}\r\n\r\nfunction MainLayoutContent({ children }: { children: ReactNode }) {\r\n  const { state, openMobile, setOpenMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\";\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-screen w-full bg-gray-50\">\r\n      <TopNavigation />\r\n      <div className=\"flex h-[calc(100vh-3.5rem)] overflow-hidden\">\r\n        {/* Desktop sidebar */}\r\n        <aside\r\n          className={cn(\r\n            \"border-r bg-white transition-all duration-300 shrink-0 hidden md:block\",\r\n            isCollapsed ? \"w-16\" : \"w-64\"\r\n          )}\r\n        >\r\n          <AppSidebar />\r\n        </aside>\r\n\r\n        {/* Mobile sidebar */}\r\n        <div className=\"md:hidden\">\r\n          <Sheet open={openMobile} onOpenChange={setOpenMobile}>\r\n            <SheetContent\r\n              side=\"left\"\r\n              className=\"p-0 w-[280px] border-r bg-white\"\r\n            >\r\n              <div className=\"h-full overflow-y-auto\">\r\n                <AppSidebar />\r\n              </div>\r\n            </SheetContent>\r\n          </Sheet>\r\n        </div>\r\n\r\n        <main className=\"flex flex-col w-full overflow-y-auto overflow-x-hidden\">\r\n          <div className=\"p-6\">\r\n            <Suspense fallback={<div>Loading content...</div>}>\r\n              <Breadcrumbs />\r\n              {children}\r\n            </Suspense>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;AAoBO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD;IAEX,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iDAAiD;QACjD,iCAAiC;QACjC,gEAAgE;QAChE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;YAC3C,WAAW,QAAQ;QACrB,OAAO;YACL,WAAW,QAAQ;QACrB;QAEA,IAAI,CAAC,eAAe;YAClB,QAAQ,uCAAuC;QACjD;QAEA,kEAAkE;QAClE,WAAW,QAAQ;QAEnB,4DAA4D;QAC5D,0DAA0D;QAC1D,IAAI,aAAa;YACf,iBAAiB;QACnB;QAEA,gEAAgE;QAChE,+CAA+C;QAC/C,IAAI,CAAC,aAAa;QAChB,qCAAqC;QACvC;IACA,uDAAuD;IACzD,GAAG;QAAC;QAAa;QAAe,gBAAgB,IAAI;KAAC;IAErD,iEAAiE;IACjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,UAAU,IAAI,EAAE;gBAClB,WAAW,QAAQ;gBACnB,iBAAiB;YACnB;QACF,GAAG,OAAO,mCAAmC;QAE7C,OAAO,IAAM,aAAa;IAC1B,uDAAuD;IACzD,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6CAA6C;IAC7C,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,qBACE,8OAAC,mIAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,8IAAA,CAAA,YAAS;sBACR,cAAA,8OAAC;0BAAmB;;;;;;;;;;;;;;;;AAI5B;AAEA,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;IAC9D,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACtD,MAAM,cAAc,UAAU;IAE9B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA,cAAc,SAAS;kCAGzB,cAAA,8OAAC,oIAAA,CAAA,aAAU;;;;;;;;;;kCAIb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAY,cAAc;sCACrC,cAAA,8OAAC,iIAAA,CAAA,eAAY;gCACX,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnB,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gCAAC,wBAAU,8OAAC;8CAAI;;;;;;;kDACvB,8OAAC,iIAAA,CAAA,cAAW;;;;;oCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 4625, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4743, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4840, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        success:\r\n          \"border-transparent bg-green-500 text-white [a&]:hover:bg-green-600 focus-visible:ring-green-500/20 dark:focus-visible:ring-green-500/40\",\r\n        warning:\r\n          \"border-transparent bg-yellow-500 text-white [a&]:hover:bg-yellow-600 focus-visible:ring-yellow-500/20 dark:focus-visible:ring-yellow-500/40\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4888, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/types/banking.ts"], "sourcesContent": ["/**\r\n * Banking Types\r\n */\r\n\r\n/**\r\n * Banking method constants\r\n */\r\nexport const BANKING_METHODS = {\r\n  BANK: \"bank\",\r\n  AGENT: \"agent\",\r\n  MPESA: \"mpesa\",\r\n} as const;\r\n\r\nexport type BankingMethod =\r\n  (typeof BANKING_METHODS)[keyof typeof BANKING_METHODS];\r\n\r\n/**\r\n * Banking status constants\r\n */\r\nexport const BANKING_STATUSES = {\r\n  PENDING: \"pending\",\r\n  COMPLETED: \"completed\",\r\n  FAILED: \"failed\",\r\n  RECONCILED: \"reconciled\",\r\n} as const;\r\n\r\nexport type BankingStatus =\r\n  (typeof BANKING_STATUSES)[keyof typeof BANKING_STATUSES];\r\n\r\n/**\r\n * Banking receipt entity\r\n */\r\nexport interface BankingReceipt {\r\n  id: number;\r\n  banking_transaction_id: number;\r\n  file_name: string;\r\n  file_path: string;\r\n  file_type: string;\r\n  file_size: number;\r\n  created_by: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at: string | null;\r\n  // Relationships\r\n  Creator?: {\r\n    id: number;\r\n    name: string;\r\n    email: string;\r\n  };\r\n}\r\n\r\n/**\r\n * Banking entity\r\n */\r\nexport interface Banking {\r\n  id: number;\r\n  tenant_id: number;\r\n  branch_id: number;\r\n  user_id: number;\r\n  transaction_date: string;\r\n  amount: number | string;\r\n  banking_method: BankingMethod;\r\n  reference_number: string;\r\n  status: BankingStatus;\r\n  expected_amount: number | string | null;\r\n  discrepancy: number | string | null;\r\n  notes: string | null;\r\n  // Approval fields\r\n  approved_by?: number | null;\r\n  approval_date?: string | null;\r\n  rejection_reason?: string | null;\r\n  // Metadata\r\n  created_by: number;\r\n  deleted_at: string | null;\r\n  last_updated_by: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n  // Relationships\r\n  Branch?: {\r\n    id: number;\r\n    name: string;\r\n    location: string;\r\n  };\r\n  User?: {\r\n    id: number;\r\n    name: string;\r\n    email: string;\r\n  };\r\n  Tenant?: {\r\n    id: number;\r\n    name: string;\r\n  };\r\n  Creator?: {\r\n    id: number;\r\n    name: string;\r\n    email: string;\r\n  };\r\n  receipts?: BankingReceipt[];\r\n}\r\n\r\n/**\r\n * Banking summary\r\n */\r\nexport interface BankingSummary {\r\n  date: string;\r\n  bank: number;\r\n  agent: number;\r\n  mpesa: number;\r\n  total: number;\r\n  transaction_count: number;\r\n}\r\n\r\n/**\r\n * Banking filters\r\n */\r\nexport interface BankingFilters {\r\n  tenant_id?: number;\r\n  branch_id?: number;\r\n  user_id?: number;\r\n  banking_method?: BankingMethod;\r\n  status?: BankingStatus;\r\n  start_date?: string;\r\n  end_date?: string;\r\n  reference_number?: string;\r\n  page?: number;\r\n  limit?: number;\r\n}\r\n\r\n/**\r\n * Banking summary filters\r\n */\r\nexport interface BankingSummaryFilters {\r\n  branch_id?: number;\r\n  region_id?: number;\r\n  user_id?: number;\r\n  start_date: string;\r\n  end_date?: string;\r\n}\r\n\r\n/**\r\n * Create banking request\r\n */\r\nexport interface CreateBankingRequest {\r\n  branch_id: number;\r\n  user_id: number;\r\n  transaction_date: string;\r\n  amount: number;\r\n  banking_method: BankingMethod;\r\n  reference_number: string;\r\n  status: BankingStatus;\r\n  expected_amount: number;\r\n  notes?: string;\r\n}\r\n\r\n/**\r\n * Update banking request\r\n */\r\nexport interface UpdateBankingRequest {\r\n  branch_id?: number;\r\n  user_id?: number;\r\n  transaction_date?: string;\r\n  amount?: number;\r\n  banking_method?: BankingMethod;\r\n  reference_number?: string;\r\n  status?: BankingStatus;\r\n  expected_amount?: number;\r\n  notes?: string;\r\n}\r\n\r\n/**\r\n * Paginated banking response\r\n */\r\nexport interface PaginatedBankingResponse {\r\n  data: Banking[];\r\n  meta: {\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n    total_pages: number;\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;CAEC;;;;AACM,MAAM,kBAAkB;IAC7B,MAAM;IACN,OAAO;IACP,OAAO;AACT;AAQO,MAAM,mBAAmB;IAC9B,SAAS;IACT,WAAW;IACX,QAAQ;IACR,YAAY;AACd", "debugId": null}}, {"offset": {"line": 4913, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/banking/api/banking-service.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport apiClient from \"@/lib/api-client\";\r\nimport {\r\n  Banking,\r\n  BankingFilters,\r\n  BankingSummary,\r\n  BankingSummaryFilters,\r\n  CreateBankingRequest,\r\n  PaginatedBankingResponse,\r\n  UpdateBankingRequest,\r\n} from \"@/types\";\r\n\r\n/**\r\n * Banking Service\r\n * Handles API calls for banking operations\r\n */\r\nconst bankingService = {\r\n  /**\r\n   * Get all banking records with optional filters\r\n   */\r\n  getBankingRecords: async (\r\n    filters?: BankingFilters\r\n  ): Promise<PaginatedBankingResponse> => {\r\n    const response = await apiClient.get<PaginatedBankingResponse>(\"/banking\", {\r\n      params: filters,\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get a banking record by ID\r\n   */\r\n  getBankingRecordById: async (id: number): Promise<Banking> => {\r\n    const response = await apiClient.get<Banking>(`/banking/${id}`);\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Create a new banking record with required receipt image\r\n   */\r\n  createBankingRecord: async (\r\n    record: CreateBankingRequest,\r\n    receiptImage?: File\r\n  ): Promise<Banking> => {\r\n    if (!receiptImage) {\r\n      throw new Error(\"Receipt image is required for banking transactions\");\r\n    }\r\n\r\n    const formData = new FormData();\r\n\r\n    // Add banking record data to form data\r\n    Object.entries(record).forEach(([key, value]) => {\r\n      if (value !== undefined && value !== null) {\r\n        formData.append(key, value.toString());\r\n      }\r\n    });\r\n\r\n    // Add receipt image to form data\r\n    formData.append(\"receipt\", receiptImage);\r\n\r\n    const response = await apiClient.post<Banking>(\"/banking\", formData, {\r\n      headers: {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      },\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Update a banking record\r\n   */\r\n  updateBankingRecord: async (\r\n    id: number,\r\n    record: UpdateBankingRequest\r\n  ): Promise<Banking> => {\r\n    const response = await apiClient.put<Banking>(`/banking/${id}`, record);\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get banking summary\r\n   */\r\n  getBankingSummary: async (\r\n    filters: BankingSummaryFilters\r\n  ): Promise<BankingSummary[]> => {\r\n    const response = await apiClient.get<BankingSummary[]>(\"/banking/summary\", {\r\n      params: filters,\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get banking records and process them into a summary format\r\n   * This is an alternative to getBankingSummary that doesn't require branch_id\r\n   */\r\n  getBankingRecordsAsSummary: async (\r\n    filters: BankingFilters\r\n  ): Promise<BankingSummary[]> => {\r\n    // Get raw banking records\r\n    const response = await bankingService.getBankingRecords({\r\n      ...filters,\r\n      limit: 1000, // Get a large number of records to ensure we have enough data for the summary\r\n    });\r\n\r\n    // Check if we have data\r\n    if (!response.data || response.data.length === 0) {\r\n      return [];\r\n    }\r\n\r\n    // Process the records into a summary format\r\n    return processBankingRecordsToSummary(response.data);\r\n  },\r\n\r\n  /**\r\n   * Upload a receipt image for a banking record\r\n   */\r\n  uploadReceiptImage: async (\r\n    id: number,\r\n    file: File\r\n  ): Promise<{ id: number; file_path: string; file_name: string }> => {\r\n    const formData = new FormData();\r\n    formData.append(\"receipt\", file);\r\n\r\n    const response = await apiClient.post<{ id: number; file_path: string; file_name: string }>(\r\n      `/banking/${id}/receipts`,\r\n      formData,\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n      }\r\n    );\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get receipt image URL\r\n   */\r\n  getReceiptImageUrl: (id: number, receiptId: string | number): string => {\r\n    // Use the file_path directly if it's a string (which means it's a file path)\r\n    if (typeof receiptId === 'string' && receiptId.includes('.')) {\r\n      // Get the base URL from the environment variable\r\n      const baseUrl = process.env.NEXT_PUBLIC_API_URL?.replace('/api/v1', '') || '';\r\n      return `${baseUrl}/uploads/receipts/${receiptId}`;\r\n    }\r\n\r\n    // Otherwise, use the API endpoint\r\n    return `${process.env.NEXT_PUBLIC_API_URL || \"/api/v1\"}/banking/${id}/receipts/${receiptId}`;\r\n  },\r\n\r\n  /**\r\n   * Delete a receipt image\r\n   */\r\n  deleteReceiptImage: async (\r\n    id: number,\r\n    receiptId: number\r\n  ): Promise<{ message: string }> => {\r\n    const response = await apiClient.delete<{ message: string }>(\r\n      `/banking/${id}/receipts/${receiptId}`\r\n    );\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Approve a banking transaction\r\n   */\r\n  approveBankingTransaction: async (id: number): Promise<Banking> => {\r\n    const response = await apiClient.post<Banking>(`/banking/${id}/approve`);\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Reject a banking transaction\r\n   */\r\n  rejectBankingTransaction: async (id: number, rejectionReason: string): Promise<Banking> => {\r\n    const response = await apiClient.post<Banking>(`/banking/${id}/reject`, {\r\n      rejection_reason: rejectionReason,\r\n    });\r\n    return response;\r\n  },\r\n};\r\n\r\n/**\r\n * Process banking records into a summary format\r\n * Groups records by date and banking method\r\n */\r\nfunction processBankingRecordsToSummary(records: Banking[]): BankingSummary[] {\r\n  // Group records by date\r\n  const groupedByDate = new Map<string, Banking[]>();\r\n\r\n  records.forEach((record) => {\r\n    // Format the date to YYYY-MM-DD\r\n    // Handle both ISO date format and plain date format\r\n    const dateKey = record.transaction_date.includes(\"T\")\r\n      ? record.transaction_date.split(\"T\")[0]\r\n      : record.transaction_date;\r\n\r\n    if (!groupedByDate.has(dateKey)) {\r\n      groupedByDate.set(dateKey, []);\r\n    }\r\n\r\n    groupedByDate.get(dateKey)!.push(record);\r\n  });\r\n\r\n  // Convert grouped records to summary format\r\n  const summaryData: BankingSummary[] = [];\r\n\r\n  groupedByDate.forEach((dateRecords, date) => {\r\n    // Initialize counters for each banking method\r\n    let bankTotal = 0;\r\n    let mpesaTotal = 0;\r\n    let agentTotal = 0;\r\n\r\n    // Process records for this date\r\n    dateRecords.forEach((record) => {\r\n      // Parse amount as a number (API returns it as a string)\r\n      const amount =\r\n        typeof record.amount === \"string\"\r\n          ? parseFloat(record.amount)\r\n          : record.amount || 0;\r\n\r\n      // Add to the appropriate total based on banking method\r\n      switch (record.banking_method) {\r\n        case \"bank\":\r\n          bankTotal += amount;\r\n          break;\r\n        case \"mpesa\":\r\n          mpesaTotal += amount;\r\n          break;\r\n        case \"agent\":\r\n          agentTotal += amount;\r\n          break;\r\n      }\r\n    });\r\n\r\n    // Create summary entry for this date\r\n    summaryData.push({\r\n      date,\r\n      bank: bankTotal,\r\n      mpesa: mpesaTotal,\r\n      agent: agentTotal,\r\n      total: bankTotal + mpesaTotal + agentTotal,\r\n      transaction_count: dateRecords.length,\r\n    });\r\n  });\r\n\r\n  // Sort by date (newest first)\r\n  summaryData.sort((a, b) => {\r\n    return new Date(b.date).getTime() - new Date(a.date).getTime();\r\n  });\r\n\r\n  return summaryData;\r\n}\r\n\r\nexport default bankingService;\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAaA;;;CAGC,GACD,MAAM,iBAAiB;IACrB;;GAEC,GACD,mBAAmB,OACjB;QAEA,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAA2B,YAAY;YACzE,QAAQ;QACV;QACA,OAAO;IACT;IAEA;;GAEC,GACD,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAU,CAAC,SAAS,EAAE,IAAI;QAC9D,OAAO;IACT;IAEA;;GAEC,GACD,qBAAqB,OACnB,QACA;QAEA,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QAErB,uCAAuC;QACvC,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,SAAS,MAAM,CAAC,KAAK,MAAM,QAAQ;YACrC;QACF;QAEA,iCAAiC;QACjC,SAAS,MAAM,CAAC,WAAW;QAE3B,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAU,YAAY,UAAU;YACnE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO;IACT;IAEA;;GAEC,GACD,qBAAqB,OACnB,IACA;QAEA,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAU,CAAC,SAAS,EAAE,IAAI,EAAE;QAChE,OAAO;IACT;IAEA;;GAEC,GACD,mBAAmB,OACjB;QAEA,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAmB,oBAAoB;YACzE,QAAQ;QACV;QACA,OAAO;IACT;IAEA;;;GAGC,GACD,4BAA4B,OAC1B;QAEA,0BAA0B;QAC1B,MAAM,WAAW,MAAM,eAAe,iBAAiB,CAAC;YACtD,GAAG,OAAO;YACV,OAAO;QACT;QAEA,wBAAwB;QACxB,IAAI,CAAC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,GAAG;YAChD,OAAO,EAAE;QACX;QAEA,4CAA4C;QAC5C,OAAO,+BAA+B,SAAS,IAAI;IACrD;IAEA;;GAEC,GACD,oBAAoB,OAClB,IACA;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,WAAW;QAE3B,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CACnC,CAAC,SAAS,EAAE,GAAG,SAAS,CAAC,EACzB,UACA;YACE,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEF,OAAO;IACT;IAEA;;GAEC,GACD,oBAAoB,CAAC,IAAY;QAC/B,6EAA6E;QAC7E,IAAI,OAAO,cAAc,YAAY,UAAU,QAAQ,CAAC,MAAM;YAC5D,iDAAiD;YACjD,MAAM,UAAU,kEAAiC,QAAQ,WAAW,OAAO;YAC3E,OAAO,GAAG,QAAQ,kBAAkB,EAAE,WAAW;QACnD;QAEA,kCAAkC;QAClC,OAAO,GAAG,oEAAmC,UAAU,SAAS,EAAE,GAAG,UAAU,EAAE,WAAW;IAC9F;IAEA;;GAEC,GACD,oBAAoB,OAClB,IACA;QAEA,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,MAAM,CACrC,CAAC,SAAS,EAAE,GAAG,UAAU,EAAE,WAAW;QAExC,OAAO;IACT;IAEA;;GAEC,GACD,2BAA2B,OAAO;QAChC,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAU,CAAC,SAAS,EAAE,GAAG,QAAQ,CAAC;QACvE,OAAO;IACT;IAEA;;GAEC,GACD,0BAA0B,OAAO,IAAY;QAC3C,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAU,CAAC,SAAS,EAAE,GAAG,OAAO,CAAC,EAAE;YACtE,kBAAkB;QACpB;QACA,OAAO;IACT;AACF;AAEA;;;CAGC,GACD,SAAS,+BAA+B,OAAkB;IACxD,wBAAwB;IACxB,MAAM,gBAAgB,IAAI;IAE1B,QAAQ,OAAO,CAAC,CAAC;QACf,gCAAgC;QAChC,oDAAoD;QACpD,MAAM,UAAU,OAAO,gBAAgB,CAAC,QAAQ,CAAC,OAC7C,OAAO,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GACrC,OAAO,gBAAgB;QAE3B,IAAI,CAAC,cAAc,GAAG,CAAC,UAAU;YAC/B,cAAc,GAAG,CAAC,SAAS,EAAE;QAC/B;QAEA,cAAc,GAAG,CAAC,SAAU,IAAI,CAAC;IACnC;IAEA,4CAA4C;IAC5C,MAAM,cAAgC,EAAE;IAExC,cAAc,OAAO,CAAC,CAAC,aAAa;QAClC,8CAA8C;QAC9C,IAAI,YAAY;QAChB,IAAI,aAAa;QACjB,IAAI,aAAa;QAEjB,gCAAgC;QAChC,YAAY,OAAO,CAAC,CAAC;YACnB,wDAAwD;YACxD,MAAM,SACJ,OAAO,OAAO,MAAM,KAAK,WACrB,WAAW,OAAO,MAAM,IACxB,OAAO,MAAM,IAAI;YAEvB,uDAAuD;YACvD,OAAQ,OAAO,cAAc;gBAC3B,KAAK;oBACH,aAAa;oBACb;gBACF,KAAK;oBACH,cAAc;oBACd;gBACF,KAAK;oBACH,cAAc;oBACd;YACJ;QACF;QAEA,qCAAqC;QACrC,YAAY,IAAI,CAAC;YACf;YACA,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO,YAAY,aAAa;YAChC,mBAAmB,YAAY,MAAM;QACvC;IACF;IAEA,8BAA8B;IAC9B,YAAY,IAAI,CAAC,CAAC,GAAG;QACnB,OAAO,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;IAC9D;IAEA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 5096, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/banking/hooks/use-banking.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  BankingFilters,\r\n  BankingSummaryFilters,\r\n  CreateBankingRequest,\r\n  UpdateBankingRequest,\r\n} from \"@/types\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport bankingService from \"../api/banking-service\";\r\n\r\n/**\r\n * Hook to fetch all banking records with optional filters\r\n */\r\nexport const useBankingRecords = (filters?: BankingFilters) => {\r\n  return useQuery({\r\n    queryKey: [\"banking\", filters],\r\n    queryFn: () => bankingService.getBankingRecords(filters),\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch a single banking record by ID\r\n */\r\nexport const useBankingRecord = (id: number) => {\r\n  return useQuery({\r\n    queryKey: [\"banking\", id],\r\n    queryFn: () => bankingService.getBankingRecordById(id),\r\n    enabled: !!id,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to create a new banking record with required receipt image\r\n */\r\nexport const useCreateBankingRecord = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: ({ data, receiptImage }: { data: CreateBankingRequest; receiptImage: File }) =>\r\n      bankingService.createBankingRecord(data, receiptImage),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"banking\"] });\r\n      toast.success(\"Banking record created successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(error.message || \"Failed to create banking record\");\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to update a banking record\r\n */\r\nexport const useUpdateBankingRecord = (id: number) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: UpdateBankingRequest) =>\r\n      bankingService.updateBankingRecord(id, data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"banking\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"banking\", id] });\r\n      toast.success(\"Banking record updated successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(error.message || \"Failed to update banking record\");\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch banking summary\r\n */\r\nexport const useBankingSummary = (\r\n  filters: BankingSummaryFilters & { banking_method?: string },\r\n  filtersApplied: boolean = false\r\n) => {\r\n  // Convert BankingSummaryFilters to BankingFilters\r\n  const bankingFilters: BankingFilters = {\r\n    start_date: filters.start_date,\r\n    end_date: filters.end_date,\r\n    branch_id: filters.branch_id,\r\n    region_id: filters.region_id,\r\n    user_id: filters.user_id,\r\n    banking_method: filters.banking_method as any,\r\n  };\r\n\r\n  return useQuery({\r\n    queryKey: [\"banking-summary\", filters],\r\n    queryFn: () => {\r\n      // If branch_id is provided and valid, use the summary endpoint\r\n      if (filters.branch_id && filters.branch_id > 0) {\r\n        return bankingService.getBankingSummary(filters);\r\n      }\r\n\r\n      // Otherwise, use the regular banking endpoint and process the data\r\n      return bankingService.getBankingRecordsAsSummary(bankingFilters);\r\n    },\r\n    enabled: filtersApplied && !!filters.start_date, // Only enable when filters are applied and start_date is set\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to upload a receipt image for a banking record\r\n */\r\nexport const useUploadReceiptImage = (id: number) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (file: File) => bankingService.uploadReceiptImage(id, file),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"banking\", id] });\r\n      toast.success(\"Receipt image uploaded successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(error.message || \"Failed to upload receipt image\");\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to approve a banking transaction\r\n */\r\nexport const useApproveBankingTransaction = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => bankingService.approveBankingTransaction(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"banking\"] });\r\n      toast.success(\"Banking transaction approved successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(error.message || \"Failed to approve banking transaction\");\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to reject a banking transaction\r\n */\r\nexport const useRejectBankingTransaction = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: ({ id, rejectionReason }: { id: number; rejectionReason: string }) =>\r\n      bankingService.rejectBankingTransaction(id, rejectionReason),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"banking\"] });\r\n      toast.success(\"Banking transaction rejected successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(error.message || \"Failed to reject banking transaction\");\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to delete a receipt image\r\n */\r\nexport const useDeleteReceiptImage = (id: number) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (receiptId: number) => bankingService.deleteReceiptImage(id, receiptId),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"banking\", id] });\r\n      toast.success(\"Receipt image deleted successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(error.message || \"Failed to delete receipt image\");\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAQA;AAAA;AAAA;AACA;AACA;AAVA;;;;AAeO,MAAM,oBAAoB,CAAC;IAChC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAQ;QAC9B,SAAS,IAAM,uJAAA,CAAA,UAAc,CAAC,iBAAiB,CAAC;IAClD;AACF;AAKO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAG;QACzB,SAAS,IAAM,uJAAA,CAAA,UAAc,CAAC,oBAAoB,CAAC;QACnD,SAAS,CAAC,CAAC;IACb;AACF;AAKO,MAAM,yBAAyB;IACpC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,EAAE,IAAI,EAAE,YAAY,EAAsD,GACrF,uJAAA,CAAA,UAAc,CAAC,mBAAmB,CAAC,MAAM;QAC3C,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;YACtD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF;AACF;AAKO,MAAM,yBAAyB,CAAC;IACrC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OACX,uJAAA,CAAA,UAAc,CAAC,mBAAmB,CAAC,IAAI;QACzC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;YACtD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW;iBAAG;YAAC;YAC1D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF;AACF;AAKO,MAAM,oBAAoB,CAC/B,SACA,iBAA0B,KAAK;IAE/B,kDAAkD;IAClD,MAAM,iBAAiC;QACrC,YAAY,QAAQ,UAAU;QAC9B,UAAU,QAAQ,QAAQ;QAC1B,WAAW,QAAQ,SAAS;QAC5B,WAAW,QAAQ,SAAS;QAC5B,SAAS,QAAQ,OAAO;QACxB,gBAAgB,QAAQ,cAAc;IACxC;IAEA,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAmB;SAAQ;QACtC,SAAS;YACP,+DAA+D;YAC/D,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,GAAG,GAAG;gBAC9C,OAAO,uJAAA,CAAA,UAAc,CAAC,iBAAiB,CAAC;YAC1C;YAEA,mEAAmE;YACnE,OAAO,uJAAA,CAAA,UAAc,CAAC,0BAA0B,CAAC;QACnD;QACA,SAAS,kBAAkB,CAAC,CAAC,QAAQ,UAAU;IACjD;AACF;AAKO,MAAM,wBAAwB,CAAC;IACpC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAAe,uJAAA,CAAA,UAAc,CAAC,kBAAkB,CAAC,IAAI;QAClE,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW;iBAAG;YAAC;YAC1D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF;AACF;AAKO,MAAM,+BAA+B;IAC1C,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,KAAe,uJAAA,CAAA,UAAc,CAAC,yBAAyB,CAAC;QACrE,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;YACtD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF;AACF;AAKO,MAAM,8BAA8B;IACzC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,EAAE,EAAE,EAAE,eAAe,EAA2C,GAC3E,uJAAA,CAAA,UAAc,CAAC,wBAAwB,CAAC,IAAI;QAC9C,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;YACtD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF;AACF;AAKO,MAAM,wBAAwB,CAAC;IACpC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,YAAsB,uJAAA,CAAA,UAAc,CAAC,kBAAkB,CAAC,IAAI;QACzE,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW;iBAAG;YAAC;YAC1D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF;AACF", "debugId": null}}, {"offset": {"line": 5277, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\r\nimport { DayPicker } from \"react-day-picker\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DayPicker>) {\r\n  return (\r\n    <DayPicker\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn(\"p-3\", className)}\r\n      classNames={{\r\n        months: \"flex flex-col sm:flex-row gap-2\",\r\n        month: \"flex flex-col gap-4\",\r\n        caption: \"flex justify-center pt-1 relative items-center w-full\",\r\n        caption_label: \"text-sm font-medium\",\r\n        nav: \"flex items-center gap-1\",\r\n        nav_button: cn(\r\n          buttonVariants({ variant: \"outline\" }),\r\n          \"size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n        ),\r\n        nav_button_previous: \"absolute left-1\",\r\n        nav_button_next: \"absolute right-1\",\r\n        table: \"w-full border-collapse space-x-1\",\r\n        head_row: \"flex\",\r\n        head_cell:\r\n          \"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]\",\r\n        row: \"flex w-full mt-2\",\r\n        cell: cn(\r\n          \"relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md\",\r\n          props.mode === \"range\"\r\n            ? \"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md\"\r\n            : \"[&:has([aria-selected])]:rounded-md\"\r\n        ),\r\n        day: cn(\r\n          buttonVariants({ variant: \"ghost\" }),\r\n          \"size-8 p-0 font-normal aria-selected:opacity-100\"\r\n        ),\r\n        day_range_start:\r\n          \"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_range_end:\r\n          \"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_selected:\r\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\r\n        day_today: \"bg-accent text-accent-foreground\",\r\n        day_outside:\r\n          \"day-outside text-muted-foreground aria-selected:text-muted-foreground\",\r\n        day_disabled: \"text-muted-foreground opacity-50\",\r\n        day_range_middle:\r\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\r\n        day_hidden: \"invisible\",\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        IconLeft: ({ className, ...props }) => (\r\n          <ChevronLeft className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n        IconRight: ({ className, ...props }) => (\r\n          <ChevronRight className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n      }}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Calendar }\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AASA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACoC;IACvC,qBACE,8OAAC,8JAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,QAAQ;YACR,OAAO;YACP,SAAS;YACT,eAAe;YACf,KAAK;YACL,YAAY,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,qBAAqB;YACrB,iBAAiB;YACjB,OAAO;YACP,UAAU;YACV,WACE;YACF,KAAK;YAC<PERSON>,MAAM,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACL,mKACA,MAAM,IAAI,KAAK,UACX,yKACA;YAEN,KAAK,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,iBACE;YACF,eACE;YACF,cACE;YACF,WAAW;YACX,aACE;YACF,cAAc;YACd,kBACE;YACF,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBAChC,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;YAE5D,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBACjC,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;QAE/D;QACC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5357, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 5426, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/date-range-picker.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { format } from \"date-fns\";\r\nimport { Calendar as CalendarIcon } from \"lucide-react\";\r\nimport { DateRange } from \"react-day-picker\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Calendar } from \"@/components/ui/calendar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\n\r\ninterface DateRangePickerProps {\r\n  value: DateRange;\r\n  onChange: (value: DateRange) => void;\r\n  className?: string;\r\n}\r\n\r\nexport function DateRangePicker({\r\n  value,\r\n  onChange,\r\n  className,\r\n}: DateRangePickerProps) {\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const checkIsMobile = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n    };\r\n\r\n    // Initial check\r\n    checkIsMobile();\r\n\r\n    // Add event listener for window resize\r\n    window.addEventListener(\"resize\", checkIsMobile);\r\n\r\n    // Cleanup\r\n    return () => window.removeEventListener(\"resize\", checkIsMobile);\r\n  }, []);\r\n  return (\r\n    <div className={cn(\"grid gap-2\", className)}>\r\n      <Popover>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            id=\"date\"\r\n            variant={\"outline\"}\r\n            className={cn(\r\n              \"w-full md:w-[250px] justify-start text-left font-normal\",\r\n              !value && \"text-muted-foreground\"\r\n            )}\r\n          >\r\n            <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n            {value?.from ? (\r\n              value.to ? (\r\n                <>\r\n                  {format(value.from, \"LLL dd, y\")} -{\" \"}\r\n                  {format(value.to, \"LLL dd, y\")}\r\n                </>\r\n              ) : (\r\n                format(value.from, \"LLL dd, y\")\r\n              )\r\n            ) : (\r\n              <span>Pick a date range</span>\r\n            )}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n          <div className=\"overflow-x-auto\">\r\n            <Calendar\r\n              initialFocus\r\n              mode=\"range\"\r\n              defaultMonth={value?.from}\r\n              selected={value}\r\n              onSelect={(range) => {\r\n                // Only call onChange if range is not undefined\r\n                if (range) {\r\n                  onChange(range);\r\n                }\r\n              }}\r\n              numberOfMonths={isMobile ? 1 : 2}\r\n            />\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAGA;AACA;AACA;AACA;AAXA;;;;;;;;;AAuBO,SAAS,gBAAgB,EAC9B,KAAK,EACL,QAAQ,EACR,SAAS,EACY;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,YAAY,OAAO,UAAU,GAAG;QAClC;QAEA,gBAAgB;QAChB;QAEA,uCAAuC;QACvC,OAAO,gBAAgB,CAAC,UAAU;QAElC,UAAU;QACV,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IACL,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;kBAC/B,cAAA,8OAAC,mIAAA,CAAA,UAAO;;8BACN,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,IAAG;wBACH,SAAS;wBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA,CAAC,SAAS;;0CAGZ,8OAAC,0MAAA,CAAA,WAAY;gCAAC,WAAU;;;;;;4BACvB,OAAO,OACN,MAAM,EAAE,iBACN;;oCACG,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EAAE;oCAAa;oCAAG;oCACnC,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,EAAE,EAAE;;+CAGpB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EAAE,6BAGrB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;8BAIZ,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAa,OAAM;8BAC3C,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4BACP,YAAY;4BACZ,MAAK;4BACL,cAAc,OAAO;4BACrB,UAAU;4BACV,UAAU,CAAC;gCACT,+CAA+C;gCAC/C,IAAI,OAAO;oCACT,SAAS;gCACX;4BACF;4BACA,gBAAgB,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C", "debugId": null}}, {"offset": {"line": 5552, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/pagination.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport {\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n  MoreHorizontalIcon,\r\n} from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button, buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Pagination({ className, ...props }: React.ComponentProps<\"nav\">) {\r\n  return (\r\n    <nav\r\n      role=\"navigation\"\r\n      aria-label=\"pagination\"\r\n      data-slot=\"pagination\"\r\n      className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"pagination-content\"\r\n      className={cn(\"flex flex-row items-center gap-1\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationItem({ ...props }: React.ComponentProps<\"li\">) {\r\n  return <li data-slot=\"pagination-item\" {...props} />\r\n}\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean\r\n} & Pick<React.ComponentProps<typeof Button>, \"size\"> &\r\n  React.ComponentProps<\"a\">\r\n\r\nfunction PaginationLink({\r\n  className,\r\n  isActive,\r\n  size = \"icon\",\r\n  ...props\r\n}: PaginationLinkProps) {\r\n  return (\r\n    <a\r\n      aria-current={isActive ? \"page\" : undefined}\r\n      data-slot=\"pagination-link\"\r\n      data-active={isActive}\r\n      className={cn(\r\n        buttonVariants({\r\n          variant: isActive ? \"outline\" : \"ghost\",\r\n          size,\r\n        }),\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationPrevious({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) {\r\n  return (\r\n    <PaginationLink\r\n      aria-label=\"Go to previous page\"\r\n      size=\"default\"\r\n      className={cn(\"gap-1 px-2.5 sm:pl-2.5\", className)}\r\n      {...props}\r\n    >\r\n      <ChevronLeftIcon />\r\n      <span className=\"hidden sm:block\">Previous</span>\r\n    </PaginationLink>\r\n  )\r\n}\r\n\r\nfunction PaginationNext({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) {\r\n  return (\r\n    <PaginationLink\r\n      aria-label=\"Go to next page\"\r\n      size=\"default\"\r\n      className={cn(\"gap-1 px-2.5 sm:pr-2.5\", className)}\r\n      {...props}\r\n    >\r\n      <span className=\"hidden sm:block\">Next</span>\r\n      <ChevronRightIcon />\r\n    </PaginationLink>\r\n  )\r\n}\r\n\r\nfunction PaginationEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      aria-hidden\r\n      data-slot=\"pagination-ellipsis\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontalIcon className=\"size-4\" />\r\n      <span className=\"sr-only\">More pages</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationLink,\r\n  PaginationItem,\r\n  PaginationPrevious,\r\n  PaginationNext,\r\n  PaginationEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AAAA;AAAA;AAMA;AACA;;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,MAAK;QACL,cAAW;QACX,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,GAAG,OAAmC;IAC9D,qBAAO,8OAAC;QAAG,aAAU;QAAmB,GAAG,KAAK;;;;;;AAClD;AAOA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,OAAO,MAAM,EACb,GAAG,OACiB;IACpB,qBACE,8OAAC;QACC,gBAAc,WAAW,SAAS;QAClC,aAAU;QACV,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YACb,SAAS,WAAW,YAAY;YAChC;QACF,IACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACyC;IAC5C,qBACE,8OAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;0BAET,8OAAC,wNAAA,CAAA,kBAAe;;;;;0BAChB,8OAAC;gBAAK,WAAU;0BAAkB;;;;;;;;;;;;AAGxC;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACyC;IAC5C,qBACE,8OAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BAAkB;;;;;;0BAClC,8OAAC,0NAAA,CAAA,mBAAgB;;;;;;;;;;;AAGvB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAW;QACX,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,8OAAC,oNAAA,CAAA,qBAAkB;gBAAC,WAAU;;;;;;0BAC9B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 5711, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 5936, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/data-pagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useMemo } from \"react\";\r\nimport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n} from \"@/components/ui/pagination\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { ChevronFirst, ChevronLast } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nexport interface DataPaginationProps {\r\n  // Required props\r\n  currentPage: number;\r\n  totalPages: number;\r\n  onPageChange: (page: number) => void;\r\n\r\n  // Optional props\r\n  pageSize?: number;\r\n  pageSizes?: number[];\r\n  onPageSizeChange?: (pageSize: number) => void;\r\n  totalItems?: number;\r\n  isLoading?: boolean;\r\n  showPageSizeSelector?: boolean;\r\n  showItemsInfo?: boolean;\r\n  showFirstLastButtons?: boolean;\r\n  maxPageButtons?: number;\r\n  className?: string;\r\n  compact?: boolean;\r\n  ariaLabel?: string;\r\n}\r\n\r\nexport function DataPagination({\r\n  currentPage,\r\n  totalPages,\r\n  onPageChange,\r\n  pageSize = 10,\r\n  pageSizes = [10, 25, 50, 100, 250, 500, 1000],\r\n  onPageSizeChange,\r\n  totalItems,\r\n  isLoading = false,\r\n  showPageSizeSelector = true,\r\n  showItemsInfo = true,\r\n  showFirstLastButtons = true,\r\n  maxPageButtons = 5,\r\n  className,\r\n  compact = false,\r\n  ariaLabel = \"Pagination\",\r\n}: DataPaginationProps) {\r\n  // Don't render pagination if there's only one page and no page size selector\r\n  if (totalPages <= 1 && (!showPageSizeSelector || !onPageSizeChange)) {\r\n    return null;\r\n  }\r\n\r\n  // Calculate the current range of items being displayed\r\n  const itemRange = useMemo(() => {\r\n    if (!totalItems) return null;\r\n\r\n    const start = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;\r\n    const end = Math.min(currentPage * pageSize, totalItems);\r\n\r\n    return { start, end };\r\n  }, [currentPage, pageSize, totalItems]);\r\n\r\n  // Generate page numbers to display\r\n  const pageNumbers = useMemo(() => {\r\n    const numbers: (number | string)[] = [];\r\n\r\n    if (totalPages <= maxPageButtons) {\r\n      // If total pages is less than or equal to maxPageButtons, show all pages\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        numbers.push(i);\r\n      }\r\n    } else {\r\n      // Always include first page\r\n      numbers.push(1);\r\n\r\n      // Calculate start and end of page numbers to show\r\n      let start = Math.max(2, currentPage - Math.floor(maxPageButtons / 2) + 1);\r\n      let end = Math.min(totalPages - 1, start + maxPageButtons - 3);\r\n\r\n      // Adjust if we're at the beginning\r\n      if (currentPage <= Math.floor(maxPageButtons / 2)) {\r\n        end = maxPageButtons - 2;\r\n        start = 2;\r\n      }\r\n\r\n      // Adjust if we're at the end\r\n      if (currentPage > totalPages - Math.floor(maxPageButtons / 2)) {\r\n        start = Math.max(2, totalPages - maxPageButtons + 2);\r\n        end = totalPages - 1;\r\n      }\r\n\r\n      // Add ellipsis before middle pages if needed\r\n      if (start > 2) {\r\n        numbers.push(\"ellipsis-start\");\r\n      }\r\n\r\n      // Add middle pages\r\n      for (let i = start; i <= end; i++) {\r\n        numbers.push(i);\r\n      }\r\n\r\n      // Add ellipsis after middle pages if needed\r\n      if (end < totalPages - 1) {\r\n        numbers.push(\"ellipsis-end\");\r\n      }\r\n\r\n      // Always include last page if more than one page\r\n      if (totalPages > 1) {\r\n        numbers.push(totalPages);\r\n      }\r\n    }\r\n\r\n    return numbers;\r\n  }, [currentPage, totalPages, maxPageButtons]);\r\n\r\n  // Handle page click with validation\r\n  const handlePageClick = (page: number, e: React.MouseEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Only trigger page change if it's not the current page and not loading\r\n    if (page !== currentPage && !isLoading) {\r\n      // Force the page to be within valid range\r\n      const validPage = Math.max(1, Math.min(page, totalPages));\r\n      onPageChange(validPage);\r\n    }\r\n  };\r\n\r\n  // Handle page size change\r\n  const handlePageSizeChange = (value: string) => {\r\n    if (onPageSizeChange) {\r\n      onPageSizeChange(parseInt(value, 10));\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex flex-col gap-4 w-full\",\r\n        compact\r\n          ? \"sm:flex-row sm:items-center sm:justify-between\"\r\n          : \"md:flex-row md:items-center md:justify-between\",\r\n        className\r\n      )}\r\n      aria-label={ariaLabel}\r\n    >\r\n      {/* Page size selector */}\r\n      {showPageSizeSelector && onPageSizeChange && (\r\n        <div className=\"flex items-center gap-2\">\r\n          <span className=\"text-sm text-muted-foreground\">Show</span>\r\n          <Select\r\n            value={pageSize.toString()}\r\n            onValueChange={handlePageSizeChange}\r\n            disabled={isLoading}\r\n          >\r\n            <SelectTrigger\r\n              className=\"h-8 w-[70px]\"\r\n              aria-label=\"Select page size\"\r\n            >\r\n              <SelectValue placeholder={pageSize.toString()} />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              {pageSizes.map((size) => (\r\n                <SelectItem key={size} value={size.toString()}>\r\n                  {size}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n          <span className=\"text-sm text-muted-foreground\">per page</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Pagination controls - Always show if we have pagination data */}\r\n      {totalPages >= 1 && (\r\n        <div className=\"bg-muted/50 rounded-md p-1\">\r\n          <Pagination>\r\n            <PaginationContent className=\"flex-wrap justify-center\">\r\n              {/* First page button */}\r\n              {showFirstLastButtons && (\r\n                <PaginationItem>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={(e) => handlePageClick(1, e)}\r\n                    disabled={currentPage === 1 || isLoading}\r\n                    className=\"h-8 w-8\"\r\n                    aria-label=\"Go to first page\"\r\n                  >\r\n                    <ChevronFirst className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </PaginationItem>\r\n              )}\r\n\r\n              {/* Previous page button */}\r\n              <PaginationItem>\r\n                <PaginationPrevious\r\n                  href=\"#\"\r\n                  onClick={(e) => handlePageClick(currentPage - 1, e)}\r\n                  aria-disabled={currentPage === 1 || isLoading}\r\n                  className={cn(\r\n                    currentPage === 1 || isLoading\r\n                      ? \"pointer-events-none opacity-50\"\r\n                      : \"\",\r\n                    \"hover:bg-primary/10 transition-colors\"\r\n                  )}\r\n                />\r\n              </PaginationItem>\r\n\r\n              {/* Page numbers */}\r\n              {!compact &&\r\n                pageNumbers.map((pageNumber, index) => {\r\n                  if (typeof pageNumber === \"string\") {\r\n                    return (\r\n                      <PaginationItem key={pageNumber}>\r\n                        <PaginationEllipsis />\r\n                      </PaginationItem>\r\n                    );\r\n                  }\r\n\r\n                  return (\r\n                    <PaginationItem key={`page-${pageNumber}`}>\r\n                      <PaginationLink\r\n                        href=\"#\"\r\n                        onClick={(e) => handlePageClick(pageNumber, e)}\r\n                        isActive={currentPage === pageNumber}\r\n                        aria-current={\r\n                          currentPage === pageNumber ? \"page\" : undefined\r\n                        }\r\n                      >\r\n                        {pageNumber}\r\n                      </PaginationLink>\r\n                    </PaginationItem>\r\n                  );\r\n                })}\r\n\r\n              {/* Compact view shows current/total instead of page numbers */}\r\n              {compact && (\r\n                <div className=\"flex items-center mx-2\">\r\n                  <span className=\"text-sm\">\r\n                    Page <span className=\"font-bold\">{currentPage}</span> of{\" \"}\r\n                    <span className=\"font-medium\">{totalPages}</span>\r\n                    {isLoading && (\r\n                      <span className=\"ml-1 text-muted-foreground\">\r\n                        (Loading...)\r\n                      </span>\r\n                    )}\r\n                  </span>\r\n                </div>\r\n              )}\r\n\r\n              {/* Next page button */}\r\n              <PaginationItem>\r\n                <PaginationNext\r\n                  href=\"#\"\r\n                  onClick={(e) => handlePageClick(currentPage + 1, e)}\r\n                  aria-disabled={currentPage === totalPages || isLoading}\r\n                  className={cn(\r\n                    currentPage === totalPages || isLoading\r\n                      ? \"pointer-events-none opacity-50\"\r\n                      : \"\",\r\n                    \"hover:bg-primary/10 transition-colors\"\r\n                  )}\r\n                />\r\n              </PaginationItem>\r\n\r\n              {/* Last page button */}\r\n              {showFirstLastButtons && (\r\n                <PaginationItem>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={(e) => handlePageClick(totalPages, e)}\r\n                    disabled={currentPage === totalPages || isLoading}\r\n                    className=\"h-8 w-8\"\r\n                    aria-label=\"Go to last page\"\r\n                  >\r\n                    <ChevronLast className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </PaginationItem>\r\n              )}\r\n            </PaginationContent>\r\n          </Pagination>\r\n        </div>\r\n      )}\r\n\r\n      {/* Items info */}\r\n      {showItemsInfo && totalItems !== undefined && (\r\n        <div className=\"text-sm text-muted-foreground whitespace-nowrap\">\r\n          {totalItems === 0 ? (\r\n            \"No items\"\r\n          ) : (\r\n            <>\r\n              Showing {itemRange?.start} to {itemRange?.end} of {totalItems}{\" \"}\r\n              items\r\n            </>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AAOA;AACA;AAAA;AACA;AArBA;;;;;;;;AA4CO,SAAS,eAAe,EAC7B,WAAW,EACX,UAAU,EACV,YAAY,EACZ,WAAW,EAAE,EACb,YAAY;IAAC;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;CAAK,EAC7C,gBAAgB,EAChB,UAAU,EACV,YAAY,KAAK,EACjB,uBAAuB,IAAI,EAC3B,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,iBAAiB,CAAC,EAClB,SAAS,EACT,UAAU,KAAK,EACf,YAAY,YAAY,EACJ;IACpB,6EAA6E;IAC7E,IAAI,cAAc,KAAK,CAAC,CAAC,wBAAwB,CAAC,gBAAgB,GAAG;QACnE,OAAO;IACT;IAEA,uDAAuD;IACvD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,IAAI,CAAC,YAAY,OAAO;QAExB,MAAM,QAAQ,eAAe,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,WAAW;QACpE,MAAM,MAAM,KAAK,GAAG,CAAC,cAAc,UAAU;QAE7C,OAAO;YAAE;YAAO;QAAI;IACtB,GAAG;QAAC;QAAa;QAAU;KAAW;IAEtC,mCAAmC;IACnC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,MAAM,UAA+B,EAAE;QAEvC,IAAI,cAAc,gBAAgB;YAChC,yEAAyE;YACzE,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,QAAQ,IAAI,CAAC;YACf;QACF,OAAO;YACL,4BAA4B;YAC5B,QAAQ,IAAI,CAAC;YAEb,kDAAkD;YAClD,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB,KAAK;YACvE,IAAI,MAAM,KAAK,GAAG,CAAC,aAAa,GAAG,QAAQ,iBAAiB;YAE5D,mCAAmC;YACnC,IAAI,eAAe,KAAK,KAAK,CAAC,iBAAiB,IAAI;gBACjD,MAAM,iBAAiB;gBACvB,QAAQ;YACV;YAEA,6BAA6B;YAC7B,IAAI,cAAc,aAAa,KAAK,KAAK,CAAC,iBAAiB,IAAI;gBAC7D,QAAQ,KAAK,GAAG,CAAC,GAAG,aAAa,iBAAiB;gBAClD,MAAM,aAAa;YACrB;YAEA,6CAA6C;YAC7C,IAAI,QAAQ,GAAG;gBACb,QAAQ,IAAI,CAAC;YACf;YAEA,mBAAmB;YACnB,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;gBACjC,QAAQ,IAAI,CAAC;YACf;YAEA,4CAA4C;YAC5C,IAAI,MAAM,aAAa,GAAG;gBACxB,QAAQ,IAAI,CAAC;YACf;YAEA,iDAAiD;YACjD,IAAI,aAAa,GAAG;gBAClB,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAa;QAAY;KAAe;IAE5C,oCAAoC;IACpC,MAAM,kBAAkB,CAAC,MAAc;QACrC,EAAE,cAAc;QAEhB,wEAAwE;QACxE,IAAI,SAAS,eAAe,CAAC,WAAW;YACtC,0CAA0C;YAC1C,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;YAC7C,aAAa;QACf;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC5B,IAAI,kBAAkB;YACpB,iBAAiB,SAAS,OAAO;QACnC;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8BACA,UACI,mDACA,kDACJ;QAEF,cAAY;;YAGX,wBAAwB,kCACvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;kCAChD,8OAAC,kIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,QAAQ;wBACxB,eAAe;wBACf,UAAU;;0CAEV,8OAAC,kIAAA,CAAA,gBAAa;gCACZ,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,aAAa,SAAS,QAAQ;;;;;;;;;;;0CAE7C,8OAAC,kIAAA,CAAA,gBAAa;0CACX,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC,kIAAA,CAAA,aAAU;wCAAY,OAAO,KAAK,QAAQ;kDACxC;uCADc;;;;;;;;;;;;;;;;kCAMvB,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;;;;;;;YAKnD,cAAc,mBACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,sIAAA,CAAA,oBAAiB;wBAAC,WAAU;;4BAE1B,sCACC,8OAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,GAAG;oCACnC,UAAU,gBAAgB,KAAK;oCAC/B,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAM9B,8OAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;oCACjB,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,cAAc,GAAG;oCACjD,iBAAe,gBAAgB,KAAK;oCACpC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBAAgB,KAAK,YACjB,mCACA,IACJ;;;;;;;;;;;4BAML,CAAC,WACA,YAAY,GAAG,CAAC,CAAC,YAAY;gCAC3B,IAAI,OAAO,eAAe,UAAU;oCAClC,qBACE,8OAAC,sIAAA,CAAA,iBAAc;kDACb,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;;;;;uCADA;;;;;gCAIzB;gCAEA,qBACE,8OAAC,sIAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;wCACb,MAAK;wCACL,SAAS,CAAC,IAAM,gBAAgB,YAAY;wCAC5C,UAAU,gBAAgB;wCAC1B,gBACE,gBAAgB,aAAa,SAAS;kDAGvC;;;;;;mCATgB,CAAC,KAAK,EAAE,YAAY;;;;;4BAa7C;4BAGD,yBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCAAU;sDACnB,8OAAC;4CAAK,WAAU;sDAAa;;;;;;wCAAmB;wCAAI;sDACzD,8OAAC;4CAAK,WAAU;sDAAe;;;;;;wCAC9B,2BACC,8OAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;;;;;;0CASrD,8OAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oCACb,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,cAAc,GAAG;oCACjD,iBAAe,gBAAgB,cAAc;oCAC7C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBAAgB,cAAc,YAC1B,mCACA,IACJ;;;;;;;;;;;4BAML,sCACC,8OAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,YAAY;oCAC5C,UAAU,gBAAgB,cAAc;oCACxC,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUpC,iBAAiB,eAAe,2BAC/B,8OAAC;gBAAI,WAAU;0BACZ,eAAe,IACd,2BAEA;;wBAAE;wBACS,WAAW;wBAAM;wBAAK,WAAW;wBAAI;wBAAK;wBAAY;wBAAI;;;;;;;;;;;;;;AAQjF", "debugId": null}}, {"offset": {"line": 6325, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/banking/components/banking-list.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { format } from \"date-fns\";\r\nimport {\r\n  ColumnDef,\r\n  ColumnFiltersState,\r\n  SortingState,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getFilteredRowModel,\r\n  getPaginationRowModel,\r\n  getSortedRowModel,\r\n  useReactTable,\r\n} from \"@tanstack/react-table\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { Banking, BankingFilters } from \"@/types\";\r\nimport { BANKING_METHODS, BANKING_STATUSES } from \"@/types/banking\";\r\nimport { useBankingRecords } from \"../hooks/use-banking\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport { Eye, Plus, Search, X } from \"lucide-react\";\r\nimport { DateRangePicker } from \"@/components/ui/date-range-picker\";\r\nimport { DateRange } from \"react-day-picker\";\r\nimport { DataPagination } from \"@/components/ui/data-pagination\";\r\n\r\nexport function BankingList() {\r\n  const router = useRouter();\r\n  const [filters, setFilters] = useState<BankingFilters>({\r\n    page: 1,\r\n    limit: 10,\r\n  });\r\n\r\n  // State for date range picker\r\n  const [dateRange, setDateRange] = useState<DateRange | undefined>();\r\n\r\n  const { data, isLoading, error } = useBankingRecords(filters);\r\n\r\n  const [sorting, setSorting] = useState<SortingState>([]);\r\n  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);\r\n  const [searchValue, setSearchValue] = useState(\"\");\r\n\r\n  // Define columns for the banking table\r\n  const columns: ColumnDef<Banking>[] = [\r\n    {\r\n      accessorKey: \"id\",\r\n      header: \"ID\",\r\n      cell: ({ row }) => <span>#{row.getValue(\"id\")}</span>,\r\n    },\r\n    {\r\n      accessorKey: \"transaction_date\",\r\n      header: \"Date\",\r\n      cell: ({ row }) =>\r\n        format(new Date(row.getValue(\"transaction_date\")), \"PPP\"),\r\n    },\r\n  \r\n    {\r\n      accessorKey: \"Branch.name\",\r\n      header: \"Branch\",\r\n      cell: ({ row }) => {\r\n        const branch = row.original.Branch;\r\n        return <span>{branch?.name || \"Unknown Branch\"}</span>;\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"banking_method\",\r\n      header: \"Method\",\r\n      cell: ({ row }) => {\r\n        const method = row.getValue(\"banking_method\") as string;\r\n        let label = \"Unknown\";\r\n        let variant = \"outline\";\r\n\r\n        switch (method) {\r\n          case BANKING_METHODS.BANK:\r\n            label = \"Bank\";\r\n            variant = \"outline\";\r\n            break;\r\n          case BANKING_METHODS.MPESA:\r\n            label = \"M-Pesa\";\r\n            variant = \"secondary\";\r\n            break;\r\n          case BANKING_METHODS.AGENT:\r\n            label = \"Agent\";\r\n            variant = \"default\";\r\n            break;\r\n          default:\r\n            label = method;\r\n        }\r\n\r\n        return <Badge variant={variant as any}>{label}</Badge>;\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"amount\",\r\n      header: \"Amount\",\r\n      cell: ({ row }) => formatCurrency(row.getValue(\"amount\")),\r\n    },\r\n    {\r\n      accessorKey: \"expected_amount\",\r\n      header: \"Expected\",\r\n      cell: ({ row }) => formatCurrency(row.getValue(\"expected_amount\")),\r\n    },\r\n    {\r\n      accessorKey: \"discrepancy\",\r\n      header: \"Discrepancy\",\r\n      cell: ({ row }) => {\r\n        const discrepancy = row.getValue(\"discrepancy\") as number;\r\n        return (\r\n          <span\r\n            className={\r\n              discrepancy < 0\r\n                ? \"text-destructive\"\r\n                : discrepancy > 0\r\n                ? \"text-amber-500\"\r\n                : \"\"\r\n            }\r\n          >\r\n            {formatCurrency(discrepancy)}\r\n          </span>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"status\",\r\n      header: \"Status\",\r\n      cell: ({ row }) => {\r\n        const status = row.getValue(\"status\") as string;\r\n        let variant: \"default\" | \"outline\" | \"secondary\" | \"destructive\" =\r\n          \"outline\";\r\n        let label = \"\";\r\n\r\n        switch (status) {\r\n          case BANKING_STATUSES.COMPLETED:\r\n            variant = \"default\";\r\n            label = \"Approved\";\r\n            break;\r\n          case BANKING_STATUSES.PENDING:\r\n            variant = \"outline\";\r\n            label = \"Pending Approval\";\r\n            break;\r\n          case BANKING_STATUSES.FAILED:\r\n            variant = \"destructive\";\r\n            label = \"Rejected\";\r\n            break;\r\n          case BANKING_STATUSES.RECONCILED:\r\n            variant = \"secondary\";\r\n            label = \"Reconciled\";\r\n            break;\r\n          default:\r\n            label = status.charAt(0).toUpperCase() + status.slice(1);\r\n        }\r\n\r\n        return <Badge variant={variant}>{label}</Badge>;\r\n      },\r\n    },\r\n    {\r\n      id: \"actions\",\r\n      cell: ({ row }) => {\r\n        const banking = row.original;\r\n        return (\r\n          <div className=\"flex items-center justify-end gap-2\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              onClick={() => router.push(`/banking/${banking.id}`)}\r\n            >\r\n              <Eye className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n  ];\r\n\r\n  // Ensure we have default values for data and meta to prevent undefined errors\r\n  const safeData = {\r\n    data: data?.data || [],\r\n    meta: data?.meta || { total: 0, page: 1, limit: 10, total_pages: 1 },\r\n  };\r\n\r\n  const table = useReactTable({\r\n    data: safeData.data,\r\n    columns,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getPaginationRowModel: getPaginationRowModel(),\r\n    onSortingChange: setSorting,\r\n    getSortedRowModel: getSortedRowModel(),\r\n    onColumnFiltersChange: setColumnFilters,\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    state: {\r\n      sorting,\r\n      columnFilters,\r\n      pagination: {\r\n        pageIndex: (safeData.meta.page || 1) - 1, // Convert 1-based to 0-based\r\n        pageSize: safeData.meta.limit || 10,\r\n      },\r\n    },\r\n    manualPagination: true,\r\n    pageCount: safeData.meta.total_pages,\r\n  });\r\n\r\n  const handleSearch = () => {\r\n    setFilters({\r\n      ...filters,\r\n      reference_number: searchValue || undefined,\r\n      page: 1,\r\n    });\r\n  };\r\n\r\n  // Handle date range change\r\n  const handleDateRangeChange = (range: DateRange | undefined) => {\r\n    setDateRange(range);\r\n\r\n    if (range?.from) {\r\n      const newFilters = { ...filters, page: 1 };\r\n      newFilters.start_date = format(range.from, \"yyyy-MM-dd\");\r\n\r\n      if (range.to) {\r\n        newFilters.end_date = format(range.to, \"yyyy-MM-dd\");\r\n      } else {\r\n        delete newFilters.end_date;\r\n      }\r\n\r\n      setFilters(newFilters);\r\n    }\r\n  };\r\n\r\n  // Clear date range filter\r\n  const handleClearDateRange = () => {\r\n    setDateRange(undefined);\r\n\r\n    const newFilters = { ...filters, page: 1 };\r\n    delete newFilters.start_date;\r\n    delete newFilters.end_date;\r\n\r\n    setFilters(newFilters);\r\n  };\r\n\r\n  const handlePageChange = (pageIndex: number) => {\r\n    setFilters({\r\n      ...filters,\r\n      page: pageIndex + 1, // Convert 0-based to 1-based for API\r\n    });\r\n  };\r\n\r\n  const handlePageSizeChange = (pageSize: number) => {\r\n    setFilters({\r\n      ...filters,\r\n      limit: pageSize,\r\n      page: 1,\r\n    });\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"space-y-4\">\r\n        <Skeleton className=\"h-10 w-full\" />\r\n        <Skeleton className=\"h-96 w-full\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"rounded-md bg-destructive/10 p-4 text-destructive\">\r\n        Error loading banking records. Please try again.\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader className=\"flex flex-row items-center justify-between\">\r\n        <div>\r\n          <CardTitle>Banking Records</CardTitle>\r\n          <CardDescription>View and manage banking records</CardDescription>\r\n        </div>\r\n        <Button onClick={() => router.push(\"/banking/create\")}>\r\n          <Plus className=\"mr-2 h-4 w-4\" />\r\n          New Banking Record\r\n        </Button>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <div className=\"mb-4 space-y-4\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className=\"flex-1\">\r\n              <div className=\"relative\">\r\n                <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n                <Input\r\n                  placeholder=\"Search by reference number...\"\r\n                  className=\"pl-8\"\r\n                  value={searchValue}\r\n                  onChange={(e) => setSearchValue(e.target.value)}\r\n                  onKeyDown={(e) => e.key === \"Enter\" && handleSearch()}\r\n                />\r\n              </div>\r\n            </div>\r\n            <Button variant=\"outline\" onClick={handleSearch}>\r\n              Search\r\n            </Button>\r\n          </div>\r\n\r\n          {/* Date Range Filter */}\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className=\"flex-1\">\r\n              <DateRangePicker\r\n                value={dateRange || { from: undefined, to: undefined }}\r\n                onChange={handleDateRangeChange}\r\n              />\r\n            </div>\r\n            {dateRange && (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                onClick={handleClearDateRange}\r\n                title=\"Clear date filter\"\r\n              >\r\n                <X className=\"h-4 w-4\" />\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"rounded-md border\">\r\n          <Table>\r\n            <TableHeader>\r\n              {table.getHeaderGroups().map((headerGroup) => (\r\n                <TableRow key={headerGroup.id}>\r\n                  {headerGroup.headers.map((header) => (\r\n                    <TableHead key={header.id}>\r\n                      {header.isPlaceholder\r\n                        ? null\r\n                        : flexRender(\r\n                            header.column.columnDef.header,\r\n                            header.getContext()\r\n                          )}\r\n                    </TableHead>\r\n                  ))}\r\n                </TableRow>\r\n              ))}\r\n            </TableHeader>\r\n            <TableBody>\r\n              {table.getRowModel().rows?.length ? (\r\n                table.getRowModel().rows.map((row) => (\r\n                  <TableRow\r\n                    key={row.id}\r\n                    data-state={row.getIsSelected() && \"selected\"}\r\n                  >\r\n                    {row.getVisibleCells().map((cell) => (\r\n                      <TableCell key={cell.id}>\r\n                        {flexRender(\r\n                          cell.column.columnDef.cell,\r\n                          cell.getContext()\r\n                        )}\r\n                      </TableCell>\r\n                    ))}\r\n                  </TableRow>\r\n                ))\r\n              ) : (\r\n                <TableRow>\r\n                  <TableCell\r\n                    colSpan={columns.length}\r\n                    className=\"h-24 text-center\"\r\n                  >\r\n                    No banking records found.\r\n                  </TableCell>\r\n                </TableRow>\r\n              )}\r\n            </TableBody>\r\n          </Table>\r\n        </div>\r\n      </CardContent>\r\n      <CardFooter>\r\n        <DataPagination\r\n          currentPage={safeData.meta.page || 1}\r\n          totalPages={safeData.meta.total_pages || 1}\r\n          onPageChange={(page) => setFilters({ ...filters, page })}\r\n          pageSize={safeData.meta.limit || 10}\r\n          onPageSizeChange={handlePageSizeChange}\r\n          totalItems={safeData.meta.total || 0}\r\n          isLoading={isLoading}\r\n          showPageSizeSelector={true}\r\n          showItemsInfo={true}\r\n          showFirstLastButtons={true}\r\n        />\r\n      </CardFooter>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAWA;AAQA;AAQA;AACA;AAQA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AAlDA;;;;;;;;;;;;;;;;;;AAoDO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACrD,MAAM;QACN,OAAO;IACT;IAEA,8BAA8B;IAC9B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAEzC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE;IAErD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,uCAAuC;IACvC,MAAM,UAAgC;QACpC;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,8OAAC;;wBAAK;wBAAE,IAAI,QAAQ,CAAC;;;;;;;QAC1C;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,IAAI,QAAQ,CAAC,sBAAsB;QACvD;QAEA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,SAAS,IAAI,QAAQ,CAAC,MAAM;gBAClC,qBAAO,8OAAC;8BAAM,QAAQ,QAAQ;;;;;;YAChC;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,SAAS,IAAI,QAAQ,CAAC;gBAC5B,IAAI,QAAQ;gBACZ,IAAI,UAAU;gBAEd,OAAQ;oBACN,KAAK,uHAAA,CAAA,kBAAe,CAAC,IAAI;wBACvB,QAAQ;wBACR,UAAU;wBACV;oBACF,KAAK,uHAAA,CAAA,kBAAe,CAAC,KAAK;wBACxB,QAAQ;wBACR,UAAU;wBACV;oBACF,KAAK,uHAAA,CAAA,kBAAe,CAAC,KAAK;wBACxB,QAAQ;wBACR,UAAU;wBACV;oBACF;wBACE,QAAQ;gBACZ;gBAEA,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAS;8BAAiB;;;;;;YAC1C;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,QAAQ,CAAC;QACjD;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,QAAQ,CAAC;QACjD;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ,CAAC;gBACjC,qBACE,8OAAC;oBACC,WACE,cAAc,IACV,qBACA,cAAc,IACd,mBACA;8BAGL,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;YAGtB;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,SAAS,IAAI,QAAQ,CAAC;gBAC5B,IAAI,UACF;gBACF,IAAI,QAAQ;gBAEZ,OAAQ;oBACN,KAAK,uHAAA,CAAA,mBAAgB,CAAC,SAAS;wBAC7B,UAAU;wBACV,QAAQ;wBACR;oBACF,KAAK,uHAAA,CAAA,mBAAgB,CAAC,OAAO;wBAC3B,UAAU;wBACV,QAAQ;wBACR;oBACF,KAAK,uHAAA,CAAA,mBAAgB,CAAC,MAAM;wBAC1B,UAAU;wBACV,QAAQ;wBACR;oBACF,KAAK,uHAAA,CAAA,mBAAgB,CAAC,UAAU;wBAC9B,UAAU;wBACV,QAAQ;wBACR;oBACF;wBACE,QAAQ,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;gBAC1D;gBAEA,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAS;8BAAU;;;;;;YACnC;QACF;QACA;YACE,IAAI;YACJ,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,UAAU,IAAI,QAAQ;gBAC5B,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;kCAEnD,cAAA,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;;;;;;YAIvB;QACF;KACD;IAED,8EAA8E;IAC9E,MAAM,WAAW;QACf,MAAM,MAAM,QAAQ,EAAE;QACtB,MAAM,MAAM,QAAQ;YAAE,OAAO;YAAG,MAAM;YAAG,OAAO;YAAI,aAAa;QAAE;IACrE;IAEA,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,MAAM,SAAS,IAAI;QACnB;QACA,iBAAiB,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;QAC/B,uBAAuB,CAAA,GAAA,qKAAA,CAAA,wBAAqB,AAAD;QAC3C,iBAAiB;QACjB,mBAAmB,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD;QACnC,uBAAuB;QACvB,qBAAqB,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD;QACvC,OAAO;YACL;YACA;YACA,YAAY;gBACV,WAAW,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;gBACvC,UAAU,SAAS,IAAI,CAAC,KAAK,IAAI;YACnC;QACF;QACA,kBAAkB;QAClB,WAAW,SAAS,IAAI,CAAC,WAAW;IACtC;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,GAAG,OAAO;YACV,kBAAkB,eAAe;YACjC,MAAM;QACR;IACF;IAEA,2BAA2B;IAC3B,MAAM,wBAAwB,CAAC;QAC7B,aAAa;QAEb,IAAI,OAAO,MAAM;YACf,MAAM,aAAa;gBAAE,GAAG,OAAO;gBAAE,MAAM;YAAE;YACzC,WAAW,UAAU,GAAG,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EAAE;YAE3C,IAAI,MAAM,EAAE,EAAE;gBACZ,WAAW,QAAQ,GAAG,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,EAAE,EAAE;YACzC,OAAO;gBACL,OAAO,WAAW,QAAQ;YAC5B;YAEA,WAAW;QACb;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB;QAC3B,aAAa;QAEb,MAAM,aAAa;YAAE,GAAG,OAAO;YAAE,MAAM;QAAE;QACzC,OAAO,WAAW,UAAU;QAC5B,OAAO,WAAW,QAAQ;QAE1B,WAAW;IACb;IAEA,MAAM,mBAAmB,CAAC;QACxB,WAAW;YACT,GAAG,OAAO;YACV,MAAM,YAAY;QACpB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,WAAW;YACT,GAAG,OAAO;YACV,OAAO;YACP,MAAM;QACR;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;8BACpB,8OAAC,oIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;;IAG1B;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBAAoD;;;;;;IAIvE;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;;0CACC,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;0CACjC,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAIrC,8OAAC,gIAAA,CAAA,cAAW;;kCACV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,WAAU;oDACV,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;;;;;;kDAI7C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;kDAAc;;;;;;;;;;;;0CAMnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,mJAAA,CAAA,kBAAe;4CACd,OAAO,aAAa;gDAAE,MAAM;gDAAW,IAAI;4CAAU;4CACrD,UAAU;;;;;;;;;;;oCAGb,2BACC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,OAAM;kDAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,iIAAA,CAAA,cAAW;8CACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,8OAAC,iIAAA,CAAA,WAAQ;sDACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,8OAAC,iIAAA,CAAA,YAAS;8DACP,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;mDALT,OAAO,EAAE;;;;;2CAFd,YAAY,EAAE;;;;;;;;;;8CAcjC,8OAAC,iIAAA,CAAA,YAAS;8CACP,MAAM,WAAW,GAAG,IAAI,EAAE,SACzB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC5B,8OAAC,iIAAA,CAAA,WAAQ;4CAEP,cAAY,IAAI,aAAa,MAAM;sDAElC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,8OAAC,iIAAA,CAAA,YAAS;8DACP,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;mDAHH,KAAK,EAAE;;;;;2CAJpB,IAAI,EAAE;;;;kEAcf,8OAAC,iIAAA,CAAA,WAAQ;kDACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;4CACR,SAAS,QAAQ,MAAM;4CACvB,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASb,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,8IAAA,CAAA,iBAAc;oBACb,aAAa,SAAS,IAAI,CAAC,IAAI,IAAI;oBACnC,YAAY,SAAS,IAAI,CAAC,WAAW,IAAI;oBACzC,cAAc,CAAC,OAAS,WAAW;4BAAE,GAAG,OAAO;4BAAE;wBAAK;oBACtD,UAAU,SAAS,IAAI,CAAC,KAAK,IAAI;oBACjC,kBAAkB;oBAClB,YAAY,SAAS,IAAI,CAAC,KAAK,IAAI;oBACnC,WAAW;oBACX,sBAAsB;oBACtB,eAAe;oBACf,sBAAsB;;;;;;;;;;;;;;;;;AAKhC", "debugId": null}}, {"offset": {"line": 6921, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 6985, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/branches/api/branch-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { Branch, PaginatedResponse, UpdateBranchStatusRequest } from \"@/types\";\r\nimport { CreateBranchRequest, UpdateBranchRequest } from \"@/types/branch\";\r\n\r\nexport const branchService = {\r\n  getBranches: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Branch>> => {\r\n    try {\r\n      console.log(\"Calling API: /branches with params:\", params);\r\n      let response;\r\n\r\n      try {\r\n        // Try the Next.js API route first\r\n        response = await apiClient.get<any>(\"/branches\", { params });\r\n      } catch (error) {\r\n        console.warn(\r\n          \"Error fetching from Next.js API route, trying direct backend:\",\r\n          error\r\n        );\r\n        // If that fails, try the backend directly\r\n        const API_URL = process.env.NEXT_PUBLIC_API_URL;\r\n        const token =\r\n          localStorage.getItem(\"token\") || localStorage.getItem(\"accessToken\");\r\n\r\n        if (!token) {\r\n          throw new Error(\"No authentication token available\");\r\n        }\r\n\r\n        const backendResponse = await fetch(\r\n          `${API_URL}/branches${\r\n            params ? `?${new URLSearchParams(params)}` : \"\"\r\n          }`,\r\n          {\r\n            headers: {\r\n              Authorization: `Bearer ${token}`,\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n          }\r\n        );\r\n\r\n        if (!backendResponse.ok) {\r\n          throw new Error(`Backend API error: ${backendResponse.status}`);\r\n        }\r\n\r\n        response = await backendResponse.json();\r\n      }\r\n\r\n      console.log(\"Raw branches API response:\", response);\r\n\r\n      // Map API response to our Branch type\r\n      const mapApiBranchToBranch = (apiBranch: any): Branch => ({\r\n        ...apiBranch,\r\n      });\r\n\r\n      // If response is an array, convert to paginated format with mapped branches\r\n      if (Array.isArray(response)) {\r\n        console.log(\r\n          \"Branches response is an array, converting to paginated format\"\r\n        );\r\n        const mappedBranches = response.map(mapApiBranchToBranch);\r\n        return {\r\n          data: mappedBranches,\r\n          pagination: {\r\n            total: mappedBranches.length,\r\n            page: 1,\r\n            limit: mappedBranches.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Check if response is already in paginated format\r\n      if (response && response.data && Array.isArray(response.data)) {\r\n        console.log(\"Branches response is already in paginated format\");\r\n        const mappedBranches = response.data.map(mapApiBranchToBranch);\r\n        return {\r\n          data: mappedBranches,\r\n          pagination: response.pagination || {\r\n            total: mappedBranches.length,\r\n            page: 1,\r\n            limit: mappedBranches.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If response has data property but it's not an array, wrap it\r\n      if (response && response.data && !Array.isArray(response.data)) {\r\n        console.log(\r\n          \"Branches response has data property but it's not an array, wrapping it\"\r\n        );\r\n        return {\r\n          data: [mapApiBranchToBranch(response.data)],\r\n          pagination: response.pagination || {\r\n            total: 1,\r\n            page: 1,\r\n            limit: 1,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If response itself is not an array but contains branches directly\r\n      if (response && !Array.isArray(response) && !response.data) {\r\n        console.log(\"Response contains branches directly, converting to array\");\r\n        // Try to extract branches from the response\r\n        const branches = Object.values(response).filter(\r\n          (item) =>\r\n            typeof item === \"object\" &&\r\n            item !== null &&\r\n            \"id\" in item &&\r\n            \"name\" in item\r\n        );\r\n\r\n        if (branches.length > 0) {\r\n          const mappedBranches = branches.map(mapApiBranchToBranch);\r\n          return {\r\n            data: mappedBranches,\r\n            pagination: {\r\n              total: mappedBranches.length,\r\n              page: 1,\r\n              limit: mappedBranches.length,\r\n              totalPages: 1,\r\n            },\r\n          };\r\n        }\r\n      }\r\n\r\n      // Default fallback\r\n      console.log(\"Using default fallback for branches response\");\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error in getBranches:\", error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getBranchById: async (id: number): Promise<Branch> => {\r\n    return apiClient.get(`/branches/${id}`);\r\n  },\r\n\r\n  createBranch: async (branch: CreateBranchRequest): Promise<Branch> => {\r\n    // Send exactly what the API expects according to the guide\r\n    const apiRequest = {\r\n      tenant_id: branch.tenant_id,\r\n      name: branch.name,\r\n      location: branch.location,\r\n      region_id: branch.region_id,\r\n      is_hq: false, // Always set to false as requested\r\n      status: \"active\", // Default status\r\n    };\r\n\r\n    // Add optional fields if they exist\r\n    if (branch.phone) {\r\n      apiRequest.phone_number = branch.phone;\r\n    }\r\n    if (branch.email) {\r\n      apiRequest.email = branch.email;\r\n    }\r\n\r\n    console.log(\"Creating branch with data:\", apiRequest);\r\n    return apiClient.post(\"/branches\", apiRequest);\r\n  },\r\n\r\n  updateBranch: async (\r\n    id: number,\r\n    branch: UpdateBranchRequest\r\n  ): Promise<Branch> => {\r\n    // Ensure we're sending the correct fields to the API\r\n    const apiRequest = {\r\n      name: branch.name,\r\n      location: branch.location,\r\n      region_id: branch.region_id,\r\n      level: branch.level,\r\n    };\r\n\r\n    console.log(\"Updating branch with data:\", apiRequest);\r\n    return apiClient.put(`/branches/${id}`, apiRequest);\r\n  },\r\n\r\n  deleteBranch: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/branches/${id}`);\r\n  },\r\n\r\n  updateBranchStatus: async (\r\n    id: number,\r\n    status: UpdateBranchStatusRequest\r\n  ): Promise<Branch> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      status: status.status, // 'active' or 'inactive'\r\n    };\r\n\r\n    return apiClient.put(`/branches/${id}/status`, apiRequest);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAIO,MAAM,gBAAgB;IAC3B,aAAa,OACX;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,uCAAuC;YACnD,IAAI;YAEJ,IAAI;gBACF,kCAAkC;gBAClC,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,aAAa;oBAAE;gBAAO;YAC5D,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CACV,iEACA;gBAEF,0CAA0C;gBAC1C,MAAM;gBACN,MAAM,QACJ,aAAa,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC;gBAExD,IAAI,CAAC,OAAO;oBACV,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,kBAAkB,MAAM,MAC5B,GAAG,QAAQ,SAAS,EAClB,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,SAAS,GAAG,IAC7C,EACF;oBACE,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,OAAO;wBAChC,gBAAgB;oBAClB;gBACF;gBAGF,IAAI,CAAC,gBAAgB,EAAE,EAAE;oBACvB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,gBAAgB,MAAM,EAAE;gBAChE;gBAEA,WAAW,MAAM,gBAAgB,IAAI;YACvC;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,sCAAsC;YACtC,MAAM,uBAAuB,CAAC,YAA2B,CAAC;oBACxD,GAAG,SAAS;gBACd,CAAC;YAED,4EAA4E;YAC5E,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,QAAQ,GAAG,CACT;gBAEF,MAAM,iBAAiB,SAAS,GAAG,CAAC;gBACpC,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,eAAe,MAAM;wBAC5B,MAAM;wBACN,OAAO,eAAe,MAAM;wBAC5B,YAAY;oBACd;gBACF;YACF;YAEA,mDAAmD;YACnD,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC7D,QAAQ,GAAG,CAAC;gBACZ,MAAM,iBAAiB,SAAS,IAAI,CAAC,GAAG,CAAC;gBACzC,OAAO;oBACL,MAAM;oBACN,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO,eAAe,MAAM;wBAC5B,MAAM;wBACN,OAAO,eAAe,MAAM;wBAC5B,YAAY;oBACd;gBACF;YACF;YAEA,+DAA+D;YAC/D,IAAI,YAAY,SAAS,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC9D,QAAQ,GAAG,CACT;gBAEF,OAAO;oBACL,MAAM;wBAAC,qBAAqB,SAAS,IAAI;qBAAE;oBAC3C,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO;wBACP,MAAM;wBACN,OAAO;wBACP,YAAY;oBACd;gBACF;YACF;YAEA,oEAAoE;YACpE,IAAI,YAAY,CAAC,MAAM,OAAO,CAAC,aAAa,CAAC,SAAS,IAAI,EAAE;gBAC1D,QAAQ,GAAG,CAAC;gBACZ,4CAA4C;gBAC5C,MAAM,WAAW,OAAO,MAAM,CAAC,UAAU,MAAM,CAC7C,CAAC,OACC,OAAO,SAAS,YAChB,SAAS,QACT,QAAQ,QACR,UAAU;gBAGd,IAAI,SAAS,MAAM,GAAG,GAAG;oBACvB,MAAM,iBAAiB,SAAS,GAAG,CAAC;oBACpC,OAAO;wBACL,MAAM;wBACN,YAAY;4BACV,OAAO,eAAe,MAAM;4BAC5B,MAAM;4BACN,OAAO,eAAe,MAAM;4BAC5B,YAAY;wBACd;oBACF;gBACF;YACF;YAEA,mBAAmB;YACnB,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,eAAe,OAAO;QACpB,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;IACxC;IAEA,cAAc,OAAO;QACnB,2DAA2D;QAC3D,MAAM,aAAa;YACjB,WAAW,OAAO,SAAS;YAC3B,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS;YAC3B,OAAO;YACP,QAAQ;QACV;QAEA,oCAAoC;QACpC,IAAI,OAAO,KAAK,EAAE;YAChB,WAAW,YAAY,GAAG,OAAO,KAAK;QACxC;QACA,IAAI,OAAO,KAAK,EAAE;YAChB,WAAW,KAAK,GAAG,OAAO,KAAK;QACjC;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,aAAa;IACrC;IAEA,cAAc,OACZ,IACA;QAEA,qDAAqD;QACrD,MAAM,aAAa;YACjB,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS;YAC3B,OAAO,OAAO,KAAK;QACrB;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;IAC1C;IAEA,cAAc,OAAO;QACnB,OAAO,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;IAC3C;IAEA,oBAAoB,OAClB,IACA;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,QAAQ,OAAO,MAAM;QACvB;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,EAAE;IACjD;AACF", "debugId": null}}, {"offset": {"line": 7161, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/branches/hooks/use-branches.ts"], "sourcesContent": ["import { UpdateBranchRequest, UpdateBranchStatusRequest } from \"@/types\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport { branchService } from \"../api/branch-service\";\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { CreateBranchRequest } from \"@/types/branch\";\r\n\r\nexport function useBranches(\r\n  params?: Record<string, any>,\r\n  options?: { enabled?: boolean }\r\n) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n\r\n  return useQuery({\r\n    queryKey: [\"branches\", params],\r\n    queryFn: () => branchService.getBranches(params),\r\n    // Only run query when authentication is ready and enabled option is true (if provided)\r\n    enabled: isInitialized && !!accessToken && (options?.enabled !== false),\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\nexport function useBranch(id: number) {\r\n  return useQuery({\r\n    queryKey: [\"branches\", id],\r\n    queryFn: () => branchService.getBranchById(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateBranch() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (branch: CreateBranchRequest) =>\r\n      branchService.createBranch(branch),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch created\", {\r\n        description: \"The branch has been created successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error creating branch\", {\r\n        description:\r\n          error.message || \"An error occurred while creating the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateBranch(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (branch: UpdateBranchRequest) =>\r\n      branchService.updateBranch(id, branch),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch updated\", {\r\n        description: \"The branch has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating branch\", {\r\n        description:\r\n          error.message || \"An error occurred while updating the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteBranch() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => branchService.deleteBranch(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch deleted\", {\r\n        description: \"The branch has been deleted successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error deleting branch\", {\r\n        description:\r\n          error.message || \"An error occurred while deleting the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateBranchStatus(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (status: UpdateBranchStatusRequest) =>\r\n      branchService.updateBranchStatus(id, status),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch status updated\", {\r\n        description: \"The branch status has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating branch status\", {\r\n        description:\r\n          error.message ||\r\n          \"An error occurred while updating the branch status.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;AAGO,SAAS,YACd,MAA4B,EAC5B,OAA+B;IAE/B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAEnD,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAO;QAC9B,SAAS,IAAM,uJAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;QACzC,uFAAuF;QACvF,SAAS,iBAAiB,CAAC,CAAC,eAAgB,SAAS,YAAY;QACjE,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,QAAQ,CAAC;YACP,IAAI,CAAC,MAAM;gBACT,OAAO;oBACL,MAAM,EAAE;oBACR,YAAY;wBACV,OAAO;wBACP,MAAM;wBACN,OAAO;wBACP,YAAY;oBACd;gBACF;YACF;YACA,OAAO;QACT;IACF;AACF;AAEO,SAAS,UAAU,EAAU;IAClC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAG;QAC1B,SAAS,IAAM,uJAAA,CAAA,gBAAa,CAAC,aAAa,CAAC;QAC3C,SAAS,CAAC,CAAC;IACb;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,SACX,uJAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;QAC7B,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;gBAC9B,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;gBACnC,aACE,MAAM,OAAO,IAAI;YACrB;QACF;IACF;AACF;AAEO,SAAS,gBAAgB,EAAU;IACxC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,SACX,uJAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,IAAI;QACjC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAY;iBAAG;YAAC;YAC3D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;gBAC9B,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;gBACnC,aACE,MAAM,OAAO,IAAI;YACrB;QACF;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,KAAe,uJAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;QACvD,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;gBAC9B,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;gBACnC,aACE,MAAM,OAAO,IAAI;YACrB;QACF;IACF;AACF;AAEO,SAAS,sBAAsB,EAAU;IAC9C,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,SACX,uJAAA,CAAA,gBAAa,CAAC,kBAAkB,CAAC,IAAI;QACvC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAY;iBAAG;YAAC;YAC3D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,yBAAyB;gBACrC,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;gBAC1C,aACE,MAAM,OAAO,IACb;YACJ;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 7322, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/regions/api/region-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { PaginatedResponse } from \"@/types/api\";\r\n\r\nexport interface Region {\r\n  id: number;\r\n  name: string;\r\n  code?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n}\r\n\r\nexport const regionService = {\r\n  getRegions: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Region>> => {\r\n    try {\r\n      const response = await apiClient.get<any>(\"/regions\", { params });\r\n\r\n      // If response is an array, convert to paginated format\r\n      if (Array.isArray(response)) {\r\n        return {\r\n          data: response,\r\n          pagination: {\r\n            total: response.length,\r\n            page: 1,\r\n            limit: response.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Check if response is already in paginated format\r\n      if (response && response.data && Array.isArray(response.data)) {\r\n        return {\r\n          data: response.data,\r\n          pagination: response.pagination || {\r\n            total: response.data.length,\r\n            page: 1,\r\n            limit: response.data.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Default fallback\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error in getRegions:\", error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getRegionById: async (id: number): Promise<Region> => {\r\n    return apiClient.get(`/regions/${id}`);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAYO,MAAM,gBAAgB;IAC3B,YAAY,OACV;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,YAAY;gBAAE;YAAO;YAE/D,uDAAuD;YACvD,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,SAAS,MAAM;wBACtB,MAAM;wBACN,OAAO,SAAS,MAAM;wBACtB,YAAY;oBACd;gBACF;YACF;YAEA,mDAAmD;YACnD,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC7D,OAAO;oBACL,MAAM,SAAS,IAAI;oBACnB,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO,SAAS,IAAI,CAAC,MAAM;wBAC3B,MAAM;wBACN,OAAO,SAAS,IAAI,CAAC,MAAM;wBAC3B,YAAY;oBACd;gBACF;YACF;YAEA,mBAAmB;YACnB,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,eAAe,OAAO;QACpB,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;IACvC;AACF", "debugId": null}}, {"offset": {"line": 7391, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/regions/hooks/use-regions.ts"], "sourcesContent": ["import { useQuery } from \"@tanstack/react-query\";\r\nimport { regionService } from \"../api/region-service\";\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\n\r\nexport function useRegions(params?: Record<string, any>) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n\r\n  return useQuery({\r\n    queryKey: [\"regions\", params],\r\n    queryFn: () => regionService.getRegions(params),\r\n    // Only run query when authentication is ready\r\n    enabled: isInitialized && !!accessToken,\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If data exists but data.data is undefined, provide a default\r\n      if (!data.data) {\r\n        return {\r\n          ...data,\r\n          data: [],\r\n          pagination: data.pagination || {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\nexport function useRegion(id: number) {\r\n  return useQuery({\r\n    queryKey: [\"regions\", id],\r\n    queryFn: () => regionService.getRegionById(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,SAAS,WAAW,MAA4B;IACrD,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAEnD,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAO;QAC7B,SAAS,IAAM,sJAAA,CAAA,gBAAa,CAAC,UAAU,CAAC;QACxC,8CAA8C;QAC9C,SAAS,iBAAiB,CAAC,CAAC;QAC5B,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,QAAQ,CAAC;YACP,IAAI,CAAC,MAAM;gBACT,OAAO;oBACL,MAAM,EAAE;oBACR,YAAY;wBACV,OAAO;wBACP,MAAM;wBACN,OAAO;wBACP,YAAY;oBACd;gBACF;YACF;YAEA,+DAA+D;YAC/D,IAAI,CAAC,KAAK,IAAI,EAAE;gBACd,OAAO;oBACL,GAAG,IAAI;oBACP,MAAM,EAAE;oBACR,YAAY,KAAK,UAAU,IAAI;wBAC7B,OAAO;wBACP,MAAM;wBACN,OAAO;wBACP,YAAY;oBACd;gBACF;YACF;YAEA,OAAO;QACT;IACF;AACF;AAEO,SAAS,UAAU,EAAU;IAClC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAG;QACzB,SAAS,IAAM,sJAAA,CAAA,gBAAa,CAAC,aAAa,CAAC;QAC3C,SAAS,CAAC,CAAC;IACb;AACF", "debugId": null}}, {"offset": {"line": 7461, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/users/api/user-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport {\r\n  User,\r\n  CreateUserRequest,\r\n  UpdateUserRequest,\r\n  PaginatedResponse,\r\n  UpdateUserStatusRequest,\r\n} from \"@/types\";\r\n\r\nimport { normalizeUser } from \"@/types/user\";\r\n\r\nexport const userService = {\r\n  getUsers: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<User>> => {\r\n    // Handle status parameter specially\r\n    const apiParams = { ...params };\r\n\r\n    // If status is provided, convert it to the appropriate deleted_at filter\r\n    if (apiParams.status) {\r\n      if (apiParams.status === \"active\") {\r\n        apiParams.deleted = false;\r\n      } else if (apiParams.status === \"inactive\") {\r\n        apiParams.deleted = true;\r\n      }\r\n      delete apiParams.status; // Remove the status parameter as the API uses deleted\r\n    }\r\n\r\n    // Add a timestamp to prevent caching issues\r\n    apiParams._t = new Date().getTime();\r\n\r\n    // Log the request for debugging\r\n    console.log(`Fetching users with API params:`, apiParams);\r\n\r\n    try {\r\n      const response = await apiClient.get<PaginatedResponse<User>>(\"/users\", { params: apiParams });\r\n      console.log(`Users API response success:`, {\r\n        page: response.pagination?.page,\r\n        totalPages: response.pagination?.totalPages,\r\n        total: response.pagination?.total,\r\n        count: response.data?.length\r\n      });\r\n      return response;\r\n    } catch (error) {\r\n      console.error(`Error fetching users:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  getUserById: async (id: number): Promise<User> => {\r\n    const user = await apiClient.get<User>(`/users/${id}`);\r\n    return user;\r\n  },\r\n\r\n  createUser: async (user: CreateUserRequest): Promise<User> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest: Record<string, any> = {\r\n      name: user.name,\r\n      email: user.email,\r\n      password: user.password,\r\n      tenant_id: user.tenant_id || 1, // Default to tenant_id 1 if not provided\r\n      branch_id: user.branch_id,\r\n      location_id: user.location_id,\r\n      phone: user.phone,\r\n      language_preference: user.language_preference || \"English\",\r\n      created_by: user.created_by, // Add created_by field\r\n    };\r\n\r\n    // Handle role_id\r\n    if (user.role_id) {\r\n      apiRequest.role_id = user.role_id;\r\n    } else if (user.role_name) {\r\n      // For backward compatibility, log a warning but don't send role parameter\r\n      console.warn(\"role_name is deprecated, please use role_id instead\");\r\n    }\r\n\r\n    console.log(\"Creating user with data:\", apiRequest);\r\n    return apiClient.post(\"/users\", apiRequest);\r\n  },\r\n\r\n  updateUser: async (id: number, user: UpdateUserRequest): Promise<User> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest: Record<string, any> = {};\r\n\r\n    // Only include fields that are provided\r\n    if (user.name !== undefined) apiRequest.name = user.name;\r\n    if (user.email !== undefined) apiRequest.email = user.email;\r\n    if (user.role_id !== undefined) apiRequest.role_id = user.role_id;\r\n    if (user.branch_id !== undefined) apiRequest.branch_id = user.branch_id;\r\n    if (user.location_id !== undefined)\r\n      apiRequest.location_id = user.location_id;\r\n    if (user.phone !== undefined) apiRequest.phone = user.phone;\r\n    if (user.language_preference !== undefined)\r\n      apiRequest.language_preference = user.language_preference;\r\n\r\n    return apiClient.put(`/users/${id}`, apiRequest);\r\n  },\r\n\r\n  deleteUser: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/users/${id}`);\r\n  },\r\n\r\n  updateUserStatus: async (\r\n    id: number,\r\n    status: UpdateUserStatusRequest\r\n  ): Promise<User> => {\r\n    try {\r\n      console.log(`Updating user ${id} status to ${status.status}`);\r\n\r\n      // Use the dedicated status endpoint\r\n      const response = await apiClient.put(`/users/${id}/status`, {\r\n        status: status.status\r\n      });\r\n\r\n      console.log(`Successfully updated user ${id} status to ${status.status}`, response);\r\n\r\n      // Verify the response has the expected status\r\n      const hasExpectedStatus = status.status === 'inactive' ?\r\n        !!response.deleted_at :\r\n        !response.deleted_at;\r\n\r\n      if (!hasExpectedStatus) {\r\n        console.warn(`API returned inconsistent status for user ${id}. Forcing correct status.`);\r\n\r\n        // Force the correct status in the response\r\n        if (status.status === 'inactive') {\r\n          response.deleted_at = new Date().toISOString();\r\n        } else {\r\n          response.deleted_at = null;\r\n        }\r\n\r\n        // First try a direct PUT request to the user endpoint as a fallback\r\n        try {\r\n          console.log(`Attempting direct PUT request to fix status for user ${id}`);\r\n          const putResponse = await apiClient.put(`/users/${id}`, {\r\n            deleted_at: status.status === 'inactive' ? new Date().toISOString() : null\r\n          });\r\n\r\n          console.log(`Direct PUT request completed for user ${id}`, putResponse);\r\n\r\n          // Use the PUT response if it has the correct status\r\n          const putHasCorrectStatus = status.status === 'inactive' ?\r\n            !!putResponse.deleted_at :\r\n            !putResponse.deleted_at;\r\n\r\n          if (putHasCorrectStatus) {\r\n            return putResponse;\r\n          }\r\n\r\n          // If PUT doesn't work, try PATCH as a last resort\r\n          console.log(`PUT request didn't set the correct status. Trying PATCH as a last resort.`);\r\n          const patchResponse = await apiClient.patch(`/users/${id}`, {\r\n            deleted_at: status.status === 'inactive' ? new Date().toISOString() : null\r\n          });\r\n\r\n          console.log(`Direct PATCH request completed for user ${id}`, patchResponse);\r\n\r\n          // Use the PATCH response if it has the correct status\r\n          const patchHasCorrectStatus = status.status === 'inactive' ?\r\n            !!patchResponse.deleted_at :\r\n            !patchResponse.deleted_at;\r\n\r\n          if (patchHasCorrectStatus) {\r\n            return patchResponse;\r\n          }\r\n        } catch (error) {\r\n          console.error(`Direct update requests failed for user ${id}:`, error);\r\n          // Continue with the forced response\r\n        }\r\n      }\r\n\r\n      return response;\r\n    } catch (error) {\r\n      console.error(`Failed to update user ${id} status to ${status.status}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAWO,MAAM,cAAc;IACzB,UAAU,OACR;QAEA,oCAAoC;QACpC,MAAM,YAAY;YAAE,GAAG,MAAM;QAAC;QAE9B,yEAAyE;QACzE,IAAI,UAAU,MAAM,EAAE;YACpB,IAAI,UAAU,MAAM,KAAK,UAAU;gBACjC,UAAU,OAAO,GAAG;YACtB,OAAO,IAAI,UAAU,MAAM,KAAK,YAAY;gBAC1C,UAAU,OAAO,GAAG;YACtB;YACA,OAAO,UAAU,MAAM,EAAE,sDAAsD;QACjF;QAEA,4CAA4C;QAC5C,UAAU,EAAE,GAAG,IAAI,OAAO,OAAO;QAEjC,gCAAgC;QAChC,QAAQ,GAAG,CAAC,CAAC,+BAA+B,CAAC,EAAE;QAE/C,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAA0B,UAAU;gBAAE,QAAQ;YAAU;YAC5F,QAAQ,GAAG,CAAC,CAAC,2BAA2B,CAAC,EAAE;gBACzC,MAAM,SAAS,UAAU,EAAE;gBAC3B,YAAY,SAAS,UAAU,EAAE;gBACjC,OAAO,SAAS,UAAU,EAAE;gBAC5B,OAAO,SAAS,IAAI,EAAE;YACxB;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,CAAC,EAAE;YACvC,MAAM;QACR;IACF;IAEA,aAAa,OAAO;QAClB,MAAM,OAAO,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAO,CAAC,OAAO,EAAE,IAAI;QACrD,OAAO;IACT;IAEA,YAAY,OAAO;QACjB,sDAAsD;QACtD,MAAM,aAAkC;YACtC,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;YACvB,WAAW,KAAK,SAAS,IAAI;YAC7B,WAAW,KAAK,SAAS;YACzB,aAAa,KAAK,WAAW;YAC7B,OAAO,KAAK,KAAK;YACjB,qBAAqB,KAAK,mBAAmB,IAAI;YACjD,YAAY,KAAK,UAAU;QAC7B;QAEA,iBAAiB;QACjB,IAAI,KAAK,OAAO,EAAE;YAChB,WAAW,OAAO,GAAG,KAAK,OAAO;QACnC,OAAO,IAAI,KAAK,SAAS,EAAE;YACzB,0EAA0E;YAC1E,QAAQ,IAAI,CAAC;QACf;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QACxC,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,UAAU;IAClC;IAEA,YAAY,OAAO,IAAY;QAC7B,sDAAsD;QACtD,MAAM,aAAkC,CAAC;QAEzC,wCAAwC;QACxC,IAAI,KAAK,IAAI,KAAK,WAAW,WAAW,IAAI,GAAG,KAAK,IAAI;QACxD,IAAI,KAAK,KAAK,KAAK,WAAW,WAAW,KAAK,GAAG,KAAK,KAAK;QAC3D,IAAI,KAAK,OAAO,KAAK,WAAW,WAAW,OAAO,GAAG,KAAK,OAAO;QACjE,IAAI,KAAK,SAAS,KAAK,WAAW,WAAW,SAAS,GAAG,KAAK,SAAS;QACvE,IAAI,KAAK,WAAW,KAAK,WACvB,WAAW,WAAW,GAAG,KAAK,WAAW;QAC3C,IAAI,KAAK,KAAK,KAAK,WAAW,WAAW,KAAK,GAAG,KAAK,KAAK;QAC3D,IAAI,KAAK,mBAAmB,KAAK,WAC/B,WAAW,mBAAmB,GAAG,KAAK,mBAAmB;QAE3D,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;IACvC;IAEA,YAAY,OAAO;QACjB,OAAO,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI;IACxC;IAEA,kBAAkB,OAChB,IACA;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,WAAW,EAAE,OAAO,MAAM,EAAE;YAE5D,oCAAoC;YACpC,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,EAAE;gBAC1D,QAAQ,OAAO,MAAM;YACvB;YAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,GAAG,WAAW,EAAE,OAAO,MAAM,EAAE,EAAE;YAE1E,8CAA8C;YAC9C,MAAM,oBAAoB,OAAO,MAAM,KAAK,aAC1C,CAAC,CAAC,SAAS,UAAU,GACrB,CAAC,SAAS,UAAU;YAEtB,IAAI,CAAC,mBAAmB;gBACtB,QAAQ,IAAI,CAAC,CAAC,0CAA0C,EAAE,GAAG,yBAAyB,CAAC;gBAEvF,2CAA2C;gBAC3C,IAAI,OAAO,MAAM,KAAK,YAAY;oBAChC,SAAS,UAAU,GAAG,IAAI,OAAO,WAAW;gBAC9C,OAAO;oBACL,SAAS,UAAU,GAAG;gBACxB;gBAEA,oEAAoE;gBACpE,IAAI;oBACF,QAAQ,GAAG,CAAC,CAAC,qDAAqD,EAAE,IAAI;oBACxE,MAAM,cAAc,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;wBACtD,YAAY,OAAO,MAAM,KAAK,aAAa,IAAI,OAAO,WAAW,KAAK;oBACxE;oBAEA,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,IAAI,EAAE;oBAE3D,oDAAoD;oBACpD,MAAM,sBAAsB,OAAO,MAAM,KAAK,aAC5C,CAAC,CAAC,YAAY,UAAU,GACxB,CAAC,YAAY,UAAU;oBAEzB,IAAI,qBAAqB;wBACvB,OAAO;oBACT;oBAEA,kDAAkD;oBAClD,QAAQ,GAAG,CAAC,CAAC,yEAAyE,CAAC;oBACvF,MAAM,gBAAgB,MAAM,2HAAA,CAAA,UAAS,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;wBAC1D,YAAY,OAAO,MAAM,KAAK,aAAa,IAAI,OAAO,WAAW,KAAK;oBACxE;oBAEA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,IAAI,EAAE;oBAE7D,sDAAsD;oBACtD,MAAM,wBAAwB,OAAO,MAAM,KAAK,aAC9C,CAAC,CAAC,cAAc,UAAU,GAC1B,CAAC,cAAc,UAAU;oBAE3B,IAAI,uBAAuB;wBACzB,OAAO;oBACT;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,GAAG,CAAC,CAAC,EAAE;gBAC/D,oCAAoC;gBACtC;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,WAAW,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC,EAAE;YACzE,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 7603, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/users/hooks/use-users.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { userService } from \"@/features/users/api/user-service\";\r\nimport {\r\n  CreateUserRequest,\r\n  UpdateUserRequest,\r\n  UpdateUserStatusRequest,\r\n} from \"@/types\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\n\r\nexport function useUsers(params?: Record<string, any>) {\r\n  return useQuery({\r\n    queryKey: [\"users\", params],\r\n    queryFn: async () => {\r\n      const response = await userService.getUsers(params);\r\n\r\n      // Normalize each user in the response\r\n      if (response.data) {\r\n        response.data = response.data.map((user) => {\r\n          // Import the normalizeUser function from @/types/user\r\n          const { normalizeUser } = require(\"@/types/user\");\r\n          return normalizeUser(user);\r\n        });\r\n      }\r\n      return response;\r\n    },\r\n    // Standard React Query settings for SPA\r\n    refetchOnWindowFocus: false,\r\n    // Keep previous data while loading new data to prevent UI jumps\r\n    placeholderData: \"keep\" as any,\r\n    // Standard stale time\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n  });\r\n}\r\n\r\nexport function useUser(id: number) {\r\n  return useQuery({\r\n    queryKey: [\"users\", id],\r\n    queryFn: async () => {\r\n      const user = await userService.getUserById(id);\r\n      // Import the normalizeUser function from @/types/user\r\n      const { normalizeUser } = require(\"@/types/user\");\r\n      return normalizeUser(user);\r\n    },\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateUser() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CreateUserRequest) => userService.createUser(data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"users\"] });\r\n      // Toast notifications are now handled in the form component\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Error in user creation mutation:\", error);\r\n      // Toast notifications are now handled in the form component\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateUser() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: ({ id, data }: { id: number; data: UpdateUserRequest }) =>\r\n      userService.updateUser(id, data),\r\n    onSuccess: (data, variables) => {\r\n      queryClient.setQueryData([\"users\", variables.id], data);\r\n      queryClient.invalidateQueries({ queryKey: [\"users\"] });\r\n      // Toast notifications are now handled in the form component\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Error in user update mutation:\", error);\r\n      // Toast notifications are now handled in the form component\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteUser() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => userService.deleteUser(id),\r\n    onSuccess: (_, id) => {\r\n      queryClient.removeQueries({ queryKey: [\"users\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"users\"] });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateUserStatus() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: ({\r\n      id,\r\n      status,\r\n    }: {\r\n      id: number;\r\n      status: UpdateUserStatusRequest;\r\n    }) => userService.updateUserStatus(id, status),\r\n    onSuccess: (data, variables) => {\r\n      // Ensure the data has the correct status\r\n      const expectedStatus = variables.status.status;\r\n      const hasCorrectStatus =\r\n        expectedStatus === \"inactive\" ? !!data.deleted_at : !data.deleted_at;\r\n\r\n      if (!hasCorrectStatus) {\r\n        console.warn(\r\n          `API returned user with incorrect status. Expected ${expectedStatus}, ` +\r\n            `but deleted_at is ${\r\n              data.deleted_at ? \"set\" : \"not set\"\r\n            }. Forcing correct status.`\r\n        );\r\n\r\n        // Force the correct status\r\n        data.deleted_at =\r\n          expectedStatus === \"inactive\" ? new Date().toISOString() : null;\r\n      }\r\n\r\n      // Update the cache with the corrected data\r\n      queryClient.setQueryData([\"users\", variables.id], data);\r\n\r\n      // Force a refetch of all user data\r\n      queryClient.invalidateQueries({ queryKey: [\"users\"] });\r\n\r\n      // Also invalidate any queries that might include this user\r\n      queryClient.invalidateQueries({ queryKey: [\"user\", variables.id] });\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Error updating user status:\", error);\r\n      // Error handling is done in the component\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAMA;AAAA;AAAA;AARA;;;AAUO,SAAS,SAAS,MAA4B;IACnD,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAS;SAAO;QAC3B,SAAS;YACP,MAAM,WAAW,MAAM,kJAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;YAE5C,sCAAsC;YACtC,IAAI,SAAS,IAAI,EAAE;gBACjB,SAAS,IAAI,GAAG,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC;oBACjC,sDAAsD;oBACtD,MAAM,EAAE,aAAa,EAAE;oBACvB,OAAO,cAAc;gBACvB;YACF;YACA,OAAO;QACT;QACA,wCAAwC;QACxC,sBAAsB;QACtB,gEAAgE;QAChE,iBAAiB;QACjB,sBAAsB;QACtB,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,SAAS,QAAQ,EAAU;IAChC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAS;SAAG;QACvB,SAAS;YACP,MAAM,OAAO,MAAM,kJAAA,CAAA,cAAW,CAAC,WAAW,CAAC;YAC3C,sDAAsD;YACtD,MAAM,EAAE,aAAa,EAAE;YACvB,OAAO,cAAc;QACvB;QACA,SAAS,CAAC,CAAC;IACb;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAA4B,kJAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QAChE,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAQ;YAAC;QACpD,4DAA4D;QAC9D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,oCAAoC;QAClD,4DAA4D;QAC9D;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,EAAE,EAAE,EAAE,IAAI,EAA2C,GAChE,kJAAA,CAAA,cAAW,CAAC,UAAU,CAAC,IAAI;QAC7B,WAAW,CAAC,MAAM;YAChB,YAAY,YAAY,CAAC;gBAAC;gBAAS,UAAU,EAAE;aAAC,EAAE;YAClD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAQ;YAAC;QACpD,4DAA4D;QAC9D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,kCAAkC;QAChD,4DAA4D;QAC9D;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,KAAe,kJAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QACnD,WAAW,CAAC,GAAG;YACb,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;oBAAS;iBAAG;YAAC;YACpD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAQ;YAAC;QACtD;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,EACX,EAAE,EACF,MAAM,EAIP,GAAK,kJAAA,CAAA,cAAW,CAAC,gBAAgB,CAAC,IAAI;QACvC,WAAW,CAAC,MAAM;YAChB,yCAAyC;YACzC,MAAM,iBAAiB,UAAU,MAAM,CAAC,MAAM;YAC9C,MAAM,mBACJ,mBAAmB,aAAa,CAAC,CAAC,KAAK,UAAU,GAAG,CAAC,KAAK,UAAU;YAEtE,IAAI,CAAC,kBAAkB;gBACrB,QAAQ,IAAI,CACV,CAAC,kDAAkD,EAAE,eAAe,EAAE,CAAC,GACrE,CAAC,kBAAkB,EACjB,KAAK,UAAU,GAAG,QAAQ,UAC3B,yBAAyB,CAAC;gBAG/B,2BAA2B;gBAC3B,KAAK,UAAU,GACb,mBAAmB,aAAa,IAAI,OAAO,WAAW,KAAK;YAC/D;YAEA,2CAA2C;YAC3C,YAAY,YAAY,CAAC;gBAAC;gBAAS,UAAU,EAAE;aAAC,EAAE;YAElD,mCAAmC;YACnC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAQ;YAAC;YAEpD,2DAA2D;YAC3D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAQ,UAAU,EAAE;iBAAC;YAAC;QACnE;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,0CAA0C;QAC5C;IACF;AACF", "debugId": null}}, {"offset": {"line": 7762, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/chart.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RechartsPrimitive from \"recharts\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\n// Format: { THEME_NAME: CSS_SELECTOR }\r\nconst THEMES = { light: \"\", dark: \".dark\" } as const\r\n\r\nexport type ChartConfig = {\r\n  [k in string]: {\r\n    label?: React.ReactNode\r\n    icon?: React.ComponentType\r\n  } & (\r\n    | { color?: string; theme?: never }\r\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\r\n  )\r\n}\r\n\r\ntype ChartContextProps = {\r\n  config: ChartConfig\r\n}\r\n\r\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\r\n\r\nfunction useChart() {\r\n  const context = React.useContext(ChartContext)\r\n\r\n  if (!context) {\r\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction ChartContainer({\r\n  id,\r\n  className,\r\n  children,\r\n  config,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  config: ChartConfig\r\n  children: React.ComponentProps<\r\n    typeof RechartsPrimitive.ResponsiveContainer\r\n  >[\"children\"]\r\n}) {\r\n  const uniqueId = React.useId()\r\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\r\n\r\n  return (\r\n    <ChartContext.Provider value={{ config }}>\r\n      <div\r\n        data-slot=\"chart\"\r\n        data-chart={chartId}\r\n        className={cn(\r\n          \"[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <ChartStyle id={chartId} config={config} />\r\n        <RechartsPrimitive.ResponsiveContainer>\r\n          {children}\r\n        </RechartsPrimitive.ResponsiveContainer>\r\n      </div>\r\n    </ChartContext.Provider>\r\n  )\r\n}\r\n\r\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\r\n  const colorConfig = Object.entries(config).filter(\r\n    ([, config]) => config.theme || config.color\r\n  )\r\n\r\n  if (!colorConfig.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <style\r\n      dangerouslySetInnerHTML={{\r\n        __html: Object.entries(THEMES)\r\n          .map(\r\n            ([theme, prefix]) => `\r\n${prefix} [data-chart=${id}] {\r\n${colorConfig\r\n  .map(([key, itemConfig]) => {\r\n    const color =\r\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\r\n      itemConfig.color\r\n    return color ? `  --color-${key}: ${color};` : null\r\n  })\r\n  .join(\"\\n\")}\r\n}\r\n`\r\n          )\r\n          .join(\"\\n\"),\r\n      }}\r\n    />\r\n  )\r\n}\r\n\r\nconst ChartTooltip = RechartsPrimitive.Tooltip\r\n\r\nfunction ChartTooltipContent({\r\n  active,\r\n  payload,\r\n  className,\r\n  indicator = \"dot\",\r\n  hideLabel = false,\r\n  hideIndicator = false,\r\n  label,\r\n  labelFormatter,\r\n  labelClassName,\r\n  formatter,\r\n  color,\r\n  nameKey,\r\n  labelKey,\r\n}: React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\r\n  React.ComponentProps<\"div\"> & {\r\n    hideLabel?: boolean\r\n    hideIndicator?: boolean\r\n    indicator?: \"line\" | \"dot\" | \"dashed\"\r\n    nameKey?: string\r\n    labelKey?: string\r\n  }) {\r\n  const { config } = useChart()\r\n\r\n  const tooltipLabel = React.useMemo(() => {\r\n    if (hideLabel || !payload?.length) {\r\n      return null\r\n    }\r\n\r\n    const [item] = payload\r\n    const key = `${labelKey || item?.dataKey || item?.name || \"value\"}`\r\n    const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n    const value =\r\n      !labelKey && typeof label === \"string\"\r\n        ? config[label as keyof typeof config]?.label || label\r\n        : itemConfig?.label\r\n\r\n    if (labelFormatter) {\r\n      return (\r\n        <div className={cn(\"font-medium\", labelClassName)}>\r\n          {labelFormatter(value, payload)}\r\n        </div>\r\n      )\r\n    }\r\n\r\n    if (!value) {\r\n      return null\r\n    }\r\n\r\n    return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\r\n  }, [\r\n    label,\r\n    labelFormatter,\r\n    payload,\r\n    hideLabel,\r\n    labelClassName,\r\n    config,\r\n    labelKey,\r\n  ])\r\n\r\n  if (!active || !payload?.length) {\r\n    return null\r\n  }\r\n\r\n  const nestLabel = payload.length === 1 && indicator !== \"dot\"\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl\",\r\n        className\r\n      )}\r\n    >\r\n      {!nestLabel ? tooltipLabel : null}\r\n      <div className=\"grid gap-1.5\">\r\n        {payload.map((item, index) => {\r\n          const key = `${nameKey || item.name || item.dataKey || \"value\"}`\r\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n          const indicatorColor = color || item.payload.fill || item.color\r\n\r\n          return (\r\n            <div\r\n              key={item.dataKey}\r\n              className={cn(\r\n                \"[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5\",\r\n                indicator === \"dot\" && \"items-center\"\r\n              )}\r\n            >\r\n              {formatter && item?.value !== undefined && item.name ? (\r\n                formatter(item.value, item.name, item, index, item.payload)\r\n              ) : (\r\n                <>\r\n                  {itemConfig?.icon ? (\r\n                    <itemConfig.icon />\r\n                  ) : (\r\n                    !hideIndicator && (\r\n                      <div\r\n                        className={cn(\r\n                          \"shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)\",\r\n                          {\r\n                            \"h-2.5 w-2.5\": indicator === \"dot\",\r\n                            \"w-1\": indicator === \"line\",\r\n                            \"w-0 border-[1.5px] border-dashed bg-transparent\":\r\n                              indicator === \"dashed\",\r\n                            \"my-0.5\": nestLabel && indicator === \"dashed\",\r\n                          }\r\n                        )}\r\n                        style={\r\n                          {\r\n                            \"--color-bg\": indicatorColor,\r\n                            \"--color-border\": indicatorColor,\r\n                          } as React.CSSProperties\r\n                        }\r\n                      />\r\n                    )\r\n                  )}\r\n                  <div\r\n                    className={cn(\r\n                      \"flex flex-1 justify-between leading-none\",\r\n                      nestLabel ? \"items-end\" : \"items-center\"\r\n                    )}\r\n                  >\r\n                    <div className=\"grid gap-1.5\">\r\n                      {nestLabel ? tooltipLabel : null}\r\n                      <span className=\"text-muted-foreground\">\r\n                        {itemConfig?.label || item.name}\r\n                      </span>\r\n                    </div>\r\n                    {item.value && (\r\n                      <span className=\"text-foreground font-mono font-medium tabular-nums\">\r\n                        {item.value.toLocaleString()}\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n          )\r\n        })}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nconst ChartLegend = RechartsPrimitive.Legend\r\n\r\nfunction ChartLegendContent({\r\n  className,\r\n  hideIcon = false,\r\n  payload,\r\n  verticalAlign = \"bottom\",\r\n  nameKey,\r\n}: React.ComponentProps<\"div\"> &\r\n  Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\r\n    hideIcon?: boolean\r\n    nameKey?: string\r\n  }) {\r\n  const { config } = useChart()\r\n\r\n  if (!payload?.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex items-center justify-center gap-4\",\r\n        verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\r\n        className\r\n      )}\r\n    >\r\n      {payload.map((item) => {\r\n        const key = `${nameKey || item.dataKey || \"value\"}`\r\n        const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n\r\n        return (\r\n          <div\r\n            key={item.value}\r\n            className={cn(\r\n              \"[&>svg]:text-muted-foreground flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3\"\r\n            )}\r\n          >\r\n            {itemConfig?.icon && !hideIcon ? (\r\n              <itemConfig.icon />\r\n            ) : (\r\n              <div\r\n                className=\"h-2 w-2 shrink-0 rounded-[2px]\"\r\n                style={{\r\n                  backgroundColor: item.color,\r\n                }}\r\n              />\r\n            )}\r\n            {itemConfig?.label}\r\n          </div>\r\n        )\r\n      })}\r\n    </div>\r\n  )\r\n}\r\n\r\n// Helper to extract item config from a payload.\r\nfunction getPayloadConfigFromPayload(\r\n  config: ChartConfig,\r\n  payload: unknown,\r\n  key: string\r\n) {\r\n  if (typeof payload !== \"object\" || payload === null) {\r\n    return undefined\r\n  }\r\n\r\n  const payloadPayload =\r\n    \"payload\" in payload &&\r\n    typeof payload.payload === \"object\" &&\r\n    payload.payload !== null\r\n      ? payload.payload\r\n      : undefined\r\n\r\n  let configLabelKey: string = key\r\n\r\n  if (\r\n    key in payload &&\r\n    typeof payload[key as keyof typeof payload] === \"string\"\r\n  ) {\r\n    configLabelKey = payload[key as keyof typeof payload] as string\r\n  } else if (\r\n    payloadPayload &&\r\n    key in payloadPayload &&\r\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\r\n  ) {\r\n    configLabelKey = payloadPayload[\r\n      key as keyof typeof payloadPayload\r\n    ] as string\r\n  }\r\n\r\n  return configLabelKey in config\r\n    ? config[configLabelKey]\r\n    : config[key as keyof typeof config]\r\n}\r\n\r\nexport {\r\n  ChartContainer,\r\n  ChartTooltip,\r\n  ChartTooltipContent,\r\n  ChartLegend,\r\n  ChartLegendContent,\r\n  ChartStyle,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAAA;AAAA;AAEA;AALA;;;;;AAOA,uCAAuC;AACvC,MAAM,SAAS;IAAE,OAAO;IAAI,MAAM;AAAQ;AAgB1C,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA4B;AAEnE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,EACtB,EAAE,EACF,SAAS,EACT,QAAQ,EACR,MAAM,EACN,GAAG,OAMJ;IACC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAC3B,MAAM,UAAU,CAAC,MAAM,EAAE,MAAM,SAAS,OAAO,CAAC,MAAM,KAAK;IAE3D,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;QAAO;kBACrC,cAAA,8OAAC;YACC,aAAU;YACV,cAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+pBACA;YAED,GAAG,KAAK;;8BAET,8OAAC;oBAAW,IAAI;oBAAS,QAAQ;;;;;;8BACjC,8OAAC,mKAAA,CAAA,sBAAqC;8BACnC;;;;;;;;;;;;;;;;;AAKX;AAEA,MAAM,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAuC;IACrE,MAAM,cAAc,OAAO,OAAO,CAAC,QAAQ,MAAM,CAC/C,CAAC,GAAG,OAAO,GAAK,OAAO,KAAK,IAAI,OAAO,KAAK;IAG9C,IAAI,CAAC,YAAY,MAAM,EAAE;QACvB,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,yBAAyB;YACvB,QAAQ,OAAO,OAAO,CAAC,QACpB,GAAG,CACF,CAAC,CAAC,OAAO,OAAO,GAAK,CAAC;AAClC,EAAE,OAAO,aAAa,EAAE,GAAG;AAC3B,EAAE,YACC,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW;oBACrB,MAAM,QACJ,WAAW,KAAK,EAAE,CAAC,MAAuC,IAC1D,WAAW,KAAK;oBAClB,OAAO,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,GAAG;gBACjD,GACC,IAAI,CAAC,MAAM;;AAEd,CAAC,EAEU,IAAI,CAAC;QACV;;;;;;AAGN;AAEA,MAAM,eAAe,uJAAA,CAAA,UAAyB;AAE9C,SAAS,oBAAoB,EAC3B,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,gBAAgB,KAAK,EACrB,KAAK,EACL,cAAc,EACd,cAAc,EACd,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EAQP;IACD,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACjC,IAAI,aAAa,CAAC,SAAS,QAAQ;YACjC,OAAO;QACT;QAEA,MAAM,CAAC,KAAK,GAAG;QACf,MAAM,MAAM,GAAG,YAAY,MAAM,WAAW,MAAM,QAAQ,SAAS;QACnE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;QAC7D,MAAM,QACJ,CAAC,YAAY,OAAO,UAAU,WAC1B,MAAM,CAAC,MAA6B,EAAE,SAAS,QAC/C,YAAY;QAElB,IAAI,gBAAgB;YAClB,qBACE,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;0BAC/B,eAAe,OAAO;;;;;;QAG7B;QAEA,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,qBAAO,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;sBAAkB;;;;;;IAC7D,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,UAAU,CAAC,SAAS,QAAQ;QAC/B,OAAO;IACT;IAEA,MAAM,YAAY,QAAQ,MAAM,KAAK,KAAK,cAAc;IAExD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0HACA;;YAGD,CAAC,YAAY,eAAe;0BAC7B,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,MAAM;oBAClB,MAAM,MAAM,GAAG,WAAW,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,SAAS;oBAChE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;oBAC7D,MAAM,iBAAiB,SAAS,KAAK,OAAO,CAAC,IAAI,IAAI,KAAK,KAAK;oBAE/D,qBACE,8OAAC;wBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,cAAc,SAAS;kCAGxB,aAAa,MAAM,UAAU,aAAa,KAAK,IAAI,GAClD,UAAU,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,OAAO,KAAK,OAAO,kBAE1D;;gCACG,YAAY,qBACX,8OAAC,WAAW,IAAI;;;;2CAEhB,CAAC,+BACC,8OAAC;oCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;wCACE,eAAe,cAAc;wCAC7B,OAAO,cAAc;wCACrB,mDACE,cAAc;wCAChB,UAAU,aAAa,cAAc;oCACvC;oCAEF,OACE;wCACE,cAAc;wCACd,kBAAkB;oCACpB;;;;;;8CAKR,8OAAC;oCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4CACA,YAAY,cAAc;;sDAG5B,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,eAAe;8DAC5B,8OAAC;oDAAK,WAAU;8DACb,YAAY,SAAS,KAAK,IAAI;;;;;;;;;;;;wCAGlC,KAAK,KAAK,kBACT,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;uBAhD/B,KAAK,OAAO;;;;;gBAwDvB;;;;;;;;;;;;AAIR;AAEA,MAAM,cAAc,sJAAA,CAAA,SAAwB;AAE5C,SAAS,mBAAmB,EAC1B,SAAS,EACT,WAAW,KAAK,EAChB,OAAO,EACP,gBAAgB,QAAQ,EACxB,OAAO,EAKN;IACD,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0CACA,kBAAkB,QAAQ,SAAS,QACnC;kBAGD,QAAQ,GAAG,CAAC,CAAC;YACZ,MAAM,MAAM,GAAG,WAAW,KAAK,OAAO,IAAI,SAAS;YACnD,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAE7D,qBACE,8OAAC;gBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;oBAGD,YAAY,QAAQ,CAAC,yBACpB,8OAAC,WAAW,IAAI;;;;6CAEhB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,KAAK,KAAK;wBAC7B;;;;;;oBAGH,YAAY;;eAfR,KAAK,KAAK;;;;;QAkBrB;;;;;;AAGN;AAEA,gDAAgD;AAChD,SAAS,4BACP,MAAmB,EACnB,OAAgB,EAChB,GAAW;IAEX,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,OAAO;IACT;IAEA,MAAM,iBACJ,aAAa,WACb,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,KAAK,OAChB,QAAQ,OAAO,GACf;IAEN,IAAI,iBAAyB;IAE7B,IACE,OAAO,WACP,OAAO,OAAO,CAAC,IAA4B,KAAK,UAChD;QACA,iBAAiB,OAAO,CAAC,IAA4B;IACvD,OAAO,IACL,kBACA,OAAO,kBACP,OAAO,cAAc,CAAC,IAAmC,KAAK,UAC9D;QACA,iBAAiB,cAAc,CAC7B,IACD;IACH;IAEA,OAAO,kBAAkB,SACrB,MAAM,CAAC,eAAe,GACtB,MAAM,CAAC,IAA2B;AACxC", "debugId": null}}, {"offset": {"line": 8053, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/components/report-chart.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\";\r\nimport { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { ChartData } from \"@/types\";\r\nimport {\r\n  <PERSON>,\r\n  <PERSON><PERSON>hart,\r\n  CartesianGrid,\r\n  Cell,\r\n  Line,\r\n  LineChart,\r\n  Pie,\r\n  <PERSON>hart,\r\n  XAxis,\r\n  YAxis,\r\n} from \"recharts\";\r\nimport {\r\n  ChartContainer,\r\n  ChartTooltip,\r\n  ChartTooltipContent,\r\n  ChartLegend,\r\n  ChartLegendContent,\r\n  type ChartConfig,\r\n} from \"@/components/ui/chart\";\r\n\r\ninterface ReportChartProps {\r\n  title: string;\r\n  description?: string;\r\n  data: ChartData;\r\n  chartTypes?: (\"line\" | \"bar\" | \"pie\")[];\r\n  defaultChartType?: \"line\" | \"bar\" | \"pie\";\r\n  height?: number;\r\n  showLegend?: boolean;\r\n  showGrid?: boolean;\r\n  showTooltip?: boolean;\r\n  className?: string;\r\n  trendInfo?: {\r\n    value: number;\r\n    isPositive: boolean;\r\n    label?: string;\r\n  };\r\n}\r\n\r\nexport function ReportChart({\r\n  title,\r\n  description,\r\n  data,\r\n  chartTypes = [\"line\", \"bar\"],\r\n  defaultChartType = \"line\",\r\n  height = 250,\r\n  showLegend = true,\r\n  showGrid = true,\r\n  showTooltip = true,\r\n  className,\r\n  trendInfo,\r\n}: ReportChartProps) {\r\n  // Generate chart config for Shadcn UI chart\r\n  const chartConfig: ChartConfig = {};\r\n\r\n  // Define colors for datasets\r\n  const colors = [\r\n    { light: \"#3b82f6\", dark: \"#60a5fa\" }, // blue-500/400\r\n    { light: \"#10b981\", dark: \"#34d399\" }, // emerald-500/400\r\n    { light: \"#f59e0b\", dark: \"#fbbf24\" }, // amber-500/400\r\n    { light: \"#ef4444\", dark: \"#f87171\" }, // red-500/400\r\n    { light: \"#8b5cf6\", dark: \"#a78bfa\" }, // violet-500/400\r\n    { light: \"#ec4899\", dark: \"#f472b6\" }, // pink-500/400\r\n    { light: \"#06b6d4\", dark: \"#22d3ee\" }, // cyan-500/400\r\n  ];\r\n\r\n  // Create chart config for each dataset\r\n  data.datasets.forEach((dataset, index) => {\r\n    chartConfig[dataset.label] = {\r\n      label: dataset.label,\r\n      theme: {\r\n        light:\r\n          (dataset.borderColor as string) ||\r\n          colors[index % colors.length].light,\r\n        dark: colors[index % colors.length].dark,\r\n      },\r\n    };\r\n\r\n    // For pie charts, add colors for each label\r\n    if (chartTypes.includes(\"pie\")) {\r\n      data.labels.forEach((label, labelIndex) => {\r\n        const labelKey = label.toString().replace(/\\s+/g, \"-\").toLowerCase();\r\n        if (!chartConfig[labelKey]) {\r\n          chartConfig[labelKey] = {\r\n            label: label.toString(),\r\n            theme: {\r\n              light: colors[labelIndex % colors.length].light,\r\n              dark: colors[labelIndex % colors.length].dark,\r\n            },\r\n          };\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  // Prepare data for charts\r\n  const chartData = data.labels.map((label, i) => ({\r\n    name: label,\r\n    ...data.datasets.reduce(\r\n      (acc, dataset) => ({\r\n        ...acc,\r\n        [dataset.label]: dataset.data[i],\r\n      }),\r\n      {}\r\n    ),\r\n  }));\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader>\r\n        <CardTitle>{title}</CardTitle>\r\n        {description && (\r\n          <p className=\"text-sm text-muted-foreground\">{description}</p>\r\n        )}\r\n      </CardHeader>\r\n      <CardContent>\r\n        <Tabs defaultValue={defaultChartType}>\r\n          <TabsList className=\"mb-4\">\r\n            {chartTypes.includes(\"line\") && (\r\n              <TabsTrigger value=\"line\">Line</TabsTrigger>\r\n            )}\r\n            {chartTypes.includes(\"bar\") && (\r\n              <TabsTrigger value=\"bar\">Bar</TabsTrigger>\r\n            )}\r\n            {chartTypes.includes(\"pie\") && (\r\n              <TabsTrigger value=\"pie\">Pie</TabsTrigger>\r\n            )}\r\n          </TabsList>\r\n\r\n          {chartTypes.includes(\"line\") && (\r\n            <TabsContent value=\"line\" className=\"space-y-4\">\r\n              <ChartContainer\r\n                config={chartConfig}\r\n                className=\"h-[250px]\"\r\n              >\r\n                <LineChart\r\n                  accessibilityLayer\r\n                  data={chartData}\r\n                  margin={{ top: 10, right: 30, left: 20, bottom: 20 }}\r\n                >\r\n                  {showGrid && (\r\n                    <CartesianGrid strokeDasharray=\"3 3\" vertical={false} />\r\n                  )}\r\n                  <XAxis\r\n                    dataKey=\"name\"\r\n                    tickLine={false}\r\n                    axisLine={false}\r\n                    tickMargin={10}\r\n                    tickFormatter={(value) =>\r\n                      typeof value === \"string\" && value.length > 10\r\n                        ? `${value.slice(0, 10)}...`\r\n                        : value\r\n                    }\r\n                  />\r\n                  <YAxis tickLine={false} axisLine={false} tickMargin={10} />\r\n                  {showTooltip && (\r\n                    <ChartTooltip\r\n                      cursor={false}\r\n                      content={<ChartTooltipContent hideLabel />}\r\n                    />\r\n                  )}\r\n                  {showLegend && (\r\n                    <ChartLegend\r\n                      content={<ChartLegendContent />}\r\n                      verticalAlign=\"top\"\r\n                      height={36}\r\n                    />\r\n                  )}\r\n                  {data.datasets.map((dataset) => (\r\n                    <Line\r\n                      key={dataset.label}\r\n                      type=\"natural\"\r\n                      dataKey={dataset.label}\r\n                      stroke={`var(--color-${dataset.label})`}\r\n                      strokeWidth={2}\r\n                      dot={{\r\n                        fill: `var(--color-${dataset.label})`,\r\n                        r: 4,\r\n                      }}\r\n                      activeDot={{\r\n                        r: 6,\r\n                        strokeWidth: 2,\r\n                      }}\r\n                    />\r\n                  ))}\r\n                </LineChart>\r\n              </ChartContainer>\r\n            </TabsContent>\r\n          )}\r\n\r\n          {chartTypes.includes(\"bar\") && (\r\n            <TabsContent value=\"bar\" className=\"space-y-4\">\r\n              <ChartContainer\r\n                config={chartConfig}\r\n                className=\"h-[250px]\"\r\n              >\r\n                <BarChart\r\n                  accessibilityLayer\r\n                  data={chartData}\r\n                  margin={{ top: 10, right: 30, left: 20, bottom: 20 }}\r\n                >\r\n                  {showGrid && (\r\n                    <CartesianGrid strokeDasharray=\"3 3\" vertical={false} />\r\n                  )}\r\n                  <XAxis\r\n                    dataKey=\"name\"\r\n                    tickLine={false}\r\n                    axisLine={false}\r\n                    tickMargin={10}\r\n                    tickFormatter={(value) =>\r\n                      typeof value === \"string\" && value.length > 10\r\n                        ? `${value.slice(0, 10)}...`\r\n                        : value\r\n                    }\r\n                  />\r\n                  <YAxis tickLine={false} axisLine={false} tickMargin={10} />\r\n                  {showTooltip && (\r\n                    <ChartTooltip content={<ChartTooltipContent hideLabel />} />\r\n                  )}\r\n                  {showLegend && (\r\n                    <ChartLegend\r\n                      content={<ChartLegendContent />}\r\n                      verticalAlign=\"top\"\r\n                      height={36}\r\n                    />\r\n                  )}\r\n                  {data.datasets.map((dataset) => (\r\n                    <Bar\r\n                      key={dataset.label}\r\n                      dataKey={dataset.label}\r\n                      fill={`var(--color-${dataset.label})`}\r\n                      radius={[4, 4, 4, 4]}\r\n                      maxBarSize={60}\r\n                    />\r\n                  ))}\r\n                </BarChart>\r\n              </ChartContainer>\r\n            </TabsContent>\r\n          )}\r\n\r\n          {chartTypes.includes(\"pie\") && (\r\n            <TabsContent value=\"pie\" className=\"space-y-4\">\r\n              <ChartContainer\r\n                config={chartConfig}\r\n                className=\"h-[250px]\"\r\n              >\r\n                <PieChart margin={{ top: 10, right: 30, left: 20, bottom: 20 }}>\r\n                  {data.datasets.map((dataset) => {\r\n                    const pieData = data.labels.map((label, i) => ({\r\n                      name: label,\r\n                      value: dataset.data[i],\r\n                      dataKey: dataset.label,\r\n                      fill: `var(--color-${label\r\n                        .toString()\r\n                        .replace(/\\s+/g, \"-\")\r\n                        .toLowerCase()})`,\r\n                    }));\r\n\r\n                    return (\r\n                      <Pie\r\n                        key={dataset.label}\r\n                        data={pieData}\r\n                        cx=\"50%\"\r\n                        cy=\"50%\"\r\n                        outerRadius={80}\r\n                        innerRadius={30}\r\n                        paddingAngle={2}\r\n                        dataKey=\"value\"\r\n                        nameKey=\"name\"\r\n                        label={({ name, percent }) =>\r\n                          percent > 0.05\r\n                            ? `${name}: ${(percent * 100).toFixed(0)}%`\r\n                            : \"\"\r\n                        }\r\n                        labelLine={false}\r\n                        className=\"[&_.recharts-pie-label-text]:fill-foreground\"\r\n                      >\r\n                        {pieData.map((entry, index) => (\r\n                          <Cell key={`cell-${index}`} fill={entry.fill} />\r\n                        ))}\r\n                      </Pie>\r\n                    );\r\n                  })}\r\n                  {showTooltip && (\r\n                    <ChartTooltip content={<ChartTooltipContent hideLabel />} />\r\n                  )}\r\n                  {showLegend && (\r\n                    <ChartLegend\r\n                      content={<ChartLegendContent />}\r\n                      verticalAlign=\"bottom\"\r\n                      height={36}\r\n                      iconSize={10}\r\n                      iconType=\"circle\"\r\n                    />\r\n                  )}\r\n                </PieChart>\r\n              </ChartContainer>\r\n            </TabsContent>\r\n          )}\r\n        </Tabs>\r\n      </CardContent>\r\n      {trendInfo && (\r\n        <div className=\"border-t px-6 py-4 flex-col items-start gap-2 text-sm\">\r\n          <div className=\"flex items-center gap-2 font-medium leading-none\">\r\n            {trendInfo.isPositive ? (\r\n              <>\r\n                Trending up by {Math.abs(trendInfo.value).toFixed(1)}%{\" \"}\r\n                {trendInfo.label || \"\"}\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  className=\"h-4 w-4 text-emerald-500\"\r\n                >\r\n                  <path d=\"m6 9 6-6 6 6\" />\r\n                  <path d=\"M6 12h12\" />\r\n                  <path d=\"M6 15h12\" />\r\n                  <path d=\"M6 18h12\" />\r\n                </svg>\r\n              </>\r\n            ) : (\r\n              <>\r\n                Trending down by {Math.abs(trendInfo.value).toFixed(1)}%{\" \"}\r\n                {trendInfo.label || \"\"}\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  className=\"h-4 w-4 text-red-500\"\r\n                >\r\n                  <path d=\"m6 15 6 6 6-6\" />\r\n                  <path d=\"M6 6h12\" />\r\n                  <path d=\"M6 9h12\" />\r\n                  <path d=\"M6 12h12\" />\r\n                </svg>\r\n              </>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAjBA;;;;;;AA4CO,SAAS,YAAY,EAC1B,KAAK,EACL,WAAW,EACX,IAAI,EACJ,aAAa;IAAC;IAAQ;CAAM,EAC5B,mBAAmB,MAAM,EACzB,SAAS,GAAG,EACZ,aAAa,IAAI,EACjB,WAAW,IAAI,EACf,cAAc,IAAI,EAClB,SAAS,EACT,SAAS,EACQ;IACjB,4CAA4C;IAC5C,MAAM,cAA2B,CAAC;IAElC,6BAA6B;IAC7B,MAAM,SAAS;QACb;YAAE,OAAO;YAAW,MAAM;QAAU;QACpC;YAAE,OAAO;YAAW,MAAM;QAAU;QACpC;YAAE,OAAO;YAAW,MAAM;QAAU;QACpC;YAAE,OAAO;YAAW,MAAM;QAAU;QACpC;YAAE,OAAO;YAAW,MAAM;QAAU;QACpC;YAAE,OAAO;YAAW,MAAM;QAAU;QACpC;YAAE,OAAO;YAAW,MAAM;QAAU;KACrC;IAED,uCAAuC;IACvC,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC,SAAS;QAC9B,WAAW,CAAC,QAAQ,KAAK,CAAC,GAAG;YAC3B,OAAO,QAAQ,KAAK;YACpB,OAAO;gBACL,OACE,AAAC,QAAQ,WAAW,IACpB,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC,CAAC,KAAK;gBACrC,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC,CAAC,IAAI;YAC1C;QACF;QAEA,4CAA4C;QAC5C,IAAI,WAAW,QAAQ,CAAC,QAAQ;YAC9B,KAAK,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO;gBAC1B,MAAM,WAAW,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,KAAK,WAAW;gBAClE,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;oBAC1B,WAAW,CAAC,SAAS,GAAG;wBACtB,OAAO,MAAM,QAAQ;wBACrB,OAAO;4BACL,OAAO,MAAM,CAAC,aAAa,OAAO,MAAM,CAAC,CAAC,KAAK;4BAC/C,MAAM,MAAM,CAAC,aAAa,OAAO,MAAM,CAAC,CAAC,IAAI;wBAC/C;oBACF;gBACF;YACF;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,YAAY,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,IAAM,CAAC;YAC/C,MAAM;YACN,GAAG,KAAK,QAAQ,CAAC,MAAM,CACrB,CAAC,KAAK,UAAY,CAAC;oBACjB,GAAG,GAAG;oBACN,CAAC,QAAQ,KAAK,CAAC,EAAE,QAAQ,IAAI,CAAC,EAAE;gBAClC,CAAC,GACD,CAAC,EACF;QACH,CAAC;IAED,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCAAE;;;;;;oBACX,6BACC,8OAAC;wBAAE,WAAU;kCAAiC;;;;;;;;;;;;0BAGlD,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,cAAc;;sCAClB,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;gCACjB,WAAW,QAAQ,CAAC,yBACnB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAO;;;;;;gCAE3B,WAAW,QAAQ,CAAC,wBACnB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAM;;;;;;gCAE1B,WAAW,QAAQ,CAAC,wBACnB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAM;;;;;;;;;;;;wBAI5B,WAAW,QAAQ,CAAC,yBACnB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAO,WAAU;sCAClC,cAAA,8OAAC,iIAAA,CAAA,iBAAc;gCACb,QAAQ;gCACR,WAAU;0CAEV,cAAA,8OAAC,qJAAA,CAAA,YAAS;oCACR,kBAAkB;oCAClB,MAAM;oCACN,QAAQ;wCAAE,KAAK;wCAAI,OAAO;wCAAI,MAAM;wCAAI,QAAQ;oCAAG;;wCAElD,0BACC,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;4CAAM,UAAU;;;;;;sDAEjD,8OAAC,qJAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,UAAU;4CACV,UAAU;4CACV,YAAY;4CACZ,eAAe,CAAC,QACd,OAAO,UAAU,YAAY,MAAM,MAAM,GAAG,KACxC,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,GAC1B;;;;;;sDAGR,8OAAC,qJAAA,CAAA,QAAK;4CAAC,UAAU;4CAAO,UAAU;4CAAO,YAAY;;;;;;wCACpD,6BACC,8OAAC,iIAAA,CAAA,eAAY;4CACX,QAAQ;4CACR,uBAAS,8OAAC,iIAAA,CAAA,sBAAmB;gDAAC,SAAS;;;;;;;;;;;wCAG1C,4BACC,8OAAC,iIAAA,CAAA,cAAW;4CACV,uBAAS,8OAAC,iIAAA,CAAA,qBAAkB;;;;;4CAC5B,eAAc;4CACd,QAAQ;;;;;;wCAGX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,8OAAC,oJAAA,CAAA,OAAI;gDAEH,MAAK;gDACL,SAAS,QAAQ,KAAK;gDACtB,QAAQ,CAAC,YAAY,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;gDACvC,aAAa;gDACb,KAAK;oDACH,MAAM,CAAC,YAAY,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;oDACrC,GAAG;gDACL;gDACA,WAAW;oDACT,GAAG;oDACH,aAAa;gDACf;+CAZK,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;wBAoB7B,WAAW,QAAQ,CAAC,wBACnB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAM,WAAU;sCACjC,cAAA,8OAAC,iIAAA,CAAA,iBAAc;gCACb,QAAQ;gCACR,WAAU;0CAEV,cAAA,8OAAC,oJAAA,CAAA,WAAQ;oCACP,kBAAkB;oCAClB,MAAM;oCACN,QAAQ;wCAAE,KAAK;wCAAI,OAAO;wCAAI,MAAM;wCAAI,QAAQ;oCAAG;;wCAElD,0BACC,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;4CAAM,UAAU;;;;;;sDAEjD,8OAAC,qJAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,UAAU;4CACV,UAAU;4CACV,YAAY;4CACZ,eAAe,CAAC,QACd,OAAO,UAAU,YAAY,MAAM,MAAM,GAAG,KACxC,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,GAC1B;;;;;;sDAGR,8OAAC,qJAAA,CAAA,QAAK;4CAAC,UAAU;4CAAO,UAAU;4CAAO,YAAY;;;;;;wCACpD,6BACC,8OAAC,iIAAA,CAAA,eAAY;4CAAC,uBAAS,8OAAC,iIAAA,CAAA,sBAAmB;gDAAC,SAAS;;;;;;;;;;;wCAEtD,4BACC,8OAAC,iIAAA,CAAA,cAAW;4CACV,uBAAS,8OAAC,iIAAA,CAAA,qBAAkB;;;;;4CAC5B,eAAc;4CACd,QAAQ;;;;;;wCAGX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,8OAAC,mJAAA,CAAA,MAAG;gDAEF,SAAS,QAAQ,KAAK;gDACtB,MAAM,CAAC,YAAY,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;gDACrC,QAAQ;oDAAC;oDAAG;oDAAG;oDAAG;iDAAE;gDACpB,YAAY;+CAJP,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;wBAY7B,WAAW,QAAQ,CAAC,wBACnB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAM,WAAU;sCACjC,cAAA,8OAAC,iIAAA,CAAA,iBAAc;gCACb,QAAQ;gCACR,WAAU;0CAEV,cAAA,8OAAC,oJAAA,CAAA,WAAQ;oCAAC,QAAQ;wCAAE,KAAK;wCAAI,OAAO;wCAAI,MAAM;wCAAI,QAAQ;oCAAG;;wCAC1D,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC;4CAClB,MAAM,UAAU,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,IAAM,CAAC;oDAC7C,MAAM;oDACN,OAAO,QAAQ,IAAI,CAAC,EAAE;oDACtB,SAAS,QAAQ,KAAK;oDACtB,MAAM,CAAC,YAAY,EAAE,MAClB,QAAQ,GACR,OAAO,CAAC,QAAQ,KAChB,WAAW,GAAG,CAAC,CAAC;gDACrB,CAAC;4CAED,qBACE,8OAAC,+IAAA,CAAA,MAAG;gDAEF,MAAM;gDACN,IAAG;gDACH,IAAG;gDACH,aAAa;gDACb,aAAa;gDACb,cAAc;gDACd,SAAQ;gDACR,SAAQ;gDACR,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GACvB,UAAU,OACN,GAAG,KAAK,EAAE,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACzC;gDAEN,WAAW;gDACX,WAAU;0DAET,QAAQ,GAAG,CAAC,CAAC,OAAO,sBACnB,8OAAC,oJAAA,CAAA,OAAI;wDAAuB,MAAM,MAAM,IAAI;uDAAjC,CAAC,KAAK,EAAE,OAAO;;;;;+CAlBvB,QAAQ,KAAK;;;;;wCAsBxB;wCACC,6BACC,8OAAC,iIAAA,CAAA,eAAY;4CAAC,uBAAS,8OAAC,iIAAA,CAAA,sBAAmB;gDAAC,SAAS;;;;;;;;;;;wCAEtD,4BACC,8OAAC,iIAAA,CAAA,cAAW;4CACV,uBAAS,8OAAC,iIAAA,CAAA,qBAAkB;;;;;4CAC5B,eAAc;4CACd,QAAQ;4CACR,UAAU;4CACV,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASxB,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,UAAU,UAAU,iBACnB;;4BAAE;4BACgB,KAAK,GAAG,CAAC,UAAU,KAAK,EAAE,OAAO,CAAC;4BAAG;4BAAE;4BACtD,UAAU,KAAK,IAAI;0CACpB,8OAAC;gCACC,OAAM;gCACN,SAAQ;gCACR,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,eAAc;gCACd,gBAAe;gCACf,WAAU;;kDAEV,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;qDAIZ;;4BAAE;4BACkB,KAAK,GAAG,CAAC,UAAU,KAAK,EAAE,OAAO,CAAC;4BAAG;4BAAE;4BACxD,UAAU,KAAK,IAAI;0CACpB,8OAAC;gCACC,OAAM;gCACN,SAAQ;gCACR,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,eAAc;gCACd,gBAAe;gCACf,WAAU;;kDAEV,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1B", "debugId": null}}, {"offset": {"line": 8648, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/banking/utils/export-banking-data.ts"], "sourcesContent": ["import * as XLSX from \"xlsx\";\r\nimport { BankingSummary } from \"@/types\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport { format } from \"date-fns\";\r\n\r\n/**\r\n * Export banking summary data to Excel\r\n * @param summaryData The banking summary data to export\r\n * @param filename The filename for the Excel file (without extension)\r\n */\r\nexport function exportBankingSummaryToExcel(\r\n  summaryData: BankingSummary[],\r\n  filename: string = \"banking-summary-report\"\r\n): void {\r\n  try {\r\n    // Create a new workbook\r\n    const workbook = XLSX.utils.book_new();\r\n\r\n    // Prepare summary data\r\n    const summaryHeaders = [\r\n      \"Date\",\r\n      \"Bank Deposits\",\r\n      \"M-Pesa Transactions\",\r\n      \"Agent Transactions\",\r\n      \"Total Amount\",\r\n      \"Transaction Count\"\r\n    ];\r\n\r\n    // Sort data by date (descending)\r\n    const sortedData = [...summaryData].sort((a, b) =>\r\n      new Date(b.date).getTime() - new Date(a.date).getTime()\r\n    );\r\n\r\n    const summaryRows = sortedData.map((item) => [\r\n      format(new Date(item.date), \"PPP\"),\r\n      formatCurrency(item.bank),\r\n      formatCurrency(item.mpesa),\r\n      formatCurrency(item.agent),\r\n      formatCurrency(item.total),\r\n      item.transaction_count\r\n    ]);\r\n\r\n    // Create summary sheet\r\n    const summarySheet = XLSX.utils.aoa_to_sheet([summaryHeaders, ...summaryRows]);\r\n    XLSX.utils.book_append_sheet(workbook, summarySheet, \"Summary\");\r\n\r\n    // Add method breakdown sheet\r\n    const methodBreakdownHeaders = [\"Banking Method\", \"Total Amount\", \"Percentage\"];\r\n    \r\n    const totalBank = sortedData.reduce((sum, item) => sum + item.bank, 0);\r\n    const totalMpesa = sortedData.reduce((sum, item) => sum + item.mpesa, 0);\r\n    const totalAgent = sortedData.reduce((sum, item) => sum + item.agent, 0);\r\n    const grandTotal = totalBank + totalMpesa + totalAgent;\r\n\r\n    const methodBreakdownRows = [\r\n      [\"Bank\", formatCurrency(totalBank), ((totalBank / grandTotal) * 100).toFixed(2) + \"%\"],\r\n      [\"M-Pesa\", formatCurrency(totalMpesa), ((totalMpesa / grandTotal) * 100).toFixed(2) + \"%\"],\r\n      [\"Agent\", formatCurrency(totalAgent), ((totalAgent / grandTotal) * 100).toFixed(2) + \"%\"],\r\n      [\"Total\", formatCurrency(grandTotal), \"100.00%\"]\r\n    ];\r\n\r\n    const methodBreakdownSheet = XLSX.utils.aoa_to_sheet([\r\n      methodBreakdownHeaders,\r\n      ...methodBreakdownRows\r\n    ]);\r\n    XLSX.utils.book_append_sheet(workbook, methodBreakdownSheet, \"Method Breakdown\");\r\n\r\n    // Add statistics sheet\r\n    const statisticsHeaders = [\"Metric\", \"Value\"];\r\n    \r\n    // Calculate statistics\r\n    const totalAmount = sortedData.reduce((sum, item) => sum + item.total, 0);\r\n    const totalTransactions = sortedData.reduce((sum, item) => sum + item.transaction_count, 0);\r\n    const averageTransaction = totalTransactions > 0 ? totalAmount / totalTransactions : 0;\r\n    \r\n    // Calculate daily averages\r\n    const dailyAmounts = sortedData.map(item => item.total);\r\n    const dailyAverage = dailyAmounts.reduce((sum, amount) => sum + amount, 0) / dailyAmounts.length;\r\n    \r\n    // Calculate median transaction amount\r\n    const sortedAmounts = [...dailyAmounts].sort((a, b) => a - b);\r\n    const medianAmount = sortedAmounts.length % 2 === 0\r\n      ? (sortedAmounts[sortedAmounts.length / 2 - 1] + sortedAmounts[sortedAmounts.length / 2]) / 2\r\n      : sortedAmounts[Math.floor(sortedAmounts.length / 2)];\r\n    \r\n    // Calculate variance\r\n    const variance = dailyAmounts.reduce((sum, amount) => sum + Math.pow(amount - dailyAverage, 2), 0) / dailyAmounts.length;\r\n    const standardDeviation = Math.sqrt(variance);\r\n\r\n    const statisticsRows = [\r\n      [\"Total Banking Amount\", formatCurrency(totalAmount)],\r\n      [\"Total Transactions\", totalTransactions.toString()],\r\n      [\"Average Transaction Amount\", formatCurrency(averageTransaction)],\r\n      [\"Daily Average\", formatCurrency(dailyAverage)],\r\n      [\"Median Daily Amount\", formatCurrency(medianAmount)],\r\n      [\"Standard Deviation\", formatCurrency(standardDeviation)],\r\n      [\"Date Range\", `${format(new Date(sortedData[sortedData.length - 1].date), \"PPP\")} to ${format(new Date(sortedData[0].date), \"PPP\")}`]\r\n    ];\r\n\r\n    const statisticsSheet = XLSX.utils.aoa_to_sheet([\r\n      statisticsHeaders,\r\n      ...statisticsRows\r\n    ]);\r\n    XLSX.utils.book_append_sheet(workbook, statisticsSheet, \"Statistics\");\r\n\r\n    // Set column widths for all sheets\r\n    const sheets = workbook.SheetNames;\r\n    sheets.forEach((sheetName) => {\r\n      const sheet = workbook.Sheets[sheetName];\r\n      const columnWidths = [\r\n        { wch: 25 }, // First column (wider for dates/names)\r\n        { wch: 20 }, // Second column\r\n        { wch: 20 }, // Third column\r\n        { wch: 20 }, // Fourth column\r\n        { wch: 20 }, // Fifth column\r\n        { wch: 15 }, // Sixth column\r\n      ];\r\n      sheet[\"!cols\"] = columnWidths;\r\n    });\r\n\r\n    // Generate Excel file and trigger download\r\n    XLSX.writeFile(workbook, `${filename}.xlsx`);\r\n  } catch (error) {\r\n    console.error(\"Error exporting banking summary to Excel:\", error);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;;;;AAOO,SAAS,4BACd,WAA6B,EAC7B,WAAmB,wBAAwB;IAE3C,IAAI;QACF,wBAAwB;QACxB,MAAM,WAAW,6HAAA,CAAA,QAAU,CAAC,QAAQ;QAEpC,uBAAuB;QACvB,MAAM,iBAAiB;YACrB;YACA;YACA;YACA;YACA;YACA;SACD;QAED,iCAAiC;QACjC,MAAM,aAAa;eAAI;SAAY,CAAC,IAAI,CAAC,CAAC,GAAG,IAC3C,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;QAGvD,MAAM,cAAc,WAAW,GAAG,CAAC,CAAC,OAAS;gBAC3C,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,IAAI,GAAG;gBAC5B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,IAAI;gBACxB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK;gBACzB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK;gBACzB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK;gBACzB,KAAK,iBAAiB;aACvB;QAED,uBAAuB;QACvB,MAAM,eAAe,6HAAA,CAAA,QAAU,CAAC,YAAY,CAAC;YAAC;eAAmB;SAAY;QAC7E,6HAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,cAAc;QAErD,6BAA6B;QAC7B,MAAM,yBAAyB;YAAC;YAAkB;YAAgB;SAAa;QAE/E,MAAM,YAAY,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,IAAI,EAAE;QACpE,MAAM,aAAa,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;QACtE,MAAM,aAAa,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;QACtE,MAAM,aAAa,YAAY,aAAa;QAE5C,MAAM,sBAAsB;YAC1B;gBAAC;gBAAQ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;gBAAY,CAAC,AAAC,YAAY,aAAc,GAAG,EAAE,OAAO,CAAC,KAAK;aAAI;YACtF;gBAAC;gBAAU,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;gBAAa,CAAC,AAAC,aAAa,aAAc,GAAG,EAAE,OAAO,CAAC,KAAK;aAAI;YAC1F;gBAAC;gBAAS,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;gBAAa,CAAC,AAAC,aAAa,aAAc,GAAG,EAAE,OAAO,CAAC,KAAK;aAAI;YACzF;gBAAC;gBAAS,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;gBAAa;aAAU;SACjD;QAED,MAAM,uBAAuB,6HAAA,CAAA,QAAU,CAAC,YAAY,CAAC;YACnD;eACG;SACJ;QACD,6HAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,sBAAsB;QAE7D,uBAAuB;QACvB,MAAM,oBAAoB;YAAC;YAAU;SAAQ;QAE7C,uBAAuB;QACvB,MAAM,cAAc,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;QACvE,MAAM,oBAAoB,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,iBAAiB,EAAE;QACzF,MAAM,qBAAqB,oBAAoB,IAAI,cAAc,oBAAoB;QAErF,2BAA2B;QAC3B,MAAM,eAAe,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;QACtD,MAAM,eAAe,aAAa,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,QAAQ,KAAK,aAAa,MAAM;QAEhG,sCAAsC;QACtC,MAAM,gBAAgB;eAAI;SAAa,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QAC3D,MAAM,eAAe,cAAc,MAAM,GAAG,MAAM,IAC9C,CAAC,aAAa,CAAC,cAAc,MAAM,GAAG,IAAI,EAAE,GAAG,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE,IAAI,IAC1F,aAAa,CAAC,KAAK,KAAK,CAAC,cAAc,MAAM,GAAG,GAAG;QAEvD,qBAAqB;QACrB,MAAM,WAAW,aAAa,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,KAAK,GAAG,CAAC,SAAS,cAAc,IAAI,KAAK,aAAa,MAAM;QACxH,MAAM,oBAAoB,KAAK,IAAI,CAAC;QAEpC,MAAM,iBAAiB;YACrB;gBAAC;gBAAwB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;aAAa;YACrD;gBAAC;gBAAsB,kBAAkB,QAAQ;aAAG;YACpD;gBAAC;gBAA8B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;aAAoB;YAClE;gBAAC;gBAAiB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;aAAc;YAC/C;gBAAC;gBAAuB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;aAAc;YACrD;gBAAC;gBAAsB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;aAAmB;YACzD;gBAAC;gBAAc,GAAG,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,IAAI,GAAG,OAAO,IAAI,EAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,CAAC,EAAE,CAAC,IAAI,GAAG,QAAQ;aAAC;SACvI;QAED,MAAM,kBAAkB,6HAAA,CAAA,QAAU,CAAC,YAAY,CAAC;YAC9C;eACG;SACJ;QACD,6HAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,iBAAiB;QAExD,mCAAmC;QACnC,MAAM,SAAS,SAAS,UAAU;QAClC,OAAO,OAAO,CAAC,CAAC;YACd,MAAM,QAAQ,SAAS,MAAM,CAAC,UAAU;YACxC,MAAM,eAAe;gBACnB;oBAAE,KAAK;gBAAG;gBACV;oBAAE,KAAK;gBAAG;gBACV;oBAAE,KAAK;gBAAG;gBACV;oBAAE,KAAK;gBAAG;gBACV;oBAAE,KAAK;gBAAG;gBACV;oBAAE,KAAK;gBAAG;aACX;YACD,KAAK,CAAC,QAAQ,GAAG;QACnB;QAEA,2CAA2C;QAC3C,CAAA,GAAA,6HAAA,CAAA,YAAc,AAAD,EAAE,UAAU,GAAG,SAAS,KAAK,CAAC;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;IAC7D;AACF", "debugId": null}}, {"offset": {"line": 8818, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/command.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Command as CommandPrimitive } from \"cmdk\"\r\nimport { SearchIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\"\r\n\r\nfunction Command({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive>) {\r\n  return (\r\n    <CommandPrimitive\r\n      data-slot=\"command\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandDialog({\r\n  title = \"Command Palette\",\r\n  description = \"Search for a command to run...\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof Dialog> & {\r\n  title?: string\r\n  description?: string\r\n}) {\r\n  return (\r\n    <Dialog {...props}>\r\n      <DialogHeader className=\"sr-only\">\r\n        <DialogTitle>{title}</DialogTitle>\r\n        <DialogDescription>{description}</DialogDescription>\r\n      </DialogHeader>\r\n      <DialogContent className=\"overflow-hidden p-0\">\r\n        <Command className=\"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nfunction CommandInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Input>) {\r\n  return (\r\n    <div\r\n      data-slot=\"command-input-wrapper\"\r\n      className=\"flex h-9 items-center gap-2 border-b px-3\"\r\n    >\r\n      <SearchIcon className=\"size-4 shrink-0 opacity-50\" />\r\n      <CommandPrimitive.Input\r\n        data-slot=\"command-input\"\r\n        className={cn(\r\n          \"placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CommandList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.List>) {\r\n  return (\r\n    <CommandPrimitive.List\r\n      data-slot=\"command-list\"\r\n      className={cn(\r\n        \"max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandEmpty({\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Empty>) {\r\n  return (\r\n    <CommandPrimitive.Empty\r\n      data-slot=\"command-empty\"\r\n      className=\"py-6 text-center text-sm\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandGroup({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Group>) {\r\n  return (\r\n    <CommandPrimitive.Group\r\n      data-slot=\"command-group\"\r\n      className={cn(\r\n        \"text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Separator>) {\r\n  return (\r\n    <CommandPrimitive.Separator\r\n      data-slot=\"command-separator\"\r\n      className={cn(\"bg-border -mx-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Item>) {\r\n  return (\r\n    <CommandPrimitive.Item\r\n      data-slot=\"command-item\"\r\n      className={cn(\r\n        \"data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"command-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AAeA,SAAS,QAAQ,EACf,SAAS,EACT,GAAG,OAC2C;IAC9C,qBACE,8OAAC,sIAAA,CAAA,UAAgB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,QAAQ,iBAAiB,EACzB,cAAc,gCAAgC,EAC9C,QAAQ,EACR,GAAG,OAIJ;IACC,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAE,GAAG,KAAK;;0BACf,8OAAC,kIAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,8OAAC,kIAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,8OAAC,kIAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAEtB,8OAAC,kIAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,8OAAC;oBAAQ,WAAU;8BAChB;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;;0BAEV,8OAAC,0MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,SAAS;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QACrC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 9001, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Switch({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\r\n  return (\r\n    <SwitchPrimitive.Root\r\n      data-slot=\"switch\"\r\n      className={cn(\r\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <SwitchPrimitive.Thumb\r\n        data-slot=\"switch-thumb\"\r\n        className={cn(\r\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\r\n        )}\r\n      />\r\n    </SwitchPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,kKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 9037, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 9065, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/banking/components/banking-preferences.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { Check, ChevronsUpDown, X } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n} from \"@/components/ui/command\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { toast } from \"sonner\";\r\n\r\n// Define the available metrics and charts for banking reports\r\nconst availableMetrics = [\r\n  { id: \"total_amount\", name: \"Total Banking Amount\", default: true },\r\n  { id: \"total_transactions\", name: \"Total Transactions\", default: true },\r\n  { id: \"average_transaction\", name: \"Average Transaction\", default: true },\r\n  { id: \"daily_average\", name: \"Daily Average\", default: false },\r\n  { id: \"median_amount\", name: \"Median Amount\", default: false },\r\n  { id: \"standard_deviation\", name: \"Standard Deviation\", default: false },\r\n  { id: \"period_comparison\", name: \"Period Comparison\", default: true },\r\n  { id: \"growth_rate\", name: \"Growth Rate\", default: false },\r\n];\r\n\r\nconst availableCharts = [\r\n  { id: \"method_breakdown\", name: \"Banking Method Breakdown\", default: true },\r\n  { id: \"daily_trend\", name: \"Daily Banking Trend\", default: true },\r\n  { id: \"method_comparison\", name: \"Method Comparison\", default: false },\r\n  { id: \"weekly_summary\", name: \"Weekly Summary\", default: false },\r\n  { id: \"monthly_trend\", name: \"Monthly Trend\", default: false },\r\n];\r\n\r\n// Define the banking preferences type\r\nexport interface BankingPreferences {\r\n  metrics: string[];\r\n  charts: string[];\r\n  showDataTable: boolean;\r\n  enableAutoRefresh: boolean;\r\n  refreshInterval: number;\r\n}\r\n\r\n// Default preferences\r\nconst defaultPreferences: BankingPreferences = {\r\n  metrics: availableMetrics.filter(m => m.default).map(m => m.id),\r\n  charts: availableCharts.filter(c => c.default).map(c => c.id),\r\n  showDataTable: true,\r\n  enableAutoRefresh: false,\r\n  refreshInterval: 5,\r\n};\r\n\r\ninterface BankingPreferencesProps {\r\n  onPreferencesChange: (preferences: BankingPreferences) => void;\r\n}\r\n\r\nexport function BankingPreferences({ onPreferencesChange }: BankingPreferencesProps) {\r\n  // Load preferences from localStorage or use defaults\r\n  const loadPreferences = (): BankingPreferences => {\r\n    if (typeof window === \"undefined\") return defaultPreferences;\r\n\r\n    const savedPreferences = localStorage.getItem(\"banking-preferences\");\r\n    if (savedPreferences) {\r\n      try {\r\n        return JSON.parse(savedPreferences);\r\n      } catch (e) {\r\n        console.error(\"Error parsing saved preferences:\", e);\r\n      }\r\n    }\r\n    return defaultPreferences;\r\n  };\r\n\r\n  const [preferences, setPreferences] = useState<BankingPreferences>(loadPreferences());\r\n  const [metricsOpen, setMetricsOpen] = useState(false);\r\n  const [chartsOpen, setChartsOpen] = useState(false);\r\n\r\n  // Save preferences to localStorage and notify parent component\r\n  const savePreferences = (newPreferences: BankingPreferences) => {\r\n    setPreferences(newPreferences);\r\n    localStorage.setItem(\"banking-preferences\", JSON.stringify(newPreferences));\r\n    onPreferencesChange(newPreferences);\r\n  };\r\n\r\n  // Toggle a metric\r\n  const toggleMetric = (metricId: string) => {\r\n    const newMetrics = preferences.metrics.includes(metricId)\r\n      ? preferences.metrics.filter(id => id !== metricId)\r\n      : [...preferences.metrics, metricId];\r\n\r\n    savePreferences({ ...preferences, metrics: newMetrics });\r\n  };\r\n\r\n  // Toggle a chart\r\n  const toggleChart = (chartId: string) => {\r\n    const newCharts = preferences.charts.includes(chartId)\r\n      ? preferences.charts.filter(id => id !== chartId)\r\n      : [...preferences.charts, chartId];\r\n\r\n    savePreferences({ ...preferences, charts: newCharts });\r\n  };\r\n\r\n  // Reset to defaults\r\n  const resetToDefaults = () => {\r\n    savePreferences(defaultPreferences);\r\n    toast.success(\"Preferences reset to defaults\");\r\n  };\r\n\r\n  // Initialize preferences on component mount\r\n  useEffect(() => {\r\n    onPreferencesChange(preferences);\r\n  }, []);\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>Dashboard Preferences</CardTitle>\r\n        <CardDescription>\r\n          Customize which metrics and charts appear in your banking reports\r\n        </CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div className=\"space-y-2\">\r\n          <Label>Selected Metrics</Label>\r\n          <div className=\"flex flex-wrap gap-2 mb-2\">\r\n            {preferences.metrics.map(metricId => {\r\n              const metric = availableMetrics.find(m => m.id === metricId);\r\n              return (\r\n                <Badge key={metricId} variant=\"secondary\" className=\"flex items-center gap-1\">\r\n                  {metric?.name}\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    className=\"h-4 w-4 p-0 ml-1\"\r\n                    onClick={() => toggleMetric(metricId)}\r\n                  >\r\n                    <X className=\"h-3 w-3\" />\r\n                  </Button>\r\n                </Badge>\r\n              );\r\n            })}\r\n          </div>\r\n          <Popover open={metricsOpen} onOpenChange={setMetricsOpen}>\r\n            <PopoverTrigger asChild>\r\n              <Button\r\n                variant=\"outline\"\r\n                role=\"combobox\"\r\n                aria-expanded={metricsOpen}\r\n                className=\"justify-between w-full\"\r\n              >\r\n                Select metrics to display\r\n                <ChevronsUpDown className=\"ml-2 h-4 w-4 shrink-0 opacity-50\" />\r\n              </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"p-0 w-full\">\r\n              <Command>\r\n                <CommandInput placeholder=\"Search metrics...\" />\r\n                <CommandEmpty>No metric found.</CommandEmpty>\r\n                <CommandGroup>\r\n                  {availableMetrics.map((metric) => (\r\n                    <CommandItem\r\n                      key={metric.id}\r\n                      value={metric.id}\r\n                      onSelect={() => toggleMetric(metric.id)}\r\n                    >\r\n                      <Check\r\n                        className={cn(\r\n                          \"mr-2 h-4 w-4\",\r\n                          preferences.metrics.includes(metric.id)\r\n                            ? \"opacity-100\"\r\n                            : \"opacity-0\"\r\n                        )}\r\n                      />\r\n                      {metric.name}\r\n                    </CommandItem>\r\n                  ))}\r\n                </CommandGroup>\r\n              </Command>\r\n            </PopoverContent>\r\n          </Popover>\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <Label>Selected Charts</Label>\r\n          <div className=\"flex flex-wrap gap-2 mb-2\">\r\n            {preferences.charts.map(chartId => {\r\n              const chart = availableCharts.find(c => c.id === chartId);\r\n              return (\r\n                <Badge key={chartId} variant=\"secondary\" className=\"flex items-center gap-1\">\r\n                  {chart?.name}\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    className=\"h-4 w-4 p-0 ml-1\"\r\n                    onClick={() => toggleChart(chartId)}\r\n                  >\r\n                    <X className=\"h-3 w-3\" />\r\n                  </Button>\r\n                </Badge>\r\n              );\r\n            })}\r\n          </div>\r\n          <Popover open={chartsOpen} onOpenChange={setChartsOpen}>\r\n            <PopoverTrigger asChild>\r\n              <Button\r\n                variant=\"outline\"\r\n                role=\"combobox\"\r\n                aria-expanded={chartsOpen}\r\n                className=\"justify-between w-full\"\r\n              >\r\n                Select charts to display\r\n                <ChevronsUpDown className=\"ml-2 h-4 w-4 shrink-0 opacity-50\" />\r\n              </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"p-0 w-full\">\r\n              <Command>\r\n                <CommandInput placeholder=\"Search charts...\" />\r\n                <CommandEmpty>No chart found.</CommandEmpty>\r\n                <CommandGroup>\r\n                  {availableCharts.map((chart) => (\r\n                    <CommandItem\r\n                      key={chart.id}\r\n                      value={chart.id}\r\n                      onSelect={() => toggleChart(chart.id)}\r\n                    >\r\n                      <Check\r\n                        className={cn(\r\n                          \"mr-2 h-4 w-4\",\r\n                          preferences.charts.includes(chart.id)\r\n                            ? \"opacity-100\"\r\n                            : \"opacity-0\"\r\n                        )}\r\n                      />\r\n                      {chart.name}\r\n                    </CommandItem>\r\n                  ))}\r\n                </CommandGroup>\r\n              </Command>\r\n            </PopoverContent>\r\n          </Popover>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between\">\r\n          <Label htmlFor=\"show-data-table\">Show Data Table</Label>\r\n          <Switch\r\n            id=\"show-data-table\"\r\n            checked={preferences.showDataTable}\r\n            onCheckedChange={(checked) =>\r\n              savePreferences({ ...preferences, showDataTable: checked })\r\n            }\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between\">\r\n          <Label htmlFor=\"auto-refresh\">Enable Auto Refresh</Label>\r\n          <Switch\r\n            id=\"auto-refresh\"\r\n            checked={preferences.enableAutoRefresh}\r\n            onCheckedChange={(checked) =>\r\n              savePreferences({ ...preferences, enableAutoRefresh: checked })\r\n            }\r\n          />\r\n        </div>\r\n      </CardContent>\r\n      <CardFooter className=\"flex justify-between\">\r\n        <Button variant=\"outline\" onClick={resetToDefaults}>\r\n          Reset to Defaults\r\n        </Button>\r\n        <Button onClick={() => toast.success(\"Preferences saved\")}>\r\n          Save Preferences\r\n        </Button>\r\n      </CardFooter>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AAOA;AAKA;AAQA;AACA;AACA;AACA;AACA;AA7BA;;;;;;;;;;;;;AA+BA,8DAA8D;AAC9D,MAAM,mBAAmB;IACvB;QAAE,IAAI;QAAgB,MAAM;QAAwB,SAAS;IAAK;IAClE;QAAE,IAAI;QAAsB,MAAM;QAAsB,SAAS;IAAK;IACtE;QAAE,IAAI;QAAuB,MAAM;QAAuB,SAAS;IAAK;IACxE;QAAE,IAAI;QAAiB,MAAM;QAAiB,SAAS;IAAM;IAC7D;QAAE,IAAI;QAAiB,MAAM;QAAiB,SAAS;IAAM;IAC7D;QAAE,IAAI;QAAsB,MAAM;QAAsB,SAAS;IAAM;IACvE;QAAE,IAAI;QAAqB,MAAM;QAAqB,SAAS;IAAK;IACpE;QAAE,IAAI;QAAe,MAAM;QAAe,SAAS;IAAM;CAC1D;AAED,MAAM,kBAAkB;IACtB;QAAE,IAAI;QAAoB,MAAM;QAA4B,SAAS;IAAK;IAC1E;QAAE,IAAI;QAAe,MAAM;QAAuB,SAAS;IAAK;IAChE;QAAE,IAAI;QAAqB,MAAM;QAAqB,SAAS;IAAM;IACrE;QAAE,IAAI;QAAkB,MAAM;QAAkB,SAAS;IAAM;IAC/D;QAAE,IAAI;QAAiB,MAAM;QAAiB,SAAS;IAAM;CAC9D;AAWD,sBAAsB;AACtB,MAAM,qBAAyC;IAC7C,SAAS,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IAC9D,QAAQ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IAC5D,eAAe;IACf,mBAAmB;IACnB,iBAAiB;AACnB;AAMO,SAAS,mBAAmB,EAAE,mBAAmB,EAA2B;IACjF,qDAAqD;IACrD,MAAM,kBAAkB;QACtB,wCAAmC,OAAO;;QAE1C,MAAM;IASR;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,+DAA+D;IAC/D,MAAM,kBAAkB,CAAC;QACvB,eAAe;QACf,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;QAC3D,oBAAoB;IACtB;IAEA,kBAAkB;IAClB,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,YAAY,OAAO,CAAC,QAAQ,CAAC,YAC5C,YAAY,OAAO,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,YACxC;eAAI,YAAY,OAAO;YAAE;SAAS;QAEtC,gBAAgB;YAAE,GAAG,WAAW;YAAE,SAAS;QAAW;IACxD;IAEA,iBAAiB;IACjB,MAAM,cAAc,CAAC;QACnB,MAAM,YAAY,YAAY,MAAM,CAAC,QAAQ,CAAC,WAC1C,YAAY,MAAM,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,WACvC;eAAI,YAAY,MAAM;YAAE;SAAQ;QAEpC,gBAAgB;YAAE,GAAG,WAAW;YAAE,QAAQ;QAAU;IACtD;IAEA,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB;IACtB,GAAG,EAAE;IAEL,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC;gCAAI,WAAU;0CACZ,YAAY,OAAO,CAAC,GAAG,CAAC,CAAA;oCACvB,MAAM,SAAS,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oCACnD,qBACE,8OAAC,iIAAA,CAAA,QAAK;wCAAgB,SAAQ;wCAAY,WAAU;;4CACjD,QAAQ;0DACT,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,aAAa;0DAE5B,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCARL;;;;;gCAYhB;;;;;;0CAEF,8OAAC,mIAAA,CAAA,UAAO;gCAAC,MAAM;gCAAa,cAAc;;kDACxC,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,iBAAe;4CACf,WAAU;;gDACX;8DAEC,8OAAC,8NAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG9B,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,WAAU;kDACxB,cAAA,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,eAAY;oDAAC,aAAY;;;;;;8DAC1B,8OAAC,mIAAA,CAAA,eAAY;8DAAC;;;;;;8DACd,8OAAC,mIAAA,CAAA,eAAY;8DACV,iBAAiB,GAAG,CAAC,CAAC,uBACrB,8OAAC,mIAAA,CAAA,cAAW;4DAEV,OAAO,OAAO,EAAE;4DAChB,UAAU,IAAM,aAAa,OAAO,EAAE;;8EAEtC,8OAAC,oMAAA,CAAA,QAAK;oEACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBACA,YAAY,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,IAClC,gBACA;;;;;;gEAGP,OAAO,IAAI;;2DAZP,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAqB5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC;gCAAI,WAAU;0CACZ,YAAY,MAAM,CAAC,GAAG,CAAC,CAAA;oCACtB,MAAM,QAAQ,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oCACjD,qBACE,8OAAC,iIAAA,CAAA,QAAK;wCAAe,SAAQ;wCAAY,WAAU;;4CAChD,OAAO;0DACR,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,YAAY;0DAE3B,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCARL;;;;;gCAYhB;;;;;;0CAEF,8OAAC,mIAAA,CAAA,UAAO;gCAAC,MAAM;gCAAY,cAAc;;kDACvC,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,iBAAe;4CACf,WAAU;;gDACX;8DAEC,8OAAC,8NAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG9B,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,WAAU;kDACxB,cAAA,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,eAAY;oDAAC,aAAY;;;;;;8DAC1B,8OAAC,mIAAA,CAAA,eAAY;8DAAC;;;;;;8DACd,8OAAC,mIAAA,CAAA,eAAY;8DACV,gBAAgB,GAAG,CAAC,CAAC,sBACpB,8OAAC,mIAAA,CAAA,cAAW;4DAEV,OAAO,MAAM,EAAE;4DACf,UAAU,IAAM,YAAY,MAAM,EAAE;;8EAEpC,8OAAC,oMAAA,CAAA,QAAK;oEACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBACA,YAAY,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,IAChC,gBACA;;;;;;gEAGP,MAAM,IAAI;;2DAZN,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAqB3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAkB;;;;;;0CACjC,8OAAC,kIAAA,CAAA,SAAM;gCACL,IAAG;gCACH,SAAS,YAAY,aAAa;gCAClC,iBAAiB,CAAC,UAChB,gBAAgB;wCAAE,GAAG,WAAW;wCAAE,eAAe;oCAAQ;;;;;;;;;;;;kCAK/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAe;;;;;;0CAC9B,8OAAC,kIAAA,CAAA,SAAM;gCACL,IAAG;gCACH,SAAS,YAAY,iBAAiB;gCACtC,iBAAiB,CAAC,UAChB,gBAAgB;wCAAE,GAAG,WAAW;wCAAE,mBAAmB;oCAAQ;;;;;;;;;;;;;;;;;;0BAKrE,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;kCAAiB;;;;;;kCAGpD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;kCAAsB;;;;;;;;;;;;;;;;;;AAMnE", "debugId": null}}, {"offset": {"line": 9638, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/banking/components/advanced-analytics.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useMemo } from \"react\";\r\nimport { format, subDays, isAfter, parseISO, subMonths } from \"date-fns\";\r\nimport { TrendingUp, TrendingDown, Calculator, BarChart3 } from \"lucide-react\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n  CardFooter,\r\n} from \"@/components/ui/card\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport { BankingSummary, ChartData } from \"@/types\";\r\nimport { ReportChart } from \"@/features/reports/components/report-chart\";\r\n\r\ninterface AdvancedAnalyticsProps {\r\n  summaryData: BankingSummary[] | undefined;\r\n  isLoading: boolean;\r\n  error: Error | null;\r\n  previousPeriodData?: BankingSummary[];\r\n  showMedian?: boolean;\r\n  showStandardDeviation?: boolean;\r\n  showGrowthRate?: boolean;\r\n  showPeriodComparison?: boolean;\r\n}\r\n\r\nexport function AdvancedAnalytics({\r\n  summaryData,\r\n  isLoading,\r\n  error,\r\n  previousPeriodData,\r\n  showMedian = true,\r\n  showStandardDeviation = true,\r\n  showGrowthRate = true,\r\n  showPeriodComparison = true,\r\n}: AdvancedAnalyticsProps) {\r\n  // Calculate advanced analytics metrics\r\n  const analytics = useMemo(() => {\r\n    if (!summaryData || summaryData.length === 0) return null;\r\n\r\n    // Sort data by date (ascending)\r\n    const sortedData = [...summaryData].sort((a, b) =>\r\n      new Date(a.date).getTime() - new Date(b.date).getTime()\r\n    );\r\n\r\n    // Calculate total amounts\r\n    const totalAmount = sortedData.reduce((sum, item) => sum + item.total, 0);\r\n    const totalTransactions = sortedData.reduce((sum, item) => sum + item.transaction_count, 0);\r\n    const averageTransaction = totalTransactions > 0 ? totalAmount / totalTransactions : 0;\r\n\r\n    // Calculate daily averages\r\n    const dailyAmounts = sortedData.map(item => item.total);\r\n    const dailyAverage = dailyAmounts.reduce((sum, amount) => sum + amount, 0) / dailyAmounts.length;\r\n    \r\n    // Calculate median transaction amount\r\n    const sortedAmounts = [...dailyAmounts].sort((a, b) => a - b);\r\n    const medianAmount = sortedAmounts.length % 2 === 0\r\n      ? (sortedAmounts[sortedAmounts.length / 2 - 1] + sortedAmounts[sortedAmounts.length / 2]) / 2\r\n      : sortedAmounts[Math.floor(sortedAmounts.length / 2)];\r\n    \r\n    // Calculate variance and standard deviation\r\n    const variance = dailyAmounts.reduce((sum, amount) => sum + Math.pow(amount - dailyAverage, 2), 0) / dailyAmounts.length;\r\n    const standardDeviation = Math.sqrt(variance);\r\n\r\n    // Calculate period-over-period comparison if previous period data is available\r\n    let periodComparison = null;\r\n    if (previousPeriodData && previousPeriodData.length > 0) {\r\n      const previousTotal = previousPeriodData.reduce((sum, item) => sum + item.total, 0);\r\n      const percentageChange = previousTotal > 0 \r\n        ? ((totalAmount - previousTotal) / previousTotal) * 100 \r\n        : 0;\r\n      \r\n      periodComparison = {\r\n        currentPeriodTotal: totalAmount,\r\n        previousPeriodTotal: previousTotal,\r\n        absoluteChange: totalAmount - previousTotal,\r\n        percentageChange,\r\n        isPositive: percentageChange >= 0\r\n      };\r\n    }\r\n\r\n    // Calculate growth rate (week-over-week or day-over-day)\r\n    let growthRate = null;\r\n    if (sortedData.length > 1) {\r\n      // Group by week\r\n      const weeklyData: Record<string, number> = {};\r\n      sortedData.forEach(item => {\r\n        const date = new Date(item.date);\r\n        const weekStart = new Date(date.setDate(date.getDate() - date.getDay()));\r\n        const weekKey = format(weekStart, 'yyyy-MM-dd');\r\n        \r\n        if (!weeklyData[weekKey]) {\r\n          weeklyData[weekKey] = 0;\r\n        }\r\n        weeklyData[weekKey] += item.total;\r\n      });\r\n\r\n      // Convert to array and sort\r\n      const weeklyAmounts = Object.entries(weeklyData)\r\n        .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())\r\n        .map(([_, amount]) => amount);\r\n\r\n      // Calculate week-over-week growth rates\r\n      const growthRates = [];\r\n      for (let i = 1; i < weeklyAmounts.length; i++) {\r\n        const previousWeek = weeklyAmounts[i - 1];\r\n        const currentWeek = weeklyAmounts[i];\r\n        const weeklyGrowthRate = previousWeek > 0 \r\n          ? ((currentWeek - previousWeek) / previousWeek) * 100 \r\n          : 0;\r\n        growthRates.push(weeklyGrowthRate);\r\n      }\r\n\r\n      // Calculate average growth rate\r\n      const averageGrowthRate = growthRates.length > 0\r\n        ? growthRates.reduce((sum, rate) => sum + rate, 0) / growthRates.length\r\n        : 0;\r\n\r\n      growthRate = {\r\n        weeklyRates: growthRates,\r\n        averageRate: averageGrowthRate,\r\n        isPositive: averageGrowthRate >= 0\r\n      };\r\n    }\r\n\r\n    // Prepare method comparison chart data\r\n    const methodComparisonData: ChartData = {\r\n      labels: sortedData.map(item => format(new Date(item.date), \"MMM d\")),\r\n      datasets: [\r\n        {\r\n          label: \"Bank\",\r\n          data: sortedData.map(item => item.bank),\r\n          backgroundColor: \"#3b82f6\",\r\n        },\r\n        {\r\n          label: \"M-Pesa\",\r\n          data: sortedData.map(item => item.mpesa),\r\n          backgroundColor: \"#10b981\",\r\n        },\r\n        {\r\n          label: \"Agent\",\r\n          data: sortedData.map(item => item.agent),\r\n          backgroundColor: \"#f59e0b\",\r\n        },\r\n      ],\r\n    };\r\n\r\n    // Prepare weekly summary chart data\r\n    const weeklyData: Record<string, { bank: number; mpesa: number; agent: number; total: number }> = {};\r\n    sortedData.forEach(item => {\r\n      const date = new Date(item.date);\r\n      const weekStart = new Date(date.setDate(date.getDate() - date.getDay()));\r\n      const weekKey = format(weekStart, 'yyyy-MM-dd');\r\n      \r\n      if (!weeklyData[weekKey]) {\r\n        weeklyData[weekKey] = { bank: 0, mpesa: 0, agent: 0, total: 0 };\r\n      }\r\n      weeklyData[weekKey].bank += item.bank;\r\n      weeklyData[weekKey].mpesa += item.mpesa;\r\n      weeklyData[weekKey].agent += item.agent;\r\n      weeklyData[weekKey].total += item.total;\r\n    });\r\n\r\n    // Convert to array and sort\r\n    const weeklySummary = Object.entries(weeklyData)\r\n      .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())\r\n      .map(([date, data]) => ({\r\n        week: format(new Date(date), \"MMM d\"),\r\n        ...data\r\n      }));\r\n\r\n    const weeklySummaryData: ChartData = {\r\n      labels: weeklySummary.map(item => item.week),\r\n      datasets: [\r\n        {\r\n          label: \"Total\",\r\n          data: weeklySummary.map(item => item.total),\r\n          backgroundColor: \"#8b5cf6\",\r\n        }\r\n      ],\r\n    };\r\n\r\n    // Prepare monthly trend chart data\r\n    const monthlyData: Record<string, number> = {};\r\n    sortedData.forEach(item => {\r\n      const month = format(new Date(item.date), 'yyyy-MM');\r\n      \r\n      if (!monthlyData[month]) {\r\n        monthlyData[month] = 0;\r\n      }\r\n      monthlyData[month] += item.total;\r\n    });\r\n\r\n    // Convert to array and sort\r\n    const monthlyTrend = Object.entries(monthlyData)\r\n      .sort(([monthA], [monthB]) => new Date(monthA).getTime() - new Date(monthB).getTime())\r\n      .map(([month, total]) => ({\r\n        month: format(new Date(month), \"MMM yyyy\"),\r\n        total\r\n      }));\r\n\r\n    const monthlyTrendData: ChartData = {\r\n      labels: monthlyTrend.map(item => item.month),\r\n      datasets: [\r\n        {\r\n          label: \"Monthly Total\",\r\n          data: monthlyTrend.map(item => item.total),\r\n          backgroundColor: \"#ec4899\",\r\n        }\r\n      ],\r\n    };\r\n\r\n    return {\r\n      totalAmount,\r\n      totalTransactions,\r\n      averageTransaction,\r\n      dailyAverage,\r\n      medianAmount,\r\n      standardDeviation,\r\n      periodComparison,\r\n      growthRate,\r\n      methodComparisonData,\r\n      weeklySummaryData,\r\n      monthlyTrendData\r\n    };\r\n  }, [summaryData, previousPeriodData]);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"space-y-4\">\r\n        <div className=\"grid grid-cols-1 gap-4 md:grid-cols-3\">\r\n          <Skeleton className=\"h-32\" />\r\n          <Skeleton className=\"h-32\" />\r\n          <Skeleton className=\"h-32\" />\r\n        </div>\r\n        <Skeleton className=\"h-80\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <Card>\r\n        <CardContent className=\"py-10 text-center\">\r\n          <p className=\"text-destructive\">\r\n            Error loading banking analytics: {error.message}\r\n          </p>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  if (!analytics) {\r\n    return (\r\n      <Card>\r\n        <CardContent className=\"py-10 text-center\">\r\n          <p className=\"text-muted-foreground\">\r\n            No banking data available for analytics.\r\n          </p>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"grid grid-cols-1 gap-4 md:grid-cols-3\">\r\n        {/* Daily Average Card */}\r\n        <Card>\r\n          <CardHeader className=\"pb-2\">\r\n            <CardTitle className=\"text-sm font-medium\">\r\n              Daily Average\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold\">\r\n              {formatCurrency(analytics.dailyAverage)}\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Median Amount Card */}\r\n        {showMedian && (\r\n          <Card>\r\n            <CardHeader className=\"pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">\r\n                Median Daily Amount\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {formatCurrency(analytics.medianAmount)}\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        )}\r\n\r\n        {/* Standard Deviation Card */}\r\n        {showStandardDeviation && (\r\n          <Card>\r\n            <CardHeader className=\"pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">\r\n                Standard Deviation\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {formatCurrency(analytics.standardDeviation)}\r\n              </div>\r\n              <div className=\"text-xs text-muted-foreground\">\r\n                Measures daily amount volatility\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        )}\r\n\r\n        {/* Period Comparison Card */}\r\n        {showPeriodComparison && analytics.periodComparison && (\r\n          <Card>\r\n            <CardHeader className=\"pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">\r\n                Period-over-Period\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {formatCurrency(analytics.periodComparison.absoluteChange)}\r\n              </div>\r\n            </CardContent>\r\n            <CardFooter className=\"pt-0\">\r\n              <div className=\"flex items-center text-sm text-muted-foreground\">\r\n                {analytics.periodComparison.isPositive ? (\r\n                  <>\r\n                    <TrendingUp className=\"mr-1 h-4 w-4 text-emerald-500\" />\r\n                    <span className=\"text-emerald-500\">\r\n                      {Math.abs(analytics.periodComparison.percentageChange).toFixed(1)}% increase\r\n                    </span>\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <TrendingDown className=\"mr-1 h-4 w-4 text-destructive\" />\r\n                    <span className=\"text-destructive\">\r\n                      {Math.abs(analytics.periodComparison.percentageChange).toFixed(1)}% decrease\r\n                    </span>\r\n                  </>\r\n                )}\r\n                <span className=\"ml-1\">vs. previous period</span>\r\n              </div>\r\n            </CardFooter>\r\n          </Card>\r\n        )}\r\n\r\n        {/* Growth Rate Card */}\r\n        {showGrowthRate && analytics.growthRate && (\r\n          <Card>\r\n            <CardHeader className=\"pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">\r\n                Average Weekly Growth\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {analytics.growthRate.averageRate.toFixed(1)}%\r\n              </div>\r\n            </CardContent>\r\n            <CardFooter className=\"pt-0\">\r\n              <div className=\"flex items-center text-sm text-muted-foreground\">\r\n                {analytics.growthRate.isPositive ? (\r\n                  <>\r\n                    <TrendingUp className=\"mr-1 h-4 w-4 text-emerald-500\" />\r\n                    <span className=\"text-emerald-500\">\r\n                      Positive growth trend\r\n                    </span>\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <TrendingDown className=\"mr-1 h-4 w-4 text-destructive\" />\r\n                    <span className=\"text-destructive\">\r\n                      Negative growth trend\r\n                    </span>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </CardFooter>\r\n          </Card>\r\n        )}\r\n      </div>\r\n\r\n      {/* Method Comparison Chart */}\r\n      <ReportChart\r\n        title=\"Banking Method Comparison\"\r\n        description=\"Compare banking methods over time\"\r\n        data={analytics.methodComparisonData}\r\n        chartTypes={[\"bar\", \"line\"]}\r\n        defaultChartType=\"bar\"\r\n        showLegend={true}\r\n      />\r\n\r\n      {/* Weekly Summary Chart */}\r\n      <ReportChart\r\n        title=\"Weekly Banking Summary\"\r\n        description=\"Total banking amounts by week\"\r\n        data={analytics.weeklySummaryData}\r\n        chartTypes={[\"bar\", \"line\"]}\r\n        defaultChartType=\"bar\"\r\n        showLegend={true}\r\n      />\r\n\r\n      {/* Monthly Trend Chart */}\r\n      {analytics.monthlyTrendData.labels.length > 1 && (\r\n        <ReportChart\r\n          title=\"Monthly Banking Trend\"\r\n          description=\"Banking trend by month\"\r\n          data={analytics.monthlyTrendData}\r\n          chartTypes={[\"line\", \"bar\"]}\r\n          defaultChartType=\"line\"\r\n          showLegend={true}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAQA;AACA;AAEA;AAhBA;;;;;;;;;AA6BO,SAAS,kBAAkB,EAChC,WAAW,EACX,SAAS,EACT,KAAK,EACL,kBAAkB,EAClB,aAAa,IAAI,EACjB,wBAAwB,IAAI,EAC5B,iBAAiB,IAAI,EACrB,uBAAuB,IAAI,EACJ;IACvB,uCAAuC;IACvC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG,OAAO;QAErD,gCAAgC;QAChC,MAAM,aAAa;eAAI;SAAY,CAAC,IAAI,CAAC,CAAC,GAAG,IAC3C,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;QAGvD,0BAA0B;QAC1B,MAAM,cAAc,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;QACvE,MAAM,oBAAoB,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,iBAAiB,EAAE;QACzF,MAAM,qBAAqB,oBAAoB,IAAI,cAAc,oBAAoB;QAErF,2BAA2B;QAC3B,MAAM,eAAe,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;QACtD,MAAM,eAAe,aAAa,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,QAAQ,KAAK,aAAa,MAAM;QAEhG,sCAAsC;QACtC,MAAM,gBAAgB;eAAI;SAAa,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QAC3D,MAAM,eAAe,cAAc,MAAM,GAAG,MAAM,IAC9C,CAAC,aAAa,CAAC,cAAc,MAAM,GAAG,IAAI,EAAE,GAAG,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE,IAAI,IAC1F,aAAa,CAAC,KAAK,KAAK,CAAC,cAAc,MAAM,GAAG,GAAG;QAEvD,4CAA4C;QAC5C,MAAM,WAAW,aAAa,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,KAAK,GAAG,CAAC,SAAS,cAAc,IAAI,KAAK,aAAa,MAAM;QACxH,MAAM,oBAAoB,KAAK,IAAI,CAAC;QAEpC,+EAA+E;QAC/E,IAAI,mBAAmB;QACvB,IAAI,sBAAsB,mBAAmB,MAAM,GAAG,GAAG;YACvD,MAAM,gBAAgB,mBAAmB,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;YACjF,MAAM,mBAAmB,gBAAgB,IACrC,AAAC,CAAC,cAAc,aAAa,IAAI,gBAAiB,MAClD;YAEJ,mBAAmB;gBACjB,oBAAoB;gBACpB,qBAAqB;gBACrB,gBAAgB,cAAc;gBAC9B;gBACA,YAAY,oBAAoB;YAClC;QACF;QAEA,yDAAyD;QACzD,IAAI,aAAa;QACjB,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,gBAAgB;YAChB,MAAM,aAAqC,CAAC;YAC5C,WAAW,OAAO,CAAC,CAAA;gBACjB,MAAM,OAAO,IAAI,KAAK,KAAK,IAAI;gBAC/B,MAAM,YAAY,IAAI,KAAK,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,KAAK,MAAM;gBACpE,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;gBAElC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;oBACxB,UAAU,CAAC,QAAQ,GAAG;gBACxB;gBACA,UAAU,CAAC,QAAQ,IAAI,KAAK,KAAK;YACnC;YAEA,4BAA4B;YAC5B,MAAM,gBAAgB,OAAO,OAAO,CAAC,YAClC,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,GAAK,IAAI,KAAK,OAAO,OAAO,KAAK,IAAI,KAAK,OAAO,OAAO,IAC9E,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,GAAK;YAExB,wCAAwC;YACxC,MAAM,cAAc,EAAE;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;gBAC7C,MAAM,eAAe,aAAa,CAAC,IAAI,EAAE;gBACzC,MAAM,cAAc,aAAa,CAAC,EAAE;gBACpC,MAAM,mBAAmB,eAAe,IACpC,AAAC,CAAC,cAAc,YAAY,IAAI,eAAgB,MAChD;gBACJ,YAAY,IAAI,CAAC;YACnB;YAEA,gCAAgC;YAChC,MAAM,oBAAoB,YAAY,MAAM,GAAG,IAC3C,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,MAAM,KAAK,YAAY,MAAM,GACrE;YAEJ,aAAa;gBACX,aAAa;gBACb,aAAa;gBACb,YAAY,qBAAqB;YACnC;QACF;QAEA,uCAAuC;QACvC,MAAM,uBAAkC;YACtC,QAAQ,WAAW,GAAG,CAAC,CAAA,OAAQ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,IAAI,GAAG;YAC3D,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;oBACtC,iBAAiB;gBACnB;gBACA;oBACE,OAAO;oBACP,MAAM,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;oBACvC,iBAAiB;gBACnB;gBACA;oBACE,OAAO;oBACP,MAAM,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;oBACvC,iBAAiB;gBACnB;aACD;QACH;QAEA,oCAAoC;QACpC,MAAM,aAA4F,CAAC;QACnG,WAAW,OAAO,CAAC,CAAA;YACjB,MAAM,OAAO,IAAI,KAAK,KAAK,IAAI;YAC/B,MAAM,YAAY,IAAI,KAAK,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,KAAK,MAAM;YACpE,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;YAElC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;gBACxB,UAAU,CAAC,QAAQ,GAAG;oBAAE,MAAM;oBAAG,OAAO;oBAAG,OAAO;oBAAG,OAAO;gBAAE;YAChE;YACA,UAAU,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,IAAI;YACrC,UAAU,CAAC,QAAQ,CAAC,KAAK,IAAI,KAAK,KAAK;YACvC,UAAU,CAAC,QAAQ,CAAC,KAAK,IAAI,KAAK,KAAK;YACvC,UAAU,CAAC,QAAQ,CAAC,KAAK,IAAI,KAAK,KAAK;QACzC;QAEA,4BAA4B;QAC5B,MAAM,gBAAgB,OAAO,OAAO,CAAC,YAClC,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,GAAK,IAAI,KAAK,OAAO,OAAO,KAAK,IAAI,KAAK,OAAO,OAAO,IAC9E,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,GAAK,CAAC;gBACtB,MAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO;gBAC7B,GAAG,IAAI;YACT,CAAC;QAEH,MAAM,oBAA+B;YACnC,QAAQ,cAAc,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;YAC3C,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM,cAAc,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;oBAC1C,iBAAiB;gBACnB;aACD;QACH;QAEA,mCAAmC;QACnC,MAAM,cAAsC,CAAC;QAC7C,WAAW,OAAO,CAAC,CAAA;YACjB,MAAM,QAAQ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,IAAI,GAAG;YAE1C,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;gBACvB,WAAW,CAAC,MAAM,GAAG;YACvB;YACA,WAAW,CAAC,MAAM,IAAI,KAAK,KAAK;QAClC;QAEA,4BAA4B;QAC5B,MAAM,eAAe,OAAO,OAAO,CAAC,aACjC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,GAAK,IAAI,KAAK,QAAQ,OAAO,KAAK,IAAI,KAAK,QAAQ,OAAO,IAClF,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,GAAK,CAAC;gBACxB,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ;gBAC/B;YACF,CAAC;QAEH,MAAM,mBAA8B;YAClC,QAAQ,aAAa,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;YAC3C,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM,aAAa,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;oBACzC,iBAAiB;gBACnB;aACD;QACH;QAEA,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAa;KAAmB;IAEpC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;8BAEtB,8OAAC,oIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;;IAG1B;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAE,WAAU;;wBAAmB;wBACI,MAAM,OAAO;;;;;;;;;;;;;;;;;IAKzD;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;IAM7C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAI7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,YAAY;;;;;;;;;;;;;;;;;oBAM3C,4BACC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAI7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,YAAY;;;;;;;;;;;;;;;;;oBAO7C,uCACC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAI7C,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,iBAAiB;;;;;;kDAE7C,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;oBAQpD,wBAAwB,UAAU,gBAAgB,kBACjD,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAI7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,gBAAgB,CAAC,cAAc;;;;;;;;;;;0CAG7D,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;wCACZ,UAAU,gBAAgB,CAAC,UAAU,iBACpC;;8DACE,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAK,WAAU;;wDACb,KAAK,GAAG,CAAC,UAAU,gBAAgB,CAAC,gBAAgB,EAAE,OAAO,CAAC;wDAAG;;;;;;;;yEAItE;;8DACE,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,8OAAC;oDAAK,WAAU;;wDACb,KAAK,GAAG,CAAC,UAAU,gBAAgB,CAAC,gBAAgB,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;sDAIxE,8OAAC;4CAAK,WAAU;sDAAO;;;;;;;;;;;;;;;;;;;;;;;oBAO9B,kBAAkB,UAAU,UAAU,kBACrC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAI7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;wCACZ,UAAU,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC;wCAAG;;;;;;;;;;;;0CAGjD,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;8CACZ,UAAU,UAAU,CAAC,UAAU,iBAC9B;;0DACE,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;qEAKrC;;0DACE,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYjD,8OAAC,4JAAA,CAAA,cAAW;gBACV,OAAM;gBACN,aAAY;gBACZ,MAAM,UAAU,oBAAoB;gBACpC,YAAY;oBAAC;oBAAO;iBAAO;gBAC3B,kBAAiB;gBACjB,YAAY;;;;;;0BAId,8OAAC,4JAAA,CAAA,cAAW;gBACV,OAAM;gBACN,aAAY;gBACZ,MAAM,UAAU,iBAAiB;gBACjC,YAAY;oBAAC;oBAAO;iBAAO;gBAC3B,kBAAiB;gBACjB,YAAY;;;;;;YAIb,UAAU,gBAAgB,CAAC,MAAM,CAAC,MAAM,GAAG,mBAC1C,8OAAC,4JAAA,CAAA,cAAW;gBACV,OAAM;gBACN,aAAY;gBACZ,MAAM,UAAU,gBAAgB;gBAChC,YAAY;oBAAC;oBAAQ;iBAAM;gBAC3B,kBAAiB;gBACjB,YAAY;;;;;;;;;;;;AAKtB", "debugId": null}}, {"offset": {"line": 10306, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/banking/components/banking-summary.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useMemo, useEffect } from \"react\";\r\nimport { format, subDays, isAfter, parseISO, subMonths } from \"date-fns\";\r\nimport { CalendarIcon, TrendingUp, TrendingDown, Download, Settings, BarChart3, User, MapPin } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n  CardFooter,\r\n} from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport {\r\n  Tabs,\r\n  TabsContent,\r\n  TabsList,\r\n  TabsTrigger,\r\n} from \"@/components/ui/tabs\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Calendar } from \"@/components/ui/calendar\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { useBankingSummary } from \"../hooks/use-banking\";\r\nimport { useBranches } from \"@/features/branches/hooks/use-branches\";\r\nimport { useRegions } from \"@/features/regions/hooks/use-regions\";\r\nimport { useUsers } from \"@/features/users/hooks/use-users\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport { ReportChart } from \"@/features/reports/components/report-chart\";\r\nimport { ChartData } from \"@/types\";\r\nimport { exportBankingSummaryToExcel } from \"../utils/export-banking-data\";\r\nimport { BankingPreferences } from \"./banking-preferences\";\r\nimport { AdvancedAnalytics } from \"./advanced-analytics\";\r\n\r\n// Default preferences - defined outside the component to avoid hoisting issues\r\nconst defaultPreferences = {\r\n  metrics: [\"total_amount\", \"total_transactions\", \"average_transaction\", \"period_comparison\"],\r\n  charts: [\"method_breakdown\", \"daily_trend\"],\r\n  showDataTable: true,\r\n  enableAutoRefresh: false,\r\n  refreshInterval: 5,\r\n};\r\n\r\nexport function BankingSummary() {\r\n  const { data: branchesData, isLoading: isLoadingBranches } = useBranches();\r\n  const { data: regionsData, isLoading: isLoadingRegions } = useRegions();\r\n  const { data: usersData, isLoading: isLoadingUsers } = useUsers();\r\n\r\n  const [branchId, setBranchId] = useState<number | null>(null);\r\n  const [regionId, setRegionId] = useState<number | null>(null);\r\n  const [userId, setUserId] = useState<number | null>(null);\r\n  const [startDate, setStartDate] = useState<Date | null>(null);\r\n  const [endDate, setEndDate] = useState<Date | undefined>(undefined);\r\n  const [bankingMethod, setBankingMethod] = useState<string | null>(null);\r\n  const [activeTab, setActiveTab] = useState<string>(\"summary\");\r\n  const [showPreferences, setShowPreferences] = useState<boolean>(false);\r\n  const [filtersApplied, setFiltersApplied] = useState<boolean>(false);\r\n\r\n  // Initialize preferences state with default values first\r\n  // We'll update it with localStorage values in useEffect\r\n  const [preferences, setPreferences] = useState(defaultPreferences);\r\n\r\n  const {\r\n    data: summaryData,\r\n    isLoading: isLoadingSummary,\r\n    error,\r\n  } = useBankingSummary({\r\n    branch_id: branchId || undefined,\r\n    region_id: regionId || undefined,\r\n    user_id: userId || undefined,\r\n    start_date: startDate ? format(startDate, \"yyyy-MM-dd\") : undefined,\r\n    end_date: endDate ? format(endDate, \"yyyy-MM-dd\") : undefined,\r\n    banking_method: bankingMethod || undefined,\r\n  }, filtersApplied);\r\n\r\n  const handleFetchSummary = () => {\r\n    // Set filtersApplied to true to enable the query\r\n    setFiltersApplied(true);\r\n  };\r\n\r\n  // Handle preferences change\r\n  const handlePreferencesChange = (newPreferences: any) => {\r\n    setPreferences(newPreferences);\r\n  };\r\n\r\n  // Handle export to Excel\r\n  const handleExportToExcel = () => {\r\n    if (summaryData && summaryData.length > 0) {\r\n      const filename = `banking-summary-${format(new Date(), \"yyyy-MM-dd\")}`;\r\n      exportBankingSummaryToExcel(summaryData, filename);\r\n    }\r\n  };\r\n\r\n  // Get previous period data for comparison\r\n  const previousPeriodData = useMemo(() => {\r\n    if (!summaryData || summaryData.length === 0) return [];\r\n\r\n    // Sort data by date (ascending)\r\n    const sortedData = [...summaryData].sort((a, b) =>\r\n      new Date(a.date).getTime() - new Date(b.date).getTime()\r\n    );\r\n\r\n    // Get date range length in days\r\n    const firstDate = new Date(sortedData[0].date);\r\n    const lastDate = new Date(sortedData[sortedData.length - 1].date);\r\n    const daysDiff = Math.ceil((lastDate.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;\r\n\r\n    // Calculate previous period date range\r\n    const previousPeriodEndDate = subDays(firstDate, 1);\r\n    const previousPeriodStartDate = subDays(previousPeriodEndDate, daysDiff - 1);\r\n\r\n    // Filter data for the previous period\r\n    return summaryData.filter(item => {\r\n      const itemDate = new Date(item.date);\r\n      return isAfter(itemDate, previousPeriodStartDate) &&\r\n             !isAfter(itemDate, previousPeriodEndDate);\r\n    });\r\n  }, [summaryData]);\r\n\r\n  // Calculate totals using useMemo - moved before conditional returns to maintain hooks order\r\n  const { totalAmount, totalTransactions } = useMemo(() => {\r\n    return {\r\n      totalAmount: summaryData?.reduce((sum, item) => sum + item.total, 0) || 0,\r\n      totalTransactions: summaryData?.reduce((sum, item) => sum + item.transaction_count, 0) || 0\r\n    };\r\n  }, [summaryData]);\r\n\r\n  // Prepare analytics data - moved before conditional returns to maintain hooks order\r\n  const analyticsData = useMemo(() => {\r\n    if (!summaryData || summaryData.length === 0) return null;\r\n\r\n    // Sort data by date (ascending)\r\n    const sortedData = [...summaryData].sort((a, b) =>\r\n      new Date(a.date).getTime() - new Date(b.date).getTime()\r\n    );\r\n\r\n    // Prepare data for banking method breakdown chart\r\n    const methodBreakdownData: ChartData = {\r\n      labels: [\"Bank\", \"M-Pesa\", \"Agent\"],\r\n      datasets: [\r\n        {\r\n          label: \"Amount\",\r\n          data: [\r\n            sortedData.reduce((sum, item) => sum + item.bank, 0),\r\n            sortedData.reduce((sum, item) => sum + item.mpesa, 0),\r\n            sortedData.reduce((sum, item) => sum + item.agent, 0),\r\n          ],\r\n          backgroundColor: [\"#3b82f6\", \"#10b981\", \"#f59e0b\"],\r\n        },\r\n      ],\r\n    };\r\n\r\n    // Prepare data for daily banking trend chart\r\n    const dailyTrendData: ChartData = {\r\n      labels: sortedData.map(item => format(new Date(item.date), \"MMM dd\")),\r\n      datasets: [\r\n        {\r\n          label: \"Total\",\r\n          data: sortedData.map(item => item.total),\r\n          borderColor: \"#3b82f6\",\r\n        },\r\n        {\r\n          label: \"Bank\",\r\n          data: sortedData.map(item => item.bank),\r\n          borderColor: \"#10b981\",\r\n        },\r\n        {\r\n          label: \"M-Pesa\",\r\n          data: sortedData.map(item => item.mpesa),\r\n          borderColor: \"#f59e0b\",\r\n        },\r\n        {\r\n          label: \"Agent\",\r\n          data: sortedData.map(item => item.agent),\r\n          borderColor: \"#ef4444\",\r\n        },\r\n      ],\r\n    };\r\n\r\n    // Calculate trend percentage (comparing total with previous period)\r\n    let trendPercentage = 0;\r\n    let isPositiveTrend = true;\r\n\r\n    if (sortedData.length > 1) {\r\n      const currentPeriodTotal = sortedData.reduce((sum, item) => sum + item.total, 0);\r\n\r\n      // Get date range length in days\r\n      const firstDate = new Date(sortedData[0].date);\r\n      const lastDate = new Date(sortedData[sortedData.length - 1].date);\r\n      const daysDiff = Math.ceil((lastDate.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;\r\n\r\n      // Calculate previous period total (same number of days before the current period)\r\n      const previousPeriodEndDate = subDays(firstDate, 1);\r\n      const previousPeriodStartDate = subDays(previousPeriodEndDate, daysDiff - 1);\r\n\r\n      // Check if we have data for the previous period\r\n      const previousPeriodData = summaryData.filter(item => {\r\n        const itemDate = new Date(item.date);\r\n        return isAfter(itemDate, previousPeriodStartDate) &&\r\n               !isAfter(itemDate, previousPeriodEndDate);\r\n      });\r\n\r\n      if (previousPeriodData.length > 0) {\r\n        const previousPeriodTotal = previousPeriodData.reduce((sum, item) => sum + item.total, 0);\r\n        if (previousPeriodTotal > 0) {\r\n          trendPercentage = ((currentPeriodTotal - previousPeriodTotal) / previousPeriodTotal) * 100;\r\n          isPositiveTrend = trendPercentage >= 0;\r\n          trendPercentage = Math.abs(trendPercentage);\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      methodBreakdownData,\r\n      dailyTrendData,\r\n      trendPercentage,\r\n      isPositiveTrend,\r\n    };\r\n  }, [summaryData]);\r\n\r\n  // Load preferences from localStorage using useEffect\r\n  useEffect(() => {\r\n    const loadPreferences = () => {\r\n      if (typeof window === \"undefined\") return defaultPreferences;\r\n\r\n      const savedPreferences = localStorage.getItem(\"banking-preferences\");\r\n      if (savedPreferences) {\r\n        try {\r\n          return JSON.parse(savedPreferences);\r\n        } catch (e) {\r\n          console.error(\"Error parsing saved preferences:\", e);\r\n        }\r\n      }\r\n      return defaultPreferences;\r\n    };\r\n\r\n    setPreferences(loadPreferences());\r\n  }, []);\r\n\r\n  // Show loading state when any data is loading\r\n  if (isLoadingBranches || isLoadingRegions || isLoadingUsers || isLoadingSummary) {\r\n    return (\r\n      <div className=\"space-y-4\">\r\n        <Skeleton className=\"h-10 w-full\" />\r\n        <div className=\"grid grid-cols-1 gap-4 md:grid-cols-3\">\r\n          <Skeleton className=\"h-32 w-full\" />\r\n          <Skeleton className=\"h-32 w-full\" />\r\n          <Skeleton className=\"h-32 w-full\" />\r\n        </div>\r\n        <div className=\"grid grid-cols-1 gap-4 md:grid-cols-2\">\r\n          <Skeleton className=\"h-64 w-full\" />\r\n          <Skeleton className=\"h-64 w-full\" />\r\n        </div>\r\n        <Skeleton className=\"h-64 w-full\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"rounded-md bg-destructive/10 p-4 text-destructive\">\r\n        Error loading banking summary. Please try again.\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Card>\r\n        <CardHeader className=\"flex flex-row items-center justify-between\">\r\n          <div>\r\n            <CardTitle>Banking Summary</CardTitle>\r\n            <CardDescription>\r\n              View banking summary by branch and date range\r\n            </CardDescription>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"icon\"\r\n              onClick={() => setShowPreferences(!showPreferences)}\r\n              title=\"Dashboard Preferences\"\r\n            >\r\n              <Settings className=\"h-4 w-4\" />\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"icon\"\r\n              onClick={handleExportToExcel}\r\n              disabled={!summaryData || summaryData.length === 0}\r\n              title=\"Export to Excel\"\r\n            >\r\n              <Download className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-6\">\r\n            <div className=\"md:col-span-1 lg:col-span-1\">\r\n              <label className=\"mb-2 block text-sm font-medium\">\r\n                <MapPin className=\"inline-block h-4 w-4 mr-1\" />\r\n                Region\r\n              </label>\r\n              <Select\r\n                value={regionId?.toString() || \"all\"}\r\n                onValueChange={(value) => {\r\n                  setRegionId(value !== \"all\" ? parseInt(value) : null);\r\n                  // Reset branch when region changes\r\n                  if (branchId) setBranchId(null);\r\n                }}\r\n              >\r\n                <SelectTrigger>\r\n                  <SelectValue placeholder=\"All regions\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All regions</SelectItem>\r\n                  {regionsData?.data?.length ? (\r\n                    regionsData.data.map((region) => (\r\n                      <SelectItem key={region.id} value={region.id.toString()}>\r\n                        {region.name}\r\n                      </SelectItem>\r\n                    ))\r\n                  ) : (\r\n                    <SelectItem value=\"no-regions\" disabled>\r\n                      No regions available\r\n                    </SelectItem>\r\n                  )}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n\r\n            <div className=\"md:col-span-1 lg:col-span-1\">\r\n              <label className=\"mb-2 block text-sm font-medium\">\r\n                <MapPin className=\"inline-block h-4 w-4 mr-1\" />\r\n                Branch\r\n              </label>\r\n              <Select\r\n                value={branchId?.toString() || \"all\"}\r\n                onValueChange={(value) =>\r\n                  setBranchId(value !== \"all\" ? parseInt(value) : null)\r\n                }\r\n              >\r\n                <SelectTrigger>\r\n                  <SelectValue placeholder=\"All branches\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All branches</SelectItem>\r\n                  {branchesData?.data?.length ? (\r\n                    branchesData.data\r\n                      .filter(branch => !regionId || branch.region_id === regionId)\r\n                      .map((branch) => (\r\n                        <SelectItem key={branch.id} value={branch.id.toString()}>\r\n                          {branch.name}\r\n                        </SelectItem>\r\n                      ))\r\n                  ) : (\r\n                    <SelectItem value=\"no-branches\" disabled>\r\n                      No branches available\r\n                    </SelectItem>\r\n                  )}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n\r\n            <div className=\"md:col-span-1 lg:col-span-1\">\r\n              <label className=\"mb-2 block text-sm font-medium\">\r\n                <User className=\"inline-block h-4 w-4 mr-1\" />\r\n                User\r\n              </label>\r\n              <Select\r\n                value={userId?.toString() || \"all\"}\r\n                onValueChange={(value) =>\r\n                  setUserId(value !== \"all\" ? parseInt(value) : null)\r\n                }\r\n              >\r\n                <SelectTrigger>\r\n                  <SelectValue placeholder=\"All users\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All users</SelectItem>\r\n                  {usersData?.data?.length ? (\r\n                    usersData.data.map((user) => (\r\n                      <SelectItem key={user.id} value={user.id.toString()}>\r\n                        {user.name}\r\n                      </SelectItem>\r\n                    ))\r\n                  ) : (\r\n                    <SelectItem value=\"no-users\" disabled>\r\n                      No users available\r\n                    </SelectItem>\r\n                  )}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n\r\n            <div className=\"md:col-span-1 lg:col-span-1\">\r\n              <label className=\"mb-2 block text-sm font-medium\">\r\n                Banking Method\r\n              </label>\r\n              <Select\r\n                value={bankingMethod || \"all\"}\r\n                onValueChange={(value) =>\r\n                  setBankingMethod(value !== \"all\" ? value : null)\r\n                }\r\n              >\r\n                <SelectTrigger>\r\n                  <SelectValue placeholder=\"Select method\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All Methods</SelectItem>\r\n                  <SelectItem value=\"bank\">Bank</SelectItem>\r\n                  <SelectItem value=\"mpesa\">M-Pesa</SelectItem>\r\n                  <SelectItem value=\"agent\">Agent</SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n\r\n            <div>\r\n              <label className=\"mb-2 block text-sm font-medium\">\r\n                Start Date\r\n              </label>\r\n              <Popover>\r\n                <PopoverTrigger asChild>\r\n                  <Button\r\n                    variant={\"outline\"}\r\n                    className={cn(\r\n                      \"w-full justify-start text-left font-normal\",\r\n                      !startDate && \"text-muted-foreground\"\r\n                    )}\r\n                  >\r\n                    <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                    {startDate ? format(startDate, \"PPP\") : \"Select date\"}\r\n                  </Button>\r\n                </PopoverTrigger>\r\n                <PopoverContent className=\"w-auto p-0\">\r\n                  <Calendar\r\n                    mode=\"single\"\r\n                    selected={startDate}\r\n                    onSelect={(date) => setStartDate(date)}\r\n                    initialFocus\r\n                  />\r\n                </PopoverContent>\r\n              </Popover>\r\n            </div>\r\n\r\n            <div>\r\n              <label className=\"mb-2 block text-sm font-medium\">\r\n                End Date (Optional)\r\n              </label>\r\n              <Popover>\r\n                <PopoverTrigger asChild>\r\n                  <Button\r\n                    variant={\"outline\"}\r\n                    className={cn(\r\n                      \"w-full justify-start text-left font-normal\",\r\n                      !endDate && \"text-muted-foreground\"\r\n                    )}\r\n                  >\r\n                    <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                    {endDate ? format(endDate, \"PPP\") : \"Select date\"}\r\n                  </Button>\r\n                </PopoverTrigger>\r\n                <PopoverContent className=\"w-auto p-0\">\r\n                  <Calendar\r\n                    mode=\"single\"\r\n                    selected={endDate}\r\n                    onSelect={setEndDate}\r\n                    initialFocus\r\n                    disabled={(date) => date < startDate}\r\n                  />\r\n                </PopoverContent>\r\n              </Popover>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mt-4\">\r\n            <Button onClick={handleFetchSummary} disabled={!startDate}>\r\n              Generate Summary\r\n            </Button>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Dashboard Preferences */}\r\n      {showPreferences && (\r\n        <div className=\"mb-6\">\r\n          <BankingPreferences onPreferencesChange={handlePreferencesChange} />\r\n        </div>\r\n      )}\r\n\r\n      {summaryData && summaryData.length > 0 ? (\r\n        <>\r\n          <Tabs defaultValue=\"summary\" value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\r\n            <TabsList className=\"grid w-full grid-cols-2\">\r\n              <TabsTrigger value=\"summary\">Summary</TabsTrigger>\r\n              <TabsTrigger value=\"advanced\">Advanced Analytics</TabsTrigger>\r\n            </TabsList>\r\n\r\n            <TabsContent value=\"summary\" className=\"space-y-4 pt-4\">\r\n              <div className=\"grid grid-cols-1 gap-4 md:grid-cols-3\">\r\n                {preferences.metrics.includes(\"total_amount\") && (\r\n                  <Card>\r\n                    <CardHeader className=\"pb-2\">\r\n                      <CardTitle className=\"text-sm font-medium\">\r\n                        Total Banking Amount\r\n                      </CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                      <div className=\"text-2xl font-bold\">\r\n                        {formatCurrency(totalAmount)}\r\n                      </div>\r\n                    </CardContent>\r\n                    {preferences.metrics.includes(\"period_comparison\") && analyticsData?.trendPercentage > 0 && (\r\n                      <CardFooter className=\"pt-0\">\r\n                        <div className=\"flex items-center text-sm text-muted-foreground\">\r\n                          {analyticsData.isPositiveTrend ? (\r\n                            <>\r\n                              <TrendingUp className=\"mr-1 h-4 w-4 text-emerald-500\" />\r\n                              <span className=\"text-emerald-500\">\r\n                                {analyticsData.trendPercentage.toFixed(1)}% increase\r\n                              </span>\r\n                            </>\r\n                          ) : (\r\n                            <>\r\n                              <TrendingDown className=\"mr-1 h-4 w-4 text-destructive\" />\r\n                              <span className=\"text-destructive\">\r\n                                {analyticsData.trendPercentage.toFixed(1)}% decrease\r\n                              </span>\r\n                            </>\r\n                          )}\r\n                          <span className=\"ml-1\">vs. previous period</span>\r\n                        </div>\r\n                      </CardFooter>\r\n                    )}\r\n                  </Card>\r\n                )}\r\n\r\n                {preferences.metrics.includes(\"total_transactions\") && (\r\n                  <Card>\r\n                    <CardHeader className=\"pb-2\">\r\n                      <CardTitle className=\"text-sm font-medium\">\r\n                        Total Transactions\r\n                      </CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                      <div className=\"text-2xl font-bold\">{totalTransactions}</div>\r\n                    </CardContent>\r\n                  </Card>\r\n                )}\r\n\r\n                {preferences.metrics.includes(\"average_transaction\") && (\r\n                  <Card>\r\n                    <CardHeader className=\"pb-2\">\r\n                      <CardTitle className=\"text-sm font-medium\">\r\n                        Average Transaction\r\n                      </CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                      <div className=\"text-2xl font-bold\">\r\n                        {formatCurrency(\r\n                          totalTransactions > 0 ? totalAmount / totalTransactions : 0\r\n                        )}\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                )}\r\n              </div>\r\n\r\n              {/* Analytics Charts */}\r\n              <div className=\"grid grid-cols-1 gap-4 md:grid-cols-2\">\r\n                {/* Banking Method Breakdown Chart */}\r\n                {preferences.charts.includes(\"method_breakdown\") && analyticsData && (\r\n                  <ReportChart\r\n                    title=\"Banking Method Breakdown\"\r\n                    description=\"Distribution of banking amounts by method\"\r\n                    data={analyticsData.methodBreakdownData}\r\n                    chartTypes={[\"pie\", \"bar\"]}\r\n                    defaultChartType=\"pie\"\r\n                    showLegend={true}\r\n                  />\r\n                )}\r\n\r\n                {/* Daily Banking Trend Chart */}\r\n                {preferences.charts.includes(\"daily_trend\") && analyticsData && analyticsData.dailyTrendData.labels.length > 1 && (\r\n                  <ReportChart\r\n                    title=\"Banking Trends\"\r\n                    description=\"Daily banking amounts by method\"\r\n                    data={analyticsData.dailyTrendData}\r\n                    chartTypes={[\"line\", \"bar\"]}\r\n                    defaultChartType=\"line\"\r\n                    showLegend={true}\r\n                  />\r\n                )}\r\n              </div>\r\n\r\n              {preferences.showDataTable && (\r\n                <Card>\r\n                  <CardHeader className=\"flex flex-row items-center justify-between\">\r\n                    <div>\r\n                      <CardTitle>Banking Summary by Method</CardTitle>\r\n                      <CardDescription>\r\n                        Breakdown of banking transactions by method\r\n                      </CardDescription>\r\n                    </div>\r\n                    <DropdownMenu>\r\n                      <DropdownMenuTrigger asChild>\r\n                        <Button variant=\"outline\" size=\"sm\">\r\n                          <Download className=\"mr-2 h-4 w-4\" />\r\n                          Export\r\n                        </Button>\r\n                      </DropdownMenuTrigger>\r\n                      <DropdownMenuContent>\r\n                        <DropdownMenuItem onClick={handleExportToExcel}>\r\n                          Export to Excel\r\n                        </DropdownMenuItem>\r\n                      </DropdownMenuContent>\r\n                    </DropdownMenu>\r\n                  </CardHeader>\r\n                  <CardContent>\r\n                    <div className=\"overflow-x-auto\">\r\n                      <table className=\"w-full border-collapse\">\r\n                        <thead>\r\n                          <tr className=\"border-b\">\r\n                            <th className=\"py-2 text-left font-medium\">Date</th>\r\n                            <th className=\"py-2 text-right font-medium\">Bank</th>\r\n                            <th className=\"py-2 text-right font-medium\">M-Pesa</th>\r\n                            <th className=\"py-2 text-right font-medium\">Agent</th>\r\n                            <th className=\"py-2 text-right font-medium\">Total</th>\r\n                            <th className=\"py-2 text-right font-medium\">\r\n                              Transactions\r\n                            </th>\r\n                          </tr>\r\n                        </thead>\r\n                        <tbody>\r\n                          {summaryData.map((item, index) => (\r\n                            <tr key={`${item.date}-${index}`} className=\"border-b\">\r\n                              <td className=\"py-2\">\r\n                                {format(new Date(item.date), \"PPP\")}\r\n                              </td>\r\n                              <td className=\"py-2 text-right\">\r\n                                {formatCurrency(item.bank)}\r\n                              </td>\r\n                              <td className=\"py-2 text-right\">\r\n                                {formatCurrency(item.mpesa)}\r\n                              </td>\r\n                              <td className=\"py-2 text-right\">\r\n                                {formatCurrency(item.agent)}\r\n                              </td>\r\n                              <td className=\"py-2 text-right\">\r\n                                {formatCurrency(item.total)}\r\n                              </td>\r\n                              <td className=\"py-2 text-right\">\r\n                                {item.transaction_count}\r\n                              </td>\r\n                            </tr>\r\n                          ))}\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              )}\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"advanced\" className=\"space-y-4 pt-4\">\r\n              <AdvancedAnalytics\r\n                summaryData={summaryData}\r\n                isLoading={isLoadingSummary}\r\n                error={error}\r\n                previousPeriodData={previousPeriodData}\r\n                showMedian={preferences.metrics.includes(\"median_amount\")}\r\n                showStandardDeviation={preferences.metrics.includes(\"standard_deviation\")}\r\n                showGrowthRate={preferences.metrics.includes(\"growth_rate\")}\r\n                showPeriodComparison={preferences.metrics.includes(\"period_comparison\")}\r\n              />\r\n            </TabsContent>\r\n          </Tabs>\r\n        </>\r\n      ) : filtersApplied ? (\r\n        <Card>\r\n          <CardContent className=\"py-10 text-center\">\r\n            <p className=\"text-muted-foreground\">\r\n              No banking data found for the selected criteria.\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n      ) : (\r\n        <Card>\r\n          <CardContent className=\"py-10 text-center\">\r\n            <p className=\"text-muted-foreground\">\r\n              Select a date and click \"Generate Summary\" to view banking data.\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAQA;AACA;AAOA;AAKA;AAMA;AAMA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAlDA;;;;;;;;;;;;;;;;;;;;;;;AAoDA,+EAA+E;AAC/E,MAAM,qBAAqB;IACzB,SAAS;QAAC;QAAgB;QAAsB;QAAuB;KAAoB;IAC3F,QAAQ;QAAC;QAAoB;KAAc;IAC3C,eAAe;IACf,mBAAmB;IACnB,iBAAiB;AACnB;AAEO,SAAS;IACd,MAAM,EAAE,MAAM,YAAY,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,cAAW,AAAD;IACvE,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,gBAAgB,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD;IACpE,MAAM,EAAE,MAAM,SAAS,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD;IAE9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAE9D,yDAAyD;IACzD,wDAAwD;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EACJ,MAAM,WAAW,EACjB,WAAW,gBAAgB,EAC3B,KAAK,EACN,GAAG,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE;QACpB,WAAW,YAAY;QACvB,WAAW,YAAY;QACvB,SAAS,UAAU;QACnB,YAAY,YAAY,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW,gBAAgB;QAC1D,UAAU,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,gBAAgB;QACpD,gBAAgB,iBAAiB;IACnC,GAAG;IAEH,MAAM,qBAAqB;QACzB,iDAAiD;QACjD,kBAAkB;IACpB;IAEA,4BAA4B;IAC5B,MAAM,0BAA0B,CAAC;QAC/B,eAAe;IACjB;IAEA,yBAAyB;IACzB,MAAM,sBAAsB;QAC1B,IAAI,eAAe,YAAY,MAAM,GAAG,GAAG;YACzC,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ,eAAe;YACtE,CAAA,GAAA,gKAAA,CAAA,8BAA2B,AAAD,EAAE,aAAa;QAC3C;IACF;IAEA,0CAA0C;IAC1C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACjC,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG,OAAO,EAAE;QAEvD,gCAAgC;QAChC,MAAM,aAAa;eAAI;SAAY,CAAC,IAAI,CAAC,CAAC,GAAG,IAC3C,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;QAGvD,gCAAgC;QAChC,MAAM,YAAY,IAAI,KAAK,UAAU,CAAC,EAAE,CAAC,IAAI;QAC7C,MAAM,WAAW,IAAI,KAAK,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,IAAI;QAChE,MAAM,WAAW,KAAK,IAAI,CAAC,CAAC,SAAS,OAAO,KAAK,UAAU,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,KAAK;QAEjG,uCAAuC;QACvC,MAAM,wBAAwB,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,WAAW;QACjD,MAAM,0BAA0B,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,uBAAuB,WAAW;QAE1E,sCAAsC;QACtC,OAAO,YAAY,MAAM,CAAC,CAAA;YACxB,MAAM,WAAW,IAAI,KAAK,KAAK,IAAI;YACnC,OAAO,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,4BAClB,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAC5B;IACF,GAAG;QAAC;KAAY;IAEhB,4FAA4F;IAC5F,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACjD,OAAO;YACL,aAAa,aAAa,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE,MAAM;YACxE,mBAAmB,aAAa,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,iBAAiB,EAAE,MAAM;QAC5F;IACF,GAAG;QAAC;KAAY;IAEhB,oFAAoF;IACpF,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG,OAAO;QAErD,gCAAgC;QAChC,MAAM,aAAa;eAAI;SAAY,CAAC,IAAI,CAAC,CAAC,GAAG,IAC3C,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;QAGvD,kDAAkD;QAClD,MAAM,sBAAiC;YACrC,QAAQ;gBAAC;gBAAQ;gBAAU;aAAQ;YACnC,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM;wBACJ,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,IAAI,EAAE;wBAClD,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;wBACnD,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;qBACpD;oBACD,iBAAiB;wBAAC;wBAAW;wBAAW;qBAAU;gBACpD;aACD;QACH;QAEA,6CAA6C;QAC7C,MAAM,iBAA4B;YAChC,QAAQ,WAAW,GAAG,CAAC,CAAA,OAAQ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,IAAI,GAAG;YAC3D,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;oBACvC,aAAa;gBACf;gBACA;oBACE,OAAO;oBACP,MAAM,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;oBACtC,aAAa;gBACf;gBACA;oBACE,OAAO;oBACP,MAAM,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;oBACvC,aAAa;gBACf;gBACA;oBACE,OAAO;oBACP,MAAM,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;oBACvC,aAAa;gBACf;aACD;QACH;QAEA,oEAAoE;QACpE,IAAI,kBAAkB;QACtB,IAAI,kBAAkB;QAEtB,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;YAE9E,gCAAgC;YAChC,MAAM,YAAY,IAAI,KAAK,UAAU,CAAC,EAAE,CAAC,IAAI;YAC7C,MAAM,WAAW,IAAI,KAAK,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,IAAI;YAChE,MAAM,WAAW,KAAK,IAAI,CAAC,CAAC,SAAS,OAAO,KAAK,UAAU,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,KAAK;YAEjG,kFAAkF;YAClF,MAAM,wBAAwB,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,WAAW;YACjD,MAAM,0BAA0B,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,uBAAuB,WAAW;YAE1E,gDAAgD;YAChD,MAAM,qBAAqB,YAAY,MAAM,CAAC,CAAA;gBAC5C,MAAM,WAAW,IAAI,KAAK,KAAK,IAAI;gBACnC,OAAO,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,4BAClB,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,UAAU;YAC5B;YAEA,IAAI,mBAAmB,MAAM,GAAG,GAAG;gBACjC,MAAM,sBAAsB,mBAAmB,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;gBACvF,IAAI,sBAAsB,GAAG;oBAC3B,kBAAkB,AAAC,CAAC,qBAAqB,mBAAmB,IAAI,sBAAuB;oBACvF,kBAAkB,mBAAmB;oBACrC,kBAAkB,KAAK,GAAG,CAAC;gBAC7B;YACF;QACF;QAEA,OAAO;YACL;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;KAAY;IAEhB,qDAAqD;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,wCAAmC,OAAO;;YAE1C,MAAM;QASR;QAEA,eAAe;IACjB,GAAG,EAAE;IAEL,8CAA8C;IAC9C,IAAI,qBAAqB,oBAAoB,kBAAkB,kBAAkB;QAC/E,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;8BACpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;8BAEtB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;8BAEtB,8OAAC,oIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;;IAG1B;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBAAoD;;;;;;IAIvE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC;;kDACC,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,mBAAmB,CAAC;wCACnC,OAAM;kDAEN,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,eAAe,YAAY,MAAM,KAAK;wCACjD,OAAM;kDAEN,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAI1B,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;0DAGlD,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,UAAU,cAAc;gDAC/B,eAAe,CAAC;oDACd,YAAY,UAAU,QAAQ,SAAS,SAAS;oDAChD,mCAAmC;oDACnC,IAAI,UAAU,YAAY;gDAC5B;;kEAEA,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;4DACvB,aAAa,MAAM,SAClB,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,uBACpB,8OAAC,kIAAA,CAAA,aAAU;oEAAiB,OAAO,OAAO,EAAE,CAAC,QAAQ;8EAClD,OAAO,IAAI;mEADG,OAAO,EAAE;;;;0FAK5B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;gEAAa,QAAQ;0EAAC;;;;;;;;;;;;;;;;;;;;;;;;kDAQhD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;0DAGlD,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,UAAU,cAAc;gDAC/B,eAAe,CAAC,QACd,YAAY,UAAU,QAAQ,SAAS,SAAS;;kEAGlD,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;4DACvB,cAAc,MAAM,SACnB,aAAa,IAAI,CACd,MAAM,CAAC,CAAA,SAAU,CAAC,YAAY,OAAO,SAAS,KAAK,UACnD,GAAG,CAAC,CAAC,uBACJ,8OAAC,kIAAA,CAAA,aAAU;oEAAiB,OAAO,OAAO,EAAE,CAAC,QAAQ;8EAClD,OAAO,IAAI;mEADG,OAAO,EAAE;;;;0FAK9B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;gEAAc,QAAQ;0EAAC;;;;;;;;;;;;;;;;;;;;;;;;kDAQjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;0DAGhD,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,QAAQ,cAAc;gDAC7B,eAAe,CAAC,QACd,UAAU,UAAU,QAAQ,SAAS,SAAS;;kEAGhD,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;4DACvB,WAAW,MAAM,SAChB,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC,kIAAA,CAAA,aAAU;oEAAe,OAAO,KAAK,EAAE,CAAC,QAAQ;8EAC9C,KAAK,IAAI;mEADK,KAAK,EAAE;;;;0FAK1B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;gEAAW,QAAQ;0EAAC;;;;;;;;;;;;;;;;;;;;;;;;kDAQ9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAGlD,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,iBAAiB;gDACxB,eAAe,CAAC,QACd,iBAAiB,UAAU,QAAQ,QAAQ;;kEAG7C,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAO;;;;;;0EACzB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;0EAC1B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;kDAKhC,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAGlD,8OAAC,mIAAA,CAAA,UAAO;;kEACN,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,aAAa;;8EAGhB,8OAAC,8MAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEACvB,YAAY,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW,SAAS;;;;;;;;;;;;kEAG5C,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACxB,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4DACP,MAAK;4DACL,UAAU;4DACV,UAAU,CAAC,OAAS,aAAa;4DACjC,YAAY;;;;;;;;;;;;;;;;;;;;;;;kDAMpB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAGlD,8OAAC,mIAAA,CAAA,UAAO;;kEACN,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,WAAW;;8EAGd,8OAAC,8MAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEACvB,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,SAAS;;;;;;;;;;;;kEAGxC,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACxB,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4DACP,MAAK;4DACL,UAAU;4DACV,UAAU;4DACV,YAAY;4DACZ,UAAU,CAAC,OAAS,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOrC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAoB,UAAU,CAAC;8CAAW;;;;;;;;;;;;;;;;;;;;;;;YAQhE,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,mKAAA,CAAA,qBAAkB;oBAAC,qBAAqB;;;;;;;;;;;YAI5C,eAAe,YAAY,MAAM,GAAG,kBACnC;0BACE,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAU,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCACpF,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAU;;;;;;8CAC7B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;;;;;;;sCAGhC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;;8CACrC,8OAAC;oCAAI,WAAU;;wCACZ,YAAY,OAAO,CAAC,QAAQ,CAAC,iCAC5B,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB;;;;;;;;;;;8DAI7C,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;gDAGnB,YAAY,OAAO,CAAC,QAAQ,CAAC,wBAAwB,eAAe,kBAAkB,mBACrF,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC;wDAAI,WAAU;;4DACZ,cAAc,eAAe,iBAC5B;;kFACE,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;kFACtB,8OAAC;wEAAK,WAAU;;4EACb,cAAc,eAAe,CAAC,OAAO,CAAC;4EAAG;;;;;;;;6FAI9C;;kFACE,8OAAC,sNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;kFACxB,8OAAC;wEAAK,WAAU;;4EACb,cAAc,eAAe,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;0EAIhD,8OAAC;gEAAK,WAAU;0EAAO;;;;;;;;;;;;;;;;;;;;;;;wCAOhC,YAAY,OAAO,CAAC,QAAQ,CAAC,uCAC5B,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB;;;;;;;;;;;8DAI7C,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAI,WAAU;kEAAsB;;;;;;;;;;;;;;;;;wCAK1C,YAAY,OAAO,CAAC,QAAQ,CAAC,wCAC5B,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB;;;;;;;;;;;8DAI7C,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EACZ,oBAAoB,IAAI,cAAc,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;8CAStE,8OAAC;oCAAI,WAAU;;wCAEZ,YAAY,MAAM,CAAC,QAAQ,CAAC,uBAAuB,+BAClD,8OAAC,4JAAA,CAAA,cAAW;4CACV,OAAM;4CACN,aAAY;4CACZ,MAAM,cAAc,mBAAmB;4CACvC,YAAY;gDAAC;gDAAO;6CAAM;4CAC1B,kBAAiB;4CACjB,YAAY;;;;;;wCAKf,YAAY,MAAM,CAAC,QAAQ,CAAC,kBAAkB,iBAAiB,cAAc,cAAc,CAAC,MAAM,CAAC,MAAM,GAAG,mBAC3G,8OAAC,4JAAA,CAAA,cAAW;4CACV,OAAM;4CACN,aAAY;4CACZ,MAAM,cAAc,cAAc;4CAClC,YAAY;gDAAC;gDAAQ;6CAAM;4CAC3B,kBAAiB;4CACjB,YAAY;;;;;;;;;;;;gCAKjB,YAAY,aAAa,kBACxB,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;;sEACC,8OAAC,gIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,gIAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,8OAAC,4IAAA,CAAA,eAAY;;sEACX,8OAAC,4IAAA,CAAA,sBAAmB;4DAAC,OAAO;sEAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,MAAK;;kFAC7B,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAIzC,8OAAC,4IAAA,CAAA,sBAAmB;sEAClB,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;gEAAC,SAAS;0EAAqB;;;;;;;;;;;;;;;;;;;;;;;sDAMtD,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;sEACC,cAAA,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAG,WAAU;kFAA6B;;;;;;kFAC3C,8OAAC;wEAAG,WAAU;kFAA8B;;;;;;kFAC5C,8OAAC;wEAAG,WAAU;kFAA8B;;;;;;kFAC5C,8OAAC;wEAAG,WAAU;kFAA8B;;;;;;kFAC5C,8OAAC;wEAAG,WAAU;kFAA8B;;;;;;kFAC5C,8OAAC;wEAAG,WAAU;kFAA8B;;;;;;;;;;;;;;;;;sEAKhD,8OAAC;sEACE,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;oEAAiC,WAAU;;sFAC1C,8OAAC;4EAAG,WAAU;sFACX,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,IAAI,GAAG;;;;;;sFAE/B,8OAAC;4EAAG,WAAU;sFACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,IAAI;;;;;;sFAE3B,8OAAC;4EAAG,WAAU;sFACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK;;;;;;sFAE5B,8OAAC;4EAAG,WAAU;sFACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK;;;;;;sFAE5B,8OAAC;4EAAG,WAAU;sFACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK;;;;;;sFAE5B,8OAAC;4EAAG,WAAU;sFACX,KAAK,iBAAiB;;;;;;;mEAjBlB,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCA6BhD,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC,kKAAA,CAAA,oBAAiB;gCAChB,aAAa;gCACb,WAAW;gCACX,OAAO;gCACP,oBAAoB;gCACpB,YAAY,YAAY,OAAO,CAAC,QAAQ,CAAC;gCACzC,uBAAuB,YAAY,OAAO,CAAC,QAAQ,CAAC;gCACpD,gBAAgB,YAAY,OAAO,CAAC,QAAQ,CAAC;gCAC7C,sBAAsB,YAAY,OAAO,CAAC,QAAQ,CAAC;;;;;;;;;;;;;;;;;gCAKzD,+BACF,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;qCAMzC,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 11813, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/app/banking/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { MainLayout } from \"@/components/layouts/main-layout\";\r\nimport { BankingList } from \"@/features/banking/components/banking-list\";\r\nimport { BankingSummary } from \"@/features/banking/components/banking-summary\";\r\nimport { Tabs, TabsContent } from \"@/components/ui/tabs\";\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { BarChart3 } from \"lucide-react\";\r\n\r\nexport default function BankingPage() {\r\n  const searchParams = useSearchParams();\r\n  const defaultTab = searchParams.get(\"tab\") || \"records\";\r\n  const [activeTab, setActiveTab] = useState<string>(defaultTab);\r\n\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center\">\r\n          <div>\r\n            <h1 className=\"text-2xl font-bold tracking-tight\">Banking Records</h1>\r\n            <p className=\"text-muted-foreground\">\r\n              Manage banking records and transactions\r\n            </p>\r\n          </div>\r\n          <div className=\"mt-4 sm:mt-0\">\r\n            <Button\r\n              variant={activeTab === \"summary\" ? \"default\" : \"outline\"}\r\n              onClick={() => setActiveTab(activeTab === \"records\" ? \"summary\" : \"records\")}\r\n              className=\"flex items-center gap-2\"\r\n            >\r\n              <BarChart3 className=\"h-4 w-4\" />\r\n              {activeTab === \"records\" ? \"View Summary\" : \"View Records\"}\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\r\n          <TabsContent value=\"records\" className=\"space-y-4 mt-0\">\r\n            <BankingList />\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"summary\" className=\"space-y-4 mt-0\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle>Banking Summary</CardTitle>\r\n                <CardDescription>View banking summary with advanced analytics</CardDescription>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <BankingSummary />\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n        </Tabs>\r\n      </div>\r\n    </MainLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,aAAa,aAAa,GAAG,CAAC,UAAU;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,qBACE,8OAAC,+IAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,cAAc,YAAY,YAAY;gCAC/C,SAAS,IAAM,aAAa,cAAc,YAAY,YAAY;gCAClE,WAAU;;kDAEV,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCACpB,cAAc,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;8BAKlD,8OAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACrC,cAAA,8OAAC,4JAAA,CAAA,cAAW;;;;;;;;;;sCAGd,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACrC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,+JAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B", "debugId": null}}]}