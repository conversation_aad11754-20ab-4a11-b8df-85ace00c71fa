"use client";

import { ExpensesDataTable } from "@/components/expenses/expenses-data-table";
import { ExpensesHeader } from "@/components/expenses/expenses-header";
import { PageHeader } from "@/components/page-header";
import { MainLayout } from "@/components/layouts/main-layout";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useCurrentUser } from "@/features/auth/hooks/use-auth";
import { useExpenses } from "@/features/expenses/hooks/use-expenses";

export default function ExpensesPage() {
  const router = useRouter();
  const { data: user, isLoading: userLoading } = useCurrentUser();

  // Pure React state management (SPA pattern)
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [statusFilter, setStatusFilter] = useState<string | undefined>(
    undefined
  );
  const [searchQuery, setSearchQuery] = useState("");
  const [branchFilter, setBranchFilter] = useState<number | undefined>(
    undefined
  );
  const [regionFilter, setRegionFilter] = useState<number | undefined>(
    undefined
  );

  // Build query parameters for API call
  const queryParams = {
    page: currentPage,
    limit: pageSize,
    status: statusFilter,
    search: searchQuery || undefined,
    branch_id: branchFilter,
    region_id: regionFilter,
  };

  // Use React Query hook for data fetching
  const { data, isLoading } = useExpenses(queryParams);

  // Extract data from the response
  const expenses = data?.expenses || [];
  const totalCount = data?.totalCount || 0;

  // Handle page changes
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle page size changes
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Check user permissions
  useEffect(() => {
    if (!userLoading && user) {
      if (
        user.role_name !== "company_admin" &&
        user.role_name !== "branch_admin" &&
        user.role_name !== "branch_manager" &&
        user.role_name !== "accountant"
      ) {
        router.replace("/dashboard");
      }
    }
  }, [user, userLoading, router]);

  if (userLoading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading...</span>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="flex flex-col gap-4">
        <PageHeader
          title="Expenses Management"
          description="Review and manage expense requests from employees"
        />

        <ExpensesHeader
          isLoading={isLoading}
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          statusFilter={statusFilter}
          onStatusChange={setStatusFilter}
          branchFilter={branchFilter}
          onBranchChange={setBranchFilter}
          regionFilter={regionFilter}
          onRegionChange={setRegionFilter}
          onPageReset={() => setCurrentPage(1)}
        />

        <ExpensesDataTable
          data={expenses}
          totalCount={totalCount}
          isLoading={isLoading}
          pagination={{
            currentPage,
            onPageChange: handlePageChange,
            pageSize,
            onPageSizeChange: handlePageSizeChange,
          }}
        />
      </div>
    </MainLayout>
  );
}
