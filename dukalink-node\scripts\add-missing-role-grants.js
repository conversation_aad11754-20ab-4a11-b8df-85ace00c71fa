/**
 * <PERSON><PERSON><PERSON> to add missing role grants to the rbac_grants table
 * This script adds permissions for auditor, finance_manager, operations, operations_manager, and stock_admin roles
 */

require("dotenv").config();
const db = require("../src/models");
const sequelize = require("../config/database");
const logger = require("../src/utils/logger");

async function addMissingRoleGrants() {
  logger.info("Starting script to add missing role grants");

  let transaction;

  try {
    // Check if the database is connected
    await sequelize.authenticate();
    logger.info("Database connection established");

    // Check if the rbacGrants model exists
    if (!db.rbacGrants) {
      logger.error("rbacGrants model not found in db object");
      process.exit(1);
    }

    // Start a transaction
    transaction = await sequelize.transaction();

    // Define grants for each role
    const rolesToAdd = [
      "auditor",
      "finance_manager",
      "operations",
      "operations_manager",
      "stock_admin",
      "float_manager",
    ];

    // Define common resources and actions for all roles
    const commonGrants = [
      { resource: "profile", action: "read:own", attributes: { "*": true } },
      { resource: "users", action: "read:any", attributes: { "*": true } },
    ];

    // Define role-specific grants
    const roleSpecificGrants = {
      auditor: [
        { resource: "sales", action: "read:any", attributes: { "*": true } },
        {
          resource: "pos_sessions",
          action: "read:any",
          attributes: { "*": true },
        },
        { resource: "expenses", action: "read:any", attributes: { "*": true } },
        {
          resource: "stock_movement",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_request",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_reports",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "inventory",
          action: "read:any",
          attributes: { "*": true },
        },
        { resource: "banking", action: "read:any", attributes: { "*": true } },
        {
          resource: "financial_reports",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "expense_reports",
          action: "read:any",
          attributes: { "*": true },
        },
      ],
      finance_manager: [
        { resource: "sales", action: "read:any", attributes: { "*": true } },
        {
          resource: "pos_sessions",
          action: "read:any",
          attributes: { "*": true },
        },
        { resource: "expenses", action: "read:any", attributes: { "*": true } },
        {
          resource: "expenses",
          action: "update:any",
          attributes: { "*": true },
        },
        {
          resource: "expense_first_approval",
          action: "create:any",
          attributes: { "*": true },
        },
        {
          resource: "expense_final_approval",
          action: "create:any",
          attributes: { "*": true },
        },
        { resource: "banking", action: "read:any", attributes: { "*": true } },
        {
          resource: "banking",
          action: "create:any",
          attributes: { "*": true },
        },
        {
          resource: "banking",
          action: "update:any",
          attributes: { "*": true },
        },
        {
          resource: "financial_reports",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "expense_reports",
          action: "read:any",
          attributes: { "*": true },
        },
      ],
      operations: [
        { resource: "sales", action: "read:any", attributes: { "*": true } },
        {
          resource: "pos_sessions",
          action: "read:any",
          attributes: { "*": true },
        },
        { resource: "expenses", action: "read:any", attributes: { "*": true } },
        {
          resource: "expenses",
          action: "create:own",
          attributes: { "*": true },
        },
        {
          resource: "expenses",
          action: "update:own",
          attributes: { "*": true },
        },
        {
          resource: "stock_movement",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_request",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "inventory",
          action: "read:any",
          attributes: { "*": true },
        },
      ],
      operations_manager: [
        { resource: "sales", action: "read:any", attributes: { "*": true } },
        {
          resource: "pos_sessions",
          action: "read:any",
          attributes: { "*": true },
        },
        { resource: "expenses", action: "read:any", attributes: { "*": true } },
        {
          resource: "expenses",
          action: "update:any",
          attributes: { "*": true },
        },
        {
          resource: "expense_first_approval",
          action: "create:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_movement",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_movement",
          action: "create:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_request",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_request",
          action: "create:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_request",
          action: "update:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_reports",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "employees",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "inventory",
          action: "read:any",
          attributes: { "*": true },
        },
      ],
      stock_admin: [
        { resource: "products", action: "read:any", attributes: { "*": true } },
        {
          resource: "products",
          action: "create:any",
          attributes: { "*": true },
        },
        {
          resource: "products",
          action: "update:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_movement",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_movement",
          action: "create:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_movement",
          action: "update:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_request",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_request",
          action: "create:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_request",
          action: "update:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_reports",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "inventory",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "inventory",
          action: "create:any",
          attributes: { "*": true },
        },
        {
          resource: "inventory",
          action: "update:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_items",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_items",
          action: "create:any",
          attributes: { "*": true },
        },
        {
          resource: "stock_items",
          action: "update:any",
          attributes: { "*": true },
        },
      ],
      float_manager: [
        {
          resource: "mpesa_float",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa-float",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa_float_assignment",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa_float_assignment",
          action: "create:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa_float_assignment",
          action: "update:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa_float_assignment",
          action: "delete:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa-float-balances",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa-float-reconciliations",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa-float-movements",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa_float_movements",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa-float-topups",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa_float_topups",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa_float_topups",
          action: "create:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa_transactions",
          action: "read:any",
          attributes: { "*": true },
        },
        {
          resource: "mpesa-transactions",
          action: "read:any",
          attributes: { "*": true },
        },
        { resource: "banking", action: "read:any", attributes: { "*": true } },
        {
          resource: "banking",
          action: "update:any",
          attributes: { "*": true },
        },
        {
          resource: "banking",
          action: "create:any",
          attributes: { "*": true },
        },
      ],
    };

    let addedCount = 0;
    let existingCount = 0;

    // Process each role
    for (const role of rolesToAdd) {
      logger.info(`Processing role: ${role}`);

      // Add common grants
      for (const grant of commonGrants) {
        const { resource, action, attributes } = grant;

        // Check if the grant already exists
        const existingGrant = await db.rbacGrants.findOne({
          where: {
            role,
            resource,
            action,
          },
          transaction,
        });

        if (existingGrant) {
          logger.info(
            `Grant already exists for ${role}:${resource}:${action}, updating attributes`
          );
          existingGrant.attributes = JSON.stringify(attributes);
          await existingGrant.save({ transaction });
          existingCount++;
        } else {
          logger.info(`Adding grant for ${role}:${resource}:${action}`);
          await db.rbacGrants.create(
            {
              role,
              resource,
              action,
              attributes: JSON.stringify(attributes),
            },
            { transaction }
          );
          addedCount++;
        }
      }

      // Add role-specific grants
      if (roleSpecificGrants[role]) {
        for (const grant of roleSpecificGrants[role]) {
          const { resource, action, attributes } = grant;

          // Check if the grant already exists
          const existingGrant = await db.rbacGrants.findOne({
            where: {
              role,
              resource,
              action,
            },
            transaction,
          });

          if (existingGrant) {
            logger.info(
              `Grant already exists for ${role}:${resource}:${action}, updating attributes`
            );
            existingGrant.attributes = JSON.stringify(attributes);
            await existingGrant.save({ transaction });
            existingCount++;
          } else {
            logger.info(`Adding grant for ${role}:${resource}:${action}`);
            await db.rbacGrants.create(
              {
                role,
                resource,
                action,
                attributes: JSON.stringify(attributes),
              },
              { transaction }
            );
            addedCount++;
          }
        }
      }
    }

    // Commit the transaction
    await transaction.commit();

    logger.info(
      `Successfully added ${addedCount} grants and updated ${existingCount} existing grants`
    );
    logger.info("Script completed successfully");
  } catch (error) {
    logger.error(`Error in addMissingRoleGrants: ${error.message}`);
    logger.error(error.stack);

    // Rollback the transaction if it exists
    if (transaction) {
      await transaction.rollback();
    }

    process.exit(1);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the script
addMissingRoleGrants()
  .then(() => {
    logger.info("Script execution completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error(`Unhandled error: ${error.message}`);
    logger.error(error.stack);
    process.exit(1);
  });
