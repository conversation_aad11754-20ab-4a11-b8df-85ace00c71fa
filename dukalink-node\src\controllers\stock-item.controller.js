const { StockItem, Branch, Product, ProductCategory, Brand, BrandType, Region, Location, Tenant } = require('../models');
const ProductDiscount = require('../models/product-discount.model');
const { Op } = require('sequelize');
const sequelize = require('../../config/database');
const AppError = require('../utils/error');
const logger = require('../utils/logger');

/**
 * Get all stock items
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAllStockItems = async (req, res, next) => {
  try {
    const {
      branch_id,
      product_id,
      product_ids, // New parameter for batch validation
      category_id,
      subcategory_id,
      brand_id,
      brand_type_id,
      has_serial,
      min_quantity,
      max_quantity,
      region_id,
      location,
      tenant_id,
      is_hq,
      is_dsa_assignment, // New parameter to filter products that can be assigned to DSA
      exclude_zero_quantity, // New parameter to exclude products with zero quantity
      minimal, // New parameter for minimal data response
      page = 1,
      limit = 20
    } = req.query;

    const whereClause = { deleted_at: null };
    const productWhereClause = { deleted_at: null };
    const categoryWhereClause = { deleted_at: null };
    const branchWhereClause = { deleted_at: null };
    const tenantWhereClause = { deleted_at: null };

    if (branch_id) {
      whereClause.branch_id = branch_id;
    }

    if (tenant_id) {
      whereClause.tenant_id = tenant_id;
    }

    // Handle HQ stock items
    if (is_hq === 'true') {
      branchWhereClause.level = 0; // HQ branch has level 0
    } else if (is_hq === 'false') {
      branchWhereClause.level = { [Op.ne]: 0 }; // Non-HQ branches
    }

    if (region_id) {
      branchWhereClause.region_id = region_id;
    }

    if (location) {
      branchWhereClause.location = { [Op.like]: `%${location}%` };
    }

    if (product_id) {
      whereClause.product_id = product_id;
    }

    // Support for batch product validation
    if (product_ids) {
      const productIdArray = product_ids.split(',').map(id => parseInt(id.trim(), 10)).filter(id => !isNaN(id));
      if (productIdArray.length > 0) {
        whereClause.product_id = { [Op.in]: productIdArray };
      }
    }

    if (category_id) {
      productWhereClause.category_id = category_id;
    }

    if (subcategory_id) {
      // For subcategories, we need to find products where the category's parent_id matches the subcategory_id
      categoryWhereClause.parent_id = subcategory_id;
    }

    if (brand_id) {
      productWhereClause.brand_id = brand_id;
    }

    if (brand_type_id) {
      productWhereClause.brand_type_id = brand_type_id;
    }

    if (has_serial !== undefined) {
      productWhereClause.has_serial = has_serial === 'true';
    }

    // Filter products that can be assigned to DSA
    if (is_dsa_assignment === 'true') {
      productWhereClause.can_assign_to_dsa = true;
      logger.info('Filtering products that can be assigned to DSA');
    }

    // Exclude products with zero quantity
    if (exclude_zero_quantity === 'true') {
      whereClause.quantity = { ...whereClause.quantity, [Op.gt]: 0 };
      logger.info('Excluding products with zero quantity');
    } else if (min_quantity !== undefined) {
      whereClause.quantity = { ...whereClause.quantity, [Op.gte]: parseInt(min_quantity) };
    }

    if (max_quantity !== undefined) {
      whereClause.quantity = { ...whereClause.quantity, [Op.lte]: parseInt(max_quantity) };
    }

    // Parse pagination parameters
    const pageInt = parseInt(page, 10);

    // Use the requested limit, with a reasonable default and maximum
    const limitInt = Math.min(parseInt(limit, 10) || 100, 500);

    // Log the limit being used
    logger.info(`Using limit of ${limitInt} for stock items query.`);

    const offset = (pageInt - 1) * limitInt;

    // Get total count for pagination
    const totalCount = await StockItem.count({
      where: whereClause,
      include: [
        {
          model: Branch,
          where: Object.keys(branchWhereClause).length > 0 ? branchWhereClause : undefined,
          required: true
        },
        {
          model: Product,
          where: Object.keys(productWhereClause).length > 0 ? productWhereClause : undefined,
          required: true
        }
      ]
    });

    // Determine if we need minimal data (for DSA assignment, stock validation, or other lightweight use cases)
    const isMinimalData = is_dsa_assignment === 'true' || minimal === 'true' || product_ids;

    // Define the query with optimized includes based on the use case
    const queryOptions = {
      where: whereClause,
      attributes: [
        'id', 'branch_id', 'product_id', 'quantity', 'quantity_reserved',
        'default_selling_price', 'default_buying_price', 'default_wholesale_price',
        // Add computed field for available quantity
        [sequelize.literal('(quantity - COALESCE(quantity_reserved, 0))'), 'quantity_available'],
        // Only include these fields if not minimal data mode
        ...(isMinimalData ? [] : [
          'buying_price_including_vat', 'buying_price_excluding_vat', 'buying_vat_amount', 'buying_vat_rate',
          'batch_number', 'expiry_date', 'manufacturing_date',
          'reorder_level', 'reorder_quantity', 'valuation_method'
        ]),
        'created_at', 'updated_at'
      ],
      include: [
        {
          model: Branch,
          attributes: ['id', 'name', 'location', 'region_id', 'level'],
          where: Object.keys(branchWhereClause).length > 0 ? branchWhereClause : undefined,
          // Only include Region if not minimal data mode
          ...(isMinimalData ? {} : {
            include: [
              {
                model: Region,
                attributes: ['id', 'name', 'code', 'description']
              }
            ]
          })
        },
        {
          model: Product,
          attributes: ['id', 'name', 'sku', 'has_serial', 'can_assign_to_dsa', 'suggested_selling_price'],
          where: Object.keys(productWhereClause).length > 0 ? productWhereClause : undefined,
          // Only include additional associations if not minimal data mode
          ...(isMinimalData ? {} : {
            include: [
              {
                model: ProductCategory,
                attributes: ['id', 'name', 'description', 'parent_id', 'level'],
                where: Object.keys(categoryWhereClause).length > 0 ? categoryWhereClause : undefined,
                include: [
                  {
                    model: ProductCategory,
                    as: 'Parent',
                    attributes: ['id', 'name', 'description', 'parent_id', 'level']
                  }
                ]
              },
              {
                model: Brand,
                attributes: ['id', 'name']
              },
              {
                model: BrandType,
                attributes: ['id', 'name'],
                include: [
                  {
                    model: Brand,
                    attributes: ['id', 'name']
                  }
                ]
              }
            ]
          })
        }
      ],
      order: [
        ['updated_at', 'DESC']
      ],
      limit: limitInt,
      offset: offset
    };

    logger.info(`Executing stock items query with ${isMinimalData ? 'minimal' : 'full'} data mode`);
    const stockItems = await StockItem.findAll(queryOptions);

    // Only enhance stock items with additional data if not in minimal mode
    if (!isMinimalData) {
      logger.info('Enhancing stock items with category hierarchy and discount information');
      const PriceCalculator = require('../utils/price-calculator');

      // Get all product IDs for batch fetching discounts
      const productIds = stockItems.map(item => item.product_id);

      // Batch fetch all product discounts at once
      const allProductDiscounts = await ProductDiscount.findAll({
        where: {
          product_id: { [Op.in]: productIds },
          is_active: true,
          [Op.or]: [
            { end_date: null },
            { end_date: { [Op.gte]: new Date() } }
          ]
        }
      });

      // Create a map of product ID to discounts for quick lookup
      const productDiscountsMap = {};
      allProductDiscounts.forEach(discount => {
        if (!productDiscountsMap[discount.product_id]) {
          productDiscountsMap[discount.product_id] = [];
        }
        productDiscountsMap[discount.product_id].push(discount);
      });

      // Process each stock item
      for (const stockItem of stockItems) {
        // Add category hierarchy
        if (stockItem.Product && stockItem.Product.ProductCategory) {
          stockItem.Product.dataValues.categoryHierarchy = await getCategoryHierarchy(stockItem.Product.ProductCategory.id);
        }

        // Calculate discount information
        if (stockItem.Product) {
          const branch = stockItem.Branch;
          const regionId = branch ? branch.region_id : null;

          try {
            // Get discounts for this product from the map
            const productDiscounts = productDiscountsMap[stockItem.product_id] || [];

            // Add discounts to the product
            stockItem.Product.dataValues.ProductDiscounts = productDiscounts;

            // Create a merged object with both product and stock item data
            const productWithPricing = {
              ...stockItem.Product.dataValues,
              default_selling_price: stockItem.default_selling_price,
              default_buying_price: stockItem.default_buying_price,
              default_wholesale_price: stockItem.default_wholesale_price,
              StockItem: stockItem
            };

            // Use the price calculator to get discount information
            const priceDetails = await PriceCalculator.calculatePrice(
              productWithPricing,
              1, // quantity
              null, // customer
              {
                date: new Date(),
                branchId: stockItem.branch_id,
                regionId: regionId
              }
            );

            // Add discount information to the product
            stockItem.Product.dataValues.is_discounted = priceDetails.is_discounted;
            stockItem.Product.dataValues.original_price = priceDetails.original_price;
            stockItem.Product.dataValues.final_price = priceDetails.final_price;
            stockItem.Product.dataValues.discount_amount = priceDetails.discount_amount;
            stockItem.Product.dataValues.discount_percentage = priceDetails.discount_percentage;
            stockItem.Product.dataValues.discount_source = priceDetails.discount_source;
            stockItem.Product.dataValues.discount_type = priceDetails.discount_type;
            stockItem.Product.dataValues.applied_discount_id = priceDetails.applied_discount_id;
          } catch (error) {
            logger.error(`Error calculating discounts for product ${stockItem.product_id}: ${error.message}`);
            // Set default values if discount calculation fails
            stockItem.Product.dataValues.is_discounted = false;
            stockItem.Product.dataValues.original_price = stockItem.default_selling_price || 0;
            stockItem.Product.dataValues.final_price = stockItem.default_selling_price || 0;
            stockItem.Product.dataValues.discount_amount = 0;
            stockItem.Product.dataValues.discount_percentage = 0;
          }
        }
      }
    } else {
      // For minimal data mode, just add basic price information
      logger.info('Using minimal data mode - skipping category hierarchy and discount calculations');
      for (const stockItem of stockItems) {
        if (stockItem.Product) {
          stockItem.Product.dataValues.original_price = stockItem.default_selling_price || 0;
          stockItem.Product.dataValues.final_price = stockItem.default_selling_price || 0;
        }
      }
    }

    return res.status(200).json({
      data: stockItems,
      pagination: {
        total: totalCount,
        page: pageInt,
        limit: limitInt,
        pages: Math.ceil(totalCount / limitInt)
      }
    });
  } catch (error) {
    logger.error(`Error fetching stock items: ${error.message}`);
    next(error);
  }
};

/**
 * Get stock item by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getStockItemById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const stockItem = await StockItem.findOne({
      where: { id, deleted_at: null },
      include: [
        {
          model: Branch,
          attributes: ['id', 'name', 'location', 'region_id', 'level'],
          include: [
            {
              model: Region,
              attributes: ['id', 'name', 'code', 'description']
            }
          ]
        },
        {
          model: Product,
          attributes: ['id', 'name', 'sku', 'has_serial'],
          include: [
            {
              model: ProductCategory,
              attributes: ['id', 'name', 'description', 'parent_id', 'level'],
              include: [
                {
                  model: ProductCategory,
                  as: 'Parent',
                  attributes: ['id', 'name', 'description', 'parent_id', 'level']
                }
              ]
            },
            {
              model: Brand,
              attributes: ['id', 'name', 'description']
            },
            {
              model: BrandType,
              attributes: ['id', 'name', 'description'],
              include: [
                {
                  model: Brand,
                  attributes: ['id', 'name']
                }
              ]
            }
          ]
        }
      ]
    });

    if (!stockItem) {
      return next(new AppError('Stock item not found', 404));
    }

    // Add category hierarchy
    if (stockItem.Product && stockItem.Product.ProductCategory) {
      stockItem.Product.dataValues.categoryHierarchy = await getCategoryHierarchy(stockItem.Product.ProductCategory.id);
    }

    // Calculate discount information
    if (stockItem.Product) {
      const branch = stockItem.Branch;
      const regionId = branch ? branch.region_id : null;

      try {
        // Fetch product discounts separately
        const productDiscounts = await ProductDiscount.findAll({
          where: {
            product_id: stockItem.product_id,
            is_active: true,
            [Op.or]: [
              { end_date: null },
              { end_date: { [Op.gte]: new Date() } }
            ]
          }
        });

        // Add discounts to the product
        stockItem.Product.dataValues.ProductDiscounts = productDiscounts;

        // Create a merged object with both product and stock item data
        const productWithPricing = {
          ...stockItem.Product.dataValues,
          default_selling_price: stockItem.default_selling_price,
          default_buying_price: stockItem.default_buying_price,
          default_wholesale_price: stockItem.default_wholesale_price,
          StockItem: stockItem
        };

        // Use the price calculator to get discount information
        const PriceCalculator = require('../utils/price-calculator');
        const priceDetails = await PriceCalculator.calculatePrice(
          productWithPricing,
          1, // quantity
          null, // customer
          {
            date: new Date(),
            branchId: stockItem.branch_id,
            regionId: regionId
          }
        );

        // Add discount information to the product
        stockItem.Product.dataValues.is_discounted = priceDetails.is_discounted;
        stockItem.Product.dataValues.original_price = priceDetails.original_price;
        stockItem.Product.dataValues.final_price = priceDetails.final_price;
        stockItem.Product.dataValues.discount_amount = priceDetails.discount_amount;
        stockItem.Product.dataValues.discount_percentage = priceDetails.discount_percentage;
        stockItem.Product.dataValues.discount_source = priceDetails.discount_source;
        stockItem.Product.dataValues.discount_type = priceDetails.discount_type;
        stockItem.Product.dataValues.applied_discount_id = priceDetails.applied_discount_id;
      } catch (error) {
        logger.error(`Error calculating discounts for product ${stockItem.product_id}: ${error.message}`);
        // Set default values if discount calculation fails
        stockItem.Product.dataValues.is_discounted = false;
        stockItem.Product.dataValues.original_price = stockItem.default_selling_price || 0;
        stockItem.Product.dataValues.final_price = stockItem.default_selling_price || 0;
        stockItem.Product.dataValues.discount_amount = 0;
        stockItem.Product.dataValues.discount_percentage = 0;
      }
    }

    return res.status(200).json(stockItem);
  } catch (error) {
    logger.error(`Error fetching stock item: ${error.message}`);
    next(error);
  }
};

/**
 * Create a new stock item
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createStockItem = async (req, res, next) => {
  try {
    const {
      branch_id,
      product_id,
      quantity,
      quantity_reserved,
      default_selling_price,
      default_buying_price,
      default_wholesale_price,
      buying_price_including_vat,
      buying_price_excluding_vat,
      buying_vat_amount,
      buying_vat_rate,
      batch_number,
      expiry_date,
      manufacturing_date,
      reorder_level,
      reorder_quantity,
      valuation_method,
      tenant_id
    } = req.body;

    // For backward compatibility
    const selling_price = req.body.selling_price || default_selling_price;
    const buying_price = req.body.buying_price || default_buying_price;
    const wholesale_price = req.body.wholesale_price || default_wholesale_price;

    if (!product_id) {
      return next(new AppError('Product ID is required', 400));
    }

    if (!branch_id && !tenant_id) {
      return next(new AppError('Either Branch ID or Tenant ID is required', 400));
    }

    let branch;
    let hqBranch;

    if (branch_id) {
      // Check if branch exists
      branch = await Branch.findOne({
        where: { id: branch_id, deleted_at: null }
      });

      if (!branch) {
        return next(new AppError('Branch not found', 404));
      }
    } else if (tenant_id) {
      // This is an HQ stock item, check if tenant exists
      const tenant = await Tenant.findOne({
        where: { id: tenant_id, deleted_at: null }
      });

      if (!tenant) {
        return next(new AppError('Tenant not found', 404));
      }

      // Get HQ branch
      hqBranch = await Branch.findOne({
        where: { level: 0, deleted_at: null }
      });

      if (!hqBranch) {
        return next(new AppError('HQ branch not found', 404));
      }
    }

    // Check if product exists
    const product = await Product.findOne({
      where: { id: product_id, deleted_at: null }
    });

    if (!product) {
      return next(new AppError('Product not found', 404));
    }

    // Check if stock item already exists
    let whereClause = {
      product_id,
      deleted_at: null
    };

    if (branch_id) {
      whereClause.branch_id = branch_id;
    } else if (hqBranch) {
      whereClause.branch_id = hqBranch.id;
      whereClause.tenant_id = tenant_id;
    }

    const existingStockItem = await StockItem.findOne({ where: whereClause });

    if (existingStockItem) {
      return next(new AppError('Stock item already exists for this branch/tenant and product', 400));
    }

    // Create stock item with all available fields
    const createData = {
      product_id,
      quantity: quantity || 0,
      quantity_reserved: quantity_reserved || 0,
      default_selling_price: selling_price || 0.00,
      default_buying_price: buying_price || 0.00,
      default_wholesale_price: wholesale_price || 0.00,

      // Batch/lot tracking
      batch_number: batch_number || null,
      expiry_date: expiry_date || null,
      manufacturing_date: manufacturing_date || null,

      // Inventory management
      reorder_level: reorder_level || null,
      reorder_quantity: reorder_quantity || null,
      valuation_method: valuation_method || 'WEIGHTED_AVERAGE'
    };

    // Compute VAT-related fields if VAT rate is provided
    if (buying_vat_rate) {
      const vatRate = parseFloat(buying_vat_rate);
      const buyingPrice = parseFloat(buying_price) || 0;

      // Calculate VAT amount and prices including/excluding VAT
      const vatAmount = (buyingPrice * vatRate) / 100;
      const priceExcludingVat = buyingPrice;
      const priceIncludingVat = buyingPrice + vatAmount;

      // Add computed VAT fields to createData
      createData.buying_vat_rate = vatRate;
      createData.buying_vat_amount = vatAmount;
      createData.buying_price_excluding_vat = priceExcludingVat;
      createData.buying_price_including_vat = priceIncludingVat;
    } else {
      // Use provided VAT fields if available
      createData.buying_price_including_vat = buying_price_including_vat || null;
      createData.buying_price_excluding_vat = buying_price_excluding_vat || null;
      createData.buying_vat_amount = buying_vat_amount || null;
      createData.buying_vat_rate = buying_vat_rate || null;
    }

    if (branch_id) {
      createData.branch_id = branch_id;
    } else if (hqBranch) {
      createData.branch_id = hqBranch.id;
      createData.tenant_id = tenant_id;
    }

    const stockItem = await StockItem.create(createData);

    return res.status(201).json(stockItem);
  } catch (error) {
    logger.error(`Error creating stock item: ${error.message}`);
    next(error);
  }
};

/**
 * Update a stock item
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updateStockItem = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      quantity,
      quantity_reserved,
      selling_price,
      buying_price,
      wholesale_price,
      default_selling_price,
      default_buying_price,
      default_wholesale_price,
      buying_price_including_vat,
      buying_price_excluding_vat,
      buying_vat_amount,
      buying_vat_rate,
      batch_number,
      expiry_date,
      manufacturing_date,
      reorder_level,
      reorder_quantity,
      valuation_method
    } = req.body;

    // Check if at least one field is provided for update
    if (Object.keys(req.body).length === 0) {
      return next(new AppError('At least one field must be provided for update', 400));
    }

    const stockItem = await StockItem.findOne({
      where: { id, deleted_at: null }
    });

    if (!stockItem) {
      return next(new AppError('Stock item not found', 404));
    }

    // Prepare update object with only the fields that are provided
    const updateData = {};

    // Basic fields
    if (quantity !== undefined) updateData.quantity = quantity;
    if (quantity_reserved !== undefined) updateData.quantity_reserved = quantity_reserved;

    // Pricing fields (with backward compatibility)
    if (selling_price !== undefined) updateData.default_selling_price = selling_price;
    if (buying_price !== undefined) updateData.default_buying_price = buying_price;
    if (wholesale_price !== undefined) updateData.default_wholesale_price = wholesale_price;
    if (default_selling_price !== undefined) updateData.default_selling_price = default_selling_price;
    if (default_buying_price !== undefined) updateData.default_buying_price = default_buying_price;
    if (default_wholesale_price !== undefined) updateData.default_wholesale_price = default_wholesale_price;

    // Location field removed as per requirement

    // VAT-related fields
    // If VAT rate is provided, compute other VAT fields
    if (buying_vat_rate !== undefined) {
      const vatRate = parseFloat(buying_vat_rate);
      // Use the updated buying price if available, otherwise use the existing one
      const buyingPrice = buying_price !== undefined ?
        parseFloat(buying_price) :
        (default_buying_price !== undefined ?
          parseFloat(default_buying_price) :
          parseFloat(stockItem.default_buying_price) || 0);

      // Calculate VAT amount and prices including/excluding VAT
      const vatAmount = (buyingPrice * vatRate) / 100;
      const priceExcludingVat = buyingPrice;
      const priceIncludingVat = buyingPrice + vatAmount;

      // Add computed VAT fields to updateData
      updateData.buying_vat_rate = vatRate;
      updateData.buying_vat_amount = vatAmount;
      updateData.buying_price_excluding_vat = priceExcludingVat;
      updateData.buying_price_including_vat = priceIncludingVat;
    } else {
      // Use provided VAT fields if available
      if (buying_price_including_vat !== undefined) updateData.buying_price_including_vat = buying_price_including_vat;
      if (buying_price_excluding_vat !== undefined) updateData.buying_price_excluding_vat = buying_price_excluding_vat;
      if (buying_vat_amount !== undefined) updateData.buying_vat_amount = buying_vat_amount;
    }

    // Batch/lot tracking
    if (batch_number !== undefined) updateData.batch_number = batch_number;
    if (expiry_date !== undefined) updateData.expiry_date = expiry_date;
    if (manufacturing_date !== undefined) updateData.manufacturing_date = manufacturing_date;

    // Inventory management
    if (reorder_level !== undefined) updateData.reorder_level = reorder_level;
    if (reorder_quantity !== undefined) updateData.reorder_quantity = reorder_quantity;
    if (valuation_method !== undefined) updateData.valuation_method = valuation_method;

    await stockItem.update(updateData);

    return res.status(200).json(stockItem);
  } catch (error) {
    logger.error(`Error updating stock item: ${error.message}`);
    next(error);
  }
};

/**
 * Delete a stock item (soft delete)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deleteStockItem = async (req, res, next) => {
  try {
    const { id } = req.params;

    const stockItem = await StockItem.findOne({
      where: { id, deleted_at: null }
    });

    if (!stockItem) {
      return next(new AppError('Stock item not found', 404));
    }

    await stockItem.update({ deleted_at: new Date() });

    return res.status(200).json({
      message: 'Stock item deleted successfully'
    });
  } catch (error) {
    logger.error(`Error deleting stock item: ${error.message}`);
    next(error);
  }
};

// Create a cache for category hierarchies to avoid redundant database queries
const categoryHierarchyCache = new Map();

/**
 * Helper function to get the complete category hierarchy for a category
 * @param {Number} categoryId - The category ID
 * @returns {Array} - Array of categories from top-level to the specified category
 */
async function getCategoryHierarchy(categoryId) {
  if (!categoryId) return [];

  // Check if we have this hierarchy in the cache
  if (categoryHierarchyCache.has(categoryId)) {
    return categoryHierarchyCache.get(categoryId);
  }

  // Get the category
  const category = await ProductCategory.findByPk(categoryId);
  if (!category) return [];

  const hierarchy = [{
    id: category.id,
    name: category.name,
    description: category.description,
    level: category.level
  }];

  // If this is a top-level category, cache and return just this category
  if (!category.parent_id) {
    categoryHierarchyCache.set(categoryId, hierarchy);
    return hierarchy;
  }

  // Otherwise, get the parent hierarchy and add this category
  const parentHierarchy = await getCategoryHierarchy(category.parent_id);
  const fullHierarchy = [...parentHierarchy, ...hierarchy];

  // Cache the result
  categoryHierarchyCache.set(categoryId, fullHierarchy);
  return fullHierarchy;
}

module.exports = {
  getAllStockItems,
  getStockItemById,
  createStockItem,
  updateStockItem,
  deleteStockItem
};
