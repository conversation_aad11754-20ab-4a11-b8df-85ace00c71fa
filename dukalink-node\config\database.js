const { Sequelize } = require("sequelize");
require("dotenv").config();

// Determine if we should use SSL based on environment
const useSSL = process.env.DB_USE_SSL === "true";

// Configure database connection options
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT || 3306,
  dialect: "mysql",
  logging: false, // Disabled SQL query logging
  dialectOptions: {},
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
};

// Add SSL configuration if needed
if (useSSL) {
  dbConfig.dialectOptions.ssl = {
    require: true,
    rejectUnauthorized: false,
  };
}

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  dbConfig
);

module.exports = sequelize;
