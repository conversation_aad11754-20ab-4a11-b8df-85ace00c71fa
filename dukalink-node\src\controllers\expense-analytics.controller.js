/**
 * Expense Analytics Controller
 * Provides analytics and reporting functionality for expenses
 */
const { Expense, ExpenseCategory, User, Branch, Region } = require("../models");
const { Op, Sequelize } = require("sequelize");
const AppError = require("../utils/error");
const logger = require("../utils/logger");
const ExcelJS = require("exceljs");
const moment = require("moment");

/**
 * Get expense summary analytics
 * Provides overall summary statistics for expenses
 */
exports.getExpenseSummary = async (req, res, next) => {
  try {
    const { start_date, end_date, branch_id, category_id, user_id } = req.query;

    // Build where clause for filtering
    const whereClause = {};

    // Filter by date range if provided
    if (start_date && end_date) {
      whereClause.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)],
      };
    } else if (start_date) {
      whereClause.created_at = {
        [Op.gte]: new Date(start_date),
      };
    } else if (end_date) {
      whereClause.created_at = {
        [Op.lte]: new Date(end_date),
      };
    }

    // Filter by branch if provided
    if (branch_id) {
      whereClause.branch_id = branch_id;
    }

    // Filter by category if provided
    if (category_id) {
      whereClause.category_id = category_id;
    }

    // Filter by user if provided
    if (user_id) {
      whereClause.user_id = user_id;
    }

    // Branch-level roles can only see expenses from their branch
    if (
      req.user.role_name === "branch_admin" ||
      req.user.role_name === "operations_manager" ||
      req.user.role_name === "assistant_operations_manager"
    ) {
      whereClause.branch_id = req.user.branch_id;
    }

    // Get summary statistics
    const summary = {
      // Total counts by status
      counts: {
        total: await Expense.count({ where: whereClause }),
        pending: await Expense.count({
          where: {
            ...whereClause,
            status: "pending",
          },
        }),
        approved: await Expense.count({
          where: {
            ...whereClause,
            status: "approved",
          },
        }),
        partially_approved: await Expense.count({
          where: {
            ...whereClause,
            status: "partially_approved",
          },
        }),
        declined: await Expense.count({
          where: {
            ...whereClause,
            status: "declined",
          },
        }),
        pending_first_approval: await Expense.count({
          where: {
            ...whereClause,
            status: "pending_first_approval",
          },
        }),
        first_approved: await Expense.count({
          where: {
            ...whereClause,
            status: "first_approved",
          },
        }),
        first_declined: await Expense.count({
          where: {
            ...whereClause,
            status: "first_declined",
          },
        }),
        pending_second_approval: await Expense.count({
          where: {
            ...whereClause,
            status: "pending_second_approval",
          },
        }),
      },

      // Total amounts
      amounts: {
        requested:
          (await Expense.sum("amount", {
            where: whereClause,
          })) || 0,
        approved:
          (await Expense.sum("second_approved_amount", {
            where: {
              ...whereClause,
              status: ["approved", "partially_approved"],
            },
          })) || 0,
        first_approved:
          (await Expense.sum("first_approved_amount", {
            where: {
              ...whereClause,
              status: [
                "first_approved",
                "pending_second_approval",
                "approved",
                "partially_approved",
                "declined",
              ],
            },
          })) || 0,
      },

      // Approval rates
      approval_rates: {
        first_level: 0, // Will calculate below
        second_level: 0, // Will calculate below
        overall: 0, // Will calculate below
      },

      // Average amounts
      averages: {
        requested: 0, // Will calculate below
        approved: 0, // Will calculate below
        approval_time_days: 0, // Will calculate below
      },
    };

    // Calculate approval rates
    const firstLevelTotal =
      summary.counts.first_approved + summary.counts.first_declined;
    if (firstLevelTotal > 0) {
      summary.approval_rates.first_level =
        (summary.counts.first_approved / firstLevelTotal) * 100;
    }

    const secondLevelTotal =
      summary.counts.approved +
      summary.counts.partially_approved +
      summary.counts.declined;
    if (secondLevelTotal > 0) {
      summary.approval_rates.second_level =
        ((summary.counts.approved + summary.counts.partially_approved) /
          secondLevelTotal) *
        100;
    }

    const overallTotal =
      summary.counts.approved +
      summary.counts.partially_approved +
      summary.counts.declined;
    if (overallTotal > 0) {
      summary.approval_rates.overall =
        ((summary.counts.approved + summary.counts.partially_approved) /
          overallTotal) *
        100;
    }

    // Calculate averages
    if (summary.counts.total > 0) {
      summary.averages.requested =
        summary.amounts.requested / summary.counts.total;
    }

    if (summary.counts.approved + summary.counts.partially_approved > 0) {
      summary.averages.approved =
        summary.amounts.approved /
        (summary.counts.approved + summary.counts.partially_approved);
    }

    // Calculate average approval time
    const approvedExpenses = await Expense.findAll({
      where: {
        ...whereClause,
        status: ["approved", "partially_approved"],
        second_approval_date: { [Op.ne]: null },
        created_at: { [Op.ne]: null },
      },
      attributes: ["created_at", "second_approval_date"],
    });

    if (approvedExpenses.length > 0) {
      let totalDays = 0;
      for (const expense of approvedExpenses) {
        const createdDate = new Date(expense.created_at);
        const approvedDate = new Date(expense.second_approval_date);
        const diffTime = Math.abs(approvedDate - createdDate);
        const diffDays = diffTime / (1000 * 60 * 60 * 24);
        totalDays += diffDays;
      }
      summary.averages.approval_time_days = totalDays / approvedExpenses.length;
    }

    return res.status(200).json({
      status: "success",
      data: summary,
    });
  } catch (error) {
    logger.error(`Error fetching expense summary analytics: ${error.message}`);
    next(error);
  }
};

/**
 * Get expense analytics by category
 * Provides breakdown of expenses by category
 */
exports.getExpensesByCategory = async (req, res, next) => {
  try {
    const { start_date, end_date, branch_id, user_id } = req.query;

    // Build where clause for filtering
    const whereClause = {};

    // Filter by date range if provided
    if (start_date && end_date) {
      whereClause.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)],
      };
    } else if (start_date) {
      whereClause.created_at = {
        [Op.gte]: new Date(start_date),
      };
    } else if (end_date) {
      whereClause.created_at = {
        [Op.lte]: new Date(end_date),
      };
    }

    // Filter by branch if provided
    if (branch_id) {
      whereClause.branch_id = branch_id;
    }

    // Filter by user if provided
    if (user_id) {
      whereClause.user_id = user_id;
    }

    // Branch-level roles can only see expenses from their branch
    if (
      req.user.role_name === "branch_admin" ||
      req.user.role_name === "operations_manager" ||
      req.user.role_name === "assistant_operations_manager"
    ) {
      whereClause.branch_id = req.user.branch_id;
    }

    // Get expenses grouped by category
    const expensesByCategory = await Expense.findAll({
      where: whereClause,
      attributes: [
        "category_id",
        [Sequelize.fn("COUNT", Sequelize.col("Expense.id")), "count"],
        [Sequelize.fn("SUM", Sequelize.col("amount")), "total_requested"],
        [
          Sequelize.fn("SUM", Sequelize.col("second_approved_amount")),
          "total_approved",
        ],
        [Sequelize.fn("AVG", Sequelize.col("amount")), "average_requested"],
      ],
      include: [
        {
          model: ExpenseCategory,
          as: "category",
          attributes: ["id", "name", "description"],
        },
      ],
      group: ["category_id", "category.id"],
      order: [[Sequelize.fn("SUM", Sequelize.col("amount")), "DESC"]],
    });

    // Format the response
    const formattedData = expensesByCategory.map((item) => ({
      category_id: item.category_id,
      category_name: item.category ? item.category.name : "Uncategorized",
      category_description: item.category ? item.category.description : "",
      count: parseInt(item.dataValues.count, 10),
      total_requested: parseFloat(item.dataValues.total_requested) || 0,
      total_approved: parseFloat(item.dataValues.total_approved) || 0,
      average_requested: parseFloat(item.dataValues.average_requested) || 0,
      approval_rate:
        parseFloat(item.dataValues.total_approved) > 0 &&
        parseFloat(item.dataValues.total_requested) > 0
          ? (parseFloat(item.dataValues.total_approved) /
              parseFloat(item.dataValues.total_requested)) *
            100
          : 0,
    }));

    return res.status(200).json({
      status: "success",
      count: formattedData.length,
      data: formattedData,
    });
  } catch (error) {
    logger.error(`Error fetching expenses by category: ${error.message}`);
    next(error);
  }
};

/**
 * Get expense trends over time
 * Provides time-based analysis of expenses
 */
exports.getExpenseTrends = async (req, res, next) => {
  try {
    const {
      start_date,
      end_date,
      branch_id,
      category_id,
      user_id,
      interval = "day",
    } = req.query;

    // Validate interval
    if (!["day", "week", "month"].includes(interval)) {
      return next(
        new AppError("Invalid interval. Must be one of: day, week, month", 400)
      );
    }

    // Build where clause for filtering
    const whereClause = {};

    // Filter by date range if provided
    if (start_date && end_date) {
      whereClause.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)],
      };
    } else if (start_date) {
      whereClause.created_at = {
        [Op.gte]: new Date(start_date),
      };
    } else if (end_date) {
      whereClause.created_at = {
        [Op.lte]: new Date(end_date),
      };
    } else {
      // Default to last 30 days if no date range provided
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);
      whereClause.created_at = {
        [Op.between]: [startDate, endDate],
      };
    }

    // Filter by branch if provided
    if (branch_id) {
      whereClause.branch_id = branch_id;
    }

    // Filter by category if provided
    if (category_id) {
      whereClause.category_id = category_id;
    }

    // Filter by user if provided
    if (user_id) {
      whereClause.user_id = user_id;
    }

    // Branch-level roles can only see expenses from their branch
    if (
      req.user.role_name === "branch_admin" ||
      req.user.role_name === "operations_manager" ||
      req.user.role_name === "assistant_operations_manager"
    ) {
      whereClause.branch_id = req.user.branch_id;
    }

    // Define the date format based on the interval
    let dateFormat;
    switch (interval) {
      case "day":
        dateFormat = "%Y-%m-%d";
        break;
      case "week":
        dateFormat = "%Y-%u"; // ISO week number
        break;
      case "month":
        dateFormat = "%Y-%m";
        break;
    }

    // Get expenses grouped by time interval
    const expenseTrends = await Expense.findAll({
      where: whereClause,
      attributes: [
        [
          Sequelize.fn("DATE_FORMAT", Sequelize.col("created_at"), dateFormat),
          "date",
        ],
        [Sequelize.fn("COUNT", Sequelize.col("Expense.id")), "count"],
        [Sequelize.fn("SUM", Sequelize.col("amount")), "total_requested"],
        [
          Sequelize.fn("SUM", Sequelize.col("second_approved_amount")),
          "total_approved",
        ],
        [Sequelize.fn("AVG", Sequelize.col("amount")), "average_requested"],
      ],
      group: [
        Sequelize.fn("DATE_FORMAT", Sequelize.col("created_at"), dateFormat),
      ],
      order: [
        [
          Sequelize.fn("DATE_FORMAT", Sequelize.col("created_at"), dateFormat),
          "ASC",
        ],
      ],
    });

    // Format the response
    const formattedData = expenseTrends.map((item) => ({
      date: item.dataValues.date,
      count: parseInt(item.dataValues.count, 10),
      total_requested: parseFloat(item.dataValues.total_requested) || 0,
      total_approved: parseFloat(item.dataValues.total_approved) || 0,
      average_requested: parseFloat(item.dataValues.average_requested) || 0,
      approval_rate:
        parseFloat(item.dataValues.total_approved) > 0 &&
        parseFloat(item.dataValues.total_requested) > 0
          ? (parseFloat(item.dataValues.total_approved) /
              parseFloat(item.dataValues.total_requested)) *
            100
          : 0,
    }));

    return res.status(200).json({
      status: "success",
      interval,
      count: formattedData.length,
      data: formattedData,
    });
  } catch (error) {
    logger.error(`Error fetching expense trends: ${error.message}`);
    next(error);
  }
};

/**
 * Get expense analytics by branch
 * Provides breakdown of expenses by branch
 */
exports.getExpensesByBranch = async (req, res, next) => {
  try {
    const { start_date, end_date, category_id, user_id } = req.query;

    // Build where clause for filtering
    const whereClause = {};

    // Filter by date range if provided
    if (start_date && end_date) {
      whereClause.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)],
      };
    } else if (start_date) {
      whereClause.created_at = {
        [Op.gte]: new Date(start_date),
      };
    } else if (end_date) {
      whereClause.created_at = {
        [Op.lte]: new Date(end_date),
      };
    }

    // Filter by category if provided
    if (category_id) {
      whereClause.category_id = category_id;
    }

    // Filter by user if provided
    if (user_id) {
      whereClause.user_id = user_id;
    }

    // Branch-level roles can only see expenses from their branch
    if (
      req.user.role_name === "branch_admin" ||
      req.user.role_name === "operations_manager" ||
      req.user.role_name === "assistant_operations_manager"
    ) {
      whereClause.branch_id = req.user.branch_id;
    }

    // Get expenses grouped by branch
    const expensesByBranch = await Expense.findAll({
      where: whereClause,
      attributes: [
        "branch_id",
        [Sequelize.fn("COUNT", Sequelize.col("Expense.id")), "count"],
        [Sequelize.fn("SUM", Sequelize.col("amount")), "total_requested"],
        [
          Sequelize.fn("SUM", Sequelize.col("second_approved_amount")),
          "total_approved",
        ],
        [Sequelize.fn("AVG", Sequelize.col("amount")), "average_requested"],
      ],
      include: [
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "name", "location"],
          include: [
            {
              model: Region,
              as: "region",
              attributes: ["id", "name"],
            },
          ],
        },
      ],
      group: ["branch_id", "branch.id", "branch.region.id"],
      order: [[Sequelize.fn("SUM", Sequelize.col("amount")), "DESC"]],
    });

    // Format the response
    const formattedData = expensesByBranch.map((item) => ({
      branch_id: item.branch_id,
      branch_name: item.branch ? item.branch.name : "Unknown Branch",
      branch_location: item.branch ? item.branch.location : "",
      region_id:
        item.branch && item.branch.region ? item.branch.region.id : null,
      region_name:
        item.branch && item.branch.region
          ? item.branch.region.name
          : "Unknown Region",
      count: parseInt(item.dataValues.count, 10),
      total_requested: parseFloat(item.dataValues.total_requested) || 0,
      total_approved: parseFloat(item.dataValues.total_approved) || 0,
      average_requested: parseFloat(item.dataValues.average_requested) || 0,
      approval_rate:
        parseFloat(item.dataValues.total_approved) > 0 &&
        parseFloat(item.dataValues.total_requested) > 0
          ? (parseFloat(item.dataValues.total_approved) /
              parseFloat(item.dataValues.total_requested)) *
            100
          : 0,
    }));

    return res.status(200).json({
      status: "success",
      count: formattedData.length,
      data: formattedData,
    });
  } catch (error) {
    logger.error(`Error fetching expenses by branch: ${error.message}`);
    next(error);
  }
};

/**
 * Get expense analytics by user
 * Provides breakdown of expenses by user
 */
exports.getExpensesByUser = async (req, res, next) => {
  try {
    const { start_date, end_date, branch_id, category_id } = req.query;

    // Build where clause for filtering
    const whereClause = {};

    // Filter by date range if provided
    if (start_date && end_date) {
      whereClause.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)],
      };
    } else if (start_date) {
      whereClause.created_at = {
        [Op.gte]: new Date(start_date),
      };
    } else if (end_date) {
      whereClause.created_at = {
        [Op.lte]: new Date(end_date),
      };
    }

    // Filter by branch if provided
    if (branch_id) {
      whereClause.branch_id = branch_id;
    }

    // Filter by category if provided
    if (category_id) {
      whereClause.category_id = category_id;
    }

    // Branch-level roles can only see expenses from their branch
    if (
      req.user.role_name === "branch_admin" ||
      req.user.role_name === "operations_manager" ||
      req.user.role_name === "assistant_operations_manager"
    ) {
      whereClause.branch_id = req.user.branch_id;
    }

    // Get expenses grouped by user
    const expensesByUser = await Expense.findAll({
      where: whereClause,
      attributes: [
        "user_id",
        [Sequelize.fn("COUNT", Sequelize.col("Expense.id")), "count"],
        [Sequelize.fn("SUM", Sequelize.col("amount")), "total_requested"],
        [
          Sequelize.fn("SUM", Sequelize.col("second_approved_amount")),
          "total_approved",
        ],
        [Sequelize.fn("AVG", Sequelize.col("amount")), "average_requested"],
      ],
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email", "role_id"],
        },
      ],
      group: ["user_id", "user.id"],
      order: [[Sequelize.fn("SUM", Sequelize.col("amount")), "DESC"]],
    });

    // Format the response
    const formattedData = expensesByUser.map((item) => ({
      user_id: item.user_id,
      user_name: item.user ? item.user.name : "Unknown User",
      user_email: item.user ? item.user.email : "",
      count: parseInt(item.dataValues.count, 10),
      total_requested: parseFloat(item.dataValues.total_requested) || 0,
      total_approved: parseFloat(item.dataValues.total_approved) || 0,
      average_requested: parseFloat(item.dataValues.average_requested) || 0,
      approval_rate:
        parseFloat(item.dataValues.total_approved) > 0 &&
        parseFloat(item.dataValues.total_requested) > 0
          ? (parseFloat(item.dataValues.total_approved) /
              parseFloat(item.dataValues.total_requested)) *
            100
          : 0,
    }));

    return res.status(200).json({
      status: "success",
      count: formattedData.length,
      data: formattedData,
    });
  } catch (error) {
    logger.error(`Error fetching expenses by user: ${error.message}`);
    next(error);
  }
};

/**
 * Generate detailed expense report
 * @param {Object} workbook - ExcelJS workbook
 * @param {Object} whereClause - Where clause for filtering expenses
 */
const generateDetailedReport = async (workbook, whereClause) => {
  // Add a worksheet for detailed expense data
  const detailedSheet = workbook.addWorksheet("Detailed Expenses");
  detailedSheet.columns = [
    { header: "ID", key: "id", width: 10 },
    { header: "Description", key: "description", width: 40 },
    { header: "Category", key: "category_name", width: 20 },
    { header: "Branch", key: "branch_name", width: 20 },
    { header: "Region", key: "region_name", width: 20 },
    { header: "User", key: "user_name", width: 20 },
    { header: "Amount", key: "amount", width: 15 },
    {
      header: "First Approved Amount",
      key: "first_approved_amount",
      width: 20,
    },
    {
      header: "Second Approved Amount",
      key: "second_approved_amount",
      width: 20,
    },
    { header: "Status", key: "status", width: 15 },
    { header: "Created At", key: "created_at", width: 20 },
    { header: "First Approval Date", key: "first_approval_date", width: 20 },
    { header: "Second Approval Date", key: "second_approval_date", width: 20 },
    { header: "Has Receipt", key: "has_receipt", width: 15 },
    { header: "Has Document", key: "has_document", width: 15 },
    { header: "Shop Required", key: "shop_required", width: 15 },
  ];

  // Get detailed expense data
  const expenses = await Expense.findAll({
    where: whereClause,
    include: [
      {
        model: ExpenseCategory,
        as: "category",
        attributes: ["id", "name"],
      },
      {
        model: Branch,
        as: "branch",
        attributes: ["id", "name", "location", "region_id"],
        include: [
          {
            model: Region,
            attributes: ["id", "name", "code"],
            required: false,
          },
        ],
      },
      {
        model: User,
        as: "user",
        attributes: ["id", "name", "email"],
      },
    ],
    order: [["created_at", "DESC"]],
  });

  // Add expense data to the worksheet
  expenses.forEach((expense) => {
    detailedSheet.addRow({
      id: expense.id,
      description: expense.description,
      category_name: expense.category ? expense.category.name : "Uncategorized",
      branch_name: expense.branch ? expense.branch.name : "Unknown Branch",
      region_name: expense.branch?.Region?.name || "Unknown Region",
      user_name: expense.user ? expense.user.name : "Unknown User",
      amount: expense.amount,
      first_approved_amount: expense.first_approved_amount,
      second_approved_amount: expense.second_approved_amount,
      status: expense.status,
      created_at: expense.created_at,
      first_approval_date: expense.first_approval_date,
      second_approval_date: expense.second_approval_date,
      has_receipt: expense.receipt_image ? "Yes" : "No",
      has_document: expense.document_image ? "Yes" : "No",
      shop_required: expense.shop_required ? "Yes" : "No",
    });
  });

  // Style the worksheet
  detailedSheet.getRow(1).font = { bold: true };
  detailedSheet.autoFilter = {
    from: { row: 1, column: 1 },
    to: { row: 1, column: 16 },
  };
};

/**
 * Generate summary expense report
 * @param {Object} workbook - ExcelJS workbook
 * @param {Object} whereClause - Where clause for filtering expenses
 */
const generateSummaryReport = async (workbook, whereClause) => {
  // Add a worksheet for summary data
  const summarySheet = workbook.addWorksheet("Summary");
  summarySheet.columns = [
    { header: "Metric", key: "metric", width: 30 },
    { header: "Value", key: "value", width: 20 },
  ];

  // Calculate summary statistics
  const totalCount = await Expense.count({ where: whereClause });
  const pendingCount = await Expense.count({
    where: { ...whereClause, status: "pending" },
  });
  const approvedCount = await Expense.count({
    where: { ...whereClause, status: "approved" },
  });
  const partiallyApprovedCount = await Expense.count({
    where: { ...whereClause, status: "partially_approved" },
  });
  const declinedCount = await Expense.count({
    where: { ...whereClause, status: "declined" },
  });

  const totalRequested =
    (await Expense.sum("amount", { where: whereClause })) || 0;
  const totalApproved =
    (await Expense.sum("second_approved_amount", {
      where: {
        ...whereClause,
        status: ["approved", "partially_approved"],
      },
    })) || 0;

  // Add summary data
  summarySheet.addRow({ metric: "Total Expenses", value: totalCount });
  summarySheet.addRow({ metric: "Pending Expenses", value: pendingCount });
  summarySheet.addRow({ metric: "Approved Expenses", value: approvedCount });
  summarySheet.addRow({
    metric: "Partially Approved Expenses",
    value: partiallyApprovedCount,
  });
  summarySheet.addRow({ metric: "Declined Expenses", value: declinedCount });
  summarySheet.addRow({
    metric: "Total Requested Amount",
    value: totalRequested,
  });
  summarySheet.addRow({
    metric: "Total Approved Amount",
    value: totalApproved,
  });

  // Calculate approval rate
  const approvalRate =
    totalRequested > 0 ? (totalApproved / totalRequested) * 100 : 0;
  summarySheet.addRow({
    metric: "Approval Rate",
    value: `${approvalRate.toFixed(2)}%`,
  });

  // Style the worksheet
  summarySheet.getColumn("metric").font = { bold: true };
  summarySheet.getRow(1).font = { bold: true };
};

/**
 * Generate category-based expense report
 * @param {Object} workbook - ExcelJS workbook
 * @param {Object} whereClause - Where clause for filtering expenses
 */
const generateCategoryReport = async (workbook, whereClause) => {
  // Add a worksheet for category data
  const categorySheet = workbook.addWorksheet("By Category");
  categorySheet.columns = [
    { header: "Category ID", key: "category_id", width: 15 },
    { header: "Category Name", key: "category_name", width: 30 },
    { header: "Count", key: "count", width: 10 },
    { header: "Total Requested", key: "total_requested", width: 20 },
    { header: "Total Approved", key: "total_approved", width: 20 },
    { header: "Average Requested", key: "average_requested", width: 20 },
    { header: "Approval Rate", key: "approval_rate", width: 15 },
  ];

  // Get expenses grouped by category
  const expensesByCategory = await Expense.findAll({
    where: whereClause,
    attributes: [
      "category_id",
      [Sequelize.fn("COUNT", Sequelize.col("Expense.id")), "count"],
      [Sequelize.fn("SUM", Sequelize.col("amount")), "total_requested"],
      [
        Sequelize.fn("SUM", Sequelize.col("second_approved_amount")),
        "total_approved",
      ],
      [Sequelize.fn("AVG", Sequelize.col("amount")), "average_requested"],
    ],
    include: [
      {
        model: ExpenseCategory,
        as: "category",
        attributes: ["id", "name", "description"],
      },
    ],
    group: ["category_id", "category.id"],
    order: [[Sequelize.fn("SUM", Sequelize.col("amount")), "DESC"]],
  });

  // Add category data
  expensesByCategory.forEach((item) => {
    const totalRequested = parseFloat(item.dataValues.total_requested) || 0;
    const totalApproved = parseFloat(item.dataValues.total_approved) || 0;
    const approvalRate =
      totalRequested > 0 ? (totalApproved / totalRequested) * 100 : 0;

    categorySheet.addRow({
      category_id: item.category_id,
      category_name: item.category ? item.category.name : "Uncategorized",
      count: parseInt(item.dataValues.count, 10),
      total_requested: totalRequested,
      total_approved: totalApproved,
      average_requested: parseFloat(item.dataValues.average_requested) || 0,
      approval_rate: `${approvalRate.toFixed(2)}%`,
    });
  });

  // Style the worksheet
  categorySheet.getRow(1).font = { bold: true };
  categorySheet.autoFilter = {
    from: { row: 1, column: 1 },
    to: { row: 1, column: 7 },
  };
};

/**
 * Generate branch-based expense report
 * @param {Object} workbook - ExcelJS workbook
 * @param {Object} whereClause - Where clause for filtering expenses
 */
const generateBranchReport = async (workbook, whereClause) => {
  // Add a worksheet for branch data
  const branchSheet = workbook.addWorksheet("By Branch");
  branchSheet.columns = [
    { header: "Branch ID", key: "branch_id", width: 15 },
    { header: "Branch Name", key: "branch_name", width: 30 },
    { header: "Location", key: "branch_location", width: 30 },
    { header: "Region", key: "region_name", width: 20 },
    { header: "Count", key: "count", width: 10 },
    { header: "Total Requested", key: "total_requested", width: 20 },
    { header: "Total Approved", key: "total_approved", width: 20 },
    { header: "Average Requested", key: "average_requested", width: 20 },
    { header: "Approval Rate", key: "approval_rate", width: 15 },
  ];

  // Get expenses grouped by branch
  const expensesByBranch = await Expense.findAll({
    where: whereClause,
    attributes: [
      "branch_id",
      [Sequelize.fn("COUNT", Sequelize.col("Expense.id")), "count"],
      [Sequelize.fn("SUM", Sequelize.col("amount")), "total_requested"],
      [
        Sequelize.fn("SUM", Sequelize.col("second_approved_amount")),
        "total_approved",
      ],
      [Sequelize.fn("AVG", Sequelize.col("amount")), "average_requested"],
    ],
    include: [
      {
        model: Branch,
        as: "branch",
        attributes: ["id", "name", "location"],
        include: [
          {
            model: Region,
            as: "region",
            attributes: ["id", "name"],
          },
        ],
      },
    ],
    group: ["branch_id", "branch.id", "branch.region.id"],
    order: [[Sequelize.fn("SUM", Sequelize.col("amount")), "DESC"]],
  });

  // Add branch data
  expensesByBranch.forEach((item) => {
    const totalRequested = parseFloat(item.dataValues.total_requested) || 0;
    const totalApproved = parseFloat(item.dataValues.total_approved) || 0;
    const approvalRate =
      totalRequested > 0 ? (totalApproved / totalRequested) * 100 : 0;

    branchSheet.addRow({
      branch_id: item.branch_id,
      branch_name: item.branch ? item.branch.name : "Unknown Branch",
      branch_location: item.branch ? item.branch.location : "",
      region_name:
        item.branch && item.branch.region
          ? item.branch.region.name
          : "Unknown Region",
      count: parseInt(item.dataValues.count, 10),
      total_requested: totalRequested,
      total_approved: totalApproved,
      average_requested: parseFloat(item.dataValues.average_requested) || 0,
      approval_rate: `${approvalRate.toFixed(2)}%`,
    });
  });

  // Style the worksheet
  branchSheet.getRow(1).font = { bold: true };
  branchSheet.autoFilter = {
    from: { row: 1, column: 1 },
    to: { row: 1, column: 9 },
  };
};

/**
 * Generate user-based expense report
 * @param {Object} workbook - ExcelJS workbook
 * @param {Object} whereClause - Where clause for filtering expenses
 */
const generateUserReport = async (workbook, whereClause) => {
  // Add a worksheet for user data
  const userSheet = workbook.addWorksheet("By User");
  userSheet.columns = [
    { header: "User ID", key: "user_id", width: 15 },
    { header: "User Name", key: "user_name", width: 30 },
    { header: "Email", key: "user_email", width: 40 },
    { header: "Count", key: "count", width: 10 },
    { header: "Total Requested", key: "total_requested", width: 20 },
    { header: "Total Approved", key: "total_approved", width: 20 },
    { header: "Average Requested", key: "average_requested", width: 20 },
    { header: "Approval Rate", key: "approval_rate", width: 15 },
  ];

  // Get expenses grouped by user
  const expensesByUser = await Expense.findAll({
    where: whereClause,
    attributes: [
      "user_id",
      [Sequelize.fn("COUNT", Sequelize.col("Expense.id")), "count"],
      [Sequelize.fn("SUM", Sequelize.col("amount")), "total_requested"],
      [
        Sequelize.fn("SUM", Sequelize.col("second_approved_amount")),
        "total_approved",
      ],
      [Sequelize.fn("AVG", Sequelize.col("amount")), "average_requested"],
    ],
    include: [
      {
        model: User,
        as: "user",
        attributes: ["id", "name", "email", "role_id"],
      },
    ],
    group: ["user_id", "user.id"],
    order: [[Sequelize.fn("SUM", Sequelize.col("amount")), "DESC"]],
  });

  // Add user data
  expensesByUser.forEach((item) => {
    const totalRequested = parseFloat(item.dataValues.total_requested) || 0;
    const totalApproved = parseFloat(item.dataValues.total_approved) || 0;
    const approvalRate =
      totalRequested > 0 ? (totalApproved / totalRequested) * 100 : 0;

    userSheet.addRow({
      user_id: item.user_id,
      user_name: item.user ? item.user.name : "Unknown User",
      user_email: item.user ? item.user.email : "",
      count: parseInt(item.dataValues.count, 10),
      total_requested: totalRequested,
      total_approved: totalApproved,
      average_requested: parseFloat(item.dataValues.average_requested) || 0,
      approval_rate: `${approvalRate.toFixed(2)}%`,
    });
  });

  // Style the worksheet
  userSheet.getRow(1).font = { bold: true };
  userSheet.autoFilter = {
    from: { row: 1, column: 1 },
    to: { row: 1, column: 8 },
  };
};

exports.exportExpenses = async (req, res, next) => {
  try {
    const {
      start_date,
      end_date,
      branch_id,
      region_id,
      category_id,
      user_id,
      status,
      report_type = "detailed", // Options: detailed, summary, by_category, by_branch, by_user
    } = req.query;

    // Build where clause for filtering
    const whereClause = {};

    // Filter by date range if provided
    if (start_date && end_date) {
      whereClause.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)],
      };
    } else if (start_date) {
      whereClause.created_at = {
        [Op.gte]: new Date(start_date),
      };
    } else if (end_date) {
      whereClause.created_at = {
        [Op.lte]: new Date(end_date),
      };
    }

    // Filter by branch if provided
    if (branch_id) {
      whereClause.branch_id = branch_id;
    }

    // Handle region filtering - get all branches in the specified region
    if (region_id) {
      const branchesInRegion = await Branch.findAll({
        where: { region_id, deleted_at: null },
        attributes: ["id"],
      });

      const branchIds = branchesInRegion.map((branch) => branch.id);

      if (branchIds.length > 0) {
        // Remove any existing branch_id filter
        delete whereClause.branch_id;

        // Add the branch_id IN clause
        whereClause.branch_id = {
          [Op.in]: branchIds,
        };
      }
    }

    // Filter by category if provided
    if (category_id) {
      whereClause.category_id = category_id;
    }

    // Filter by user if provided
    if (user_id) {
      whereClause.user_id = user_id;
    }

    // Filter by status if provided
    if (status) {
      whereClause.status = status;
    }

    // Branch-level roles can only see expenses from their branch
    if (
      req.user.role_name === "branch_admin" ||
      req.user.role_name === "operations_manager" ||
      req.user.role_name === "assistant_operations_manager"
    ) {
      whereClause.branch_id = req.user.branch_id;
    }

    // Create a new workbook
    const workbook = new ExcelJS.Workbook();
    workbook.creator = "Expense Analytics System";
    workbook.lastModifiedBy = req.user.name;
    workbook.created = new Date();
    workbook.modified = new Date();

    // Add a title worksheet
    const titleSheet = workbook.addWorksheet("Report Info");
    titleSheet.columns = [
      { header: "Property", key: "property", width: 30 },
      { header: "Value", key: "value", width: 50 },
    ];

    // Add report metadata
    titleSheet.addRow({
      property: "Report Type",
      value:
        report_type.charAt(0).toUpperCase() +
        report_type.slice(1) +
        " Expense Report",
    });
    titleSheet.addRow({ property: "Generated By", value: req.user.name });
    titleSheet.addRow({
      property: "Generated On",
      value: moment().format("YYYY-MM-DD HH:mm:ss"),
    });
    titleSheet.addRow({
      property: "Date Range",
      value:
        start_date && end_date ? `${start_date} to ${end_date}` : "All Time",
    });

    if (branch_id) {
      const branch = await Branch.findByPk(branch_id);
      titleSheet.addRow({
        property: "Branch",
        value: branch ? branch.name : branch_id,
      });
    }

    if (category_id) {
      const category = await ExpenseCategory.findByPk(category_id);
      titleSheet.addRow({
        property: "Category",
        value: category ? category.name : category_id,
      });
    }

    if (user_id) {
      const user = await User.findByPk(user_id);
      titleSheet.addRow({
        property: "User",
        value: user ? user.name : user_id,
      });
    }

    if (status) {
      titleSheet.addRow({ property: "Status", value: status });
    }

    // Style the title sheet
    titleSheet.getColumn("property").font = { bold: true };
    titleSheet.getRow(1).font = { bold: true };

    // Generate the appropriate report based on report_type
    switch (report_type) {
      case "detailed":
        await generateDetailedReport(workbook, whereClause);
        break;
      case "summary":
        await generateSummaryReport(workbook, whereClause);
        break;
      case "by_category":
        await generateCategoryReport(workbook, whereClause);
        break;
      case "by_branch":
        await generateBranchReport(workbook, whereClause);
        break;
      case "by_user":
        await generateUserReport(workbook, whereClause);
        break;
      default:
        await generateDetailedReport(workbook, whereClause);
    }

    // Set response headers
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=expense-report-${report_type}-${moment().format("YYYY-MM-DD")}.xlsx`
    );

    // Write to response
    await workbook.xlsx.write(res);
    return res.end();
  } catch (error) {
    logger.error(`Error exporting expenses: ${error.message}`);
    next(error);
  }
};
