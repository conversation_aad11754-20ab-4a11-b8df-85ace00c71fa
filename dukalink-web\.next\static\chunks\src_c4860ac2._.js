(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/providers/search-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SearchProvider": (()=>SearchProvider),
    "useSearch": (()=>useSearch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
const SearchContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function SearchProvider({ children }) {
    _s();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const openSearch = ()=>setIsOpen(true);
    const closeSearch = ()=>setIsOpen(false);
    const toggleSearch = ()=>setIsOpen((prev)=>!prev);
    // Add keyboard shortcut (Ctrl+K or Cmd+K) to open search
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SearchProvider.useEffect": ()=>{
            const handleKeyDown = {
                "SearchProvider.useEffect.handleKeyDown": (e)=>{
                    if ((e.ctrlKey || e.metaKey) && e.key === "k") {
                        e.preventDefault();
                        toggleSearch();
                    }
                    // Close on escape key
                    if (e.key === "Escape" && isOpen) {
                        closeSearch();
                    }
                }
            }["SearchProvider.useEffect.handleKeyDown"];
            window.addEventListener("keydown", handleKeyDown);
            return ({
                "SearchProvider.useEffect": ()=>window.removeEventListener("keydown", handleKeyDown)
            })["SearchProvider.useEffect"];
        }
    }["SearchProvider.useEffect"], [
        isOpen
    ]);
    // Close search when route changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SearchProvider.useEffect": ()=>{
            const handleRouteChange = {
                "SearchProvider.useEffect.handleRouteChange": ()=>{
                    closeSearch();
                }
            }["SearchProvider.useEffect.handleRouteChange"];
            // This is a simplified approach since Next.js App Router doesn't have a direct route change event
            // In a real implementation, you might want to use a more robust solution
            window.addEventListener("popstate", handleRouteChange);
            return ({
                "SearchProvider.useEffect": ()=>{
                    window.removeEventListener("popstate", handleRouteChange);
                }
            })["SearchProvider.useEffect"];
        }
    }["SearchProvider.useEffect"], [
        router
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SearchContext.Provider, {
        value: {
            isOpen,
            openSearch,
            closeSearch,
            toggleSearch
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/providers/search-provider.tsx",
        lineNumber: 57,
        columnNumber: 5
    }, this);
}
_s(SearchProvider, "ekFPP4L/yk6rucKDCOZ8PM+jyug=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = SearchProvider;
function useSearch() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(SearchContext);
    if (context === undefined) {
        throw new Error("useSearch must be used within a SearchProvider");
    }
    return context;
}
_s1(useSearch, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "SearchProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn),
    "formatCurrency": (()=>formatCurrency),
    "formatDate": (()=>formatDate),
    "formatDateForApi": (()=>formatDateForApi),
    "formatNumber": (()=>formatNumber)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.js [app-client] (ecmascript) <locals>");
;
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatDate(dateString, options = {
    year: "numeric",
    month: "short",
    day: "numeric"
}) {
    if (!dateString) return "";
    const date = new Date(dateString);
    // Check if the date is valid
    if (isNaN(date.getTime())) {
        return "Invalid date";
    }
    return new Intl.DateTimeFormat("en-US", options).format(date);
}
function formatNumber(value, decimals = 2, defaultValue = '0.00') {
    if (value === undefined || value === null) {
        return defaultValue;
    }
    try {
        const numValue = typeof value === 'number' ? value : Number(value);
        if (isNaN(numValue)) {
            return defaultValue;
        }
        return numValue.toFixed(decimals);
    } catch (error) {
        console.error('Error formatting number:', error);
        return defaultValue;
    }
}
function formatCurrency(amount, currency = "KES") {
    // Handle null, undefined, or empty string
    if (amount === null || amount === undefined || amount === "") {
        return new Intl.NumberFormat("en-KE", {
            style: "currency",
            currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(0);
    }
    // Convert string to number if needed
    const numericAmount = typeof amount === "string" ? parseFloat(amount) : amount;
    // Check if the conversion resulted in a valid number
    if (isNaN(numericAmount)) {
        return new Intl.NumberFormat("en-KE", {
            style: "currency",
            currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(0);
    }
    return new Intl.NumberFormat("en-KE", {
        style: "currency",
        currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(numericAmount);
}
function formatDateForApi(date) {
    if (!date) return "";
    const d = typeof date === "string" ? new Date(date) : date;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(d, "yyyy-MM-dd");
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/dialog.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Dialog": (()=>Dialog),
    "DialogClose": (()=>DialogClose),
    "DialogContent": (()=>DialogContent),
    "DialogDescription": (()=>DialogDescription),
    "DialogFooter": (()=>DialogFooter),
    "DialogHeader": (()=>DialogHeader),
    "DialogOverlay": (()=>DialogOverlay),
    "DialogPortal": (()=>DialogPortal),
    "DialogTitle": (()=>DialogTitle),
    "DialogTrigger": (()=>DialogTrigger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-dialog/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as XIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
function Dialog({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        "data-slot": "dialog",
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.tsx",
        lineNumber: 12,
        columnNumber: 10
    }, this);
}
_c = Dialog;
function DialogTrigger({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Trigger"], {
        "data-slot": "dialog-trigger",
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.tsx",
        lineNumber: 18,
        columnNumber: 10
    }, this);
}
_c1 = DialogTrigger;
function DialogPortal({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Portal"], {
        "data-slot": "dialog-portal",
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.tsx",
        lineNumber: 24,
        columnNumber: 10
    }, this);
}
_c2 = DialogPortal;
function DialogClose({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Close"], {
        "data-slot": "dialog-close",
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.tsx",
        lineNumber: 30,
        columnNumber: 10
    }, this);
}
_c3 = DialogClose;
function DialogOverlay({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Overlay"], {
        "data-slot": "dialog-overlay",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
}
_c4 = DialogOverlay;
function DialogContent({ className, children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DialogPortal, {
        "data-slot": "dialog-portal",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DialogOverlay, {}, void 0, false, {
                fileName: "[project]/src/components/ui/dialog.tsx",
                lineNumber: 56,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Content"], {
                "data-slot": "dialog-content",
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg", className),
                ...props,
                children: [
                    children,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Close"], {
                        className: "ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XIcon$3e$__["XIcon"], {}, void 0, false, {
                                fileName: "[project]/src/components/ui/dialog.tsx",
                                lineNumber: 67,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "sr-only",
                                children: "Close"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/dialog.tsx",
                                lineNumber: 68,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/dialog.tsx",
                        lineNumber: 66,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/dialog.tsx",
                lineNumber: 57,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/dialog.tsx",
        lineNumber: 55,
        columnNumber: 5
    }, this);
}
_c5 = DialogContent;
function DialogHeader({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "dialog-header",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-col gap-2 text-center sm:text-left", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.tsx",
        lineNumber: 77,
        columnNumber: 5
    }, this);
}
_c6 = DialogHeader;
function DialogFooter({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "dialog-footer",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.tsx",
        lineNumber: 87,
        columnNumber: 5
    }, this);
}
_c7 = DialogFooter;
function DialogTitle({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Title"], {
        "data-slot": "dialog-title",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-lg leading-none font-semibold", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.tsx",
        lineNumber: 103,
        columnNumber: 5
    }, this);
}
_c8 = DialogTitle;
function DialogDescription({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Description"], {
        "data-slot": "dialog-description",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.tsx",
        lineNumber: 116,
        columnNumber: 5
    }, this);
}
_c9 = DialogDescription;
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;
__turbopack_context__.k.register(_c, "Dialog");
__turbopack_context__.k.register(_c1, "DialogTrigger");
__turbopack_context__.k.register(_c2, "DialogPortal");
__turbopack_context__.k.register(_c3, "DialogClose");
__turbopack_context__.k.register(_c4, "DialogOverlay");
__turbopack_context__.k.register(_c5, "DialogContent");
__turbopack_context__.k.register(_c6, "DialogHeader");
__turbopack_context__.k.register(_c7, "DialogFooter");
__turbopack_context__.k.register(_c8, "DialogTitle");
__turbopack_context__.k.register(_c9, "DialogDescription");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/cookies.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "deleteCookie": (()=>deleteCookie),
    "getCookie": (()=>getCookie),
    "setCookie": (()=>setCookie),
    "useCookie": (()=>useCookie)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
"use client";
;
function getCookie(name) {
    if (typeof document === "undefined") {
        console.log(`getCookie(${name}) called on server side, returning null`);
        return null; // Return null on server side
    }
    console.log(`Getting cookie: ${name}, all cookies:`, document.cookie);
    // Try to get from cookie first
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
        const cookieValue = parts.pop()?.split(";").shift() || null;
        console.log(`Cookie ${name} found with value: ${cookieValue ? "exists" : "null"}`);
        return cookieValue;
    }
    // If not found in cookie, try localStorage as fallback
    try {
        const localStorageValue = localStorage.getItem(name);
        if (localStorageValue) {
            console.log(`Cookie ${name} not found in cookies, but found in localStorage`);
            // If found in localStorage but not in cookies, restore the cookie
            setCookie(name, localStorageValue);
            return localStorageValue;
        }
    } catch (e) {
        console.warn("Failed to access localStorage:", e);
    }
    console.log(`Cookie ${name} not found in cookies or localStorage`);
    return null;
}
function setCookie(name, value, days = 30) {
    if (typeof document === "undefined") {
        console.log("setCookie called on server side, ignoring");
        return; // Do nothing on server side
    }
    const expires = new Date();
    expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
    // Build cookie string with appropriate attributes
    // Using path=/ to ensure the cookie is available across the entire site
    // Using SameSite=Lax to allow the cookie to be sent with same-site requests
    // Not using HttpOnly as we need to access the cookie from JavaScript
    let cookieString = `${name}=${encodeURIComponent(value)};expires=${expires.toUTCString()};path=/;SameSite=Lax;max-age=${days * 24 * 60 * 60}`;
    // Add Secure flag in production or if using HTTPS
    if (window.location.protocol === "https:") {
        cookieString += ";Secure";
    }
    document.cookie = cookieString;
    // Store in localStorage as a fallback
    try {
        localStorage.setItem(name, value);
    } catch (e) {
        console.warn("Failed to store token in localStorage:", e);
    }
    console.log(`Cookie set: ${name}=${value ? "value exists" : "empty"}, expires in ${days} days`);
    // Log cookies immediately after setting
    const allCookies = document.cookie;
    console.log("All cookies after setting:", allCookies);
    // Double-check if cookie was set correctly
    setTimeout(()=>{
        const checkCookie = getCookie(name);
        console.log(`Cookie check after setting ${name}:`, checkCookie ? "exists" : "missing");
    }, 100);
}
function deleteCookie(name) {
    if (typeof document === "undefined") {
        console.log(`deleteCookie(${name}) called on server side, ignoring`);
        return; // Do nothing on server side
    }
    console.log(`Deleting cookie: ${name}`);
    // Delete the cookie by setting an expired date
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;SameSite=Lax`;
    // Also try with different paths to ensure it's deleted
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;`;
    // Also remove from localStorage if it exists
    try {
        localStorage.removeItem(name);
    } catch (e) {
        console.warn("Failed to remove token from localStorage:", e);
    }
    // Log cookies immediately after deletion
    const allCookies = document.cookie;
    console.log("All cookies after deletion:", allCookies);
}
function useCookie(key, initialValue = null) {
    _s();
    const [storedValue, setStoredValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(initialValue);
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Initialize cookie value on client-side
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCookie.useEffect": ()=>{
            // Use a flag to ensure we only set initialized once
            let isMounted = true;
            const initializeCookie = {
                "useCookie.useEffect.initializeCookie": ()=>{
                    console.log(`useCookie(${key}) initializing...`);
                    try {
                        const item = getCookie(key);
                        if (item !== null && isMounted) {
                            console.log(`useCookie(${key}) found value, setting state`);
                            setStoredValue(item);
                        } else if (isMounted) {
                            console.log(`useCookie(${key}) no value found, using initialValue:`, initialValue);
                        }
                        if (isMounted) {
                            setIsInitialized(true);
                            console.log(`useCookie(${key}) initialized with value:`, item || initialValue);
                        }
                    } catch (error) {
                        console.error(`Error initializing cookie ${key}:`, error);
                        if (isMounted) {
                            // Even on error, we should set initialized to true to avoid getting stuck
                            setIsInitialized(true);
                        }
                    }
                }
            }["useCookie.useEffect.initializeCookie"];
            // Initialize immediately
            initializeCookie();
            // Set a backup timeout to ensure we don't get stuck in loading state
            const timeoutId = setTimeout({
                "useCookie.useEffect.timeoutId": ()=>{
                    if (!isInitialized && isMounted) {
                        console.warn(`useCookie(${key}) initialization timed out, forcing initialized state`);
                        setIsInitialized(true);
                    }
                }
            }["useCookie.useEffect.timeoutId"], 1000); // 1 second timeout as a safety
            return ({
                "useCookie.useEffect": ()=>{
                    isMounted = false;
                    clearTimeout(timeoutId);
                }
            })["useCookie.useEffect"];
        }
    }["useCookie.useEffect"], [
        key,
        initialValue,
        isInitialized
    ]);
    // Function to update cookie
    const setValue = (value)=>{
        console.log(`useCookie(${key}) setValue called with:`, value ? "value exists" : "null");
        setStoredValue(value);
        if (value === null) {
            deleteCookie(key);
        } else {
            setCookie(key, value);
        }
    };
    return {
        value: storedValue,
        setValue,
        isInitialized
    };
}
_s(useCookie, "uIJ64FGIxKf/cFUsDdEMe2ktjcY=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api-client.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cookies.ts [app-client] (ecmascript)");
"use client";
;
;
// Default redirect implementation - will be overridden by client components
let redirectCallback = (path)=>{
    console.warn("Redirect callback not set, attempted to redirect to:", path);
    // Use window.location as a fallback if we're in the browser
    if ("TURBOPACK compile-time truthy", 1) {
        window.location.href = path;
    }
};
// Default auth error callback - will be overridden by client components
let authErrorCallback = (message)=>{
    console.warn("Auth error callback not set, auth error:", message);
};
// Reference to the query client for cache invalidation
let queryClientRef = null;
// Use the environment variable for API URL
const API_URL = ("TURBOPACK compile-time value", "http://localhost:3000/api/v1");
class ApiClient {
    // Method to set a custom redirect callback
    setRedirectCallback(callback) {
        redirectCallback = callback;
    }
    // Method to set the query client reference
    setQueryClient(queryClient) {
        queryClientRef = queryClient;
    }
    // Method to set a custom auth error callback
    setAuthErrorCallback(callback) {
        authErrorCallback = callback;
    }
    // Method to handle authentication errors
    handleAuthError(errorMessage) {
        console.log("Handling auth error - clearing tokens and showing auth error dialog");
        // Get a user-friendly error message
        const message = errorMessage || "Your session has expired. Please log in again.";
        // Call the auth error callback with the message
        authErrorCallback(message);
    // We don't clear tokens or redirect here anymore
    // That will be handled by the auth error context when the user confirms the dialog
    }
    client;
    isRefreshing = false;
    refreshSubscribers = [];
    constructor(){
        this.client = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: API_URL,
            headers: {
                "Content-Type": "application/json"
            },
            timeout: 60000
        });
        this.setupInterceptors();
    }
    // Method to set auth token for requests
    setAuthToken(token) {
        if (token) {
            this.client.defaults.headers.common["Authorization"] = `Bearer ${token}`;
            // Also store in localStorage as a fallback
            try {
                localStorage.setItem("token", token);
            } catch (e) {
                console.warn("Failed to store token in localStorage:", e);
            }
        } else {
            delete this.client.defaults.headers.common["Authorization"];
            // Also remove from localStorage
            try {
                localStorage.removeItem("token");
            } catch (e) {
                console.warn("Failed to remove token from localStorage:", e);
            }
        }
    }
    setupInterceptors() {
        // Request interceptor
        this.client.interceptors.request.use(async (config)=>{
            // Debug log to check if Authorization header is set
            const hasAuthHeader = config.headers?.Authorization ? true : false;
            console.log(`API Request to ${config.url}: Auth header ${hasAuthHeader ? "present" : "missing"}`);
            // If no auth header is set but we have a token in localStorage, set it
            if (!hasAuthHeader && "object" !== "undefined") {
                const token = localStorage.getItem("token") || localStorage.getItem("accessToken");
                if (token && config.headers) {
                    console.log(`Setting missing auth header for ${config.url} from localStorage`);
                    config.headers.Authorization = `Bearer ${token}`;
                }
            }
            return config;
        }, (error)=>Promise.reject(error));
        // Response interceptor
        this.client.interceptors.response.use((response)=>response, async (error)=>{
            const originalRequest = error.config;
            // If unauthorized error and not retrying
            if (error.response?.status === 401 && !originalRequest._retry) {
                console.log("401 Unauthorized error detected:", error.config?.url);
                // Check if the error message indicates an invalid or expired token
                const errorData = error.response?.data;
                const isTokenError = errorData?.message === "Invalid or expired token" || errorData?.error === "Unauthorized" || errorData?.error === "Error";
                // If already refreshing, queue this request
                if (this.isRefreshing) {
                    console.log("Token refresh already in progress, queuing request");
                    return new Promise((resolve)=>{
                        this.refreshSubscribers.push((token)=>{
                            if (originalRequest.headers) {
                                originalRequest.headers.Authorization = `Bearer ${token}`;
                            }
                            resolve(this.client(originalRequest));
                        });
                    });
                }
                // Mark as retrying and refreshing
                originalRequest._retry = true;
                this.isRefreshing = true;
                // Try to get refresh token from cookies or localStorage
                const refreshToken = typeof document !== "undefined" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCookie"])("refreshToken") || localStorage.getItem("refreshToken") : null;
                if (refreshToken) {
                    console.log("Refresh token found, attempting to refresh access token");
                    try {
                        // Call the refresh token endpoint
                        const response = await this.client.post("/auth/refresh-token", {
                            refreshToken
                        });
                        const { accessToken, refreshToken: newRefreshToken } = response.data;
                        if (accessToken) {
                            console.log("Token refresh successful");
                            // Update tokens in cookies and localStorage
                            if (typeof document !== "undefined") {
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookie"])("accessToken", accessToken);
                                if (newRefreshToken) {
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookie"])("refreshToken", newRefreshToken);
                                }
                            }
                            // Update auth header
                            this.setAuthToken(accessToken);
                            // Execute all queued requests with new token
                            this.refreshSubscribers.forEach((callback)=>callback(accessToken));
                            this.refreshSubscribers = [];
                            // Reset refreshing flag
                            this.isRefreshing = false;
                            // Retry the original request
                            if (originalRequest.headers) {
                                originalRequest.headers.Authorization = `Bearer ${accessToken}`;
                            }
                            return this.client(originalRequest);
                        }
                    } catch (refreshError) {
                        console.error("Token refresh failed:", refreshError);
                    }
                }
                // If we get here, token refresh failed or no refresh token available
                this.isRefreshing = false;
                this.refreshSubscribers = [];
                // If it's a token error or refresh failed, handle auth error
                console.log("Token refresh failed or not possible, handling auth error");
                const errorMessage = errorData?.message || "Your session has expired. Please log in again.";
                this.handleAuthError(errorMessage);
                return Promise.reject(error);
            }
            // Handle other errors
            return Promise.reject(this.handleError(error));
        });
    }
    handleError(error) {
        // Customize error handling
        if (error.response) {
            // Server responded with error status
            const data = error.response.data;
            if (data.message) {
                error.message = data.message;
            }
            // Add custom error properties
            error.isApiError = true;
            error.statusCode = error.response.status;
        } else if (error.request) {
            // Request made but no response received
            error.message = "Network error. Please check your connection.";
            error.isNetworkError = true;
        }
        return error;
    }
    // Public methods
    async get(url, config) {
        try {
            console.log(`Making GET request to ${url}`);
            const response = await this.client.get(url, config);
            return response.data;
        } catch (error) {
            // Suppress specific tenant settings errors to avoid console noise
            if (url.includes("/tenants/") && url.includes("/settings") && error.response?.status === 404) {
                console.log(`Resource not found for ${url} - suppressing error`);
            } else {
                console.error(`Error in GET request to ${url}:`, error.message);
            }
            // Check if it's a connection error
            if (error.code === "ECONNREFUSED" || error.code === "ECONNABORTED" || error.message.includes("Network Error")) {
                console.warn("Connection error detected, trying direct backend API");
                // Try direct backend API as fallback
                try {
                    // Get the API URL from environment variable
                    const baseUrl = ("TURBOPACK compile-time value", "http://localhost:3000/api/v1");
                    // Get token from localStorage
                    const token = ("TURBOPACK compile-time truthy", 1) ? localStorage.getItem("token") || localStorage.getItem("accessToken") : ("TURBOPACK unreachable", undefined);
                    if (!token) {
                        throw new Error("No authentication token available");
                    }
                    // Make direct request to backend
                    const directResponse = await fetch(`${baseUrl}${url}`, {
                        headers: {
                            Authorization: `Bearer ${token}`,
                            "Content-Type": "application/json"
                        },
                        ...config
                    });
                    if (!directResponse.ok) {
                        const errorData = await directResponse.json();
                        throw new Error(errorData.message || `Backend API error: ${directResponse.status}`);
                    }
                    return await directResponse.json();
                } catch (fallbackError) {
                    console.error("Fallback request also failed:", fallbackError.message);
                    throw fallbackError;
                }
            }
            throw error;
        }
    }
    async post(url, data, config) {
        // Special handling for FormData (file uploads)
        if (data instanceof FormData) {
            console.log(`Posting FormData to ${url}`);
            // For FormData, explicitly set the Content-Type header to multipart/form-data
            // but let Axios handle the boundary
            const formDataConfig = {
                ...config,
                headers: {
                    ...config?.headers,
                    "Content-Type": "multipart/form-data"
                },
                // Increase timeout for file uploads
                timeout: config?.timeout || 180000,
                // Add onUploadProgress handler if not provided
                onUploadProgress: config?.onUploadProgress || ((progressEvent)=>{
                    const percentCompleted = Math.round(progressEvent.loaded * 100 / (progressEvent.total || 1));
                    console.log(`Upload progress: ${percentCompleted}%`);
                })
            };
            try {
                // Log the FormData contents for debugging
                console.log("FormData contents:");
                for (const pair of data.entries()){
                    if (pair[1] instanceof File) {
                        const file = pair[1];
                        console.log(`${pair[0]}: File(${file.name}, ${file.size} bytes, ${file.type})`);
                    } else {
                        console.log(`${pair[0]}: ${pair[1]}`);
                    }
                }
                console.log("Sending file upload request with config:", JSON.stringify({
                    url,
                    method: "POST",
                    timeout: formDataConfig.timeout,
                    headers: {
                        ...formDataConfig.headers
                    }
                }));
                // Use Axios for file uploads with proper CORS settings
                console.log("Using Axios for file upload with FormData");
                // Create a special config for file uploads
                const uploadConfig = {
                    ...formDataConfig,
                    // Don't set Content-Type header, let Axios set it with the boundary
                    headers: {
                        ...formDataConfig.headers,
                        "Content-Type": undefined
                    },
                    // Don't use withCredentials for cross-origin requests if you're using '*' in CORS
                    withCredentials: false
                };
                console.log("Upload config:", JSON.stringify({
                    url,
                    method: "POST",
                    timeout: uploadConfig.timeout,
                    withCredentials: uploadConfig.withCredentials,
                    headers: {
                        ...uploadConfig.headers
                    }
                }));
                const response = await this.client.post(url, data, uploadConfig);
                return response.data;
            } catch (error) {
                console.error(`Error posting FormData to ${url}:`, error.message);
                // Add more detailed error logging
                if (error.code === "ECONNABORTED") {
                    console.error("Request timed out. Consider increasing the timeout value.");
                } else if (error.response) {
                    console.error("Server responded with error:", error.response.status, error.response.data);
                } else if (error.request) {
                    console.error("No response received from server. Network issue or CORS problem.");
                }
                throw error;
            }
        } else {
            // Regular post request
            try {
                const response = await this.client.post(url, data, config);
                return response.data;
            } catch (error) {
                // Check for stock limit exceeded error
                if (error.message && error.message.includes("Assignment would exceed DSA user's stock limit")) {
                    // Extract the limit information from the error message
                    const match = error.message.match(/stock limit of (\d+) items\. Current total: (\d+), Requested: (\d+)/);
                    if (match) {
                        const [, limit, current, requested] = match;
                        throw new Error(`Stock limit exceeded: You cannot assign ${requested} more items. The DSA already has ${current} items assigned out of a limit of ${limit}.`);
                    } else {
                        throw new Error("Cannot assign more stock: The DSA user has reached their stock limit.");
                    }
                }
                // Check for transaction errors
                if (error.message && error.message.includes("Transaction cannot be rolled back")) {
                    console.error("Database transaction error detected. The operation could not be completed.");
                    throw new Error("The operation could not be completed due to a database error. Please try again later.");
                }
                // Add more detailed error logging for other types of errors
                if (error.code === "ECONNABORTED") {
                    console.error("Request timed out. Consider increasing the timeout value.");
                } else if (error.response) {
                    console.error("Server responded with error:", error.response.status, error.response.data);
                } else if (error.request) {
                    console.error("No response received from server. Network issue or CORS problem.");
                } else {
                    console.error(`Error posting to ${url}:`, error.message);
                }
                throw error;
            }
        }
    }
    // Helper method to get auth headers
    getAuthHeaders() {
        const token = localStorage.getItem("token");
        return token ? {
            Authorization: `Bearer ${token}`
        } : {};
    }
    async put(url, data, config) {
        try {
            const response = await this.client.put(url, data, config);
            return response.data;
        } catch (error) {
            // Check for stock limit exceeded error
            if (error.message && error.message.includes("Assignment would exceed DSA user's stock limit")) {
                // Extract the limit information from the error message
                const match = error.message.match(/stock limit of (\d+) items\. Current total: (\d+), Requested: (\d+)/);
                if (match) {
                    const [, limit, current, requested] = match;
                    throw new Error(`Stock limit exceeded: You cannot assign ${requested} more items. The DSA already has ${current} items assigned out of a limit of ${limit}.`);
                } else {
                    throw new Error("Cannot assign more stock: The DSA user has reached their stock limit.");
                }
            }
            // Check for transaction errors
            if (error.message && error.message.includes("Transaction cannot be rolled back")) {
                console.error("Database transaction error detected. The operation could not be completed.");
                throw new Error("The operation could not be completed due to a database error. Please try again later.");
            }
            console.error(`Error putting to ${url}:`, error.message);
            throw error;
        }
    }
    async delete(url, config) {
        try {
            const response = await this.client.delete(url, config);
            return response.data;
        } catch (error) {
            // Check for stock limit exceeded error
            if (error.message && error.message.includes("Assignment would exceed DSA user's stock limit")) {
                // Extract the limit information from the error message
                const match = error.message.match(/stock limit of (\d+) items\. Current total: (\d+), Requested: (\d+)/);
                if (match) {
                    const [, limit, current, requested] = match;
                    throw new Error(`Stock limit exceeded: You cannot assign ${requested} more items. The DSA already has ${current} items assigned out of a limit of ${limit}.`);
                } else {
                    throw new Error("Cannot assign more stock: The DSA user has reached their stock limit.");
                }
            }
            // Check for transaction errors
            if (error.message && error.message.includes("Transaction cannot be rolled back")) {
                console.error("Database transaction error detected. The operation could not be completed.");
                throw new Error("The operation could not be completed due to a database error. Please try again later.");
            }
            console.error(`Error deleting from ${url}:`, error.message);
            throw error;
        }
    }
    async patch(url, data, config) {
        try {
            const response = await this.client.patch(url, data, config);
            return response.data;
        } catch (error) {
            // Check for stock limit exceeded error
            if (error.message && error.message.includes("Assignment would exceed DSA user's stock limit")) {
                // Extract the limit information from the error message
                const match = error.message.match(/stock limit of (\d+) items\. Current total: (\d+), Requested: (\d+)/);
                if (match) {
                    const [, limit, current, requested] = match;
                    throw new Error(`Stock limit exceeded: You cannot assign ${requested} more items. The DSA already has ${current} items assigned out of a limit of ${limit}.`);
                } else {
                    throw new Error("Cannot assign more stock: The DSA user has reached their stock limit.");
                }
            }
            // Check for transaction errors
            if (error.message && error.message.includes("Transaction cannot be rolled back")) {
                console.error("Database transaction error detected. The operation could not be completed.");
                throw new Error("The operation could not be completed due to a database error. Please try again later.");
            }
            console.error(`Error patching to ${url}:`, error.message);
            throw error;
        }
    }
}
// Create singleton instance
const apiClient = new ApiClient();
const __TURBOPACK__default__export__ = apiClient;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/auth/api/auth-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "authService": (()=>authService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
;
const authService = {
    login: async (credentials)=>{
        console.log('Attempting login with credentials:', {
            email: credentials.email,
            password: '******'
        });
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/auth/login", credentials, {
                // Add a longer timeout specifically for login
                timeout: 60000
            });
            console.log('Login successful');
            // Tokens will be stored by the hook that calls this method
            // We don't directly interact with localStorage here to avoid hydration issues
            return response;
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    },
    refreshToken: async (refreshToken)=>{
        const request = {
            refreshToken
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/auth/refresh-token", request);
        // Tokens will be stored by the hook that calls this method
        // We don't directly interact with localStorage here to avoid hydration issues
        return response;
    },
    logout: async ()=>{
    // Tokens will be cleared by the hook that calls this method
    // We don't directly interact with localStorage here to avoid hydration issues
    },
    getCurrentUser: async ()=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/auth/me");
    },
    isAuthenticated: ()=>{
        // This should be called from client components only
        // For server components, default to not authenticated
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            // Use document.cookie instead of localStorage
            const cookies = document.cookie.split(";");
            const tokenCookie = cookies.find((cookie)=>cookie.trim().startsWith("accessToken="));
            return !!tokenCookie;
        } catch  {
            return false;
        }
    },
    forgotPassword: async (data)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/auth/forgot-password", data);
    },
    resetPassword: async (data)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/auth/reset-password", data);
    },
    updateProfile: async (data)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put("/auth/profile", data);
    },
    changePassword: async (data)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/auth/change-password", data);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/use-auth-tokens.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAuthTokens": (()=>useAuthTokens)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cookies.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$api$2f$auth$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/auth/api/auth-service.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
function useAuthTokens() {
    _s();
    const { value: accessToken, setValue: setAccessToken, isInitialized: isAccessTokenInitialized } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCookie"])("accessToken");
    const { value: refreshToken, setValue: setRefreshToken, isInitialized: isRefreshTokenInitialized } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCookie"])("refreshToken");
    const isInitialized = isAccessTokenInitialized && isRefreshTokenInitialized;
    const setTokens = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthTokens.useCallback[setTokens]": (access, refresh)=>{
            // Set in cookies
            setAccessToken(access);
            setRefreshToken(refresh);
            // Also set in localStorage as fallback
            if ("TURBOPACK compile-time truthy", 1) {
                try {
                    localStorage.setItem("accessToken", access);
                    localStorage.setItem("refreshToken", refresh);
                    localStorage.setItem("token", access); // For backward compatibility
                } catch (e) {
                    console.warn("Failed to store tokens in localStorage:", e);
                }
            }
            // Update API client auth header
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setAuthToken(access);
        }
    }["useAuthTokens.useCallback[setTokens]"], [
        setAccessToken,
        setRefreshToken
    ]);
    const clearTokens = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthTokens.useCallback[clearTokens]": ()=>{
            // Clear cookies
            setAccessToken(null);
            setRefreshToken(null);
            // Also clear localStorage
            if ("TURBOPACK compile-time truthy", 1) {
                try {
                    localStorage.removeItem("accessToken");
                    localStorage.removeItem("refreshToken");
                    localStorage.removeItem("token"); // For backward compatibility
                } catch (e) {
                    console.warn("Failed to remove tokens from localStorage:", e);
                }
            }
            // Clear API client auth header
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setAuthToken(null);
        }
    }["useAuthTokens.useCallback[clearTokens]"], [
        setAccessToken,
        setRefreshToken
    ]);
    // Function to refresh the token
    const refreshAccessToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthTokens.useCallback[refreshAccessToken]": async ()=>{
            // Try to get refresh token from cookie or localStorage
            const currentRefreshToken = refreshToken || (("TURBOPACK compile-time truthy", 1) ? localStorage.getItem("refreshToken") : ("TURBOPACK unreachable", undefined));
            if (!currentRefreshToken) {
                console.error("No refresh token available");
                return false;
            }
            try {
                console.log("Attempting to refresh token...");
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$api$2f$auth$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].refreshToken(currentRefreshToken);
                if (response && response.accessToken) {
                    console.log("Token refresh successful");
                    // Update tokens in cookies and localStorage
                    setTokens(response.accessToken, response.refreshToken || currentRefreshToken);
                    // Update auth header
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setAuthToken(response.accessToken);
                    // Also store in localStorage as fallback
                    if ("TURBOPACK compile-time truthy", 1) {
                        try {
                            localStorage.setItem("accessToken", response.accessToken);
                            if (response.refreshToken) {
                                localStorage.setItem("refreshToken", response.refreshToken);
                            }
                        } catch (e) {
                            console.warn("Failed to store tokens in localStorage:", e);
                        }
                    }
                    return true;
                }
                return false;
            } catch (error) {
                console.error("Failed to refresh token:", error);
                return false;
            }
        }
    }["useAuthTokens.useCallback[refreshAccessToken]"], [
        refreshToken,
        setTokens
    ]);
    // Set up a timer to refresh the token before it expires
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAuthTokens.useEffect": ()=>{
            if (!accessToken || !refreshToken) return;
            // Refresh token 5 minutes before it expires (assuming 1 hour expiry)
            const refreshInterval = 55 * 60 * 1000; // 55 minutes
            const timerId = setTimeout({
                "useAuthTokens.useEffect.timerId": ()=>{
                    refreshAccessToken();
                }
            }["useAuthTokens.useEffect.timerId"], refreshInterval);
            return ({
                "useAuthTokens.useEffect": ()=>clearTimeout(timerId)
            })["useAuthTokens.useEffect"];
        }
    }["useAuthTokens.useEffect"], [
        accessToken,
        refreshToken,
        refreshAccessToken
    ]);
    return {
        accessToken,
        refreshToken,
        setTokens,
        clearTokens,
        refreshAccessToken,
        isInitialized,
        isAuthenticated: !!accessToken
    };
}
_s(useAuthTokens, "9dN6EzBr0WFUiXdvZf11QN23ABc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCookie"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCookie"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/types/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * DukaLink API TypeScript Definitions
 *
 * This file contains TypeScript type definitions for the DukaLink API.
 * These types are based on the Swagger documentation and provide type safety
 * for API requests and responses.
 */ // ==========================================
// Common Types
// ==========================================
/**
 * Paginated response wrapper
 */ __turbopack_context__.s({});
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/types/user.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * User types based on the Swagger definition
 */ __turbopack_context__.s({
    "normalizeUser": (()=>normalizeUser)
});
function normalizeUser(apiUser) {
    // Create a normalized user object
    const normalizedUser = {
        ...apiUser,
        // Normalize properties that might have inconsistent casing
        role: apiUser.role || apiUser.Role || null,
        tenant: apiUser.tenant || apiUser.Tenant || null,
        branch: apiUser.branch || apiUser.Branch || null,
        location: apiUser.location || apiUser.Location || null
    };
    // If we have a role_id but no role object, create a minimal role object with the name from role_name
    if (apiUser.role_id && !normalizedUser.role && apiUser.role_name) {
        normalizedUser.role = {
            id: apiUser.role_id,
            name: apiUser.role_name,
            description: null,
            created_at: '',
            updated_at: '',
            permissions: []
        };
    }
    return normalizedUser;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/types/reports.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "DATE_RANGE_OPTIONS": (()=>DATE_RANGE_OPTIONS),
    "TIME_RANGE_OPTIONS": (()=>TIME_RANGE_OPTIONS),
    "processSalesByItem": (()=>processSalesByItem),
    "processSalesSummary": (()=>processSalesSummary)
});
function processSalesSummary(sales) {
    if (!sales || sales.length === 0) {
        return {
            grossSales: 0,
            netSales: 0,
            grossProfit: 0,
            totalSales: 0,
            averageSale: 0,
            totalItems: 0,
            totalCustomers: 0
        };
    }
    const grossSales = sales.reduce((sum, sale)=>sum + parseFloat(sale.total_amount), 0);
    // Calculate total cost (buying price * quantity) for all items
    const totalCost = sales.reduce((sum, sale)=>{
        if (!sale.SaleItems) return sum;
        const saleCost = sale.SaleItems.reduce((itemSum, item)=>itemSum + parseFloat(item.buying_price) * item.quantity, 0);
        return sum + saleCost;
    }, 0);
    // Calculate gross profit
    const grossProfit = grossSales - totalCost;
    // Count unique customers
    const uniqueCustomers = new Set(sales.map((sale)=>sale.customer_id)).size;
    // Count total items sold
    const totalItems = sales.reduce((sum, sale)=>{
        if (!sale.SaleItems) return sum;
        return sum + sale.SaleItems.reduce((itemSum, item)=>itemSum + item.quantity, 0);
    }, 0);
    return {
        grossSales,
        netSales: grossSales,
        grossProfit,
        totalSales: sales.length,
        averageSale: sales.length > 0 ? grossSales / sales.length : 0,
        totalItems,
        totalCustomers: uniqueCustomers
    };
}
function processSalesByItem(sales) {
    if (!sales || sales.length === 0) {
        return [];
    }
    const itemMap = new Map();
    sales.forEach((sale)=>{
        if (!sale.SaleItems) return;
        sale.SaleItems.forEach((item)=>{
            if (!item.Product) return;
            const productId = item.product_id;
            const existingItem = itemMap.get(productId);
            if (existingItem) {
                existingItem.quantity += item.quantity;
                existingItem.totalSales += parseFloat(item.total_price);
                const itemProfit = (parseFloat(item.unit_price) - parseFloat(item.buying_price)) * item.quantity;
                existingItem.profit += itemProfit;
            } else {
                const itemProfit = (parseFloat(item.unit_price) - parseFloat(item.buying_price)) * item.quantity;
                itemMap.set(productId, {
                    productId,
                    productName: item.Product.name,
                    sku: item.Product.sku,
                    quantity: item.quantity,
                    totalSales: parseFloat(item.total_price),
                    averagePrice: parseFloat(item.unit_price),
                    profit: itemProfit
                });
            }
        });
    });
    // Calculate average price for each item
    itemMap.forEach((item)=>{
        item.averagePrice = item.quantity > 0 ? item.totalSales / item.quantity : 0;
    });
    return Array.from(itemMap.values());
}
const DATE_RANGE_OPTIONS = [
    {
        label: "Today",
        value: "today"
    },
    {
        label: "Yesterday",
        value: "yesterday"
    },
    {
        label: "This Week",
        value: "this_week"
    },
    {
        label: "Last Week",
        value: "last_week"
    },
    {
        label: "This Month",
        value: "this_month"
    },
    {
        label: "Last Month",
        value: "last_month"
    },
    {
        label: "This Year",
        value: "this_year"
    },
    {
        label: "Last Year",
        value: "last_year"
    },
    {
        label: "Custom Range",
        value: "custom"
    }
];
const TIME_RANGE_OPTIONS = [
    {
        label: "All Day",
        value: "all_day"
    },
    {
        label: "Morning (6AM-12PM)",
        value: "morning",
        startHour: 6,
        endHour: 12
    },
    {
        label: "Afternoon (12PM-6PM)",
        value: "afternoon",
        startHour: 12,
        endHour: 18
    },
    {
        label: "Evening (6PM-12AM)",
        value: "evening",
        startHour: 18,
        endHour: 24
    },
    {
        label: "Night (12AM-6AM)",
        value: "night",
        startHour: 0,
        endHour: 6
    }
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/types/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$user$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/user.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$reports$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/reports.ts [app-client] (ecmascript)");
;
;
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/types/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$user$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/user.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$reports$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/reports.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/components/ui/api-loader.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiLoader": (()=>ApiLoader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
function ApiLoader({ isVisible, timeout = 15000, className }) {
    _s();
    const [show, setShow] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Add a small delay before showing the loader to prevent flashing
    // for very quick requests
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ApiLoader.useEffect": ()=>{
            let showTimer;
            let hideTimer;
            if (isVisible) {
                // Show the loader after a short delay (150ms) - reduced from 300ms
                // to make it more responsive
                showTimer = setTimeout({
                    "ApiLoader.useEffect": ()=>{
                        setShow(true);
                    }
                }["ApiLoader.useEffect"], 150);
                // Set a timeout to hide the loader after the specified time
                hideTimer = setTimeout({
                    "ApiLoader.useEffect": ()=>{
                        setShow(false);
                    }
                }["ApiLoader.useEffect"], timeout);
            } else {
                setShow(false);
            }
            return ({
                "ApiLoader.useEffect": ()=>{
                    clearTimeout(showTimer);
                    clearTimeout(hideTimer);
                }
            })["ApiLoader.useEffect"];
        }
    }["ApiLoader.useEffect"], [
        isVisible,
        timeout
    ]);
    if (!show) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("fixed inset-0 z-[9999] flex items-center justify-center bg-background/30 backdrop-blur-[2px]", className),
        "data-testid": "api-loader",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "rounded-md bg-background p-6 shadow-lg border border-border",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                className: "h-10 w-10 animate-spin text-primary"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/api-loader.tsx",
                lineNumber: 72,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/api-loader.tsx",
            lineNumber: 71,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/api-loader.tsx",
        lineNumber: 64,
        columnNumber: 5
    }, this);
}
_s(ApiLoader, "bXBd/WbmO9A8Q7bxaOKZvuJyGc0=");
_c = ApiLoader;
var _c;
__turbopack_context__.k.register(_c, "ApiLoader");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/providers/loading-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LoadingProvider": (()=>LoadingProvider),
    "LoadingScreen": (()=>LoadingScreen),
    "useLoading": (()=>useLoading)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-auth-tokens.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$api$2d$loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/api-loader.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
;
// Key for localStorage to track if we've already shown loading screens
const LOADING_SHOWN_KEY = "loading_shown";
const LoadingContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
function useLoading() {
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(LoadingContext);
    if (!context) {
        throw new Error("useLoading must be used within a LoadingProvider");
    }
    return context;
}
_s(useLoading, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function LoadingProvider({ children }) {
    _s1();
    // Track active API requests
    const [activeApiRequests, setActiveApiRequests] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    // Function to increment active API requests
    const incrementApiRequests = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "LoadingProvider.useCallback[incrementApiRequests]": ()=>{
            setActiveApiRequests({
                "LoadingProvider.useCallback[incrementApiRequests]": (prev)=>{
                    const newCount = prev + 1;
                    if (newCount === 1) {
                        // First active request, show the loader
                        setIsLoadingState({
                            "LoadingProvider.useCallback[incrementApiRequests]": (prev)=>({
                                    ...prev,
                                    api: true
                                })
                        }["LoadingProvider.useCallback[incrementApiRequests]"]);
                    }
                    return newCount;
                }
            }["LoadingProvider.useCallback[incrementApiRequests]"]);
        }
    }["LoadingProvider.useCallback[incrementApiRequests]"], []);
    // Function to decrement active API requests
    const decrementApiRequests = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "LoadingProvider.useCallback[decrementApiRequests]": ()=>{
            setActiveApiRequests({
                "LoadingProvider.useCallback[decrementApiRequests]": (prev)=>{
                    const newCount = Math.max(0, prev - 1);
                    if (newCount === 0) {
                        // No more active requests, hide the loader
                        setIsLoadingState({
                            "LoadingProvider.useCallback[decrementApiRequests]": (prev)=>({
                                    ...prev,
                                    api: false
                                })
                        }["LoadingProvider.useCallback[decrementApiRequests]"]);
                    }
                    return newCount;
                }
            }["LoadingProvider.useCallback[decrementApiRequests]"]);
        }
    }["LoadingProvider.useCallback[decrementApiRequests]"], []);
    const { accessToken, isInitialized } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"])();
    // Track loading state for different screens
    const [isLoading, setIsLoadingState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        auth: false,
        main: false,
        role: false,
        api: false
    });
    // Track if we've shown loading for different screens in this session
    const [hasShownLoading, setHasShownLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "LoadingProvider.useState": ()=>{
            // Only run this on the client side
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            // Try to get the loading state from localStorage
            try {
                const storedState = localStorage.getItem(LOADING_SHOWN_KEY);
                if (storedState) {
                    return JSON.parse(storedState);
                }
            } catch (error) {
                console.error("Error reading loading state from localStorage:", error);
            }
            return {
                auth: false,
                main: false,
                role: false,
                api: false
            };
        }
    }["LoadingProvider.useState"]);
    // Set loading state for a specific screen - memoized with useCallback
    const setLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "LoadingProvider.useCallback[setLoading]": (type, value)=>{
            setIsLoadingState({
                "LoadingProvider.useCallback[setLoading]": (prev)=>({
                        ...prev,
                        [type]: value
                    })
            }["LoadingProvider.useCallback[setLoading]"]);
        }
    }["LoadingProvider.useCallback[setLoading]"], []);
    // Mark that we've shown loading for a specific screen - memoized with useCallback
    const markLoadingShown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "LoadingProvider.useCallback[markLoadingShown]": (type)=>{
            setHasShownLoading({
                "LoadingProvider.useCallback[markLoadingShown]": (prev)=>{
                    const newState = {
                        ...prev,
                        [type]: true
                    };
                    // Save to localStorage
                    if ("TURBOPACK compile-time truthy", 1) {
                        localStorage.setItem(LOADING_SHOWN_KEY, JSON.stringify(newState));
                    }
                    return newState;
                }
            }["LoadingProvider.useCallback[markLoadingShown]"]);
        }
    }["LoadingProvider.useCallback[markLoadingShown]"], []);
    // Reset loading state (for all screens or a specific one) - memoized with useCallback
    const resetLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "LoadingProvider.useCallback[resetLoading]": (type)=>{
            if (type) {
                // Reset for a specific screen
                setHasShownLoading({
                    "LoadingProvider.useCallback[resetLoading]": (prev)=>{
                        const newState = {
                            ...prev,
                            [type]: false
                        };
                        // Save to localStorage
                        if ("TURBOPACK compile-time truthy", 1) {
                            localStorage.setItem(LOADING_SHOWN_KEY, JSON.stringify(newState));
                        }
                        return newState;
                    }
                }["LoadingProvider.useCallback[resetLoading]"]);
            } else {
                // Reset for all screens
                const newState = {
                    auth: false,
                    main: false,
                    role: false,
                    api: false
                };
                // Save to localStorage
                if ("TURBOPACK compile-time truthy", 1) {
                    localStorage.setItem(LOADING_SHOWN_KEY, JSON.stringify(newState));
                }
                setHasShownLoading(newState);
            }
        }
    }["LoadingProvider.useCallback[resetLoading]"], []);
    // Effect to clear the localStorage flag when the user logs out
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LoadingProvider.useEffect": ()=>{
            // If auth is initialized but we don't have an access token,
            // the user might have logged out, so clear all flags
            if (isInitialized && !accessToken) {
                resetLoading();
            }
        }
    }["LoadingProvider.useEffect"], [
        isInitialized,
        accessToken,
        resetLoading
    ]);
    // Memoize the context value to prevent unnecessary re-renders
    const value = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "LoadingProvider.useMemo[value]": ()=>({
                isLoading,
                setLoading,
                hasShownLoading,
                markLoadingShown,
                resetLoading,
                incrementApiRequests,
                decrementApiRequests,
                activeApiRequests
            })
    }["LoadingProvider.useMemo[value]"], [
        isLoading,
        setLoading,
        hasShownLoading,
        markLoadingShown,
        resetLoading,
        incrementApiRequests,
        decrementApiRequests,
        activeApiRequests
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LoadingContext.Provider, {
        value: value,
        children: [
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$api$2d$loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApiLoader"], {
                isVisible: isLoading.api,
                timeout: 15000
            }, void 0, false, {
                fileName: "[project]/src/components/providers/loading-provider.tsx",
                lineNumber: 212,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/providers/loading-provider.tsx",
        lineNumber: 210,
        columnNumber: 5
    }, this);
}
_s1(LoadingProvider, "yWJzEasQVgJvbtrWqMXwfzzx4cI=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"]
    ];
});
_c = LoadingProvider;
function LoadingScreen({ message = "Loading...", showSpinner = true, showLogo = true }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 z-[9999] flex flex-col items-center justify-center bg-background",
        children: [
            showLogo && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                    src: "/logo.png",
                    alt: "Dukalink Logo",
                    className: "h-12 w-auto",
                    onError: (e)=>{
                        // Hide the image if it fails to load
                        e.target.style.display = "none";
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/providers/loading-provider.tsx",
                    lineNumber: 233,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/providers/loading-provider.tsx",
                lineNumber: 232,
                columnNumber: 9
            }, this),
            showSpinner && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent"
                }, void 0, false, {
                    fileName: "[project]/src/components/providers/loading-provider.tsx",
                    lineNumber: 247,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/providers/loading-provider.tsx",
                lineNumber: 246,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-lg font-medium text-primary",
                children: message
            }, void 0, false, {
                fileName: "[project]/src/components/providers/loading-provider.tsx",
                lineNumber: 251,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/providers/loading-provider.tsx",
        lineNumber: 230,
        columnNumber: 5
    }, this);
}
_c1 = LoadingScreen;
var _c, _c1;
__turbopack_context__.k.register(_c, "LoadingProvider");
__turbopack_context__.k.register(_c1, "LoadingScreen");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/auth/hooks/use-auth.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useCurrentUser": (()=>useCurrentUser),
    "useIsAuthenticated": (()=>useIsAuthenticated),
    "useLogin": (()=>useLogin),
    "useLogout": (()=>useLogout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$api$2f$auth$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/auth/api/auth-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-auth-tokens.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$user$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/user.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$loading$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/providers/loading-provider.tsx [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
function useLogin() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { setTokens } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"])();
    const { setLoading, resetLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$loading$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLoading"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useLogin.useMutation": (credentials)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$api$2f$auth$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].login(credentials)
        }["useLogin.useMutation"],
        onSuccess: {
            "useLogin.useMutation": (data)=>{
                console.log("Login successful, setting tokens:", {
                    accessToken: data.accessToken ? "exists" : "missing"
                });
                // Show the loading screen with "Preparing Dashboard" message
                // Reset loading state to ensure it shows even if it was shown before
                resetLoading("main");
                setLoading("main", true);
                // Store tokens in cookies via the hook
                setTokens(data.accessToken, data.refreshToken);
                // Set the token for API requests
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setAuthToken(data.accessToken);
                // Invalidate and refetch user data
                queryClient.invalidateQueries({
                    queryKey: [
                        "currentUser"
                    ]
                });
                // Always redirect to dashboard page
                const dashboardRoute = "/dashboard";
                console.log("Redirecting to dashboard:", dashboardRoute);
                // Check if cookie was set properly
                const cookies = document.cookie;
                console.log("Cookies before redirect:", cookies);
                // Use Next.js router for client-side navigation
                console.log("Redirecting to dashboard:", dashboardRoute);
                router.replace(dashboardRoute);
            }
        }["useLogin.useMutation"],
        onError: {
            "useLogin.useMutation": (error)=>{
                console.error("Login error:", error);
                // Ensure loading is turned off in case of error
                setLoading("main", false);
            }
        }["useLogin.useMutation"]
    });
}
_s(useLogin, "tuuORMs1CV8u3yMVeyKMHS//T0k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$loading$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLoading"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useLogout() {
    _s1();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { clearTokens } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useLogout.useMutation": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$api$2f$auth$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].logout()
        }["useLogout.useMutation"],
        onSuccess: {
            "useLogout.useMutation": ()=>{
                // Clear tokens from cookies
                clearTokens();
                // Clear the auth token from API client
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setAuthToken(null);
                // Clear user data from cache
                queryClient.invalidateQueries({
                    queryKey: [
                        "currentUser"
                    ]
                });
                queryClient.clear();
                // Use Next.js router for client-side navigation
                console.log("Redirecting to login");
                router.replace("/login");
            }
        }["useLogout.useMutation"]
    });
}
_s1(useLogout, "Zrp2bmc3nGRqPMuREYSqSCQdJGo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useCurrentUser() {
    _s2();
    const { accessToken, refreshAccessToken, clearTokens, isInitialized } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    // Set the token for API requests when it changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCurrentUser.useEffect": ()=>{
            console.log("useCurrentUser effect:", {
                accessToken: accessToken ? "exists" : "missing",
                isInitialized
            });
            if (isInitialized) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setAuthToken(accessToken);
            }
        }
    }["useCurrentUser.useEffect"], [
        accessToken,
        isInitialized
    ]);
    // Try to get the user from the cache first
    const cachedUser = queryClient.getQueryData([
        "currentUser"
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "currentUser"
        ],
        queryFn: {
            "useCurrentUser.useQuery": async ()=>{
                console.log("Fetching current user with token:", accessToken ? "exists" : "missing");
                try {
                    const apiUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$api$2f$auth$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].getCurrentUser();
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$user$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeUser"])(apiUser);
                } catch (error) {
                    // If we get a 401 error, try to refresh the token
                    if (error?.statusCode === 401) {
                        console.log("Token expired, attempting to refresh...");
                        // Check if the error message indicates an invalid or expired token
                        const isTokenError = error?.message === "Invalid or expired token" || error?.error === "Unauthorized" || error?.error === "Error";
                        // If it's a specific token error, handle it immediately
                        if (isTokenError) {
                            console.log("Invalid or expired token detected, clearing auth state");
                            clearTokens();
                            queryClient.clear();
                            router.replace("/login");
                            throw error;
                        }
                        // Otherwise, try to refresh the token
                        const refreshed = await refreshAccessToken();
                        if (refreshed) {
                            // If refresh was successful, retry the request
                            console.log("Token refreshed, retrying request");
                            const apiUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$api$2f$auth$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].getCurrentUser();
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$user$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeUser"])(apiUser);
                        } else {
                            // If refresh failed, redirect to login
                            console.log("Token refresh failed, redirecting to login");
                            clearTokens();
                            queryClient.clear();
                            router.replace("/login");
                            throw error;
                        }
                    }
                    throw error;
                }
            }
        }["useCurrentUser.useQuery"],
        enabled: isInitialized && !!accessToken,
        staleTime: 5 * 60 * 1000,
        // Disable automatic refetching to reduce loading states
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        initialData: cachedUser || undefined
    });
}
_s2(useCurrentUser, "dyFaqTrNusMQ5tYEinhut937TX0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useIsAuthenticated() {
    _s3();
    const { accessToken, isInitialized } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"])();
    const { data: user, isLoading: isUserLoading } = useCurrentUser();
    const isLoading = !isInitialized || isUserLoading;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useIsAuthenticated.useEffect": ()=>{
            console.log("useIsAuthenticated state:", {
                accessToken: accessToken ? "exists" : "missing",
                isInitialized,
                user: user ? "exists" : "missing",
                isUserLoading,
                isLoading
            });
        }
    }["useIsAuthenticated.useEffect"], [
        accessToken,
        isInitialized,
        user,
        isUserLoading,
        isLoading
    ]);
    return {
        isAuthenticated: !!user && !!accessToken,
        isLoading,
        user: user || null
    };
}
_s3(useIsAuthenticated, "FkBhisil1n+hnNVCvOft0qM1aG8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"],
        useCurrentUser
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/role-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Determines the appropriate dashboard route based on user role
 * @param roleName The user's role name
 * @returns The dashboard route for the user's role
 */ __turbopack_context__.s({
    "getDashboardByRole": (()=>getDashboardByRole),
    "hasRouteAccess": (()=>hasRouteAccess)
});
function getDashboardByRole() {
    // Redirect all roles to the reports summary page
    return "/reports/sales-summary";
}
function hasRouteAccess(route, roleName) {
    if (!roleName) return false;
    // Add detailed debugging
    console.log(`[hasRouteAccess] Checking access for route: ${route}, role: ${roleName}`);
    // Super admin has access to everything
    if (roleName.toLowerCase() === "super_admin") {
        console.log(`[hasRouteAccess] Super admin has access to all routes`);
        return true;
    }
    // Define route access by role
    const roleRouteAccess = {
        branch_admin: [
            "/dashboard",
            "/dashboard/branch",
            "/users",
            "/employees",
            "/employees/[id]",
            "/products",
            "/categories",
            "/brands",
            "/inventory",
            "/inventory/transfers",
            "/inventory/reports",
            "/banking",
            "/banking/summary",
            "/mpesa",
            "/mpesa/transactions",
            "/dsa",
            "/dsa/agents",
            "/dsa/assignments",
            "/dsa/reconciliations",
            "/phone-repairs",
            "/expenses",
            "/expenses/[id]",
            "/invoices",
            "/invoices/[id]",
            "/invoices/new",
            "/invoices/[id]/edit",
            "/credit-notes",
            "/credit-notes/[id]",
            "/credit-notes/new",
            "/credit-notes/[id]/edit",
            "/reports",
            "/reports/sales-summary",
            "/reports/sales-by-item",
            "/reports/sales-by-category",
            "/reports/sales-by-employee",
            "/reports/sales-by-payment-type",
            "/reports/mpesa-banking",
            "/reports/cash-status",
            "/reports/dsa-sales",
            "/reports/receipts",
            "/reports/shifts",
            "/reports/phone-repairs",
            "/profile",
            "/pos",
            "/pos/sessions",
            "/sales",
            "/customers",
            "/procurement",
            "/procurement/requests",
            "/procurement/requests/new",
            "/procurement/receipts",
            "/suppliers"
        ],
        super_admin: [
            "/dashboard",
            "/super-admin",
            "/tenants",
            "/users",
            "/roles",
            "/rbac",
            "/branches",
            "/locations",
            "/locations/create",
            "/locations/[id]",
            "/locations/[id]/edit",
            "/employees",
            "/products",
            "/categories",
            "/brands",
            "/inventory",
            "/inventory/transfers",
            "/inventory/reports",
            "/float",
            // "/float/transactions", // Temporarily disabled until API is implemented
            "/float/movements",
            "/float/reconciliations",
            "/float/topups",
            "/banking",
            "/banking/summary",
            "/dsa",
            "/dsa/agents",
            "/dsa/assignments",
            "/dsa/reconciliations",
            "/phone-repairs",
            "/expenses",
            "/invoices",
            "/invoices/[id]",
            "/invoices/new",
            "/invoices/[id]/edit",
            "/credit-notes",
            "/credit-notes/[id]",
            "/credit-notes/new",
            "/credit-notes/[id]/edit",
            "/settings/system",
            "/settings/company",
            "/settings/payment-methods",
            "/settings/health",
            "/profile",
            "/procurement",
            "/procurement/requests",
            "/procurement/requests/new",
            "/procurement/receipts",
            "/suppliers"
        ],
        company_admin: [
            "/dashboard",
            "/dashboard/company",
            "/users",
            "/roles",
            // "/rbac", // Add RBAC route for company_admin
            "/branches",
            "/locations",
            "/locations/create",
            "/locations/[id]",
            "/locations/[id]/edit",
            "/employees",
            "/products",
            "/categories",
            "/brands",
            "/inventory",
            "/inventory/transfers",
            "/inventory/reports",
            "/float",
            // "/float/transactions", // Temporarily disabled until API is implemented
            "/float/movements",
            "/float/reconciliations",
            "/float/topups",
            "/banking",
            "/banking/summary",
            "/mpesa",
            "/mpesa/transactions",
            "/dsa",
            "/dsa/agents",
            "/dsa/assignments",
            "/dsa/reconciliations",
            "/phone-repairs",
            "/expenses",
            "/expense-analytics",
            "/invoices",
            "/invoices/[id]",
            "/invoices/new",
            "/invoices/[id]/edit",
            "/credit-notes",
            "/credit-notes/[id]",
            "/credit-notes/new",
            "/credit-notes/[id]/edit",
            "/reports",
            "/reports/sales-summary",
            "/reports/sales-by-item",
            "/reports/sales-by-category",
            "/reports/sales-by-employee",
            "/reports/sales-by-payment-type",
            "/reports/mpesa-banking",
            "/reports/running-balances",
            "/reports/mpesa-transactions",
            "/reports/cash-status",
            "/reports/dsa-sales",
            "/reports/receipts",
            "/reports/shifts",
            "/reports/phone-repairs",
            "/settings/company",
            "/settings/payment-methods",
            "/profile",
            "/pos",
            "/pos/sessions",
            "/sales",
            "/customers",
            "/procurement",
            "/procurement/requests",
            "/procurement/requests/new",
            "/procurement/receipts",
            "/suppliers"
        ],
        tenant_admin: [
            "/dashboard",
            "/dashboard/company",
            "/users",
            "/roles",
            // "/rbac", // Add RBAC route for tenant_admin
            "/branches",
            "/locations",
            "/locations/create",
            "/locations/[id]",
            "/locations/[id]/edit",
            "/employees",
            "/products",
            "/categories",
            "/brands",
            "/inventory",
            "/inventory/transfers",
            "/inventory/reports",
            "/float",
            // "/float/transactions", // Temporarily disabled until API is implemented
            "/float/movements",
            "/float/reconciliations",
            "/float/topups",
            "/banking",
            "/banking/summary",
            "/mpesa",
            "/mpesa/transactions",
            "/dsa",
            "/dsa/agents",
            "/dsa/assignments",
            "/dsa/reconciliations",
            "/phone-repairs",
            "/expenses",
            "/invoices",
            "/invoices/[id]",
            "/invoices/new",
            "/invoices/[id]/edit",
            "/reports",
            "/reports/sales-summary",
            "/reports/sales-by-item",
            "/reports/sales-by-category",
            "/reports/sales-by-employee",
            "/reports/running-balances",
            "/reports/sales-by-payment-type",
            "/reports/mpesa-transactions",
            "/reports/mpesa-banking",
            "/reports/cash-status",
            "/reports/dsa-sales",
            "/reports/receipts",
            "/reports/shifts",
            "/reports/phone-repairs",
            "/settings/company",
            "/settings/payment-methods",
            "/profile",
            "/pos",
            "/pos/sessions",
            "/sales",
            "/customers",
            "/procurement",
            "/procurement/requests",
            "/procurement/requests/new",
            "/procurement/receipts",
            "/suppliers"
        ],
        // Branch Manager role removed as they don't login via the web
        stock_admin: [
            "/dashboard",
            "/dashboard/stock",
            "/products",
            "/categories",
            "/brands",
            "/inventory",
            "/inventory/transfers",
            "/inventory/reports",
            "/phone-repairs",
            "/reports",
            "/reports/running-balances",
            "/reports/sales-summary",
            "/reports/sales-by-item",
            "/reports/sales-by-category",
            "/reports/mpesa-transactions",
            "/reports/sales-by-employee",
            "/reports/sales-by-payment-type",
            "/reports/mpesa-banking",
            "/reports/cash-status",
            "/reports/dsa-sales",
            "/reports/receipts",
            "/reports/shifts",
            "/reports/phone-repairs",
            "/profile",
            "/procurement",
            "/procurement/requests",
            "/procurement/requests/new",
            "/procurement/receipts",
            "/suppliers"
        ],
        float_manager: [
            "/dashboard",
            "/dashboard/float",
            "/dashboard/cash-balance",
            "/float",
            "/brands",
            "/inventory",
            "/inventory/reports",
            // "/float/transactions", // Temporarily disabled until API is implemented
            "/float/movements",
            "/float/reconciliations",
            "/float/topups",
            "/banking",
            "/banking/summary",
            "/mpesa",
            "/mpesa/transactions",
            "/pos/sessions",
            "/reports/running-balances",
            "/reports",
            "/reports/sales-summary",
            "/reports/sales-by-item",
            "/reports/mpesa-transactions",
            "/reports/sales-by-category",
            "/reports/sales-by-employee",
            "/reports/sales-by-payment-type",
            "/reports/mpesa-banking",
            "/reports/cash-status",
            "/reports/cash-float",
            "/reports/dsa-sales",
            "/reports/receipts",
            "/reports/shifts",
            "/reports/phone-repairs",
            "/expense-analytics",
            "/profile",
            "/procurement",
            "/procurement/requests",
            "/procurement/receipts",
            "/suppliers"
        ],
        accountant: [
            "/dashboard",
            "/dashboard/finance",
            "/reports",
            "/reports/sales-summary",
            "/reports/mpesa-transactions",
            "/reports/sales-by-item",
            "/reports/sales-by-category",
            "/reports/sales-by-employee",
            "/reports/sales-by-payment-type",
            "/reports/mpesa-banking",
            "/reports/cash-status",
            "/reports/dsa-sales",
            "/reports/receipts",
            "/reports/shifts",
            "/reports/phone-repairs",
            "/reports/expense-reports",
            "/brands",
            "/inventory",
            "/inventory/reports",
            "/banking",
            "/banking/summary",
            "/expenses",
            "/expenses/[id]",
            "/expense-analytics",
            "/invoices",
            "/invoices/[id]",
            "/invoices/new",
            "/invoices/[id]/edit",
            "/credit-notes",
            "/credit-notes/[id]",
            "/credit-notes/new",
            "/credit-notes/[id]/edit",
            "/profile",
            "/users",
            "/locations",
            "/locations/create",
            "/locations/[id]",
            "/locations/[id]/edit",
            "/employees",
            "/products",
            "/categories",
            "/procurement",
            "/procurement/requests",
            "/procurement/receipts",
            "/suppliers"
        ],
        finance_manager: [
            "/reports/running-balances",
            "/dashboard",
            "/dashboard/pos",
            "/reports",
            "/brands",
            "/inventory",
            "/inventory/reports",
            "/reports/mpesa-transactions",
            "/reports/sales-summary",
            "/reports/sales-by-item",
            "/reports/sales-by-category",
            "/reports/sales-by-employee",
            "/reports/sales-by-payment-type",
            "/reports/mpesa-banking",
            "/reports/cash-status",
            "/reports/dsa-sales",
            "/reports/receipts",
            "/reports/shifts",
            "/reports/phone-repairs",
            "/profile",
            "/pos",
            "/pos/sessions",
            "/sales",
            "/customers",
            "/procurement",
            "/procurement/requests",
            "/procurement/receipts",
            "/suppliers"
        ],
        operations_manager: [
            "/dashboard",
            "/dashboard/operations",
            "/users",
            "/employees",
            "/employees/[id]",
            "/products",
            "/categories",
            "/brands",
            "/inventory",
            "/inventory/transfers",
            "/inventory/reports",
            "/banking",
            "/banking/summary",
            "/mpesa",
            "/mpesa/transactions",
            "/dsa",
            "/reports/running-balances",
            "/dsa/agents",
            "/dsa/assignments",
            "/dsa/reconciliations",
            "/phone-repairs",
            "/expenses",
            "/expenses/[id]",
            "/expense-analytics",
            "/invoices",
            "/invoices/[id]",
            "/invoices/new",
            "/invoices/[id]/edit",
            "/reports/mpesa-transactions",
            "/reports",
            "/reports/sales-summary",
            "/reports/sales-by-item",
            "/reports/sales-by-category",
            "/reports/sales-by-employee",
            "/reports/sales-by-payment-type",
            "/reports/mpesa-banking",
            "/reports/cash-status",
            "/reports/dsa-sales",
            "/reports/receipts",
            "/reports/shifts",
            "/reports/phone-repairs",
            "/reports/expense-reports",
            "/profile",
            "/pos",
            "/pos/sessions",
            "/sales",
            "/customers",
            "/procurement",
            "/procurement/requests",
            "/procurement/requests/new",
            "/procurement/receipts",
            "/suppliers"
        ],
        assistant_operations_manager: [
            "/dashboard",
            "/dashboard/operations",
            "/users",
            "/employees",
            "/employees/[id]",
            "/products",
            "/categories",
            "/brands",
            "/inventory",
            "/inventory/transfers",
            "/inventory/reports",
            "/banking",
            "/banking/summary",
            "/mpesa",
            "/reports/running-balances",
            "/mpesa/transactions",
            "/dsa",
            "/dsa/agents",
            "/dsa/assignments",
            "/dsa/reconciliations",
            "/phone-repairs",
            "/expenses",
            "/expenses/[id]",
            "/reports/mpesa-transactions",
            "/expense-analytics",
            "/invoices",
            "/invoices/[id]",
            "/invoices/new",
            "/invoices/[id]/edit",
            "/reports",
            "/reports/sales-summary",
            "/reports/sales-by-item",
            "/reports/sales-by-category",
            "/reports/sales-by-employee",
            "/reports/sales-by-payment-type",
            "/reports/mpesa-banking",
            "/reports/cash-status",
            "/reports/dsa-sales",
            "/reports/receipts",
            "/reports/shifts",
            "/reports/phone-repairs",
            "/reports/expense-reports",
            "/profile",
            "/pos",
            "/pos/sessions",
            "/sales",
            "/customers",
            "/procurement",
            "/procurement/requests",
            "/procurement/requests/new",
            "/procurement/receipts",
            "/suppliers"
        ]
    };
    // Check if the role has access to the route or its parent route
    const roleLower = roleName.toLowerCase();
    const accessibleRoutes = roleRouteAccess[roleLower] || [];
    const hasAccess = accessibleRoutes.some((accessibleRoute)=>route === accessibleRoute || route.startsWith(`${accessibleRoute}/`));
    // Add more detailed debugging
    console.log(`[hasRouteAccess] Access result for ${route}: ${hasAccess ? "ALLOWED" : "DENIED"}`);
    if (!hasAccess) {
        // Find the closest matching route for better debugging
        const closestRoutes = accessibleRoutes.filter((r)=>route.includes(r) || r.includes(route)).sort((a, b)=>{
            // Sort by similarity (length of common substring)
            const aCommon = commonSubstringLength(route, a);
            const bCommon = commonSubstringLength(route, b);
            return bCommon - aCommon;
        }).slice(0, 3); // Show top 3 closest matches
        if (closestRoutes.length > 0) {
            console.log(`[hasRouteAccess] Closest matching routes for ${roleLower}:`, closestRoutes);
        } else {
            console.log(`[hasRouteAccess] No similar routes found for ${roleLower}`);
        }
        // Log the first 10 accessible routes for this role (to avoid console spam)
        console.log(`[hasRouteAccess] First 10 accessible routes for ${roleLower}:`, accessibleRoutes.slice(0, 10));
    }
    return hasAccess;
}
/**
 * Helper function to find the length of the longest common substring
 */ function commonSubstringLength(str1, str2) {
    const s1 = str1.toLowerCase();
    const s2 = str2.toLowerCase();
    // Simple implementation - length of common characters
    let common = 0;
    for(let i = 0; i < Math.min(s1.length, s2.length); i++){
        if (s1[i] === s2[i]) {
            common++;
        } else {
            break;
        }
    }
    return common;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/route-data.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "routeGroups": (()=>routeGroups),
    "routes": (()=>routes)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$banknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Banknote$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/banknote.js [app-client] (ecmascript) <export default as Banknote>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-client] (ecmascript) <export default as BarChart3>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/building.js [app-client] (ecmascript) <export default as Building>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/credit-card.js [app-client] (ecmascript) <export default as CreditCard>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-client] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/package.js [app-client] (ecmascript) <export default as Package>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/phone.js [app-client] (ecmascript) <export default as Phone>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Scale$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/scale.js [app-client] (ecmascript) <export default as Scale>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-client] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shield.js [app-client] (ecmascript) <export default as Shield>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shopping$2d$bag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShoppingBag$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shopping-bag.js [app-client] (ecmascript) <export default as ShoppingBag>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/tag.js [app-client] (ecmascript) <export default as Tag>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$truck$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Truck$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/truck.js [app-client] (ecmascript) <export default as Truck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-client] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserCheck$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user-check.js [app-client] (ecmascript) <export default as UserCheck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
;
const routeGroups = [
    "Reports",
    "Administration",
    "Products",
    "Inventory",
    "Banking",
    "DSA",
    "Phone Repairs",
    "Settings"
];
const routes = [
    // Reports
    {
        title: "Sales Summary",
        path: "/reports/sales-summary",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"],
        description: "Overview of sales performance and trends",
        keywords: [
            "sales",
            "summary",
            "overview",
            "performance",
            "trends",
            "dashboard"
        ],
        group: "Reports"
    },
    {
        title: "Sales by Item",
        path: "/reports/sales-by-item",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shopping$2d$bag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShoppingBag$3e$__["ShoppingBag"],
        description: "Sales breakdown by individual products",
        keywords: [
            "sales",
            "items",
            "products",
            "breakdown"
        ],
        group: "Reports"
    },
    {
        title: "Sales by Category",
        path: "/reports/sales-by-category",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"],
        description: "Sales breakdown by product categories",
        keywords: [
            "sales",
            "categories",
            "breakdown"
        ],
        group: "Reports"
    },
    {
        title: "Banking Report",
        path: "/reports/mpesa-banking",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$banknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Banknote$3e$__["Banknote"],
        description: "Banking and financial transactions report",
        keywords: [
            "banking",
            "finance",
            "transactions",
            "mpesa"
        ],
        group: "Reports"
    },
    {
        title: "Cash Status",
        path: "/reports/cash-status",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$banknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Banknote$3e$__["Banknote"],
        description: "Cash position and movements across all locations",
        keywords: [
            "cash",
            "status",
            "position",
            "movements",
            "finance"
        ],
        group: "Reports"
    },
    {
        title: "DSA Sales",
        path: "/reports/dsa-sales",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserCheck$3e$__["UserCheck"],
        description: "Direct Sales Agent performance and sales",
        keywords: [
            "dsa",
            "sales",
            "agents",
            "direct sales"
        ],
        group: "Reports"
    },
    {
        title: "Receipts",
        path: "/reports/receipts",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"],
        description: "Sales receipts and transaction records",
        keywords: [
            "receipts",
            "transactions",
            "records"
        ],
        group: "Reports"
    },
    {
        title: "Shifts",
        path: "/reports/shifts",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"],
        description: "POS session and shift reports",
        keywords: [
            "shifts",
            "sessions",
            "pos"
        ],
        group: "Reports"
    },
    {
        title: "Phone Repairs",
        path: "/reports/phone-repairs",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__["Phone"],
        description: "Phone repair tracking and status",
        keywords: [
            "phone",
            "repairs",
            "tracking",
            "status"
        ],
        group: "Reports"
    },
    {
        title: "Stock Valuation",
        path: "/inventory/valuation",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Scale$3e$__["Scale"],
        description: "Detailed stock valuation report by method",
        keywords: [
            "inventory",
            "valuation",
            "stock",
            "fifo",
            "lifo",
            "weighted average"
        ],
        group: "Reports"
    },
    // Administration
    {
        title: "Tenants",
        path: "/tenants",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__["Building"],
        description: "Manage company tenants",
        keywords: [
            "tenants",
            "companies",
            "manage"
        ],
        roles: [
            "super_admin"
        ],
        group: "Administration"
    },
    {
        title: "Users",
        path: "/users",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
        description: "Manage system users and permissions",
        keywords: [
            "users",
            "staff",
            "permissions",
            "accounts"
        ],
        group: "Administration"
    },
    {
        title: "Roles",
        path: "/roles",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__["Shield"],
        description: "Manage user roles and permissions",
        keywords: [
            "roles",
            "permissions",
            "access"
        ],
        roles: [
            "super_admin",
            "tenant_admin",
            "company_admin"
        ],
        group: "Administration"
    },
    {
        title: "Branches",
        path: "/branches",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__["Building"],
        description: "Manage company branches and locations",
        keywords: [
            "branches",
            "locations",
            "stores"
        ],
        group: "Administration"
    },
    // Products
    {
        title: "Products",
        path: "/products",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__["Package"],
        description: "Manage product catalog",
        keywords: [
            "products",
            "catalog",
            "items"
        ],
        group: "Products"
    },
    {
        title: "Categories",
        path: "/categories",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"],
        description: "Manage product categories",
        keywords: [
            "categories",
            "classification",
            "groups"
        ],
        group: "Products"
    },
    // Inventory
    {
        title: "Inventory",
        path: "/inventory",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__["Package"],
        description: "Manage product inventory and stock",
        keywords: [
            "inventory",
            "stock",
            "products"
        ],
        group: "Inventory"
    },
    {
        title: "Stock Transfers",
        path: "/inventory/transfers",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$truck$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Truck$3e$__["Truck"],
        description: "Manage inventory transfers between branches",
        keywords: [
            "transfers",
            "stock",
            "movement",
            "branches"
        ],
        group: "Inventory"
    },
    {
        title: "Inventory Reports",
        path: "/inventory/reports",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"],
        description: "Inventory reports and analytics",
        keywords: [
            "inventory",
            "reports",
            "analytics",
            "stock"
        ],
        group: "Inventory"
    },
    // Banking
    {
        title: "Banking Management",
        path: "/banking",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$banknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Banknote$3e$__["Banknote"],
        description: "Manage banking records and view summary analytics",
        keywords: [
            "banking",
            "summary",
            "transactions",
            "finance",
            "analytics",
            "reports",
            "mpesa",
            "bank",
            "agent"
        ],
        group: "Banking"
    },
    {
        title: "M-Pesa Transactions",
        path: "/mpesa/transactions",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$banknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Banknote$3e$__["Banknote"],
        description: "Manage M-Pesa transactions",
        keywords: [
            "mpesa",
            "transactions",
            "mobile money",
            "finance"
        ],
        group: "Banking"
    },
    {
        title: "Float Movements",
        path: "/float/movements",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$banknote$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Banknote$3e$__["Banknote"],
        description: "Track float movements and transfers",
        keywords: [
            "float",
            "movements",
            "transfers",
            "mpesa"
        ],
        group: "Banking"
    },
    {
        title: "Float Reconciliations",
        path: "/float/reconciliations",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"],
        description: "Reconcile float transactions",
        keywords: [
            "float",
            "reconciliations",
            "balance"
        ],
        group: "Banking"
    },
    // DSA
    {
        title: "DSA Agents",
        path: "/dsa/customers",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
        description: "Manage Direct Sales Agents",
        keywords: [
            "dsa",
            "customers",
            "sales",
            "direct"
        ],
        group: "DSA"
    },
    {
        title: "DSA Assignments",
        path: "/dsa/assignments",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"],
        description: "Manage DSA product assignments",
        keywords: [
            "dsa",
            "assignments",
            "products"
        ],
        group: "DSA"
    },
    {
        title: "DSA Reconciliations",
        path: "/dsa/reconciliations",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"],
        description: "Reconcile DSA sales and inventory",
        keywords: [
            "dsa",
            "reconciliations",
            "sales"
        ],
        group: "DSA"
    },
    // Phone Repairs
    {
        title: "Phone Repairs",
        path: "/phone-repairs",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__["Phone"],
        description: "Manage phone repair tickets and status",
        keywords: [
            "phone",
            "repairs",
            "tickets",
            "status"
        ],
        group: "Phone Repairs"
    },
    // Settings
    {
        title: "System Settings",
        path: "/settings/system",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"],
        description: "Configure system settings",
        keywords: [
            "settings",
            "system",
            "configuration"
        ],
        roles: [
            "super_admin"
        ],
        group: "Settings"
    },
    {
        title: "Company Settings",
        path: "/settings/company",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__["Building"],
        description: "Configure company settings",
        keywords: [
            "settings",
            "company",
            "configuration"
        ],
        roles: [
            "tenant_admin",
            "company_admin"
        ],
        group: "Settings"
    },
    {
        title: "Payment Methods",
        path: "/settings/payment-methods",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"],
        description: "Configure payment methods",
        keywords: [
            "settings",
            "payment",
            "methods"
        ],
        roles: [
            "super_admin",
            "tenant_admin",
            "company_admin"
        ],
        group: "Settings"
    },
    {
        title: "System Health",
        path: "/settings/health",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__["Shield"],
        description: "Monitor system health and status",
        keywords: [
            "health",
            "status",
            "monitoring"
        ],
        roles: [
            "super_admin"
        ],
        group: "Settings"
    },
    {
        title: "User Profile",
        path: "/profile",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"],
        description: "Manage your user profile",
        keywords: [
            "profile",
            "user",
            "account",
            "settings"
        ],
        group: "Settings"
    }
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/global-search.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "GlobalSearch": (()=>GlobalSearch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$search$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/providers/search-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dialog.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/auth/hooks/use-auth.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$role$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/role-utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$route$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/route-data.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cmdk$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cmdk/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
function GlobalSearch() {
    _s();
    const { isOpen, closeSearch } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$search$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearch"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { data: user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCurrentUser"])();
    const [search, setSearch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [filteredRoutes, setFilteredRoutes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const inputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Filter routes based on search query and user role
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GlobalSearch.useEffect": ()=>{
            if (!isOpen) return;
            const userRole = user?.role_name?.toLowerCase() || "";
            // Filter routes based on search query and user role
            const filtered = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$route$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["routes"].filter({
                "GlobalSearch.useEffect.filtered": (route)=>{
                    // Check if user has access to this route
                    if (route.roles && !route.roles.includes(userRole)) {
                        return false;
                    }
                    // Check if route has access based on role utils
                    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$role$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasRouteAccess"])(route.path, userRole)) {
                        return false;
                    }
                    // If no search query, include all accessible routes
                    if (!search) return true;
                    // Search in title, description, path, and keywords
                    const searchLower = search.toLowerCase();
                    return route.title.toLowerCase().includes(searchLower) || route.description.toLowerCase().includes(searchLower) || route.path.toLowerCase().includes(searchLower) || route.keywords?.some({
                        "GlobalSearch.useEffect.filtered": (keyword)=>keyword.toLowerCase().includes(searchLower)
                    }["GlobalSearch.useEffect.filtered"]);
                }
            }["GlobalSearch.useEffect.filtered"]);
            setFilteredRoutes(filtered);
        }
    }["GlobalSearch.useEffect"], [
        search,
        isOpen,
        user
    ]);
    // Focus input when dialog opens
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GlobalSearch.useEffect": ()=>{
            if (isOpen && inputRef.current) {
                setTimeout({
                    "GlobalSearch.useEffect": ()=>{
                        inputRef.current?.focus();
                    }
                }["GlobalSearch.useEffect"], 100);
            } else {
                setSearch("");
            }
        }
    }["GlobalSearch.useEffect"], [
        isOpen
    ]);
    // Handle route selection
    const handleSelect = (route)=>{
        router.push(route.path);
        closeSearch();
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Dialog"], {
        open: isOpen,
        onOpenChange: (open)=>!open && closeSearch(),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogContent"], {
            className: "p-0 gap-0 max-w-3xl max-h-[85vh] overflow-hidden",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "sr-only",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogTitle"], {
                        children: "Search"
                    }, void 0, false, {
                        fileName: "[project]/src/components/global-search.tsx",
                        lineNumber: 77,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/global-search.tsx",
                    lineNumber: 76,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cmdk$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Command"], {
                    className: "rounded-lg border shadow-md",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center border-b px-3",
                            "cmdk-input-wrapper": "",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cmdk$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Command"].Input, {
                                ref: inputRef,
                                value: search,
                                onValueChange: setSearch,
                                placeholder: "Search for pages, features, or settings...",
                                className: "flex h-12 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                            }, void 0, false, {
                                fileName: "[project]/src/components/global-search.tsx",
                                lineNumber: 84,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/global-search.tsx",
                            lineNumber: 80,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cmdk$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Command"].List, {
                            className: "max-h-[500px] overflow-y-auto overflow-x-hidden",
                            children: [
                                search.length > 0 && filteredRoutes.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cmdk$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Command"].Empty, {
                                    className: "py-6 text-center text-sm",
                                    children: "No results found."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/global-search.tsx",
                                    lineNumber: 94,
                                    columnNumber: 15
                                }, this),
                                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$route$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["routeGroups"].map((group)=>{
                                    const groupRoutes = filteredRoutes.filter((route)=>route.group === group);
                                    if (groupRoutes.length === 0) return null;
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cmdk$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Command"].Group, {
                                        heading: group,
                                        className: "px-2 py-1",
                                        children: groupRoutes.map((route)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cmdk$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Command"].Item, {
                                                value: `${route.title} ${route.path}`,
                                                onSelect: ()=>handleSelect(route),
                                                className: "flex items-center gap-2 px-2 py-1.5 rounded-md text-sm cursor-pointer hover:bg-accent data-[selected=true]:bg-accent",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex h-8 w-8 items-center justify-center rounded-md border bg-background",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(route.icon, {
                                                            className: "h-4 w-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/global-search.tsx",
                                                            lineNumber: 120,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/global-search.tsx",
                                                        lineNumber: 119,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex flex-col",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "font-medium",
                                                                children: route.title
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/global-search.tsx",
                                                                lineNumber: 123,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-xs text-muted-foreground",
                                                                children: route.description
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/global-search.tsx",
                                                                lineNumber: 124,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/global-search.tsx",
                                                        lineNumber: 122,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, route.path, true, {
                                                fileName: "[project]/src/components/global-search.tsx",
                                                lineNumber: 113,
                                                columnNumber: 21
                                            }, this))
                                    }, group, false, {
                                        fileName: "[project]/src/components/global-search.tsx",
                                        lineNumber: 107,
                                        columnNumber: 17
                                    }, this);
                                })
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/global-search.tsx",
                            lineNumber: 92,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "border-t p-2",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between text-xs text-muted-foreground",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex gap-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "Navigate with ↑↓"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/global-search.tsx",
                                                lineNumber: 138,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "Select with Enter"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/global-search.tsx",
                                                lineNumber: 139,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/global-search.tsx",
                                        lineNumber: 137,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("kbd", {
                                                className: "rounded border bg-muted px-1.5 font-mono text-[10px]",
                                                children: "ESC"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/global-search.tsx",
                                                lineNumber: 142,
                                                columnNumber: 17
                                            }, this),
                                            " ",
                                            "to close"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/global-search.tsx",
                                        lineNumber: 141,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/global-search.tsx",
                                lineNumber: 136,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/global-search.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/global-search.tsx",
                    lineNumber: 79,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/global-search.tsx",
            lineNumber: 75,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/global-search.tsx",
        lineNumber: 74,
        columnNumber: 5
    }, this);
}
_s(GlobalSearch, "GeC47lr10R18ITNJt7ToLKkXep4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$search$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearch"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCurrentUser"]
    ];
});
_c = GlobalSearch;
var _c;
__turbopack_context__.k.register(_c, "GlobalSearch");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/react-query.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "queryClient": (()=>queryClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-client] (ecmascript)");
;
const queryClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClient"]({
    defaultOptions: {
        queries: {
            refetchOnWindowFocus: false,
            retry: 1,
            staleTime: 5 * 60 * 1000
        }
    }
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/providers/api-redirect-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiRedirectProvider": (()=>ApiRedirectProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-auth-tokens.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$react$2d$query$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/react-query.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
function ApiRedirectProvider({ children }) {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const { accessToken, isInitialized } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ApiRedirectProvider.useEffect": ()=>{
            // Set up the redirect callback to use Next.js router
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setRedirectCallback({
                "ApiRedirectProvider.useEffect": (path)=>{
                    router.replace(path);
                }
            }["ApiRedirectProvider.useEffect"]);
            // Set the query client for API client to use for cache invalidation
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setQueryClient(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$react$2d$query$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryClient"]);
        }
    }["ApiRedirectProvider.useEffect"], [
        router
    ]);
    // Set auth token for API requests when it changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ApiRedirectProvider.useEffect": ()=>{
            console.log("ApiRedirectProvider: Auth state change", {
                accessToken: accessToken ? "exists" : "missing",
                isInitialized
            });
            if (isInitialized) {
                // If we have an access token in cookies, use it
                if (accessToken) {
                    console.log("ApiRedirectProvider: Setting auth token from cookies");
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setAuthToken(accessToken);
                } else if ("TURBOPACK compile-time truthy", 1) {
                    const localToken = localStorage.getItem('token') || localStorage.getItem('accessToken');
                    if (localToken) {
                        console.log("ApiRedirectProvider: Setting auth token from localStorage");
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setAuthToken(localToken);
                    }
                }
            }
        }
    }["ApiRedirectProvider.useEffect"], [
        accessToken,
        isInitialized
    ]);
    // Handle authentication redirects - only on initial mount and auth state changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ApiRedirectProvider.useEffect": ()=>{
            if (!isInitialized) {
                console.log("ApiRedirectProvider: Auth state not initialized yet");
                return; // Wait until auth state is initialized
            }
            console.log("ApiRedirectProvider: Auth state initialized", {
                accessToken: accessToken ? "exists" : "missing"
            });
            // Set up API client with the current token
            if (accessToken) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setAuthToken(accessToken);
            } else if ("TURBOPACK compile-time truthy", 1) {
                const localToken = localStorage.getItem('token') || localStorage.getItem('accessToken');
                if (localToken) {
                    console.log("ApiRedirectProvider: Setting auth token from localStorage (second effect)");
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setAuthToken(localToken);
                }
            }
        }
    }["ApiRedirectProvider.useEffect"], [
        accessToken,
        isInitialized
    ]);
    // We're disabling client-side route-specific redirects to avoid redirect loops
    // The middleware will handle redirects instead
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ApiRedirectProvider.useEffect": ()=>{
            if (!isInitialized) return;
            console.log("ApiRedirectProvider: Auth state initialized", {
                pathname,
                accessToken: accessToken ? "exists" : "missing"
            });
        // We're not redirecting here anymore to avoid conflicts with middleware
        // This helps prevent redirect loops between login and dashboard
        }
    }["ApiRedirectProvider.useEffect"], [
        accessToken,
        isInitialized,
        pathname
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(ApiRedirectProvider, "+j3RAEr8ubju5mbPlGtC9KYQHaQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"]
    ];
});
_c = ApiRedirectProvider;
var _c;
__turbopack_context__.k.register(_c, "ApiRedirectProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/button.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>Button),
    "buttonVariants": (()=>buttonVariants)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
;
;
const buttonVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", {
    variants: {
        variant: {
            default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
            destructive: "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
            outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
            secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
            ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
            link: "text-primary underline-offset-4 hover:underline"
        },
        size: {
            default: "h-9 px-4 py-2 has-[>svg]:px-3",
            sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
            lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
            icon: "size-9"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
function Button({ className, variant, size, asChild = false, ...props }) {
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slot"] : "button";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        "data-slot": "button",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(buttonVariants({
            variant,
            size,
            className
        })),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/button.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
_c = Button;
;
var _c;
__turbopack_context__.k.register(_c, "Button");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/alert-dialog.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AlertDialog": (()=>AlertDialog),
    "AlertDialogAction": (()=>AlertDialogAction),
    "AlertDialogCancel": (()=>AlertDialogCancel),
    "AlertDialogContent": (()=>AlertDialogContent),
    "AlertDialogDescription": (()=>AlertDialogDescription),
    "AlertDialogFooter": (()=>AlertDialogFooter),
    "AlertDialogHeader": (()=>AlertDialogHeader),
    "AlertDialogOverlay": (()=>AlertDialogOverlay),
    "AlertDialogPortal": (()=>AlertDialogPortal),
    "AlertDialogTitle": (()=>AlertDialogTitle),
    "AlertDialogTrigger": (()=>AlertDialogTrigger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$alert$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-alert-dialog/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
"use client";
;
;
;
;
function AlertDialog({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$alert$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        "data-slot": "alert-dialog",
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/alert-dialog.tsx",
        lineNumber: 12,
        columnNumber: 10
    }, this);
}
_c = AlertDialog;
function AlertDialogTrigger({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$alert$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Trigger"], {
        "data-slot": "alert-dialog-trigger",
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/alert-dialog.tsx",
        lineNumber: 19,
        columnNumber: 5
    }, this);
}
_c1 = AlertDialogTrigger;
function AlertDialogPortal({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$alert$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Portal"], {
        "data-slot": "alert-dialog-portal",
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/alert-dialog.tsx",
        lineNumber: 27,
        columnNumber: 5
    }, this);
}
_c2 = AlertDialogPortal;
function AlertDialogOverlay({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$alert$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Overlay"], {
        "data-slot": "alert-dialog-overlay",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/alert-dialog.tsx",
        lineNumber: 36,
        columnNumber: 5
    }, this);
}
_c3 = AlertDialogOverlay;
function AlertDialogContent({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AlertDialogPortal, {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AlertDialogOverlay, {}, void 0, false, {
                fileName: "[project]/src/components/ui/alert-dialog.tsx",
                lineNumber: 53,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$alert$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Content"], {
                "data-slot": "alert-dialog-content",
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg", className),
                ...props
            }, void 0, false, {
                fileName: "[project]/src/components/ui/alert-dialog.tsx",
                lineNumber: 54,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/alert-dialog.tsx",
        lineNumber: 52,
        columnNumber: 5
    }, this);
}
_c4 = AlertDialogContent;
function AlertDialogHeader({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "alert-dialog-header",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-col gap-2 text-center sm:text-left", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/alert-dialog.tsx",
        lineNumber: 71,
        columnNumber: 5
    }, this);
}
_c5 = AlertDialogHeader;
function AlertDialogFooter({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "alert-dialog-footer",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/alert-dialog.tsx",
        lineNumber: 84,
        columnNumber: 5
    }, this);
}
_c6 = AlertDialogFooter;
function AlertDialogTitle({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$alert$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Title"], {
        "data-slot": "alert-dialog-title",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-lg font-semibold", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/alert-dialog.tsx",
        lineNumber: 100,
        columnNumber: 5
    }, this);
}
_c7 = AlertDialogTitle;
function AlertDialogDescription({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$alert$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Description"], {
        "data-slot": "alert-dialog-description",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/alert-dialog.tsx",
        lineNumber: 113,
        columnNumber: 5
    }, this);
}
_c8 = AlertDialogDescription;
function AlertDialogAction({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$alert$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Action"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buttonVariants"])(), className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/alert-dialog.tsx",
        lineNumber: 126,
        columnNumber: 5
    }, this);
}
_c9 = AlertDialogAction;
function AlertDialogCancel({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$alert$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Cancel"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buttonVariants"])({
            variant: "outline"
        }), className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/alert-dialog.tsx",
        lineNumber: 138,
        columnNumber: 5
    }, this);
}
_c10 = AlertDialogCancel;
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10;
__turbopack_context__.k.register(_c, "AlertDialog");
__turbopack_context__.k.register(_c1, "AlertDialogTrigger");
__turbopack_context__.k.register(_c2, "AlertDialogPortal");
__turbopack_context__.k.register(_c3, "AlertDialogOverlay");
__turbopack_context__.k.register(_c4, "AlertDialogContent");
__turbopack_context__.k.register(_c5, "AlertDialogHeader");
__turbopack_context__.k.register(_c6, "AlertDialogFooter");
__turbopack_context__.k.register(_c7, "AlertDialogTitle");
__turbopack_context__.k.register(_c8, "AlertDialogDescription");
__turbopack_context__.k.register(_c9, "AlertDialogAction");
__turbopack_context__.k.register(_c10, "AlertDialogCancel");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/auth/context/auth-error-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthErrorProvider": (()=>AuthErrorProvider),
    "useAuthError": (()=>useAuthError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/alert-dialog.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-auth-tokens.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
const AuthErrorContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthErrorProvider({ children }) {
    _s();
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { clearTokens } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const showAuthError = (message)=>{
        setError(message);
    };
    const clearAuthError = ()=>{
        setError(null);
    };
    const handleLogout = ()=>{
        // Clear tokens
        clearTokens();
        // Clear auth header
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.removeItem("token");
            localStorage.removeItem("refreshToken");
        }
        // Clear query cache
        queryClient.clear();
        // Clear the error
        clearAuthError();
        // Redirect to login
        router.replace("/login");
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthErrorContext.Provider, {
        value: {
            showAuthError,
            clearAuthError
        },
        children: [
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialog"], {
                open: !!error,
                onOpenChange: ()=>setError(null),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialogContent"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialogHeader"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialogTitle"], {
                                    children: "Authentication Error"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/auth/context/auth-error-context.tsx",
                                    lineNumber: 65,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialogDescription"], {
                                    children: error || "Your session has expired. Please log in again."
                                }, void 0, false, {
                                    fileName: "[project]/src/features/auth/context/auth-error-context.tsx",
                                    lineNumber: 66,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/auth/context/auth-error-context.tsx",
                            lineNumber: 64,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialogFooter"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialogAction"], {
                                onClick: handleLogout,
                                children: "Go to Login"
                            }, void 0, false, {
                                fileName: "[project]/src/features/auth/context/auth-error-context.tsx",
                                lineNumber: 71,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/auth/context/auth-error-context.tsx",
                            lineNumber: 70,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/auth/context/auth-error-context.tsx",
                    lineNumber: 63,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/auth/context/auth-error-context.tsx",
                lineNumber: 62,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/auth/context/auth-error-context.tsx",
        lineNumber: 59,
        columnNumber: 5
    }, this);
}
_s(AuthErrorProvider, "Tmn7k+hAN3nq/SMfSoOVUv1yaFI=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$auth$2d$tokens$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthTokens"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"]
    ];
});
_c = AuthErrorProvider;
function useAuthError() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthErrorContext);
    if (context === undefined) {
        throw new Error("useAuthError must be used within an AuthErrorProvider");
    }
    return context;
}
_s1(useAuthError, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "AuthErrorProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/providers/auth-error-handler.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthErrorHandler": (()=>AuthErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$context$2f$auth$2d$error$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/auth/context/auth-error-context.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
function AuthErrorHandler({ children }) {
    _s();
    const { showAuthError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$context$2f$auth$2d$error$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthError"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthErrorHandler.useEffect": ()=>{
            // Set up the auth error callback
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setAuthErrorCallback({
                "AuthErrorHandler.useEffect": (message)=>{
                    showAuthError(message);
                }
            }["AuthErrorHandler.useEffect"]);
            // Cleanup function
            return ({
                "AuthErrorHandler.useEffect": ()=>{
                    // Reset the auth error callback to a no-op function
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setAuthErrorCallback({
                        "AuthErrorHandler.useEffect": (message)=>{
                            console.warn("Auth error callback not set, auth error:", message);
                        }
                    }["AuthErrorHandler.useEffect"]);
                }
            })["AuthErrorHandler.useEffect"];
        }
    }["AuthErrorHandler.useEffect"], [
        showAuthError
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(AuthErrorHandler, "b8xb/2oPqH1DOaTY0S6ZDhZ23Z4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$context$2f$auth$2d$error$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthError"]
    ];
});
_c = AuthErrorHandler;
var _c;
__turbopack_context__.k.register(_c, "AuthErrorHandler");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Toaster": (()=>Toaster)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
const Toaster = ({ ...props })=>{
    _s();
    const { theme = "system" } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Toaster"], {
        theme: theme,
        className: "toaster group",
        style: {
            "--normal-bg": "var(--popover)",
            "--normal-text": "var(--popover-foreground)",
            "--normal-border": "var(--border)"
        },
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/sonner.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
};
_s(Toaster, "EriOrahfenYKDCErPq+L6926Dw4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"]
    ];
});
_c = Toaster;
;
var _c;
__turbopack_context__.k.register(_c, "Toaster");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/users/api/rbac-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "rbacService": (()=>rbacService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
;
const rbacService = {
    // Get all roles
    getRoles: async ()=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/rbac/roles");
    },
    // Get role by ID
    getRole: async (id)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/rbac/roles/${id}`);
    },
    // Create role
    createRole: async (roleData)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/rbac/roles", roleData);
    },
    // Update role
    updateRole: async (id, roleData)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/rbac/roles/${id}`, roleData);
    },
    // Delete role
    deleteRole: async (id)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/rbac/roles/${id}`);
    },
    // Get all permissions
    getPermissions: async ()=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/rbac/permissions");
    },
    // Get all grants
    getAllGrants: async ()=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/rbac/grants");
    },
    // Get grants for a specific role
    getRoleGrants: async (role)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/rbac/grants/${role}`);
    },
    // Get all available resources and actions
    getResourcesAndActions: async ()=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/rbac/resources");
    },
    // Create or update a grant
    updateGrant: async (data)=>{
        // Validate required fields
        if (!data.role || !data.resource || !data.action) {
            throw new Error("Role, resource, and action are required");
        }
        // Validate action format
        const validActionPattern = /^(create|read|update|delete):(any|own)$/;
        if (!validActionPattern.test(data.action)) {
            throw new Error(`Invalid action format: ${data.action}. Must be in format 'operation:scope' (e.g., 'create:any', 'read:own')`);
        }
        // Create a deep copy of the data to avoid modifying the original
        const formattedData = JSON.parse(JSON.stringify(data));
        // Parse the action to get operation and scope
        const [operation, scope] = data.action.split(":");
        // COMPREHENSIVE FIX: Handle all action types properly
        // 1. Handle null/undefined/invalid attributes for all action types
        if (!formattedData.attributes || typeof formattedData.attributes !== "object" || Array.isArray(formattedData.attributes) || Object.keys(formattedData.attributes).length === 0) {
            console.log(`Invalid or missing attributes for ${data.action}, using wildcard`);
            formattedData.attributes = {
                "*": true
            };
        } else {
            // For create:any and read:any, ALWAYS use wildcard attributes
            if ((operation === "create" || operation === "read") && scope === "any") {
                console.log(`CRITICAL FIX: Using wildcard attributes for ${data.action} action`);
                formattedData.attributes = {
                    "*": true
                };
            } else if ((operation === "update" || operation === "delete") && scope === "any") {
                // If wildcard is already present, ensure it's properly formatted
                if (formattedData.attributes.hasOwnProperty("*")) {
                    console.log(`Ensuring proper wildcard format for ${data.action}`);
                    formattedData.attributes = {
                        "*": true
                    };
                } else {
                    console.log(`Validating specific attributes for ${data.action}`);
                    Object.keys(formattedData.attributes).forEach((key)=>{
                        if (typeof formattedData.attributes[key] !== "boolean") {
                            formattedData.attributes[key] = true;
                        }
                    });
                    // Check if any attributes are true, if not use wildcard
                    const hasTrueAttribute = Object.values(formattedData.attributes).some((val)=>val === true);
                    if (!hasTrueAttribute) {
                        console.log(`No true attributes found for ${data.action}, using wildcard`);
                        formattedData.attributes = {
                            "*": true
                        };
                    }
                }
            } else if (scope === "own") {
                console.log(`Processing ${data.action} with 'own' scope`);
                // Ensure all values are booleans
                Object.keys(formattedData.attributes).forEach((key)=>{
                    if (typeof formattedData.attributes[key] !== "boolean") {
                        formattedData.attributes[key] = true;
                    }
                });
                // For read:own, ensure at least one attribute is true
                if (operation === "read") {
                    const hasTrueAttribute = Object.values(formattedData.attributes).some((val)=>val === true);
                    if (!hasTrueAttribute) {
                        console.log(`No true attributes found for ${data.action}, using wildcard`);
                        formattedData.attributes = {
                            "*": true
                        };
                    }
                }
            }
        }
        // Final safety check for all action types
        if (formattedData.attributes && typeof formattedData.attributes === "object" && !Array.isArray(formattedData.attributes)) {
            // If it's an empty object after processing, use wildcard
            if (Object.keys(formattedData.attributes).length === 0) {
                console.log(`Empty attributes object after processing for ${data.action}, using wildcard`);
                formattedData.attributes = {
                    "*": true
                };
            }
            // Double-check for read:any and create:any to ensure wildcard attributes
            if ((operation === "create" || operation === "read") && scope === "any") {
                if (!formattedData.attributes.hasOwnProperty("*") || formattedData.attributes["*"] !== true) {
                    console.log(`FINAL SAFETY CHECK: Fixing attributes for ${data.action} action`);
                    formattedData.attributes = {
                        "*": true
                    };
                }
            }
        } else {
            // If attributes somehow became invalid during processing, use wildcard
            console.warn(`Invalid attributes after processing for ${data.action}, using wildcard`);
            formattedData.attributes = {
                "*": true
            };
        }
        console.log("Sending formatted data to API:", formattedData);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/rbac/grants", formattedData);
    },
    // Delete a grant
    deleteGrant: async (role, resource, action)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/rbac/grants/${role}/${resource}/${action}`);
    },
    // Refresh the RBAC cache
    refreshCache: async ()=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/rbac/refresh");
    },
    // Update role grants
    updateRoleGrants: async (roleId, grants)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/rbac/grants/${roleId}`, {
            grants
        }, {
            withCredentials: true
        });
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/use-toast.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useToast": (()=>useToast)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
"use client";
;
function useToast() {
    return {
        toast: {
            success: (title, props)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(title, props);
            },
            error: (title, props)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(title, props);
            },
            warning: (title, props)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warning(title, props);
            },
            info: (title, props)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info(title, props);
            },
            message: (title, props)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"])(title, props);
            }
        }
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/auth/context/permission-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PermissionProvider": (()=>PermissionProvider),
    "usePermissionContext": (()=>usePermissionContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/auth/hooks/use-auth.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$users$2f$api$2f$rbac$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/users/api/rbac-service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/use-toast.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
const PermissionContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function PermissionProvider({ children }) {
    _s();
    const { data: user, isLoading: isUserLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCurrentUser"])();
    const [permissions, setPermissions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const fetchPermissions = async ()=>{
        if (!user) return;
        try {
            setIsLoading(true);
            setError(null);
            // Get the user's role
            const roleName = user.role_name;
            // Fetch permissions for this role
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$users$2f$api$2f$rbac$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rbacService"].getRoleGrants(roleName);
            // Set the permissions in state
            setPermissions(response.grants);
        } catch (err) {
            console.error("Error fetching permissions:", err);
            setError("Failed to load permissions");
            toast.error("Error", {
                description: "Failed to load permissions. Some features may be unavailable."
            });
        } finally{
            setIsLoading(false);
        }
    };
    // Fetch permissions when the user data is loaded
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PermissionProvider.useEffect": ()=>{
            if (!isUserLoading && user) {
                fetchPermissions();
            }
        }
    }["PermissionProvider.useEffect"], [
        isUserLoading,
        user
    ]);
    // Function to refresh permissions
    const refreshPermissions = async ()=>{
        await fetchPermissions();
    };
    // Helper function to log available grants for debugging
    const logAvailableGrants = (resource)=>{
        if (!permissions) {
            console.log(`[PermissionContext] No permissions loaded yet`);
            return;
        }
        if (!permissions[resource]) {
            console.log(`[PermissionContext] No grants found for resource: ${resource}`);
            return;
        }
        console.log(`[PermissionContext] Available grants for resource ${resource}:`);
        Object.keys(permissions[resource]).forEach((action)=>{
            console.log(`  - ${action}`);
        });
    };
    // Function to check if a user has a specific permission
    const hasPermission = (resource, action, scope = "any")=>{
        // If permissions are still loading or there's an error, deny access
        if (isLoading || error || !permissions || !user) {
            return false;
        }
        // Format the action string
        const formattedAction = `${action}:${scope}`;
        // Special case for company_admin, super_admin, and float_manager
        if (user.role_name === "company_admin" || user.role_name === "super_admin" || user.role_name === "float_manager") {
            // For debugging purposes, still check if the permission exists
            if (permissions[resource]) {
                const hasAction = !!permissions[resource][formattedAction];
                if (!hasAction) {
                    console.log(`[PermissionContext] Admin override: ${resource}:${formattedAction} not found in grants but access granted`);
                }
            } else {
                console.log(`[PermissionContext] Admin override: Resource ${resource} not found in grants but access granted`);
            }
            // Always grant access to admin roles and float_manager
            return true;
        }
        // For accountant role, check if they have the permission or a related permission
        if (user.role_name === "accountant") {
            // Check if the user has the resource permission
            if (!permissions[resource]) {
                // For accountants, check if they have access to related resources
                // For example, if checking for "banking" but they have "financial_reports"
                if (resource === "banking" && permissions["financial_reports"] || resource === "expenses" && permissions["expense_reports"] || resource === "inventory" && permissions["stock_reports"]) {
                    console.log(`[PermissionContext] Accountant has access to related resource for: ${resource}`);
                    return true;
                }
                // For debugging, log that the resource wasn't found
                console.log(`[PermissionContext] Resource not found for accountant: ${resource}`);
                console.log(`[PermissionContext] Available resources for accountant:`, Object.keys(permissions));
                return false;
            }
            // Check if the resource has the action
            const hasAction = !!permissions[resource][formattedAction];
            // For debugging, log the result
            if (!hasAction) {
                // For accountants, check if they have a read permission when checking for other actions
                if (action.startsWith("create:") || action.startsWith("update:") || action.startsWith("delete:")) {
                    const readAction = `read:${scope}`;
                    if (permissions[resource][readAction]) {
                        console.log(`[PermissionContext] Accountant has read permission for: ${resource}`);
                        // For accountants, allow read access but not write access
                        return false;
                    }
                }
                console.log(`[PermissionContext] Action not found for accountant: ${formattedAction} for resource: ${resource}`);
                logAvailableGrants(resource);
            }
            return hasAction;
        }
        // For branch_admin role, be more permissive
        if (user.role_name === "branch_admin") {
            // Check if the user has the resource permission
            if (!permissions[resource]) {
                // For branch_admin, allow access to certain resources even if not explicitly granted
                if (resource === "dashboard" || resource === "profile" || resource === "employees" || resource === "inventory" || resource === "products" || resource === "sales" || resource === "pos_sessions" || resource === "customers") {
                    console.log(`[PermissionContext] Branch admin has implicit access to: ${resource}`);
                    return true;
                }
                // For debugging, log that the resource wasn't found
                console.log(`[PermissionContext] Resource not found for branch_admin: ${resource}`);
                console.log(`[PermissionContext] Available resources for branch_admin:`, Object.keys(permissions));
                return false;
            }
            // Check if the resource has the action
            const hasAction = !!permissions[resource][formattedAction];
            // For debugging, log the result
            if (!hasAction) {
                console.log(`[PermissionContext] Action not found for branch_admin: ${formattedAction} for resource: ${resource}`);
                logAvailableGrants(resource);
            }
            return hasAction;
        }
        // For all other roles, check if they have the exact permission
        // Check if the user has the resource permission
        if (!permissions[resource]) {
            // For debugging, log that the resource wasn't found
            console.log(`[PermissionContext] Resource not found: ${resource}`);
            // Log all available resources for debugging
            console.log(`[PermissionContext] Available resources:`, Object.keys(permissions));
            return false;
        }
        // Check if the resource has the action
        const hasAction = !!permissions[resource][formattedAction];
        // For debugging, log the result
        if (!hasAction) {
            console.log(`[PermissionContext] Action not found: ${formattedAction} for resource: ${resource}`);
            logAvailableGrants(resource);
        }
        return hasAction;
    };
    const value = {
        permissions,
        isLoading,
        error,
        refreshPermissions,
        hasPermission,
        logAvailableGrants
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PermissionContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/features/auth/context/permission-context.tsx",
        lineNumber: 282,
        columnNumber: 5
    }, this);
}
_s(PermissionProvider, "WpL5Cmf7kQuMUHb+udHhyJ5eRxk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCurrentUser"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
_c = PermissionProvider;
function usePermissionContext() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(PermissionContext);
    if (context === undefined) {
        throw new Error("usePermissionContext must be used within a PermissionProvider");
    }
    return context;
}
_s1(usePermissionContext, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "PermissionProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/inventory/context/bulk-transfer-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BulkTransferProvider": (()=>BulkTransferProvider),
    "useBulkTransfer": (()=>useBulkTransfer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
const BulkTransferContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Storage keys
const TRANSFER_ITEMS_STORAGE_KEY = "bulk_transfer_items";
const TRANSFER_NOTES_STORAGE_KEY = "bulk_transfer_notes";
const BulkTransferProvider = ({ children })=>{
    _s();
    const [transferItems, setTransferItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [notes, setNotes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load data from localStorage on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BulkTransferProvider.useEffect": ()=>{
            try {
                // Only run on client-side
                if ("TURBOPACK compile-time truthy", 1) {
                    const storedItems = localStorage.getItem(TRANSFER_ITEMS_STORAGE_KEY);
                    const storedNotes = localStorage.getItem(TRANSFER_NOTES_STORAGE_KEY);
                    if (storedItems) {
                        setTransferItems(JSON.parse(storedItems));
                    }
                    if (storedNotes) {
                        setNotes(storedNotes);
                    }
                    setIsInitialized(true);
                }
            } catch (error) {
                console.error("Error loading bulk transfer data from localStorage:", error);
                setIsInitialized(true);
            }
        }
    }["BulkTransferProvider.useEffect"], []);
    // Save transferItems to localStorage whenever it changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BulkTransferProvider.useEffect": ()=>{
            if (isInitialized && "object" !== "undefined" && transferItems.length > 0) {
                localStorage.setItem(TRANSFER_ITEMS_STORAGE_KEY, JSON.stringify(transferItems));
            }
        }
    }["BulkTransferProvider.useEffect"], [
        transferItems,
        isInitialized
    ]);
    // Save notes to localStorage whenever it changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BulkTransferProvider.useEffect": ()=>{
            if (isInitialized && "object" !== "undefined" && notes !== "") {
                localStorage.setItem(TRANSFER_NOTES_STORAGE_KEY, notes);
            }
        }
    }["BulkTransferProvider.useEffect"], [
        notes,
        isInitialized
    ]);
    const addTransferItem = (item)=>{
        setTransferItems((prev)=>[
                ...prev,
                item
            ]);
    };
    const updateTransferItem = (index, item)=>{
        setTransferItems((prev)=>{
            const newItems = [
                ...prev
            ];
            newItems[index] = {
                ...newItems[index],
                ...item
            };
            return newItems;
        });
    };
    const removeTransferItem = (index)=>{
        setTransferItems((prev)=>prev.filter((_, i)=>i !== index));
    };
    const clearTransferItems = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useCallback({
        "BulkTransferProvider.useCallback[clearTransferItems]": ()=>{
            // Check if there's anything to clear to avoid unnecessary state updates
            if (transferItems.length > 0 || notes !== "") {
                // Clear localStorage first to prevent the useEffect from triggering again
                if ("TURBOPACK compile-time truthy", 1) {
                    localStorage.removeItem(TRANSFER_ITEMS_STORAGE_KEY);
                    localStorage.removeItem(TRANSFER_NOTES_STORAGE_KEY);
                }
                // Then update state
                setTransferItems([]);
                setNotes("");
            }
        }
    }["BulkTransferProvider.useCallback[clearTransferItems]"], [
        transferItems.length,
        notes
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(BulkTransferContext.Provider, {
        value: {
            transferItems,
            addTransferItem,
            updateTransferItem,
            removeTransferItem,
            clearTransferItems,
            notes,
            setNotes,
            isInitialized
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/features/inventory/context/bulk-transfer-context.tsx",
        lineNumber: 98,
        columnNumber: 5
    }, this);
};
_s(BulkTransferProvider, "1ULuS0GljYtRUqtQsZxHCq/x9ik=");
_c = BulkTransferProvider;
const useBulkTransfer = ()=>{
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(BulkTransferContext);
    if (context === undefined) {
        throw new Error("useBulkTransfer must be used within a BulkTransferProvider");
    }
    return context;
};
_s1(useBulkTransfer, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "BulkTransferProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/providers.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Providers": (()=>Providers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$global$2d$search$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/global-search.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$api$2d$redirect$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/providers/api-redirect-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$auth$2d$error$2d$handler$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/providers/auth-error-handler.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$loading$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/providers/loading-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$search$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/providers/search-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sonner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$context$2f$auth$2d$error$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/auth/context/auth-error-context.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$context$2f$permission$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/auth/context/permission-context.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$context$2f$bulk$2d$transfer$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/inventory/context/bulk-transfer-context.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$react$2d$query$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/react-query.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
function Providers({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$react$2d$query$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryClient"],
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$context$2f$auth$2d$error$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthErrorProvider"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$auth$2d$error$2d$handler$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthErrorHandler"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$api$2d$redirect$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApiRedirectProvider"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$loading$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LoadingProvider"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GlobalLoadingIndicator, {}, void 0, false, {
                                fileName: "[project]/src/app/providers.tsx",
                                lineNumber: 27,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$auth$2f$context$2f$permission$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PermissionProvider"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$search$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SearchProvider"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$inventory$2f$context$2f$bulk$2d$transfer$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BulkTransferProvider"], {
                                        children: [
                                            children,
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$global$2d$search$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GlobalSearch"], {}, void 0, false, {
                                                fileName: "[project]/src/app/providers.tsx",
                                                lineNumber: 32,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sonner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Toaster"], {
                                                position: "top-right",
                                                richColors: true
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/providers.tsx",
                                                lineNumber: 33,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/providers.tsx",
                                        lineNumber: 30,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/providers.tsx",
                                    lineNumber: 29,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/providers.tsx",
                                lineNumber: 28,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/providers.tsx",
                        lineNumber: 26,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/providers.tsx",
                    lineNumber: 25,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/providers.tsx",
                lineNumber: 24,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/providers.tsx",
            lineNumber: 23,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/providers.tsx",
        lineNumber: 22,
        columnNumber: 5
    }, this);
}
_c = Providers;
// Component that monitors React Query loading state and navigation
function GlobalLoadingIndicator() {
    _s();
    const { setLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$loading$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLoading"])();
    const [isAnyQueryLoading, setIsAnyQueryLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isNavigating, setIsNavigating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Track navigation state changes
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    // Show loader on navigation
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GlobalLoadingIndicator.useEffect": ()=>{
            // When the path or search params change, show the loader
            setIsNavigating(true);
            // Use setTimeout to ensure this doesn't happen during rendering
            setTimeout({
                "GlobalLoadingIndicator.useEffect": ()=>{
                    setLoading("api", true);
                }
            }["GlobalLoadingIndicator.useEffect"], 0);
            // Hide the loader after a short delay
            const navigationTimeout = setTimeout({
                "GlobalLoadingIndicator.useEffect.navigationTimeout": ()=>{
                    setIsNavigating(false);
                    // Only hide the loader if there are no active queries
                    if (!isAnyQueryLoading) {
                        setLoading("api", false);
                    }
                }
            }["GlobalLoadingIndicator.useEffect.navigationTimeout"], 300); // Short delay to ensure the loader is visible during navigation
            return ({
                "GlobalLoadingIndicator.useEffect": ()=>{
                    clearTimeout(navigationTimeout);
                }
            })["GlobalLoadingIndicator.useEffect"];
        }
    }["GlobalLoadingIndicator.useEffect"], [
        pathname,
        searchParams,
        setLoading,
        isAnyQueryLoading
    ]);
    // Check for loading queries periodically
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GlobalLoadingIndicator.useEffect": ()=>{
            let timeoutId;
            const checkLoadingState = {
                "GlobalLoadingIndicator.useEffect.checkLoadingState": ()=>{
                    try {
                        // Get all queries from the cache
                        const queries = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$react$2d$query$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryClient"].getQueryCache().getAll();
                        const mutations = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$react$2d$query$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryClient"].getMutationCache().getAll();
                        // Check if any query or mutation is loading
                        const hasLoadingQueries = queries.some({
                            "GlobalLoadingIndicator.useEffect.checkLoadingState.hasLoadingQueries": (query)=>query.state.status === "loading"
                        }["GlobalLoadingIndicator.useEffect.checkLoadingState.hasLoadingQueries"]);
                        const hasLoadingMutations = mutations.some({
                            "GlobalLoadingIndicator.useEffect.checkLoadingState.hasLoadingMutations": (mutation)=>mutation.state.status === "loading"
                        }["GlobalLoadingIndicator.useEffect.checkLoadingState.hasLoadingMutations"]);
                        // Update loading state
                        const isLoading = hasLoadingQueries || hasLoadingMutations;
                        if (isLoading !== isAnyQueryLoading) {
                            setIsAnyQueryLoading(isLoading);
                            // Only update the global loading state if we're not navigating
                            // (navigation already sets the loading state)
                            if (!isNavigating) {
                                // Use setTimeout to ensure this doesn't happen during rendering
                                setTimeout({
                                    "GlobalLoadingIndicator.useEffect.checkLoadingState": ()=>{
                                        setLoading("api", isLoading);
                                    }
                                }["GlobalLoadingIndicator.useEffect.checkLoadingState"], 0);
                            }
                        }
                    } catch (error) {
                        console.error("Error checking query loading state:", error);
                    }
                    // Schedule next check
                    timeoutId = setTimeout(checkLoadingState, 100); // Check every 100ms
                }
            }["GlobalLoadingIndicator.useEffect.checkLoadingState"];
            // Start checking
            checkLoadingState();
            // Clean up
            return ({
                "GlobalLoadingIndicator.useEffect": ()=>{
                    clearTimeout(timeoutId);
                }
            })["GlobalLoadingIndicator.useEffect"];
        }
    }["GlobalLoadingIndicator.useEffect"], [
        setLoading,
        isAnyQueryLoading,
        isNavigating
    ]);
    // Add a global event listener for fetch requests
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GlobalLoadingIndicator.useEffect": ()=>{
            // Keep track of active fetch requests
            let activeFetchCount = 0;
            // Create a proxy for the original fetch function
            const originalFetch = window.fetch;
            window.fetch = ({
                "GlobalLoadingIndicator.useEffect": function(...args) {
                    // Increment the counter when a fetch request starts
                    activeFetchCount++;
                    if (activeFetchCount > 0 && !isNavigating && !isAnyQueryLoading) {
                        // Use setTimeout to ensure this doesn't happen during rendering
                        setTimeout({
                            "GlobalLoadingIndicator.useEffect": ()=>{
                                setLoading("api", true);
                            }
                        }["GlobalLoadingIndicator.useEffect"], 0);
                    }
                    // Call the original fetch function
                    return originalFetch.apply(this, args).finally({
                        "GlobalLoadingIndicator.useEffect": ()=>{
                            // Decrement the counter when the fetch request completes
                            activeFetchCount--;
                            if (activeFetchCount === 0 && !isNavigating && !isAnyQueryLoading) {
                                // Small delay to prevent flickering
                                setTimeout({
                                    "GlobalLoadingIndicator.useEffect": ()=>{
                                        setLoading("api", false);
                                    }
                                }["GlobalLoadingIndicator.useEffect"], 100);
                            }
                        }
                    }["GlobalLoadingIndicator.useEffect"]);
                }
            })["GlobalLoadingIndicator.useEffect"];
            // Restore the original fetch function when the component unmounts
            return ({
                "GlobalLoadingIndicator.useEffect": ()=>{
                    window.fetch = originalFetch;
                }
            })["GlobalLoadingIndicator.useEffect"];
        }
    }["GlobalLoadingIndicator.useEffect"], [
        setLoading,
        isNavigating,
        isAnyQueryLoading
    ]);
    return null;
}
_s(GlobalLoadingIndicator, "tpAt+eMlB8p2iMQ9URAoszXrXEI=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$loading$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLoading"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"]
    ];
});
_c1 = GlobalLoadingIndicator;
var _c, _c1;
__turbopack_context__.k.register(_c, "Providers");
__turbopack_context__.k.register(_c1, "GlobalLoadingIndicator");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/client-only.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ClientOnly": (()=>ClientOnly)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
function ClientOnly({ children }) {
    _s();
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ClientOnly.useEffect": ()=>{
            setMounted(true);
        }
    }["ClientOnly.useEffect"], []);
    return mounted ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false) : null;
}
_s(ClientOnly, "LrrVfNW3d1raFE0BNzCTILYmIfo=");
_c = ClientOnly;
var _c;
__turbopack_context__.k.register(_c, "ClientOnly");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_c4860ac2._.js.map