import {
  BankingSummary,
  BankingSummaryFilters,
  BankingFilters,
  ChartData,
  ReportFilterParams,
  Sale,
  SalesByCategoryData,
  SalesByEmployeeData,
  SalesByItemData,
  SalesByPaymentTypeData,
  TaxReportData,
  processSalesByItem,
  processSalesSummary,
} from "@/types";
import { PhoneRepair, PhoneRepairFilters } from "@/types/phone-repair";
import { MpesaTransaction } from "@/types/mpesa";
import { useQuery } from "@tanstack/react-query";
import { format, parseISO } from "date-fns";
import bankingService from "@/features/banking/api/banking-service";
import reportsService from "../api/reports-service";
// import apiClient from "@/lib/api-client";

/**
 * Hook to fetch sales summary report data
 */
export const useSalesSummaryReport = (filters?: ReportFilterParams) => {
  return useQuery({
    queryKey: ["reports", "sales-summary", filters],
    queryFn: async () => {
      // Create a copy of filters without employee_id for the API call
      // We now pass region_id to the API since we've updated the backend to support it
      const { employee_id, ...apiFilters } = filters || {};

      // Fetch sales data with region filter (API now supports region_id)
      const salesData = await reportsService.getSalesReport(apiFilters);

      // Apply employee filter client-side
      let filteredSalesData = salesData;

      // Apply payment method filter client-side if API doesn't handle it correctly
      if (apiFilters.payment_method_id) {
        filteredSalesData = filteredSalesData.filter(
          (sale) => sale.payment_method_id === apiFilters.payment_method_id
        );
      }

      // Apply employee filter client-side
      if (employee_id) {
        filteredSalesData = filteredSalesData.filter(
          (sale) => sale.employee_id === employee_id || sale.sale_employee_id === employee_id
        );
      }

      const summaryData = processSalesSummary(filteredSalesData);

      // Process data for charts
      const chartData = processChartData(filteredSalesData);

      return {
        salesData: filteredSalesData,
        summaryData,
        chartData,
      };
    },
  });
};

/**
 * Hook to fetch sales by item report data
 */
export const useSalesByItemReport = (filters?: ReportFilterParams) => {
  return useQuery({
    queryKey: ["reports", "sales-by-item", filters],
    queryFn: async () => {
      // Create a copy of filters without employee_id for the API call
      // We now pass region_id to the API since we've updated the backend to support it
      const { employee_id, ...apiFilters } = filters || {};

      // Fetch sales data with region filter (API now supports region_id)
      const salesData = await reportsService.getSalesByItemReport(apiFilters);

      // Apply employee filter client-side if needed
      let filteredSalesData = salesData;

      if (employee_id) {
        filteredSalesData = filteredSalesData.filter(
          (sale) => sale.employee_id === employee_id || sale.sale_employee_id === employee_id
        );
      }

      const itemsData = processSalesByItem(filteredSalesData);

      // Sort by total sales descending
      itemsData.sort((a, b) => b.totalSales - a.totalSales);

      // Process data for charts
      const chartData = processItemChartData(itemsData);

      return {
        salesData: filteredSalesData,
        itemsData,
        chartData,
      };
    },
  });
};

/**
 * Hook to fetch sales by category report data
 */
export const useSalesByCategoryReport = (filters?: ReportFilterParams) => {
  return useQuery({
    queryKey: ["reports", "sales-by-category", filters],
    queryFn: async () => {
      // Create a copy of filters without employee_id for the API call
      // We now pass region_id to the API since we've updated the backend to support it
      const { employee_id, ...apiFilters } = filters || {};

      // Fetch sales data with region filter (API now supports region_id)
      const salesData = await reportsService.getSalesByCategoryReport(apiFilters);

      // Process category data
      const categoryMap = new Map<number, SalesByCategoryData>();
      let totalSales = 0;

      salesData.forEach((sale) => {
        if (!sale.SaleItems) return;

        sale.SaleItems.forEach((item) => {
          if (!item.Product) return;

          // Get category information from the Product object
          // The backend now includes the ProductCategory model in the Product
          const categoryId = item.Product.category_id || 0;
          // Access the ProductCategory directly (no alias)
          const categoryName = item.Product.ProductCategory?.name || "Uncategorized";
          const saleAmount = parseFloat(item.total_price);
          const cost = parseFloat(item.buying_price) * item.quantity;
          const profit = saleAmount - cost;

          totalSales += saleAmount;

          const existingCategory = categoryMap.get(categoryId);
          if (existingCategory) {
            existingCategory.quantity += item.quantity;
            existingCategory.totalSales += saleAmount;
            existingCategory.profit += profit;
          } else {
            categoryMap.set(categoryId, {
              categoryId,
              categoryName,
              quantity: item.quantity,
              totalSales: saleAmount,
              profit,
              percentage: 0, // Will calculate after processing all sales
            });
          }
        });
      });

      // Calculate percentages
      const categoryData = Array.from(categoryMap.values());
      categoryData.forEach((category) => {
        category.percentage =
          totalSales > 0 ? (category.totalSales / totalSales) * 100 : 0;
      });

      // Sort by total sales descending
      categoryData.sort((a, b) => b.totalSales - a.totalSales);

      // Process data for charts
      const chartData = processCategoryChartData(categoryData);

      return {
        salesData,
        categoryData,
        chartData,
      };
    },
  });
};

/**
 * Hook to fetch sales by employee report data
 */
export const useSalesByEmployeeReport = (filters?: ReportFilterParams) => {
  return useQuery({
    queryKey: ["reports", "sales-by-employee", filters],
    queryFn: async () => {
      // Create a copy of filters without employee_id for the API call
      // We now pass region_id to the API since we've updated the backend to support it
      const { employee_id, ...apiFilters } = filters || {};

      // Fetch sales data with region filter (API now supports region_id)
      const salesData = await reportsService.getSalesByEmployeeReport(apiFilters);

      // Apply employee filter client-side if needed
      let filteredSalesData = salesData;
      if (employee_id) {
        filteredSalesData = filteredSalesData.filter(
          (sale) => sale.employee_id === employee_id || sale.sale_employee_id === employee_id || sale.user_id === employee_id
        );
      }

      // Process employee data
      const employeeMap = new Map<number, SalesByEmployeeData>();

      filteredSalesData.forEach((sale) => {
        if (!sale.User) return;

        const userId = sale.user_id;
        const userName = sale.User.name || "Unknown";
        const saleAmount = parseFloat(sale.total_amount);

        // Calculate profit
        let cost = 0;
        if (sale.SaleItems) {
          cost = sale.SaleItems.reduce(
            (sum, item) => sum + parseFloat(item.buying_price) * item.quantity,
            0
          );
        }
        const profit = saleAmount - cost;

        const existingEmployee = employeeMap.get(userId);
        if (existingEmployee) {
          existingEmployee.totalSales += saleAmount;
          existingEmployee.totalTransactions += 1;
          existingEmployee.profit += profit;
        } else {
          employeeMap.set(userId, {
            userId,
            userName,
            totalSales: saleAmount,
            totalTransactions: 1,
            averageSale: saleAmount,
            profit,
          });
        }
      });

      // Calculate average sale
      const employeeData = Array.from(employeeMap.values());
      employeeData.forEach((employee) => {
        employee.averageSale =
          employee.totalTransactions > 0
            ? employee.totalSales / employee.totalTransactions
            : 0;
      });

      // Sort by total sales descending
      employeeData.sort((a, b) => b.totalSales - a.totalSales);

      // Process data for charts
      const chartData = processEmployeeChartData(employeeData);

      return {
        salesData: filteredSalesData,
        employeeData,
        chartData,
      };
    },
  });
};

/**
 * Hook to fetch sales by payment type report data
 */
export const useSalesByPaymentTypeReport = (filters?: ReportFilterParams) => {
  return useQuery({
    queryKey: ["reports", "sales-by-payment-type", filters],
    queryFn: async () => {
      const salesData = await reportsService.getSalesByPaymentTypeReport(
        filters
      );

      // Process payment type data
      const paymentMap = new Map<number, SalesByPaymentTypeData>();
      let totalSales = 0;

      salesData.forEach((sale) => {
        if (!sale.PaymentMethod) return;

        const paymentMethodId = sale.payment_method_id;
        const paymentMethodName = sale.PaymentMethod.name || "Unknown";
        const saleAmount = parseFloat(sale.total_amount);

        totalSales += saleAmount;

        const existingPayment = paymentMap.get(paymentMethodId);
        if (existingPayment) {
          existingPayment.totalSales += saleAmount;
          existingPayment.totalTransactions += 1;
        } else {
          paymentMap.set(paymentMethodId, {
            paymentMethodId,
            paymentMethodName,
            totalSales: saleAmount,
            totalTransactions: 1,
            averageSale: saleAmount,
            percentage: 0, // Will calculate after processing all sales
          });
        }
      });

      // Calculate percentages and average sale
      const paymentData = Array.from(paymentMap.values());
      paymentData.forEach((payment) => {
        payment.percentage =
          totalSales > 0 ? (payment.totalSales / totalSales) * 100 : 0;
        payment.averageSale =
          payment.totalTransactions > 0
            ? payment.totalSales / payment.totalTransactions
            : 0;
      });

      // Sort by total sales descending
      paymentData.sort((a, b) => b.totalSales - a.totalSales);

      // Process data for charts
      const chartData = processPaymentChartData(paymentData);

      return {
        salesData,
        paymentData,
        chartData,
      };
    },
  });
};

/**
 * Hook to fetch shifts (POS sessions) report data
 */
export const useShiftsReport = (filters?: ReportFilterParams) => {
  return useQuery({
    queryKey: ["reports", "shifts", filters],
    queryFn: async () => {
      const shiftsData = await reportsService.getShiftsReport(filters);

      // Process shifts data
      const processedShifts = shiftsData.map((shift) => {
        // Use the total sales and transaction count from the backend if available
        // Otherwise, fall back to calculating from Sales array (for backward compatibility)
        let totalSales = 0;
        let totalTransactions = 0;

        if (shift.total_sales !== undefined && shift.transaction_count !== undefined) {
          // Use the values provided by the backend
          totalSales = parseFloat(shift.total_sales);
          totalTransactions = parseInt(shift.transaction_count);
          console.log(`Using backend totals for shift ${shift.id}: sales=${totalSales}, transactions=${totalTransactions}`);
        } else if (shift.Sales) {
          // Fall back to calculating from Sales array
          totalSales = shift.Sales.reduce(
            (sum: number, sale: any) => sum + parseFloat(sale.total_amount),
            0
          );
          totalTransactions = shift.Sales.length;
          console.log(`Calculated totals for shift ${shift.id}: sales=${totalSales}, transactions=${totalTransactions}`);
        }

        // ✅ Expenses calculation
        const expenses = shift.Expenses
          ? shift.Expenses.reduce((sum: number, expense: any) => {
              return expense.status === "approved"
                ? sum + parseFloat(expense.amount)
                : sum;
            }, 0)
          : 0;
        return {
          id: shift.id,
          startTime: shift.start_time,
          endTime: shift.end_time,
          status: shift.status,
          openingBalance: shift.opening_balance
            ? parseFloat(shift.opening_balance)
            : 0,
          closingBalance: shift.closing_balance
            ? parseFloat(shift.closing_balance)
            : null,
          totalSales,
          totalTransactions,
          mpesaTransactionCount: shift.mpesa_transaction_count || 0,
          cashPaidIn: shift.cash_paid_in
            ? parseFloat(shift.cash_paid_in)
            : null,
          cashPaidOut: shift.cash_paid_out
            ? parseFloat(shift.cash_paid_out)
            : null,
          discrepancies: shift.discrepancies
            ? parseFloat(shift.discrepancies)
            : null,
          userId: shift.user_id,
          userName: shift.User?.name || "Unknown",
          branchId: shift.branch_id,
          branchName: shift.Branch?.name || "Unknown",
          regionId: shift.Branch?.region_id || null,
          regionName: shift.Branch?.Region?.name || "Unknown",
          reconciliation: shift.reconciliation || null,
          expenses, // computed approved expenses total

        };
      });

      // Sort by start time descending
      processedShifts.sort(
        (a, b) =>
          new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
      );

      return {
        shiftsData: processedShifts,
      };
    },
  });
};

/**
 * Process sales data into chart data
 */
function processChartData(salesData: Sale[]): ChartData {
  // Group sales by date
  const salesByDate = new Map<string, number>();

  salesData.forEach((sale) => {
    const date = format(parseISO(sale.created_at), "yyyy-MM-dd");
    const amount = parseFloat(sale.total_amount);

    const existingAmount = salesByDate.get(date) || 0;
    salesByDate.set(date, existingAmount + amount);
  });

  // Sort dates
  const sortedDates = Array.from(salesByDate.keys()).sort();

  // Create chart data
  return {
    labels: sortedDates.map((date) => format(parseISO(date), "MMM dd")),
    datasets: [
      {
        label: "Sales",
        data: sortedDates.map((date) => salesByDate.get(date) || 0),
        backgroundColor: "rgba(59, 130, 246, 0.5)",
        borderColor: "rgb(59, 130, 246)",
        borderWidth: 2,
      },
    ],
  };
}

/**
 * Hook to fetch MPESA transactions report data
 */
export const useMpesaTransactionsReport = (filters?: ReportFilterParams) => {
  return useQuery({
    queryKey: ["reports", "mpesa-transactions", filters],
    queryFn: async () => {
      const transactionsData = await reportsService.getMpesaTransactionsReport(filters);

      // Process transactions data
      const depositsData = transactionsData.filter(tx => tx.type === 'deposit');
      const withdrawalsData = transactionsData.filter(tx => tx.type === 'withdrawal');

      // Calculate summary metrics
      const totalDeposits = depositsData.reduce((sum, tx) => sum + parseFloat(tx.amount as string), 0);
      const totalWithdrawals = withdrawalsData.reduce((sum, tx) => sum + parseFloat(tx.amount as string), 0);
      const totalTransactions = transactionsData.length;
      const netCashflow = totalDeposits - totalWithdrawals;

      // Process data for charts - group by date
      const depositsByDate = new Map<string, number>();
      const withdrawalsByDate = new Map<string, number>();

      transactionsData.forEach((tx) => {
        const date = format(parseISO(tx.transaction_date), "yyyy-MM-dd");
        const amount = parseFloat(tx.amount as string);

        if (tx.type === 'deposit') {
          const existingAmount = depositsByDate.get(date) || 0;
          depositsByDate.set(date, existingAmount + amount);
        } else if (tx.type === 'withdrawal') {
          const existingAmount = withdrawalsByDate.get(date) || 0;
          withdrawalsByDate.set(date, existingAmount + amount);
        }
      });

      // Sort dates
      const allDates = new Set([
        ...Array.from(depositsByDate.keys()),
        ...Array.from(withdrawalsByDate.keys())
      ]);
      const sortedDates = Array.from(allDates).sort();

      // Create chart data
      const chartData = {
        labels: sortedDates.map((date) => format(parseISO(date), "MMM dd")),
        datasets: [
          {
            label: "Deposits",
            data: sortedDates.map((date) => depositsByDate.get(date) || 0),
            backgroundColor: "rgba(16, 185, 129, 0.5)",
            borderColor: "rgb(16, 185, 129)",
            borderWidth: 2,
          },
          {
            label: "Withdrawals",
            data: sortedDates.map((date) => withdrawalsByDate.get(date) || 0),
            backgroundColor: "rgba(239, 68, 68, 0.5)",
            borderColor: "rgb(239, 68, 68)",
            borderWidth: 2,
          }
        ],
      };

      // Process data for summary table
      const summaryData = sortedDates.map(date => {
        const formattedDate = format(parseISO(date), "MMM dd, yyyy");
        const deposits = depositsByDate.get(date) || 0;
        const withdrawals = withdrawalsByDate.get(date) || 0;
        const net = deposits - withdrawals;

        return {
          date: formattedDate,
          rawDate: date,
          deposits,
          withdrawals,
          net
        };
      });

      // Sort by date descending (newest first)
      summaryData.sort((a, b) => new Date(b.rawDate).getTime() - new Date(a.rawDate).getTime());

      // Process data for branch summary
      const branchMap = new Map<number, {
        branchId: number;
        branchName: string;
        deposits: number;
        withdrawals: number;
        net: number;
        transactionCount: number;
      }>();

      transactionsData.forEach(tx => {
        if (!tx.Branch) return;

        const branchId = tx.branch_id;
        const branchName = tx.Branch.name;
        const amount = parseFloat(tx.amount as string);

        const existingBranch = branchMap.get(branchId);
        if (existingBranch) {
          if (tx.type === 'deposit') {
            existingBranch.deposits += amount;
          } else if (tx.type === 'withdrawal') {
            existingBranch.withdrawals += amount;
          }
          existingBranch.transactionCount += 1;
          existingBranch.net = existingBranch.deposits - existingBranch.withdrawals;
        } else {
          branchMap.set(branchId, {
            branchId,
            branchName,
            deposits: tx.type === 'deposit' ? amount : 0,
            withdrawals: tx.type === 'withdrawal' ? amount : 0,
            net: tx.type === 'deposit' ? amount : -amount,
            transactionCount: 1
          });
        }
      });

      const branchData = Array.from(branchMap.values());

      // Sort by total transaction amount descending
      branchData.sort((a, b) => (b.deposits + b.withdrawals) - (a.deposits + a.withdrawals));

      return {
        transactionsData,
        depositsData,
        withdrawalsData,
        summaryData,
        branchData,
        chartData,
        metrics: {
          totalDeposits,
          totalWithdrawals,
          totalTransactions,
          netCashflow
        }
      };
    },
  });
};

/**
 * Process items data into chart data
 */
function processItemChartData(itemsData: SalesByItemData[]): ChartData {
  // Take top 10 items
  const topItems = itemsData.slice(0, 10);

  return {
    labels: topItems.map((item) => item.productName),
    datasets: [
      {
        label: "Sales",
        data: topItems.map((item) => item.totalSales),
        backgroundColor: "rgba(16, 185, 129, 0.5)",
        borderColor: "rgb(16, 185, 129)",
        borderWidth: 2,
      },
    ],
  };
}

/**
 * Process category data into chart data
 */
function processCategoryChartData(
  categoryData: SalesByCategoryData[]
): ChartData {
  // Take top 10 categories
  const topCategories = categoryData.slice(0, 10);

  return {
    labels: topCategories.map((category) => category.categoryName),
    datasets: [
      {
        label: "Sales",
        data: topCategories.map((category) => category.totalSales),
        backgroundColor: [
          "rgba(59, 130, 246, 0.5)",
          "rgba(16, 185, 129, 0.5)",
          "rgba(245, 158, 11, 0.5)",
          "rgba(239, 68, 68, 0.5)",
          "rgba(139, 92, 246, 0.5)",
          "rgba(236, 72, 153, 0.5)",
          "rgba(6, 182, 212, 0.5)",
          "rgba(168, 85, 247, 0.5)",
          "rgba(234, 179, 8, 0.5)",
          "rgba(249, 115, 22, 0.5)",
        ],
        borderColor: [
          "rgb(59, 130, 246)",
          "rgb(16, 185, 129)",
          "rgb(245, 158, 11)",
          "rgb(239, 68, 68)",
          "rgb(139, 92, 246)",
          "rgb(236, 72, 153)",
          "rgb(6, 182, 212)",
          "rgb(168, 85, 247)",
          "rgb(234, 179, 8)",
          "rgb(249, 115, 22)",
        ],
        borderWidth: 2,
      },
    ],
  };
}

/**
 * Hook to fetch tax report data
 */
export const useTaxReport = (filters?: ReportFilterParams) => {
  return useQuery({
    queryKey: ["reports", "tax-report", filters],
    queryFn: async () => {
      // Create a copy of filters without employee_id for the API call
      const { /* employee_id, */ ...apiFilters } = filters || {};

      // Fetch sales data with region filter
      const salesData = await reportsService.getTaxReport(apiFilters);

      // Process tax data
      const vatRateMap = new Map<number, TaxReportData>();
      let totalVatAmount = 0;
      let totalTaxableSales = 0;
      let totalNonTaxableSales = 0;
      let totalTransactions = 0;

      // Group sales by date for chart data
      const vatByDate = new Map<string, number>();
      const taxableSalesByDate = new Map<string, number>();

      salesData.forEach((sale) => {
        const saleAmount = parseFloat(sale.total_amount);
        const vatAmount = parseFloat(sale.total_vat_amount || "0");
        const taxableAmount = parseFloat(sale.total_excluding_vat || "0");
        const date = format(parseISO(sale.created_at), "yyyy-MM-dd");

        // Add to date maps for charts
        const existingVatAmount = vatByDate.get(date) || 0;
        vatByDate.set(date, existingVatAmount + vatAmount);

        const existingTaxableAmount = taxableSalesByDate.get(date) || 0;
        taxableSalesByDate.set(date, existingTaxableAmount + taxableAmount);

        // Update totals
        totalVatAmount += vatAmount;

        if (vatAmount > 0) {
          totalTaxableSales += taxableAmount;
          totalTransactions += 1;
        } else {
          totalNonTaxableSales += saleAmount;
        }

        // Process sale items to get VAT rates
        if (sale.SaleItems) {
          sale.SaleItems.forEach((item) => {
            const vatRate = parseFloat(item.vat_rate || "0");
            if (vatRate <= 0) return; // Skip non-taxable items

            const itemVatAmount = parseFloat(item.vat_amount || "0");
            const itemTaxableAmount = parseFloat(item.price_excluding_vat || "0") * item.quantity;

            // Get VAT name from product if available
            const vatName = item.Product?.VatRate?.name || `${vatRate}% VAT`;

            const existingVatData = vatRateMap.get(vatRate);
            if (existingVatData) {
              existingVatData.vatAmount += itemVatAmount;
              existingVatData.taxableSales += itemTaxableAmount;
              existingVatData.transactionCount += 1;
            } else {
              vatRateMap.set(vatRate, {
                vatRate,
                vatName,
                vatAmount: itemVatAmount,
                taxableSales: itemTaxableAmount,
                transactionCount: 1,
              });
            }
          });
        }
      });

      // Convert map to array and sort by VAT rate
      const taxData = Array.from(vatRateMap.values());
      taxData.sort((a, b) => b.vatRate - a.vatRate);

      // Process chart data
      const sortedDates = Array.from(vatByDate.keys()).sort();

      const vatChartData: ChartData = {
        labels: sortedDates.map((date) => format(parseISO(date), "MMM dd")),
        datasets: [
          {
            label: "VAT Collected",
            data: sortedDates.map((date) => vatByDate.get(date) || 0),
            backgroundColor: "rgba(239, 68, 68, 0.5)",
            borderColor: "rgb(239, 68, 68)",
            borderWidth: 2,
          },
        ],
      };

      const taxableChartData: ChartData = {
        labels: sortedDates.map((date) => format(parseISO(date), "MMM dd")),
        datasets: [
          {
            label: "Taxable Sales",
            data: sortedDates.map((date) => taxableSalesByDate.get(date) || 0),
            backgroundColor: "rgba(16, 185, 129, 0.5)",
            borderColor: "rgb(16, 185, 129)",
            borderWidth: 2,
          },
        ],
      };

      return {
        salesData,
        taxData,
        summaryData: {
          totalVatAmount,
          totalTaxableSales,
          totalNonTaxableSales,
          totalTransactions,
        },
        vatChartData,
        taxableChartData,
      };
    },
  });
};

/**
 * Process employee data into chart data
 */
function processEmployeeChartData(
  employeeData: SalesByEmployeeData[]
): ChartData {
  return {
    labels: employeeData.map((employee) => employee.userName),
    datasets: [
      {
        label: "Sales",
        data: employeeData.map((employee) => employee.totalSales),
        backgroundColor: "rgba(245, 158, 11, 0.5)",
        borderColor: "rgb(245, 158, 11)",
        borderWidth: 2,
      },
    ],
  };
}

/**
 * Process payment type data into chart data
 */
function processPaymentChartData(
  paymentData: SalesByPaymentTypeData[]
): ChartData {
  return {
    labels: paymentData.map((payment) => payment.paymentMethodName),
    datasets: [
      {
        label: "Sales",
        data: paymentData.map((payment) => payment.totalSales),
        backgroundColor: [
          "rgba(59, 130, 246, 0.5)",
          "rgba(16, 185, 129, 0.5)",
          "rgba(245, 158, 11, 0.5)",
          "rgba(239, 68, 68, 0.5)",
          "rgba(139, 92, 246, 0.5)",
        ],
        borderColor: [
          "rgb(59, 130, 246)",
          "rgb(16, 185, 129)",
          "rgb(245, 158, 11)",
          "rgb(239, 68, 68)",
          "rgb(139, 92, 246)",
        ],
        borderWidth: 2,
      },
    ],
  };
}

/**
 * Hook to fetch Mpesa banking report data
 */
export const useMpesaBankingReport = (filters?: ReportFilterParams) => {
  // Convert ReportFilterParams to BankingSummaryFilters
  const bankingFilters: BankingSummaryFilters & { banking_method?: string } = {
    branch_id: filters?.branch_id, // Make branch_id optional
    start_date: filters?.start_date || format(new Date(), "yyyy-MM-dd"),
    end_date: filters?.end_date,
    banking_method: filters?.payment_method_id
      ? String(filters.payment_method_id)
      : undefined, // Use payment_method_id as banking_method if provided
  };

  return useQuery({
    queryKey: ["reports", "mpesa-banking", filters],
    queryFn: async () => {
      // Use the regular banking endpoint if branch_id is not provided
      let summaryData: BankingSummary[];
      if (bankingFilters.branch_id && bankingFilters.branch_id > 0) {
        summaryData = await bankingService.getBankingSummary(bankingFilters);
      } else {
        // Convert to BankingFilters
        const regularFilters: BankingFilters = {
          branch_id: bankingFilters.branch_id,
          start_date: bankingFilters.start_date,
          end_date: bankingFilters.end_date,
          banking_method: bankingFilters.banking_method as any,
        };
        summaryData = await bankingService.getBankingRecordsAsSummary(
          regularFilters
        );
      }

      // Process data for charts
      const chartData = processMpesaBankingChartData(summaryData);

      // Calculate totals and metrics
      const totalBank = summaryData.reduce(
        (sum: number, item: BankingSummary) => sum + item.bank,
        0
      );
      const totalMpesa = summaryData.reduce(
        (sum: number, item: BankingSummary) => sum + item.mpesa,
        0
      );
      const totalAgent = summaryData.reduce(
        (sum: number, item: BankingSummary) => sum + item.agent,
        0
      );
      const totalAmount = summaryData.reduce(
        (sum: number, item: BankingSummary) => sum + item.total,
        0
      );
      const totalTransactions = summaryData.reduce(
        (sum: number, item: BankingSummary) => sum + item.transaction_count,
        0
      );

      return {
        summaryData,
        chartData,
        metrics: {
          totalBank,
          totalMpesa,
          totalAgent,
          totalAmount,
          totalTransactions,
          averageTransaction:
            totalTransactions > 0 ? totalAmount / totalTransactions : 0,
        },
      };
    },
    enabled: !!bankingFilters.start_date, // Only require start_date
  });
};

/**
 * Process chart data for Mpesa banking
 */
function processMpesaBankingChartData(
  summaryData: BankingSummary[]
): ChartData {
  return {
    labels: summaryData.map((item) => format(new Date(item.date), "MMM dd")),
    datasets: [
      {
        label: "Bank",
        data: summaryData.map((item) => item.bank),
        backgroundColor: "rgba(59, 130, 246, 0.5)",
        borderColor: "rgb(59, 130, 246)",
        borderWidth: 2,
      },
      {
        label: "M-Pesa",
        data: summaryData.map((item) => item.mpesa),
        backgroundColor: "rgba(16, 185, 129, 0.5)",
        borderColor: "rgb(16, 185, 129)",
        borderWidth: 2,
      },
      {
        label: "Agent",
        data: summaryData.map((item) => item.agent),
        backgroundColor: "rgba(245, 158, 11, 0.5)",
        borderColor: "rgb(245, 158, 11)",
        borderWidth: 2,
      },
    ],
  };
}

/**
 * Hook to fetch DSA sales report data
 */
export const useDsaSalesReport = (filters?: ReportFilterParams) => {
  // Ensure is_dsa is set to true
  const dsaFilters: ReportFilterParams = {
    ...filters,
    is_dsa: true,
  };

  return useQuery({
    queryKey: ["reports", "dsa-sales", dsaFilters],
    queryFn: async () => {
      const salesData = await reportsService.getSalesReport(dsaFilters);
      const summaryData = processSalesSummary(salesData);

      // Process data for charts
      const chartData = processChartData(salesData);

      // Process data for payment methods
      const paymentMethodsMap = new Map<number, SalesByPaymentTypeData>();
      salesData.forEach((sale) => {
        if (sale.PaymentMethod) {
          const methodId = sale.PaymentMethod.id;
          const amount = parseFloat(sale.total_amount);

          if (!paymentMethodsMap.has(methodId)) {
            paymentMethodsMap.set(methodId, {
              paymentMethodId: methodId,
              paymentMethodName: sale.PaymentMethod.name,
              totalSales: 0,
              totalTransactions: 0,
              averageSale: 0,
              percentage: 0,
            });
          }

          const method = paymentMethodsMap.get(methodId)!;
          method.totalSales += amount;
          method.totalTransactions += 1;
        }
      });

      const paymentData = Array.from(paymentMethodsMap.values());

      // Calculate total sales for percentage calculation
      const totalSalesAmount = paymentData.reduce(
        (sum, method) => sum + method.totalSales,
        0
      );

      // Calculate average sale and percentage for each payment method
      paymentData.forEach((method) => {
        method.averageSale =
          method.totalTransactions > 0
            ? method.totalSales / method.totalTransactions
            : 0;
        method.percentage =
          totalSalesAmount > 0
            ? (method.totalSales / totalSalesAmount) * 100
            : 0;
      });

      paymentData.sort((a, b) => b.totalSales - a.totalSales);

      // Process data for agents
      const agentsMap = new Map<number, SalesByEmployeeData>();
      salesData.forEach((sale) => {
        if (sale.User) {
          const userId = sale.User.id;
          const amount = parseFloat(sale.total_amount);

          if (!agentsMap.has(userId)) {
            agentsMap.set(userId, {
              userId,
              userName: sale.User.name,
              totalSales: 0,
              totalTransactions: 0,
              averageSale: 0,
              profit: 0,
            });
          }

          const agent = agentsMap.get(userId)!;
          agent.totalSales += amount;
          agent.totalTransactions += 1;
        }
      });

      const agentData = Array.from(agentsMap.values());

      // Calculate average sale for each agent
      agentData.forEach((agent) => {
        agent.averageSale =
          agent.totalTransactions > 0
            ? agent.totalSales / agent.totalTransactions
            : 0;
        // For simplicity, we're setting profit to 30% of sales as we don't have item-level data here
        agent.profit = agent.totalSales * 0.3;
      });

      agentData.sort((a, b) => b.totalSales - a.totalSales);

      // Process payment method chart data
      const paymentChartData = processPaymentChartData(paymentData);

      return {
        salesData,
        summaryData,
        chartData,
        paymentData,
        agentData,
        paymentChartData,
      };
    },
  });
};

/**
 * Hook to fetch phone repairs report data
 */
export const usePhoneRepairsReport = (filters?: PhoneRepairFilters) => {
  return useQuery({
    queryKey: ["reports", "phone-repairs", filters],
    queryFn: async () => {
      const repairsData = await reportsService.getPhoneRepairsReport(filters);

      // Process data for charts
      const chartData = processPhoneRepairsChartData(repairsData);

      // Process data for status breakdown
      const statusData = processPhoneRepairsStatusData(repairsData);

      return {
        repairsData,
        chartData,
        statusData,
      };
    },
  });
};

/**
 * Process phone repairs data for charts
 */
function processPhoneRepairsChartData(repairs: PhoneRepair[]): ChartData {
  // Count repairs by status
  const statusCounts: Record<string, number> = {};
  repairs.forEach((repair) => {
    const status = repair.status;
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });

  // Count repairs by branch
  const branchCounts: Record<string, number> = {};
  repairs.forEach((repair) => {
    const branchName = repair.Branch?.name || "Unknown";
    branchCounts[branchName] = (branchCounts[branchName] || 0) + 1;
  });

  // Create chart data
  return {
    labels: Object.keys(statusCounts),
    datasets: [
      {
        label: "Repairs by Status",
        data: Object.values(statusCounts),
        backgroundColor: [
          "rgba(59, 130, 246, 0.5)",
          "rgba(16, 185, 129, 0.5)",
          "rgba(245, 158, 11, 0.5)",
          "rgba(239, 68, 68, 0.5)",
          "rgba(139, 92, 246, 0.5)",
        ],
        borderColor: [
          "rgb(59, 130, 246)",
          "rgb(16, 185, 129)",
          "rgb(245, 158, 11)",
          "rgb(239, 68, 68)",
          "rgb(139, 92, 246)",
        ],
        borderWidth: 2,
      },
    ],
    secondaryData: {
      labels: Object.keys(branchCounts),
      datasets: [
        {
          label: "Repairs by Branch",
          data: Object.values(branchCounts),
          backgroundColor: [
            "rgba(59, 130, 246, 0.5)",
            "rgba(16, 185, 129, 0.5)",
            "rgba(245, 158, 11, 0.5)",
            "rgba(239, 68, 68, 0.5)",
            "rgba(139, 92, 246, 0.5)",
          ],
          borderColor: [
            "rgb(59, 130, 246)",
            "rgb(16, 185, 129)",
            "rgb(245, 158, 11)",
            "rgb(239, 68, 68)",
            "rgb(139, 92, 246)",
          ],
          borderWidth: 2,
        },
      ],
    },
  };
}

/**
 * Process phone repairs data for status breakdown
 */
function processPhoneRepairsStatusData(repairs: PhoneRepair[]) {
  // Group repairs by status
  const statusGroups: Record<string, PhoneRepair[]> = {};
  repairs.forEach((repair) => {
    const status = repair.status;
    if (!statusGroups[status]) {
      statusGroups[status] = [];
    }
    statusGroups[status].push(repair);
  });

  // Calculate totals and averages
  const statusData = Object.entries(statusGroups).map(([status, repairs]) => {
    const count = repairs.length;

    // Calculate average estimated cost
    const totalEstimatedCost = repairs.reduce(
      (sum, repair) => sum + parseFloat(repair.estimated_cost || "0"),
      0
    );
    const avgEstimatedCost = count > 0 ? totalEstimatedCost / count : 0;

    // Calculate average actual cost for completed repairs
    const completedRepairs = repairs.filter((r) => r.amount_charged);
    const totalActualCost = completedRepairs.reduce(
      (sum, repair) => sum + parseFloat(repair.amount_charged || "0"),
      0
    );
    const avgActualCost =
      completedRepairs.length > 0
        ? totalActualCost / completedRepairs.length
        : 0;

    return {
      status,
      count,
      totalEstimatedCost,
      avgEstimatedCost,
      totalActualCost,
      avgActualCost,
      completedCount: completedRepairs.length,
    };
  });

  return statusData;
}
