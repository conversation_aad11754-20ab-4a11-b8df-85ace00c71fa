/**
 * Banking Transactions Report Controller
 */
const { BankingTransaction, BankingTransactionReceipt, Branch, Region, User, Bank } = require('../../models');
const AppError = require('../../utils/error');
const { Op } = require('sequelize');
const sequelize = require('../../../config/database');
const logger = require('../../utils/logger');
const moment = require('moment');
const ExcelJS = require('exceljs');

/**
 * Get banking transactions report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getBankingTransactionsReport = async (req, res, next) => {
  try {
    const {
      start_date,
      end_date,
      branch_id,
      region_id,
      banking_method,
      transaction_type,
      bank_id,
      status,
      format = 'json',
      page = 1,
      limit = 100
    } = req.query;

    // Build where clause for filtering
    const whereClause = {
      deleted_at: null
    };

    // Filter by date range if provided
    if (start_date && end_date) {
      whereClause.transaction_date = {
        [Op.between]: [start_date, end_date]
      };
    } else if (start_date) {
      whereClause.transaction_date = {
        [Op.gte]: start_date
      };
    } else if (end_date) {
      whereClause.transaction_date = {
        [Op.lte]: end_date
      };
    }

    // Filter by branch if provided
    if (branch_id) {
      whereClause.branch_id = branch_id;
    }

    // Handle region filtering - get all branches in the specified region
    if (region_id) {
      const branchesInRegion = await Branch.findAll({
        where: { region_id, deleted_at: null },
        attributes: ["id"],
      });

      const branchIds = branchesInRegion.map((branch) => branch.id);

      if (branchIds.length > 0) {
        // Remove any existing branch_id filter
        delete whereClause.branch_id;

        // Add the branch_id IN clause
        whereClause.branch_id = {
          [Op.in]: branchIds,
        };
      }
    }

    // Filter by banking method if provided
    if (banking_method) {
      whereClause.banking_method = banking_method;
    }

    // Filter by transaction type if provided
    if (transaction_type) {
      whereClause.transaction_type = transaction_type;
    }

    // Filter by bank if provided
    if (bank_id) {
      whereClause.bank_id = bank_id;
    }

    // Filter by status if provided
    if (status) {
      whereClause.status = status;
    }

    // Branch-level roles can only see transactions from their branch
    if (req.user.role_name === 'branch_admin' ||
        req.user.role_name === 'operations_manager' ||
        req.user.role_name === 'assistant_operations_manager') {
      whereClause.branch_id = req.user.branch_id;
    }

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Get banking transactions
    const { count, rows: transactions } = await BankingTransaction.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Branch,
          attributes: ['id', 'name', 'location', 'region_id'],
          include: [
            {
              model: Region,
              attributes: ['id', 'name', 'code'],
              required: false,
            },
          ],
        },
        {
          model: User,
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'Approver',
          attributes: ['id', 'name', 'email']
        },
        {
          model: BankingTransactionReceipt,
          as: 'receipts',
          attributes: ['id', 'file_name', 'file_path', 'file_type', 'file_size', 'created_at']
        },
        {
          model: Bank,
          attributes: ['id', 'name', 'code', 'type']
        }
      ],
      order: [['transaction_date', 'DESC'], ['created_at', 'DESC']],
      limit: parseInt(limit),
      offset
    });

    // Format transactions
    const formattedTransactions = transactions.map(transaction => {
      // Determine bank name based on banking method
      let bankName = 'Unknown Bank';
      if (transaction.banking_method === 'mpesa') {
        bankName = 'M-Pesa';
      } else if (transaction.Bank?.name) {
        bankName = transaction.Bank.name;
      }

      return {
        id: transaction.id,
        branch_id: transaction.branch_id,
        branch_name: transaction.Branch?.name || 'Unknown Branch',
        branch_location: transaction.Branch?.location || 'Unknown Location',
        region_id: transaction.Branch?.region_id || null,
        region_name: transaction.Branch?.Region?.name || 'Unknown Region',
        user_id: transaction.user_id,
        user_name: transaction.User?.name || 'Unknown User',
        transaction_date: transaction.transaction_date,
        amount: parseFloat(transaction.amount || 0),
        banking_method: transaction.banking_method,
        transaction_type: transaction.transaction_type,
        bank_id: transaction.bank_id,
        bank_name: bankName,
        bank_type: transaction.Bank?.type || 'bank',
        reference_number: transaction.reference_number,
        status: transaction.status,
        approval_status: transaction.approval_status,
        approved_by: transaction.approved_by,
        approver_name: transaction.Approver?.name || null,
        approval_date: transaction.approval_date,
        rejection_reason: transaction.rejection_reason,
        expected_amount: parseFloat(transaction.expected_amount || 0),
        discrepancy: parseFloat(transaction.discrepancy || 0),
        notes: transaction.notes,
        has_receipt: transaction.receipts && transaction.receipts.length > 0,
        receipts: transaction.receipts?.map(receipt => ({
          id: receipt.id,
          file_name: receipt.file_name,
          file_type: receipt.file_type,
          created_at: receipt.created_at
        })),
        created_by: transaction.created_by,
        creator_name: transaction.Creator?.name || 'Unknown',
        created_at: transaction.created_at
      };
    });

    // Calculate summary statistics
    const totalAmount = await BankingTransaction.sum('amount', { where: whereClause });
    const totalCount = count;
    const methodCounts = await BankingTransaction.findAll({
      attributes: [
        'banking_method',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('amount')), 'total']
      ],
      where: whereClause,
      group: ['banking_method']
    });

    const methodSummary = methodCounts.map(item => ({
      method: item.banking_method,
      count: parseInt(item.dataValues.count, 10),
      total: parseFloat(item.dataValues.total || 0)
    }));

    // Calculate type summary if transaction_type exists
    let typeSummary = [];
    if (await hasTransactionTypeColumn()) {
      const typeCounts = await BankingTransaction.findAll({
        attributes: [
          'transaction_type',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
          [sequelize.fn('SUM', sequelize.col('amount')), 'total']
        ],
        where: whereClause,
        group: ['transaction_type']
      });

      typeSummary = typeCounts.map(item => ({
        type: item.transaction_type,
        count: parseInt(item.dataValues.count, 10),
        total: parseFloat(item.dataValues.total || 0)
      }));
    }

    // Prepare response
    const response = {
      status: 'success',
      filters: {
        start_date: start_date || null,
        end_date: end_date || null,
        branch_id: branch_id || null,
        region_id: region_id || null,
        banking_method: banking_method || null,
        transaction_type: transaction_type || null,
        bank_id: bank_id || null,
        status: status || null
      },
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      },
      summary: {
        total_transactions: totalCount,
        total_amount: parseFloat(totalAmount || 0),
        by_method: methodSummary,
        by_type: typeSummary
      },
      transactions: formattedTransactions
    };

    // Return response based on requested format
    if (format === 'excel') {
      return await generateExcelReport(response, res);
    }

    return res.status(200).json(response);
  } catch (error) {
    logger.error(`Error getting banking transactions report: ${error.message}`);
    next(error);
  }
};

/**
 * Check if transaction_type column exists in banking_transactions table
 * @returns {Promise<boolean>} True if column exists, false otherwise
 */
const hasTransactionTypeColumn = async () => {
  try {
    // Try to query with transaction_type column
    await BankingTransaction.findOne({
      attributes: ['transaction_type'],
      limit: 1
    });
    return true;
  } catch (error) {
    // If error contains "unknown column", the column doesn't exist
    if (error.message.includes('unknown column')) {
      return false;
    }
    throw error;
  }
};

/**
 * Generate Excel report for banking transactions
 * @param {Object} data - Report data
 * @param {Object} res - Express response object
 */
const generateExcelReport = async (data, res) => {
  try {
    const workbook = new ExcelJS.Workbook();

    // Add summary sheet
    const summarySheet = workbook.addWorksheet('Summary');
    summarySheet.columns = [
      { header: 'Metric', key: 'metric', width: 30 },
      { header: 'Value', key: 'value', width: 20 }
    ];

    // Add summary data
    summarySheet.addRow({ metric: 'Report Period', value: data.filters.start_date && data.filters.end_date ? `${data.filters.start_date} to ${data.filters.end_date}` : 'All Time' });
    summarySheet.addRow({ metric: 'Total Transactions', value: data.summary.total_transactions });
    summarySheet.addRow({ metric: 'Total Amount', value: data.summary.total_amount });

    // Add method summary
    summarySheet.addRow({ metric: '', value: '' });
    summarySheet.addRow({ metric: 'By Banking Method', value: '' });
    data.summary.by_method.forEach(method => {
      summarySheet.addRow({ metric: `${method.method} (${method.count})`, value: method.total });
    });

    // Add type summary if available
    if (data.summary.by_type && data.summary.by_type.length > 0) {
      summarySheet.addRow({ metric: '', value: '' });
      summarySheet.addRow({ metric: 'By Transaction Type', value: '' });
      data.summary.by_type.forEach(type => {
        summarySheet.addRow({ metric: `${type.type || 'Unknown'} (${type.count})`, value: type.total });
      });
    }

    // Style the summary sheet
    summarySheet.getColumn('metric').font = { bold: true };
    summarySheet.getRow(1).font = { bold: true };

    // Add transactions sheet
    const transactionsSheet = workbook.addWorksheet('Transactions');
    transactionsSheet.columns = [
      { header: 'ID', key: 'id', width: 10 },
      { header: 'Date', key: 'transaction_date', width: 15 },
      { header: 'Branch', key: 'branch_name', width: 20 },
      { header: 'Region', key: 'region_name', width: 20 },
      { header: 'User', key: 'user_name', width: 20 },
      { header: 'Amount', key: 'amount', width: 15 },
      { header: 'Method', key: 'banking_method', width: 15 },
      { header: 'Type', key: 'transaction_type', width: 15 },
      { header: 'Bank/Agent', key: 'bank_name', width: 20 },
      { header: 'Reference', key: 'reference_number', width: 20 },
      { header: 'Status', key: 'status', width: 15 },
      { header: 'Approval Status', key: 'approval_status', width: 15 },
      { header: 'Approved By', key: 'approver_name', width: 20 },
      { header: 'Approval Date', key: 'approval_date', width: 20 },
      { header: 'Expected Amount', key: 'expected_amount', width: 15 },
      { header: 'Discrepancy', key: 'discrepancy', width: 15 },
      { header: 'Has Receipt', key: 'has_receipt', width: 15 },
      { header: 'Created By', key: 'creator_name', width: 20 },
      { header: 'Created At', key: 'created_at', width: 20 },
      { header: 'Notes', key: 'notes', width: 30 }
    ];

    // Add transaction data
    data.transactions.forEach(transaction => {
      transactionsSheet.addRow({
        id: transaction.id,
        transaction_date: transaction.transaction_date,
        branch_name: transaction.branch_name,
        region_name: transaction.region_name,
        user_name: transaction.user_name,
        amount: transaction.amount,
        banking_method: transaction.banking_method,
        transaction_type: transaction.transaction_type,
        bank_name: transaction.bank_name,
        reference_number: transaction.reference_number,
        status: transaction.status,
        approval_status: transaction.approval_status,
        approver_name: transaction.approver_name,
        approval_date: transaction.approval_date,
        expected_amount: transaction.expected_amount,
        discrepancy: transaction.discrepancy,
        has_receipt: transaction.has_receipt ? 'Yes' : 'No',
        creator_name: transaction.creator_name,
        created_at: transaction.created_at,
        notes: transaction.notes
      });
    });

    // Style the transactions sheet
    transactionsSheet.getRow(1).font = { bold: true };
    transactionsSheet.autoFilter = {
      from: { row: 1, column: 1 },
      to: { row: 1, column: 20 }
    };

    // Set response headers
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=banking-transactions-${moment().format('YYYY-MM-DD')}.xlsx`);

    // Write to response
    await workbook.xlsx.write(res);

    return res.end();
  } catch (error) {
    logger.error(`Error generating Excel report: ${error.message}`);
    throw error;
  }
};

module.exports = {
  getBankingTransactionsReport
};
