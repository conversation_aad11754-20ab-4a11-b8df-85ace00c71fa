const mysql = require('mysql2/promise');
require('dotenv').config();

const logger = {
  info: (msg) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
  error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`),
  success: (msg) => console.log(`[SUCCESS] ${new Date().toISOString()} - ${msg}`)
};

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dukalink_api',
  port: process.env.DB_PORT || 3306
};

async function testBankingPermissionFix() {
  let connection;
  
  try {
    logger.info('🔧 Testing banking permission fix...');
    
    // Connect to database
    logger.info('Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    
    // Step 1: Test the exact permission check that the controller uses
    logger.info('=== STEP 1: Testing float_manager banking_approval permission ===');
    
    const mappedRole = 'float_manager'; // This is what the controller will use
    
    const [approvalPermission] = await connection.execute(`
      SELECT role, resource, action, attributes 
      FROM rbac_grants 
      WHERE role = ? AND resource = ? AND action = ?
    `, [mappedRole, 'banking_approval', 'create:any']);
    
    if (approvalPermission.length > 0) {
      logger.success(`✅ banking_approval permission found for ${mappedRole}:`);
      logger.info(`   - Role: ${approvalPermission[0].role}`);
      logger.info(`   - Resource: ${approvalPermission[0].resource}`);
      logger.info(`   - Action: ${approvalPermission[0].action}`);
      logger.info(`   - Attributes: ${approvalPermission[0].attributes}`);
    } else {
      logger.error(`❌ banking_approval permission NOT found for ${mappedRole}`);
    }
    
    // Step 2: Test banking_rejection permission
    logger.info('=== STEP 2: Testing float_manager banking_rejection permission ===');
    
    const [rejectionPermission] = await connection.execute(`
      SELECT role, resource, action, attributes 
      FROM rbac_grants 
      WHERE role = ? AND resource = ? AND action = ?
    `, [mappedRole, 'banking_rejection', 'create:any']);
    
    if (rejectionPermission.length > 0) {
      logger.success(`✅ banking_rejection permission found for ${mappedRole}:`);
      logger.info(`   - Role: ${rejectionPermission[0].role}`);
      logger.info(`   - Resource: ${rejectionPermission[0].resource}`);
      logger.info(`   - Action: ${rejectionPermission[0].action}`);
      logger.info(`   - Attributes: ${rejectionPermission[0].attributes}`);
    } else {
      logger.error(`❌ banking_rejection permission NOT found for ${mappedRole}`);
    }
    
    // Step 3: Test the RBACGrants model import
    logger.info('=== STEP 3: Testing RBACGrants model import ===');
    
    try {
      // Add the project root to the require path
      const path = require('path');
      const projectRoot = path.resolve(__dirname, '..');
      process.env.NODE_PATH = projectRoot;
      require('module').Module._initPaths();
      
      const { RBACGrants } = require('../src/models');
      
      if (RBACGrants) {
        logger.success('✅ RBACGrants model imported successfully');
        
        // Test the exact query that the controller uses
        const permission = await RBACGrants.findOne({
          where: {
            role: mappedRole,
            resource: "banking_approval",
            action: "create:any"
          }
        });
        
        if (permission) {
          logger.success('✅ RBACGrants.findOne() works correctly');
          logger.info(`   - Found permission: ${permission.role} -> ${permission.resource} -> ${permission.action}`);
        } else {
          logger.error('❌ RBACGrants.findOne() returned null');
        }
        
      } else {
        logger.error('❌ RBACGrants model is undefined');
      }
      
    } catch (modelError) {
      logger.error(`❌ Error testing RBACGrants model: ${modelError.message}`);
    }
    
    // Step 4: Check float_manager user exists
    logger.info('=== STEP 4: Checking float_manager user ===');
    
    const [floatManagerUsers] = await connection.execute(`
      SELECT id, name, email, role_name 
      FROM users 
      WHERE role_name = 'float_manager' AND deleted_at IS NULL
    `);
    
    if (floatManagerUsers.length > 0) {
      logger.success(`✅ Found ${floatManagerUsers.length} float_manager user(s):`);
      floatManagerUsers.forEach(user => {
        logger.info(`   - ${user.name} (${user.email}) - ID: ${user.id}`);
      });
    } else {
      logger.error('❌ No float_manager users found');
    }
    
    // Step 5: Check pending banking transactions
    logger.info('=== STEP 5: Checking pending banking transactions ===');
    
    const [pendingTransactions] = await connection.execute(`
      SELECT id, amount, banking_method, status, created_at 
      FROM banking_transactions 
      WHERE status = 'pending' AND deleted_at IS NULL 
      ORDER BY created_at DESC 
      LIMIT 3
    `);
    
    if (pendingTransactions.length > 0) {
      logger.success(`✅ Found ${pendingTransactions.length} pending banking transaction(s) for testing:`);
      pendingTransactions.forEach(txn => {
        logger.info(`   - ID: ${txn.id}, Amount: ${txn.amount}, Method: ${txn.banking_method}, Status: ${txn.status}`);
      });
    } else {
      logger.warn('⚠️ No pending banking transactions found for testing');
    }
    
    // Step 6: Summary
    logger.info('=== SUMMARY ===');
    
    const hasApprovalPerm = approvalPermission.length > 0;
    const hasRejectionPerm = rejectionPermission.length > 0;
    const hasFloatManagerUser = floatManagerUsers.length > 0;
    const hasPendingTxns = pendingTransactions.length > 0;
    
    if (hasApprovalPerm && hasRejectionPerm && hasFloatManagerUser) {
      logger.success('🎉 ALL CHECKS PASSED! Banking approval should work now.');
      
      if (hasPendingTxns) {
        logger.info('\n🔄 READY FOR TESTING:');
        logger.info('1. Login as float_manager user');
        logger.info('2. Try to approve/reject a pending banking transaction');
        logger.info('3. Should no longer get "Only company admins can approve" error');
      } else {
        logger.info('\n📝 NOTE: Create a pending banking transaction to test approval');
      }
      
    } else {
      logger.error('❌ Some checks failed:');
      if (!hasApprovalPerm) logger.error('   - Missing banking_approval permission');
      if (!hasRejectionPerm) logger.error('   - Missing banking_rejection permission');
      if (!hasFloatManagerUser) logger.error('   - No float_manager user found');
    }
    
  } catch (error) {
    logger.error(`Error testing banking permission fix: ${error.message}`);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      logger.info('Database connection closed.');
    }
  }
}

// Execute the script
if (require.main === module) {
  testBankingPermissionFix()
    .then(() => {
      logger.success('🎉 Banking permission fix test completed!');
      process.exit(0);
    })
    .catch((error) => {
      logger.error(`❌ Banking permission fix test failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { testBankingPermissionFix };
