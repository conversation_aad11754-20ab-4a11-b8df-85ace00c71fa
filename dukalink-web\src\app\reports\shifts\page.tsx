"use client";

import { useState } from "react";
import { MainLayout } from "@/components/layouts/main-layout";
import { ReportFilters } from "@/features/reports/components/report-filters";
import { ReportDataTable } from "@/features/reports/components/report-data-table";
import { useShiftsReport } from "@/features/reports/hooks/use-reports";
import { ReportFilterParams } from "@/types";
import { Eye } from "lucide-react";
import { format, subDays } from "date-fns";
import { formatCurrency } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import { exportTableData } from "@/lib/export-utils";

export default function ShiftsPage() {
  const router = useRouter();

  // Initialize with default filters (last 7 days)
  const today = new Date();
  const sevenDaysAgo = subDays(today, 7);

  const [filters, setFilters] = useState<ReportFilterParams>({
    start_date: format(sevenDaysAgo, "yyyy-MM-dd"),
    end_date: format(today, "yyyy-MM-dd"),
  });

  const { data, isLoading, error } = useShiftsReport(filters);

  const handleFilterChange = (newFilters: ReportFilterParams) => {
    setFilters(newFilters);
  };

  const handleViewShift = (shiftId: number) => {
    router.push(`/pos/sessions/${shiftId}`);
  };

  const handleCustomExport = () => {
    if (!data?.shiftsData || data.shiftsData.length === 0) {
      return;
    }

    const exportColumns = [
      { key: "id", header: "Shift #" },
      { key: "startTime", header: "Start Time", format: (value: string) => format(new Date(value), "PPp") },
      { key: "endTime", header: "End Time", format: (value: string) => value ? format(new Date(value), "PPp") : "In Progress" },
      { key: "userName", header: "Employee" },
      { key: "branchName", header: "Branch" },
      { key: "regionName", header: "Region" },
      { key: "totalTransactions", header: "Sales Transactions" },
      { key: "mpesaTransactionCount", header: "M-Pesa Transactions" },
      { key: "totalSales", header: "Total Sales", format: (value: number) => formatCurrency(value) },
      { key: "reconciliation.closing_cash_balance", header: "Closing Cash", format: (value: number) => value ? formatCurrency(value) : "N/A" },
      { key: "reconciliation.closing_mpesa_float", header: "Closing Float", format: (value: number) => value ? formatCurrency(value) : "N/A" },
      { key: "expenses", header: "Expenses", format: (value: number) => formatCurrency(value) },
      { key: "status", header: "Status", format: (value: string) => value === "open" ? "Open" : "Closed" },
    ];

    // Generate filename with region filter if applied
    let filename = "shifts-report";
    if (filters.region_id) {
      const regionName = data.shiftsData.find(shift => shift.regionId === filters.region_id)?.regionName;
      if (regionName) {
        filename += `-${regionName.toLowerCase().replace(/\s+/g, '-')}`;
      }
    }
    if (filters.branch_id) {
      const branchName = data.shiftsData.find(shift => shift.branchId === filters.branch_id)?.branchName;
      if (branchName) {
        filename += `-${branchName.toLowerCase().replace(/\s+/g, '-')}`;
      }
    }

    exportTableData(data.shiftsData, exportColumns, filename);
  };

  // Define columns for the shifts table
  const columns = [
    {
      accessorKey: "id",
      header: "Shift #",
    },
    {
      accessorKey: "startTime",
      header: "Start Time",
      cell: ({ row }) => format(new Date(row.original.startTime), "PPp"),
    },
    {
      accessorKey: "endTime",
      header: "End Time",
      cell: ({ row }) =>
        row.original.endTime ? (
          format(new Date(row.original.endTime), "PPp")
        ) : (
          <span className="flex items-center text-primary font-medium">
            <span className="relative flex h-2 w-2 mr-2">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary opacity-75"></span>
              <span className="relative inline-flex rounded-full h-2 w-2 bg-primary"></span>
            </span>
            In Progress
          </span>
        ),
    },
    {
      accessorKey: "userName",
      header: "Employee",
    },
    {
      accessorKey: "branchName",
      header: "Branch",
    },
    {
      accessorKey: "regionName",
      header: "Region",
      cell: ({ row }) => row.original.regionName || "Unknown",
    },
    {
      accessorKey: "totalTransactions",
      header: "Sales Transactions",
      cell: ({ row }) => row.original.totalTransactions.toLocaleString(),
    },
    {
      accessorKey: "mpesaTransactionCount",
      header: "M-Pesa Transactions",
      cell: ({ row }) => (row.original.mpesaTransactionCount || 0).toLocaleString(),
    },
    {
      accessorKey: "totalSales",
      header: "Total Sales",
      cell: ({ row }) => formatCurrency(row.original.totalSales),
    },
    {
        accessorKey: "reconciliation.closing_cash_balance",
        header: "Closing Cash",
        cell: ({ row }) => {
          const closingCash = row.original.reconciliation?.closing_cash_balance;
          return <span>{closingCash || "N/A"}</span>;
        },
    },
     {
        accessorKey: "reconciliation.closing_mpesa_float",
        header: "Closing float",
        cell: ({ row }) => {
          const closingFloat = row.original.reconciliation?.closing_mpesa_float;
          return <span>{closingFloat || "N/A"}</span>;
        },
    },
    {
      header: "Expenses",
      accessorKey: "expenses",
      cell: ({ row }) => {
        const amount = row.original.expenses;
        return amount != null ? `KES ${amount.toFixed(2)}` : "N/A";
      },
    },

    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const isOpen = row.original.status !== "closed";
        return (
          <Badge
            variant={isOpen ? "default" : "secondary"}
            className={
              isOpen
                ? "bg-primary text-primary-foreground hover:bg-primary/90"
                : ""
            }
          >
            {isOpen ? "Open" : "Closed"}
          </Badge>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => handleViewShift(row.original.id)}
        >
          <Eye className="h-4 w-4" />
        </Button>
      ),
    },
  ];

  return (
    <MainLayout>
      <div className="space-y-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Shifts</h1>
          <p className="text-muted-foreground">
            View POS session (shift) reports
          </p>
        </div>

        <ReportFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          showTimeFilter={false}
          showBranchFilter={true}
          showRegionFilter={true}
          showUserFilter={true}
          showSessionFilter={false}
        />

        {isLoading ? (
          <Skeleton className="h-96" />
        ) : error ? (
          <div className="rounded-md bg-destructive/10 p-4 text-destructive">
            Error loading shifts data. Please try again.
          </div>
        ) : data ? (
          <>
             {/* Custom Export Button */}
            <div className="flex justify-end mt-4">
              <Button
                onClick={handleCustomExport}
                disabled={!data?.shiftsData || data.shiftsData.length === 0}
                variant="outline"
              >
                Export to Excel
              </Button>
            </div>
            <ReportDataTable
              columns={columns}
              data={data.shiftsData}
              title="Shifts"
              description="List of all POS sessions (shifts)"
              searchColumn="userName"
              searchPlaceholder="Search by employee name..."
              exportFilename="shifts-report"
              showExport={false}
              rowClassName={(row) =>
                row.original.status !== "closed"
                  ? "bg-primary/5 hover:bg-primary/10"
                  : ""
              }
            />
          </>
        ) : null}
      </div>
    </MainLayout>
  );
}
